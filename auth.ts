import NextAuth from "next-auth"
import { PrismaClient } from "./src/generated/prisma/client"
import Credentials from "next-auth/providers/credentials"
import bcrypt from "bcryptjs"
import { z } from "zod"

const prisma = new PrismaClient()

// Validation schema for credentials
const signInSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
})

export const { handlers, signIn, signOut, auth } = NextAuth({
  secret: process.env.AUTH_SECRET,
  session: { 
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  providers: [
    Credentials({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        try {
          // Validate input
          const { email, password } = await signInSchema.parseAsync(credentials)

          // Find user in database
          const user = await prisma.user.findUnique({
            where: { email: email.toLowerCase() },
          })

          if (!user || !user.password) {
            console.log("User not found or no password set for:", email)
            return null
          }

          // Verify password
          const isPasswordValid = await bcrypt.compare(password, user.password)
          if (!isPasswordValid) {
            console.log("Invalid password for user:", email)
            return null
          }

          // Return user object
          const userObj = {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
            role: user.role,
          }
          console.log("User authenticated successfully:", user.email)
          return userObj
        } catch (error) {
          console.error("Authentication error:", error)
          return null
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      // Persist user data to token on first sign in
      if (user) {
        token.id = user.id
        token.role = user.role
      }
      return token
    },
    async session({ session, token }) {
      // Send properties to the client
      if (token) {
        session.user.id = token.id as string
        session.user.role = token.role as string
      }
      return session
    },
    async signIn({ user, account }) {
      // Only allow credentials sign-ins
      if (account?.provider === "credentials") {
        return true
      }
      return false
    },
    async redirect({ url, baseUrl }) {
      // If the URL is relative, make it absolute
      if (url.startsWith("/")) {
        return `${baseUrl}${url}`
      }
      
      // If the URL is the same origin, allow it
      if (url.startsWith(baseUrl)) {
        return url
      }
      
      // Default to base URL for external URLs
      return baseUrl
    },
  },
  events: {
    async signIn({ user }) {
      console.log("User signed in:", user.email)
    },
  },
  debug: process.env.NODE_ENV === "development",
})