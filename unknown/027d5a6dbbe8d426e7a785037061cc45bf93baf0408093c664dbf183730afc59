import { NextRequest, NextResponse } from 'next/server';
import { getAllProjects } from '@/lib/services/projects';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const tag = searchParams.get('tag');
    const limit = searchParams.get('limit');
    
    let projects = await getAllProjects();
    
    // Only return published projects for public API
    projects = projects.filter(project => project.status === 'PUBLISHED');
    
    // Filter by category
    if (category) {
      projects = projects.filter(project => project.category === category);
    }
    
    // Filter by tag
    if (tag) {
      projects = projects.filter(project => project.tags.includes(tag));
    }
    
    // Limit results
    if (limit) {
      const limitNum = parseInt(limit, 10);
      if (!isNaN(limitNum)) {
        projects = projects.slice(0, limitNum);
      }
    }
    
    return NextResponse.json({ projects });
  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    );
  }
}