export interface ServiceBuilding {
  id: string;
  name: string;
  position: [number, number, number];
  size: [number, number, number];
  color: string;
  type: 'data-center' | 'smart-city' | 'consulting' | 'training' | 'media' | 'development';
  description: string;
  features: string[];
  link: string;
}

export interface SmartCityConfig {
  buildings: ServiceBuilding[];
  cameraPosition: [number, number, number];
  groundSize: [number, number];
}