@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --background: 220.0000 23.0769% 94.9020%;
  --foreground: 233.7931 16.0221% 35.4902%;
  --card: 0 0% 100%;
  --card-foreground: 233.7931 16.0221% 35.4902%;
  --popover: 222.8571 15.9091% 82.7451%;
  --popover-foreground: 233.7931 16.0221% 35.4902%;
  --primary: 240 95.0413% 23.7255%;
  --primary-foreground: 0 0% 100%;
  --secondary: 222.8571 15.9091% 82.7451%;
  --secondary-foreground: 233.7931 16.0221% 35.4902%;
  --muted: 220.0000 20.6897% 88.6275%;
  --muted-foreground: 232.8000 10.3734% 47.2549%;
  --accent: 38.1553 81.7460% 50.5882%;
  --accent-foreground: 0 0% 100%;
  --destructive: 347.0769 86.6667% 44.1176%;
  --destructive-foreground: 0 0% 100%;
  --border: 225.0000 13.5593% 76.8627%;
  --input: 222.8571 15.9091% 82.7451%;
  --ring: 240 95.0413% 23.7255%;
  --chart-1: 266.0440 85.0467% 58.0392%;
  --chart-2: 197.0667 96.5665% 45.6863%;
  --chart-3: 109.2308 57.6355% 39.8039%;
  --chart-4: 21.9753 99.1837% 51.9608%;
  --chart-5: 10.8000 58.8235% 66.6667%;
  --sidebar: 220.0000 21.9512% 91.9608%;
  --sidebar-foreground: 233.7931 16.0221% 35.4902%;
  --sidebar-primary: 240 95.0413% 23.7255%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 38.1553 81.7460% 50.5882%;
  --sidebar-accent-foreground: 0 0% 100%;
  --sidebar-border: 225.0000 13.5593% 76.8627%;
  --sidebar-ring: 240 95.0413% 23.7255%;
  --font-sans: Montserrat, sans-serif;
  --font-serif: Plus Jakarta Sans, sans-serif;
  --font-mono: Fira Code, monospace;
  --radius: 0.35rem;
  --shadow-2xs: 0px 4px 6px 0px hsl(240 30% 25% / 0.06);
  --shadow-xs: 0px 4px 6px 0px hsl(240 30% 25% / 0.06);
  --shadow-sm: 0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow: 0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow-md: 0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12);
  --shadow-lg: 0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 4px 6px -1px hsl(240 30% 25% / 0.12);
  --shadow-xl: 0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12);
  --shadow-2xl: 0px 4px 6px 0px hsl(240 30% 25% / 0.30);
  --tracking-normal: normal;
  --spacing: 0.2rem;
}

.dark {
  --background: 240 21.3115% 11.9608%;
  --foreground: 226.1538 63.9344% 88.0392%;
  --card: 240 21.0526% 14.9020%;
  --card-foreground: 226.1538 63.9344% 88.0392%;
  --popover: 234.2857 13.2075% 31.1765%;
  --popover-foreground: 226.1538 63.9344% 88.0392%;
  --primary: 240 95.0413% 23.7255%;
  --primary-foreground: 0 0% 100%;
  --secondary: 232.5000 12% 39.2157%;
  --secondary-foreground: 226.1538 63.9344% 88.0392%;
  --muted: 230.5263 18.8119% 19.8039%;
  --muted-foreground: 227.6471 23.6111% 71.7647%;
  --accent: 38.1553 81.7460% 50.5882%;
  --accent-foreground: 240 21.0526% 14.9020%;
  --destructive: 343.2692 81.2500% 74.9020%;
  --destructive-foreground: 240 21.0526% 14.9020%;
  --border: 236.8421 16.2393% 22.9412%;
  --input: 236.8421 16.2393% 22.9412%;
  --ring: 240 95.0413% 23.7255%;
  --chart-1: 267.4074 83.5052% 80.9804%;
  --chart-2: 189.1837 71.0145% 72.9412%;
  --chart-3: 115.4545 54.0984% 76.0784%;
  --chart-4: 22.9565 92.0000% 75.4902%;
  --chart-5: 9.6000 55.5556% 91.1765%;
  --sidebar: 240 22.7273% 8.6275%;
  --sidebar-foreground: 226.1538 63.9344% 88.0392%;
  --sidebar-primary: 240 95.0413% 23.7255%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 38.1553 81.7460% 50.5882%;
  --sidebar-accent-foreground: 240 21.0526% 14.9020%;
  --sidebar-border: 234.2857 13.2075% 31.1765%;
  --sidebar-ring: 240 95.0413% 23.7255%;
  --font-sans: Montserrat, sans-serif;
  --font-serif: Plus Jakarta Sans, sans-serif;
  --font-mono: Fira Code, monospace;
  --radius: 0.35rem;
  --shadow-2xs: 0px 4px 6px 0px hsl(240 30% 25% / 0.06);
  --shadow-xs: 0px 4px 6px 0px hsl(240 30% 25% / 0.06);
  --shadow-sm: 0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow: 0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow-md: 0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12);
  --shadow-lg: 0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 4px 6px -1px hsl(240 30% 25% / 0.12);
  --shadow-xl: 0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12);
  --shadow-2xl: 0px 4px 6px 0px hsl(240 30% 25% / 0.30);
}

body {
  letter-spacing: var(--tracking-normal);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}