/* Success Stories Section Styles */
.success-stories-nav {
  background-color: #f9fafc;
  border-radius: 10px;
  padding: 30px;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.success-stories-nav h3 {
  color: #03276e;
  font-weight: 600;
  margin-bottom: 20px;
  border-bottom: 2px solid #e89d1a;
  padding-bottom: 15px;
}

.custom-tabs {
  list-style: none;
  padding: 0;
}

.custom-tabs .nav-item {
  margin-bottom: 15px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.custom-tabs .nav-item:hover {
  background-color: rgba(3, 39, 110, 0.05);
}

.custom-tabs .nav-item.active {
  background-color: rgba(3, 39, 110, 0.1);
  border-left: 3px solid #03276e;
}

.story-tab-item {
  padding: 15px;
}

.story-tab-item h4 {
  font-size: 16px;
  margin: 8px 0 0 0;
  color: #333;
}

.category-badge {
  display: inline-block;
  background-color: #e89d1a;
  color: white;
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 20px;
}

.success-story-content {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  height: 100%;
}

.story-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.story-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-overlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background-color: #03276e;
  color: white;
  padding: 8px 15px;
  border-radius: 5px;
  font-weight: 500;
}

.story-details {
  padding: 30px;
}

.story-details h3 {
  color: #03276e;
  margin-bottom: 20px;
  font-weight: 600;
}

.story-section {
  margin-bottom: 25px;
}

.story-section h4 {
  color: #6F6F87;
  font-size: 18px;
  margin-bottom: 10px;
  font-weight: 500;
}

.results-list {
  list-style: none;
  padding: 0;
}

.results-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}

.results-list li i {
  color: #e89d1a;
  margin-right: 10px;
  margin-top: 5px;
}

.testimonial-box {
  background-color: #f9fafc;
  border-left: 4px solid #e89d1a;
  padding: 20px;
  margin: 30px 0;
  position: relative;
}

.quote-icon {
  position: absolute;
  top: -15px;
  left: 20px;
  background-color: #e89d1a;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quote-text {
  font-style: italic;
  margin-bottom: 15px;
}

.author-name {
  font-weight: 600;
  margin-bottom: 0;
}

.author-position {
  color: #6F6F87;
  font-size: 14px;
}

.story-cta {
  margin-top: 30px;
  text-align: center;
}

/* Services Explorer Styles */
.services-explorer-container {
  margin-top: 50px;
}

.services-categories {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 40px;
}

.category-item {
  flex: 1;
  min-width: 150px;
  text-align: center;
  padding: 20px 15px;
  cursor: pointer;
  border-radius: 10px;
  transition: all 0.3s ease;
  margin: 0 10px 20px;
  background-color: white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.category-item:hover {
  transform: translateY(-5px);
}

.category-item.active {
  background-color: #f9fafc;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.category-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  color: white;
  font-size: 24px;
}

.category-item h4 {
  font-size: 16px;
  margin: 0;
  color: #333;
}

.services-content-wrapper {
  display: flex;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.services-list {
  width: 30%;
  background-color: #f9fafc;
  padding: 30px;
  border-right: 1px solid #ededed;
}

.services-list h3 {
  color: #03276e;
  font-weight: 600;
  margin-bottom: 20px;
}

.services-list ul {
  list-style: none;
  padding: 0;
}

.service-list-item {
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.service-list-item:hover {
  background-color: rgba(3, 39, 110, 0.05);
}

.service-list-item.active {
  background-color: #03276e;
  color: white;
}

.service-list-item h4 {
  font-size: 16px;
  margin: 0;
}

.service-details {
  width: 70%;
  padding: 30px;
  transition: opacity 0.3s ease;
}

.service-details.fade-out {
  opacity: 0;
}

.service-details.fade-in {
  opacity: 1;
}

.service-image {
  margin-bottom: 25px;
  height: 250px;
  overflow: hidden;
  border-radius: 8px;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-info h3 {
  color: #03276e;
  margin-bottom: 15px;
  font-weight: 600;
}

.service-features {
  margin: 25px 0;
}

.service-features h4 {
  color: #6F6F87;
  font-size: 18px;
  margin-bottom: 15px;
  font-weight: 500;
}

.service-features ul {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.service-features li {
  display: flex;
  align-items: center;
}

.service-features li i {
  color: #e89d1a;
  margin-right: 10px;
}

/* Technology Stack Styles */
.tech-stack-section {
  background-color: #f9fafc;
}

.tech-categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
}

.tech-category {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 25px;
  background-color: white;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.tech-category:hover {
  transform: translateY(-5px);
}

.tech-category.active {
  background-color: #03276e;
}

.tech-category.active h4 {
  color: white;
}

.tech-category.active .category-icon {
  background-color: white;
  color: #03276e;
}

.tech-category .category-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #03276e;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 20px;
}

.tech-category h4 {
  font-size: 16px;
  margin: 0;
  color: #333;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

.tech-card {
  background-color: white;
  border-radius: 10px;
  padding: 25px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.5s ease;
  opacity: 0;
  transform: translateY(20px);
}

.tech-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.tech-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tech-logo img {
  max-width: 100%;
  max-height: 100%;
}

.tech-info h4 {
  color: #03276e;
  margin-bottom: 10px;
  font-weight: 600;
}

.tech-info p {
  color: #6F6F87;
  font-size: 14px;
  margin: 0;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .services-content-wrapper {
    flex-direction: column;
  }
  
  .services-list, .service-details {
    width: 100%;
  }
  
  .services-list {
    border-right: none;
    border-bottom: 1px solid #ededed;
  }
  
  .service-features ul {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .category-item {
    min-width: 120px;
  }
  
  .tech-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

@media (max-width: 576px) {
  .tech-categories {
    flex-direction: column;
    align-items: center;
  }
  
  .tech-category {
    width: 80%;
  }
}