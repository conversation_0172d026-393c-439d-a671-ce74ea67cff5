import { NextRequest, NextResponse } from 'next/server'
import { BlockService } from '@/lib/services/block.service'

// GET /api/blocks/types - Get all available block types
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const grouped = searchParams.get('grouped') === 'true'

    if (grouped) {
      // Return block types grouped by category
      const blockTypesByCategory = BlockService.getBlockTypesByCategory()
      
      // Filter by category if specified
      if (category) {
        const filteredTypes = blockTypesByCategory[category] || []
        return NextResponse.json({
          blockTypes: { [category]: filteredTypes }
        })
      }

      return NextResponse.json({
        blockTypes: blockTypesByCategory
      })
    } else {
      // Return flat list of block types
      let blockTypes = BlockService.getBlockTypes()
      
      // Filter by category if specified
      if (category) {
        blockTypes = blockTypes.filter(type => type.category === category)
      }

      return NextResponse.json({
        blockTypes
      })
    }

  } catch (error) {
    console.error('Block types API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch block types' },
      { status: 500 }
    )
  }
}
