import { NextRequest, NextResponse } from 'next/server'
import { BlockService } from '@/lib/services/block.service'

// GET /api/blocks/types/[type] - Get a specific block type
export async function GET(
  request: NextRequest,
  { params }: { params: { type: string } }
) {
  try {
    const blockType = BlockService.getBlockType(params.type)

    if (!blockType) {
      return NextResponse.json(
        { error: 'Block type not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      blockType
    })

  } catch (error) {
    console.error('Block type API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch block type' },
      { status: 500 }
    )
  }
}

// POST /api/blocks/types/[type]/validate - Validate block data against schema
export async function POST(
  request: NextRequest,
  { params }: { params: { type: string } }
) {
  try {
    const blockType = BlockService.getBlockType(params.type)

    if (!blockType) {
      return NextResponse.json(
        { error: 'Block type not found' },
        { status: 404 }
      )
    }

    const body = await request.json()
    const { data } = body

    if (!data) {
      return NextResponse.json(
        { error: 'Block data is required' },
        { status: 400 }
      )
    }

    const validation = BlockService.validateBlockData(params.type, data)

    return NextResponse.json({
      valid: validation.valid,
      errors: validation.errors || []
    })

  } catch (error) {
    console.error('Block validation API error:', error)
    return NextResponse.json(
      { error: 'Failed to validate block data' },
      { status: 500 }
    )
  }
}
