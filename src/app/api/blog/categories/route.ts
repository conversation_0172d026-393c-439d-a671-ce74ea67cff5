import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { BlogService } from '@/lib/services/blog.service'

// GET /api/blog/categories - Get all blog categories
export async function GET(request: NextRequest) {
  try {
    const categories = await BlogService.getCategories()

    return NextResponse.json({
      success: true,
      data: categories
    })

  } catch (error) {
    console.error('Blog categories API error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch blog categories' 
      },
      { status: 500 }
    )
  }
}

// POST /api/blog/categories - Create a new blog category
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Authentication required' 
        },
        { status: 401 }
      )
    }

    const body = await request.json()
    
    const category = await BlogService.createCategory(body, session.user.id)

    return NextResponse.json({
      success: true,
      data: category
    }, { status: 201 })

  } catch (error) {
    console.error('Create blog category API error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('slug already exists')) {
        return NextResponse.json(
          { 
            success: false,
            error: error.message 
          },
          { status: 409 }
        )
      }
      
      if (error.message.includes('validation')) {
        return NextResponse.json(
          { 
            success: false,
            error: error.message 
          },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to create blog category' 
      },
      { status: 500 }
    )
  }
}
