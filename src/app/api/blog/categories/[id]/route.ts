import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { BlogService } from '@/lib/services/blog.service'

// PUT /api/blog/categories/[id] - Update a category
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Authentication required' 
        },
        { status: 401 }
      )
    }

    const body = await request.json()

    const category = await BlogService.updateCategory(params.id, body)

    return NextResponse.json({
      success: true,
      data: category
    })

  } catch (error) {
    console.error('Update category API error:', error)

    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          {
            success: false,
            error: error.message
          },
          { status: 404 }
        )
      }

      if (error.message.includes('slug already exists')) {
        return NextResponse.json(
          {
            success: false,
            error: error.message
          },
          { status: 409 }
        )
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update category'
      },
      { status: 500 }
    )
  }
}

// DELETE /api/blog/categories/[id] - Delete a category
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Authentication required' 
        },
        { status: 401 }
      )
    }

    await BlogService.deleteCategory(params.id)

    return NextResponse.json({
      success: true,
      data: { message: 'Category deleted successfully' }
    })

  } catch (error) {
    console.error('Delete category API error:', error)

    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          {
            success: false,
            error: error.message
          },
          { status: 404 }
        )
      }

      if (error.message.includes('Cannot delete category')) {
        return NextResponse.json(
          {
            success: false,
            error: error.message
          },
          { status: 409 }
        )
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete category'
      },
      { status: 500 }
    )
  }
}
