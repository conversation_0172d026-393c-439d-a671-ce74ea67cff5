import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { BlogService } from '@/lib/services/blog.service'

// GET /api/blog/[id] - Get a single blog post by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const includeUnpublished = searchParams.get('includeUnpublished') === 'true'
    
    const post = await BlogService.getPostById(params.id, includeUnpublished)

    if (!post) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Blog post not found' 
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: post
    })

  } catch (error) {
    console.error('Get blog post API error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch blog post' 
      },
      { status: 500 }
    )
  }
}

// PUT /api/blog/[id] - Update a blog post
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Authentication required' 
        },
        { status: 401 }
      )
    }

    const body = await request.json()
    const updateData = { ...body, id: params.id }
    
    const post = await BlogService.updatePost(updateData)

    return NextResponse.json({
      success: true,
      data: post
    })

  } catch (error) {
    console.error('Update blog post API error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { 
            success: false,
            error: error.message 
          },
          { status: 404 }
        )
      }
      
      if (error.message.includes('slug already exists')) {
        return NextResponse.json(
          { 
            success: false,
            error: error.message 
          },
          { status: 409 }
        )
      }
      
      if (error.message.includes('validation')) {
        return NextResponse.json(
          { 
            success: false,
            error: error.message 
          },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to update blog post' 
      },
      { status: 500 }
    )
  }
}

// DELETE /api/blog/[id] - Delete a blog post
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Authentication required' 
        },
        { status: 401 }
      )
    }

    await BlogService.deletePost(params.id)

    return NextResponse.json({
      success: true,
      data: { message: 'Blog post deleted successfully' }
    })

  } catch (error) {
    console.error('Delete blog post API error:', error)
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { 
          success: false,
          error: error.message 
        },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to delete blog post' 
      },
      { status: 500 }
    )
  }
}
