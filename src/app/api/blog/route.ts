import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { BlogService } from '@/lib/services/blog.service'
import { BlogPostQueryOptions } from '@/types/blog'

// GET /api/blog - Get all blog posts with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const options: BlogPostQueryOptions = {
      page: parseInt(searchParams.get('page') || '1'),
      perPage: parseInt(searchParams.get('perPage') || '10'),
      orderBy: (searchParams.get('orderBy') as any) || 'createdAt',
      order: (searchParams.get('order') as 'asc' | 'desc') || 'desc',
      status: (searchParams.get('status') as any) || undefined,
      categoryId: searchParams.get('categoryId') || undefined,
      tagIds: searchParams.get('tagIds')?.split(',') || undefined,
      authorId: searchParams.get('authorId') || undefined,
      search: searchParams.get('search') || undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined,
      includeUnpublished: searchParams.get('includeUnpublished') === 'true',
      includeDrafts: searchParams.get('includeDrafts') === 'true'
    }

    const result = await BlogService.getPosts(options)

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('Blog posts API error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch blog posts' 
      },
      { status: 500 }
    )
  }
}

// POST /api/blog - Create a new blog post
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Authentication required' 
        },
        { status: 401 }
      )
    }

    const body = await request.json()
    
    const post = await BlogService.createPost(body, session.user.id)

    return NextResponse.json({
      success: true,
      data: post
    }, { status: 201 })

  } catch (error) {
    console.error('Create blog post API error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('slug already exists')) {
        return NextResponse.json(
          { 
            success: false,
            error: error.message 
          },
          { status: 409 }
        )
      }
      
      if (error.message.includes('validation')) {
        return NextResponse.json(
          { 
            success: false,
            error: error.message 
          },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to create blog post' 
      },
      { status: 500 }
    )
  }
}
