import { NextRequest, NextResponse } from 'next/server'
import { BlogService } from '@/lib/services/blog.service'

// GET /api/blog/search - Search blog posts
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q') || ''
    const limit = parseInt(searchParams.get('limit') || '10')

    if (!query.trim()) {
      return NextResponse.json({
        success: true,
        data: {
          posts: [],
          total: 0,
          query: query
        }
      })
    }

    const posts = await BlogService.searchPosts(query, limit)

    return NextResponse.json({
      success: true,
      data: {
        posts,
        total: posts.length,
        query: query
      }
    })

  } catch (error) {
    console.error('Blog search API error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to search blog posts' 
      },
      { status: 500 }
    )
  }
}
