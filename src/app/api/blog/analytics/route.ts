import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { BlogService } from '@/lib/services/blog.service'

// GET /api/blog/analytics - Get blog analytics and statistics
export async function GET(request: NextRequest) {
  try {
    // Check authentication for admin analytics
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Authentication required' 
        },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'full'

    if (type === 'admin') {
      const stats = await BlogService.getAdminStats()
      return NextResponse.json({
        success: true,
        data: stats
      })
    }

    const analytics = await BlogService.getAnalytics()

    return NextResponse.json({
      success: true,
      data: analytics
    })

  } catch (error) {
    console.error('Blog analytics API error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch blog analytics' 
      },
      { status: 500 }
    )
  }
}
