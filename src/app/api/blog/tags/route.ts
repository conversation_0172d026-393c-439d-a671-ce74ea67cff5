import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { BlogService } from '@/lib/services/blog.service'

// GET /api/blog/tags - Get all blog tags
export async function GET(request: NextRequest) {
  try {
    const tags = await BlogService.getTags()

    return NextResponse.json({
      success: true,
      data: tags
    })

  } catch (error) {
    console.error('Blog tags API error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch blog tags' 
      },
      { status: 500 }
    )
  }
}

// POST /api/blog/tags - Create a new blog tag
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Authentication required' 
        },
        { status: 401 }
      )
    }

    const body = await request.json()
    
    const tag = await BlogService.createTag(body, session.user.id)

    return NextResponse.json({
      success: true,
      data: tag
    }, { status: 201 })

  } catch (error) {
    console.error('Create blog tag API error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('slug already exists')) {
        return NextResponse.json(
          { 
            success: false,
            error: error.message 
          },
          { status: 409 }
        )
      }
      
      if (error.message.includes('validation')) {
        return NextResponse.json(
          { 
            success: false,
            error: error.message 
          },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to create blog tag' 
      },
      { status: 500 }
    )
  }
}
