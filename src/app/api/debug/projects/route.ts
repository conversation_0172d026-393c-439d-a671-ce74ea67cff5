import { NextResponse } from 'next/server';
import { debugProjects } from '@/lib/debug/projects-debug';

export async function GET() {
  try {
    const result = await debugProjects();
    
    return NextResponse.json({
      success: result.success,
      message: result.success 
        ? `Successfully loaded ${result.publishedProjects} published projects out of ${result.totalProjects} total projects`
        : `Error: ${result.error}`,
      data: {
        totalProjects: result.totalProjects,
        publishedProjects: result.publishedProjects,
        projects: result.projects.map(p => ({
          id: p.id,
          title: p.title,
          slug: p.slug,
          status: p.status,
          category: p.category,
          featuredImage: p.featuredImage,
          excerpt: p.excerpt?.substring(0, 100),
          clientName: p.clientName,
          location: p.location
        }))
      }
    });
  } catch (error) {
    console.error('Debug API Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to debug projects',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}