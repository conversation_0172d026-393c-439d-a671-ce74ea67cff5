import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'
import { z } from 'zod'

// Validation schema for updating pages
const updatePageSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  slug: z.string().min(1, 'Slug is required').regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens').optional(),
  content: z.string().optional(),
  template: z.string().optional(),
  status: z.enum(['DRAFT', 'PUBLISHED', 'ARCHIVED']).optional(),
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  featuredImage: z.string().optional(),
  excerpt: z.string().optional(),
})

// GET /api/pages/[slug] - Get a single page by slug
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const preview = searchParams.get('preview') === 'true'
    const version = searchParams.get('version')
    const locale = searchParams.get('locale') || 'en'

    // Build where clause
    const where: any = { slug: params.slug }
    
    // If not in preview mode, only show published pages
    if (!preview) {
      where.status = 'PUBLISHED'
    }

    // Find the page
    const page = await prisma.page.findFirst({
      where,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        blocks: {
          orderBy: { order: 'asc' }
        }
      }
    })

    if (!page) {
      return NextResponse.json(
        { error: 'Page not found' },
        { status: 404 }
      )
    }

    // Get related pages (same template, excluding current page)
    const relatedPages = await prisma.page.findMany({
      where: {
        template: page.template,
        status: 'PUBLISHED',
        id: { not: page.id }
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      },
      take: 3,
      orderBy: { updatedAt: 'desc' }
    })

    // Track page view (only for published pages, not in preview mode)
    if (!preview && page.status === 'PUBLISHED') {
      // Create page view record
      await prisma.pageView.create({
        data: {
          path: `/${page.slug}`,
          userAgent: request.headers.get('user-agent') || '',
          ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '',
          referer: request.headers.get('referer') || ''
        }
      }).catch(error => {
        // Don't fail the request if view tracking fails
        console.error('Failed to track page view:', error)
      })
    }

    return NextResponse.json({
      page,
      relatedPages
    })

  } catch (error) {
    console.error('Get page error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch page' },
      { status: 500 }
    )
  }
}

// PUT /api/pages/[slug] - Update a page
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Find the existing page
    const existingPage = await prisma.page.findUnique({
      where: { slug: params.slug }
    })

    if (!existingPage) {
      return NextResponse.json(
        { error: 'Page not found' },
        { status: 404 }
      )
    }

    // Check if user can edit this page (author or admin)
    if (existingPage.authorId !== session.user.id && session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = updatePageSchema.parse(body)

    // If slug is being changed, check if new slug already exists
    if (validatedData.slug && validatedData.slug !== existingPage.slug) {
      const slugExists = await prisma.page.findUnique({
        where: { slug: validatedData.slug }
      })

      if (slugExists) {
        return NextResponse.json(
          { error: 'A page with this slug already exists' },
          { status: 409 }
        )
      }
    }

    // Update the page
    const updateData: any = { ...validatedData }
    
    // Set publishedAt if status is being changed to PUBLISHED
    if (validatedData.status === 'PUBLISHED' && existingPage.status !== 'PUBLISHED') {
      updateData.publishedAt = new Date()
    } else if (validatedData.status !== 'PUBLISHED') {
      updateData.publishedAt = null
    }

    const page = await prisma.page.update({
      where: { slug: params.slug },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        blocks: {
          orderBy: { order: 'asc' }
        }
      }
    })

    return NextResponse.json({ page })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Update page error:', error)
    return NextResponse.json(
      { error: 'Failed to update page' },
      { status: 500 }
    )
  }
}

// DELETE /api/pages/[slug] - Delete a page
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Find the existing page
    const existingPage = await prisma.page.findUnique({
      where: { slug: params.slug }
    })

    if (!existingPage) {
      return NextResponse.json(
        { error: 'Page not found' },
        { status: 404 }
      )
    }

    // Check if user can delete this page (author or admin)
    if (existingPage.authorId !== session.user.id && session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      )
    }

    // Delete the page (blocks will be deleted automatically due to cascade)
    await prisma.page.delete({
      where: { slug: params.slug }
    })

    return NextResponse.json({ message: 'Page deleted successfully' })

  } catch (error) {
    console.error('Delete page error:', error)
    return NextResponse.json(
      { error: 'Failed to delete page' },
      { status: 500 }
    )
  }
}
