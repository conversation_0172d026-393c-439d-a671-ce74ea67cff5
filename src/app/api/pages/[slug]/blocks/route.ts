import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'
import { z } from 'zod'

// Validation schema for blocks
const blockSchema = z.object({
  type: z.string().min(1, 'Block type is required'),
  data: z.record(z.any()),
  order: z.number().int().min(0).optional().default(0),
})

const blocksArraySchema = z.array(blockSchema)

// GET /api/pages/[slug]/blocks - Get all blocks for a page
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Find the page first
    const page = await prisma.page.findUnique({
      where: { slug: params.slug },
      select: { id: true, status: true }
    })

    if (!page) {
      return NextResponse.json(
        { error: 'Page not found' },
        { status: 404 }
      )
    }

    // Get blocks for the page
    const blocks = await prisma.block.findMany({
      where: { pageId: page.id },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        updater: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      },
      orderBy: { order: 'asc' }
    })

    return NextResponse.json({ blocks })

  } catch (error) {
    console.error('Get page blocks error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch page blocks' },
      { status: 500 }
    )
  }
}

// PUT /api/pages/[slug]/blocks - Update all blocks for a page
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Find the page
    const page = await prisma.page.findUnique({
      where: { slug: params.slug }
    })

    if (!page) {
      return NextResponse.json(
        { error: 'Page not found' },
        { status: 404 }
      )
    }

    // Check if user can edit this page (author or admin)
    if (page.authorId !== session.user.id && session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedBlocks = blocksArraySchema.parse(body.blocks || [])

    // Use transaction to update blocks atomically
    const result = await prisma.$transaction(async (tx) => {
      // Delete existing blocks for this page
      await tx.block.deleteMany({
        where: { pageId: page.id }
      })

      // Create new blocks
      const createdBlocks = []
      for (let i = 0; i < validatedBlocks.length; i++) {
        const blockData = validatedBlocks[i]
        const block = await tx.block.create({
          data: {
            type: blockData.type,
            data: blockData.data,
            order: blockData.order ?? i,
            pageId: page.id,
            createdBy: session.user.id,
            updatedBy: session.user.id
          },
          include: {
            creator: {
              select: {
                id: true,
                name: true,
                image: true
              }
            },
            updater: {
              select: {
                id: true,
                name: true,
                image: true
              }
            }
          }
        })
        createdBlocks.push(block)
      }

      return createdBlocks
    })

    return NextResponse.json({ blocks: result })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Update page blocks error:', error)
    return NextResponse.json(
      { error: 'Failed to update page blocks' },
      { status: 500 }
    )
  }
}

// POST /api/pages/[slug]/blocks - Add a new block to a page
export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Find the page
    const page = await prisma.page.findUnique({
      where: { slug: params.slug }
    })

    if (!page) {
      return NextResponse.json(
        { error: 'Page not found' },
        { status: 404 }
      )
    }

    // Check if user can edit this page (author or admin)
    if (page.authorId !== session.user.id && session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedBlock = blockSchema.parse(body)

    // Get the next order number if not provided
    let order = validatedBlock.order
    if (order === undefined || order === 0) {
      const lastBlock = await prisma.block.findFirst({
        where: { pageId: page.id },
        orderBy: { order: 'desc' }
      })
      order = (lastBlock?.order ?? -1) + 1
    }

    // Create the block
    const block = await prisma.block.create({
      data: {
        type: validatedBlock.type,
        data: validatedBlock.data,
        order,
        pageId: page.id,
        createdBy: session.user.id,
        updatedBy: session.user.id
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        updater: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return NextResponse.json({ block }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Create page block error:', error)
    return NextResponse.json(
      { error: 'Failed to create page block' },
      { status: 500 }
    )
  }
}
