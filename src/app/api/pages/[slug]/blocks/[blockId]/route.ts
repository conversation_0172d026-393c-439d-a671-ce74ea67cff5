import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'
import { z } from 'zod'

// Validation schema for updating a block
const updateBlockSchema = z.object({
  type: z.string().min(1, 'Block type is required').optional(),
  data: z.record(z.any()).optional(),
  order: z.number().int().min(0).optional(),
})

// GET /api/pages/[slug]/blocks/[blockId] - Get a specific block
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string; blockId: string } }
) {
  try {
    // Find the page first
    const page = await prisma.page.findUnique({
      where: { slug: params.slug },
      select: { id: true }
    })

    if (!page) {
      return NextResponse.json(
        { error: 'Page not found' },
        { status: 404 }
      )
    }

    // Find the block
    const block = await prisma.block.findFirst({
      where: {
        id: params.blockId,
        pageId: page.id
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        updater: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    if (!block) {
      return NextResponse.json(
        { error: 'Block not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ block })

  } catch (error) {
    console.error('Get block error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch block' },
      { status: 500 }
    )
  }
}

// PUT /api/pages/[slug]/blocks/[blockId] - Update a specific block
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string; blockId: string } }
) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Find the page
    const page = await prisma.page.findUnique({
      where: { slug: params.slug }
    })

    if (!page) {
      return NextResponse.json(
        { error: 'Page not found' },
        { status: 404 }
      )
    }

    // Check if user can edit this page (author or admin)
    if (page.authorId !== session.user.id && session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      )
    }

    // Find the existing block
    const existingBlock = await prisma.block.findFirst({
      where: {
        id: params.blockId,
        pageId: page.id
      }
    })

    if (!existingBlock) {
      return NextResponse.json(
        { error: 'Block not found' },
        { status: 404 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = updateBlockSchema.parse(body)

    // Update the block
    const block = await prisma.block.update({
      where: { id: params.blockId },
      data: {
        ...validatedData,
        updatedBy: session.user.id
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        updater: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return NextResponse.json({ block })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Update block error:', error)
    return NextResponse.json(
      { error: 'Failed to update block' },
      { status: 500 }
    )
  }
}

// DELETE /api/pages/[slug]/blocks/[blockId] - Delete a specific block
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string; blockId: string } }
) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Find the page
    const page = await prisma.page.findUnique({
      where: { slug: params.slug }
    })

    if (!page) {
      return NextResponse.json(
        { error: 'Page not found' },
        { status: 404 }
      )
    }

    // Check if user can edit this page (author or admin)
    if (page.authorId !== session.user.id && session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      )
    }

    // Find the existing block
    const existingBlock = await prisma.block.findFirst({
      where: {
        id: params.blockId,
        pageId: page.id
      }
    })

    if (!existingBlock) {
      return NextResponse.json(
        { error: 'Block not found' },
        { status: 404 }
      )
    }

    // Delete the block
    await prisma.block.delete({
      where: { id: params.blockId }
    })

    // Reorder remaining blocks to fill the gap
    await prisma.block.updateMany({
      where: {
        pageId: page.id,
        order: { gt: existingBlock.order }
      },
      data: {
        order: { decrement: 1 }
      }
    })

    return NextResponse.json({ message: 'Block deleted successfully' })

  } catch (error) {
    console.error('Delete block error:', error)
    return NextResponse.json(
      { error: 'Failed to delete block' },
      { status: 500 }
    )
  }
}
