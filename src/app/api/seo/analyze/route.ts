import { NextRequest, NextResponse } from 'next/server'
import { SEOService } from '@/lib/services/seo.service'
import { z } from 'zod'

// Validation schema for SEO analysis
const seoAnalysisSchema = z.object({
  title: z.string().optional(),
  description: z.string().optional(),
  keywords: z.array(z.string()).optional(),
  image: z.string().optional(),
  url: z.string(),
  content: z.string().optional(),
  type: z.enum(['website', 'article', 'product', 'profile']).optional(),
})

// POST /api/seo/analyze - Analyze SEO for given data
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = seoAnalysisSchema.parse(body)

    // Validate SEO data
    const validation = SEOService.validateSEO(validatedData)

    // Calculate SEO score
    const scoreAnalysis = SEOService.calculateSEOScore(
      validatedData as any,
      validatedData.content
    )

    // Generate meta tags
    const metaTags = SEOService.generateMetaTags(validatedData as any)

    // Generate structured data
    const structuredData = SEOService.generateStructuredData(
      validatedData.type === 'article' ? 'Article' : 'WebPage',
      {
        title: validatedData.title,
        description: validatedData.description,
        url: validatedData.url,
        image: validatedData.image,
        keywords: validatedData.keywords
      }
    )

    return NextResponse.json({
      validation,
      score: scoreAnalysis,
      metaTags,
      structuredData,
      recommendations: generateRecommendations(validatedData, scoreAnalysis)
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('SEO analysis error:', error)
    return NextResponse.json(
      { error: 'Failed to analyze SEO' },
      { status: 500 }
    )
  }
}

function generateRecommendations(data: any, scoreAnalysis: any): string[] {
  const recommendations: string[] = []

  // Title recommendations
  if (!data.title) {
    recommendations.push('Add a compelling title that includes your main keyword')
  } else if (data.title.length > 60) {
    recommendations.push('Shorten your title to under 60 characters for better display in search results')
  } else if (data.title.length < 10) {
    recommendations.push('Make your title more descriptive (aim for 10-60 characters)')
  }

  // Description recommendations
  if (!data.description) {
    recommendations.push('Add a meta description that summarizes your page content')
  } else if (data.description.length > 160) {
    recommendations.push('Shorten your description to under 160 characters')
  } else if (data.description.length < 50) {
    recommendations.push('Expand your description to better explain your page content')
  }

  // Image recommendations
  if (!data.image) {
    recommendations.push('Add a featured image to improve social media sharing')
  }

  // Keywords recommendations
  if (!data.keywords || data.keywords.length === 0) {
    recommendations.push('Add relevant keywords to help search engines understand your content')
  } else if (data.keywords.length > 10) {
    recommendations.push('Reduce the number of keywords to focus on the most important ones')
  }

  // Content recommendations
  if (data.content) {
    const wordCount = data.content.replace(/<[^>]*>/g, '').split(/\s+/).length
    if (wordCount < 300) {
      recommendations.push('Consider adding more content (aim for 300+ words) to provide more value')
    }
  }

  // Score-based recommendations
  if (scoreAnalysis.score < 70) {
    recommendations.push('Focus on improving your title and description first for the biggest SEO impact')
  }

  return recommendations
}
