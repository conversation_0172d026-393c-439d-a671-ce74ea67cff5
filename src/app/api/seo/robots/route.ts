import { NextRequest, NextResponse } from 'next/server'
import { SEOService } from '@/lib/services/seo.service'

// GET /api/seo/robots - Generate robots.txt
export async function GET(request: NextRequest) {
  try {
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com'
    const robotsContent = SEOService.generateRobotsTxt(siteUrl)

    return new NextResponse(robotsContent, {
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'public, max-age=86400, s-maxage=86400'
      }
    })

  } catch (error) {
    console.error('Robots.txt generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate robots.txt' },
      { status: 500 }
    )
  }
}
