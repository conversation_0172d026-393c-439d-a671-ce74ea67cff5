import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { changePasswordSchema, getUserById, verifyPassword, updateUserPassword } from '@/lib/auth-utils'
import { z } from 'zod'

export async function PUT(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
    
    const body = await request.json()
    const validatedData = changePasswordSchema.parse(body)
    
    // Get current user with password
    const user = await getUserById(session.user.id)
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
    
    // Verify current password
    const isCurrentPasswordValid = await verifyPassword(
      validatedData.currentPassword,
      user.password!
    )
    
    if (!isCurrentPasswordValid) {
      return NextResponse.json(
        { error: 'Current password is incorrect' },
        { status: 400 }
      )
    }
    
    // Update password
    await updateUserPassword(session.user.id, validatedData.newPassword)
    
    return NextResponse.json({
      message: 'Password changed successfully',
    })
    
  } catch (error) {
    console.error('Change password error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}