import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { updateUserProfile } from '@/lib/auth-utils'
import { z } from 'zod'

const updateProfileSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").optional(),
  email: z.string().email("Invalid email address").optional(),
})

export async function PUT(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
    
    const body = await request.json()
    const validatedData = updateProfileSchema.parse(body)
    
    // Update user profile
    const updatedUser = await updateUserProfile(session.user.id, validatedData)
    
    return NextResponse.json({
      message: 'Profile updated successfully',
      user: updatedUser,
    })
    
  } catch (error) {
    console.error('Update profile error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}