import { NextRequest, NextResponse } from 'next/server';
import { getAllProjects, createProject } from '@/lib/services/projects';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    
    let projects = await getAllProjects();
    
    // Filter by status
    if (status && status !== 'all') {
      projects = projects.filter(project => project.status === status);
    }
    
    // Filter by category
    if (category) {
      projects = projects.filter(project => project.category === category);
    }
    
    // Search filter
    if (search) {
      const searchLower = search.toLowerCase();
      projects = projects.filter(project => 
        project.title.toLowerCase().includes(searchLower) ||
        project.description?.toLowerCase().includes(searchLower) ||
        project.clientName?.toLowerCase().includes(searchLower)
      );
    }
    
    return NextResponse.json({ projects });
  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.title || !body.slug) {
      return NextResponse.json(
        { error: 'Title and slug are required' },
        { status: 400 }
      );
    }
    
    const project = await createProject(body);
    
    return NextResponse.json({ project }, { status: 201 });
  } catch (error) {
    console.error('Error creating project:', error);
    return NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    );
  }
}