import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/auth"
import { getAllUsers } from "@/lib/auth-utils"
import { UserRole } from "@/generated/prisma"

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    if (session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      )
    }

    // Get all users
    const users = await getAllUsers()

    return NextResponse.json({ users })

  } catch (error) {
    console.error("Admin users API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    if (session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { userId, role } = body

    if (!userId || !role) {
      return NextResponse.json(
        { error: "User ID and role are required" },
        { status: 400 }
      )
    }

    // Validate role (only ADMIN and USER allowed)
    if (!Object.values(UserRole).includes(role)) {
      return NextResponse.json(
        { error: "Invalid role. Only ADMIN and USER are allowed." },
        { status: 400 }
      )
    }

    // Prevent admin from demoting themselves
    if (userId === session.user.id && role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: "Cannot change your own admin role" },
        { status: 400 }
      )
    }

    // Update user role using our utility function
    const { updateUserRole } = await import("@/lib/auth-utils")
    const updatedUser = await updateUserRole(userId, role)

    return NextResponse.json({
      message: "User role updated successfully",
      user: updatedUser,
    })

  } catch (error) {
    console.error("Admin users update API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}