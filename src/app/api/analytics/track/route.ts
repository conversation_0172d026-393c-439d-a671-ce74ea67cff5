import { NextRequest, NextResponse } from 'next/server'
import { AnalyticsService } from '@/lib/services/analytics.service'
import { z } from 'zod'

// Validation schema for tracking data
const trackingSchema = z.object({
  path: z.string().min(1, 'Path is required'),
  userAgent: z.string().optional(),
  referer: z.string().optional(),
  sessionId: z.string().optional(),
  userId: z.string().optional(),
})

// POST /api/analytics/track - Track a page view
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = trackingSchema.parse(body)

    // Get additional data from headers
    const userAgent = validatedData.userAgent || request.headers.get('user-agent') || ''
    const referer = validatedData.referer || request.headers.get('referer') || ''
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               '127.0.0.1'

    await AnalyticsService.trackPageView({
      path: validatedData.path,
      userAgent,
      ip,
      referer,
      sessionId: validatedData.sessionId,
      userId: validatedData.userId
    })

    return NextResponse.json({ success: true })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Analytics tracking error:', error)
    return NextResponse.json(
      { error: 'Failed to track page view' },
      { status: 500 }
    )
  }
}
