import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { AnalyticsService } from '@/lib/services/analytics.service'
import { UserRole } from '@prisma/client'

// GET /api/analytics/pages/[slug] - Get analytics for a specific page
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Check authentication and authorization
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    if (session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined

    // Get page analytics
    const pageAnalytics = await AnalyticsService.getPageAnalytics(params.slug, {
      startDate,
      endDate
    })

    return NextResponse.json({
      slug: params.slug,
      analytics: pageAnalytics
    })

  } catch (error) {
    console.error('Page analytics error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch page analytics' },
      { status: 500 }
    )
  }
}
