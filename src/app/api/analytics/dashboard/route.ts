import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { AnalyticsService } from '@/lib/services/analytics.service'
import { UserRole } from '@prisma/client'

// GET /api/analytics/dashboard - Get analytics dashboard data
export async function GET(request: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    if (session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined
    const path = searchParams.get('path') || undefined

    // Get analytics data
    const analytics = await AnalyticsService.getAnalytics({
      startDate,
      endDate,
      path
    })

    // Get page performance metrics
    const pageMetrics = await AnalyticsService.getPagePerformanceMetrics({
      startDate,
      endDate,
      limit: 20
    })

    // Get real-time data
    const realTime = await AnalyticsService.getRealTimeAnalytics()

    return NextResponse.json({
      analytics,
      pageMetrics,
      realTime
    })

  } catch (error) {
    console.error('Analytics dashboard error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    )
  }
}
