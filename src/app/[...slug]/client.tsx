'use client'

import { useState, useEffect } from 'react'
import { notFound } from 'next/navigation'
import { DynamicPage } from '@/components/pages'
import { LoadingSpinner } from '@/components/admin/ui/loading-spinner'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, Home } from 'lucide-react'
import Link from 'next/link'

interface DynamicPageClientProps {
  slug: string
  preview: boolean
  version: string
  locale: string
  params: Record<string, string | string[]>
  searchParams: Record<string, string | string[] | undefined>
}

export function DynamicPageClient({
  slug,
  preview,
  version,
  locale,
  params,
  searchParams
}: DynamicPageClientProps) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Simulate loading delay for demo
    const timer = setTimeout(() => {
      setLoading(false)
    }, 500)

    return () => clearTimeout(timer)
  }, [slug])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <LoadingSpinner size="lg" />
          <p className="text-muted-foreground">Loading page...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="h-6 w-6 text-destructive" />
            </div>
            <CardTitle>Error Loading Page</CardTitle>
            <CardDescription>
              {error}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-2">
              <Button 
                variant="outline" 
                className="flex-1"
                onClick={() => window.location.reload()}
              >
                Try Again
              </Button>
              <Link href="/" className="flex-1">
                <Button className="w-full">
                  <Home className="mr-2 h-4 w-4" />
                  Go Home
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <DynamicPage
      slug={slug}
      preview={preview}
      version={version}
      locale={locale}
      params={params}
      searchParams={Object.fromEntries(
        Object.entries(searchParams).filter(([_, value]) => value !== undefined)
      )}
      onError={(error) => setError(error)}
      onNotFound={() => notFound()}
      onPageLoad={(page) => {
        // Page loaded successfully, could track analytics here
        console.log('Page loaded:', page.title)
      }}
    />
  )
}