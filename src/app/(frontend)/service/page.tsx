import fs from 'fs';
import path from 'path';
import Layout from '@/components/layout/Layout';
import ServicesGrid from '@/components/sections/services/ServicesGrid';
import SectionHeader from '@/components/layout/SectionHeader';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: "Our Services | Motshwanelo IT Consulting - Complete IT Solutions Portfolio",
  description: "Explore our comprehensive IT services: Smart City Solutions, Data Centre Infrastructure, Software Development, IT Consulting & Digital Transformation. Enterprise-grade solutions with 24/7 support.",
  keywords: ["IT Services", "Smart City Solutions", "Data Centre", "Software Development", "IT Consulting", "Digital Transformation", "Enterprise Solutions"],
  openGraph: {
    title: "Our Services | Complete IT Solutions Portfolio",
    description: "From Smart City infrastructure to enterprise software development - discover our comprehensive IT services that transform businesses across Africa.",
    type: "website",
    url: "https://motshwaneloitconsulting.co.za/service",
  },
};

// Helper to get all services
function getAllServices() {
  const dir = path.join(process.cwd(), 'src/data/services');
  const files = fs.readdirSync(dir).filter(f => f.endsWith('.json'));
  return files.map(file => {
    const id = file.replace('.json', '');
    const data = JSON.parse(fs.readFileSync(path.join(dir, file), 'utf-8'));
    return { id, ...data };
  });
}

export default function ServicesPage() {
  const services = getAllServices();
  return (
    <Layout>
      <SectionHeader 
        title="Our Complete IT Solutions Portfolio" 
        group_page="From Smart Cities to Enterprise Software" 
        current_page="Services" 
        display="d-none" 
      />
      <ServicesGrid services={services} />
    </Layout>
  );
}
