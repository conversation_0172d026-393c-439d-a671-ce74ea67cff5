import fs from 'fs';
import path from 'path';
import { notFound } from 'next/navigation';
import Layout from '@/components/layout/Layout';
import ServiceBanner from '@/components/sections/service-details/ServiceBanner';
import ServiceAbout from '@/components/sections/service-details/ServiceAbout';
import ServiceFeatures from '@/components/sections/service-details/ServiceFeatures';
import ServiceRequestForm from '@/components/sections/service-details/ServiceRequestForm';

export async function generateStaticParams() {
  const dir = path.join(process.cwd(), 'src/data/services');
  const files = fs.readdirSync(dir);
  return files.map((file) => ({ id: file.replace('.json', '') }));
}

async function getServiceData(id: string) {
  const filePath = path.join(process.cwd(), 'src/data/services', `${id}.json`);
  if (!fs.existsSync(filePath)) return null;
  const data = fs.readFileSync(filePath, 'utf-8');
  return JSON.parse(data);
}

export default async function ServicePage({ params }: { params: { id: string } }) {
  const data = await getServiceData(params.id);
  if (!data) return notFound();
  return (
    <Layout>
      <ServiceBanner banner={data.pageBanner} />
      <ServiceAbout about={data.about} />
      <ServiceFeatures features={data.features} />
      <ServiceRequestForm form={data.requestForm} />
    </Layout>
  );
}
