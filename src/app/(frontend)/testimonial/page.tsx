import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";
import Section1 from "@/components/sections/testimonial/Section1";
import Section2 from "@/components/sections/about/Section3";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Client Testimonials | Motshwanelo IT Consulting - 98% Client Satisfaction",
  description: "Read what our clients say about Motshwanelo IT Consulting's exceptional IT services. 98% client satisfaction rate with testimonials from businesses across South Africa who've experienced our digital transformation solutions.",
  keywords: ["Client Testimonials", "IT Service Reviews", "Customer Feedback", "Client Satisfaction", "IT Consulting Reviews", "South Africa IT Testimonials"],
  openGraph: {
    title: "Client Testimonials | 98% Client Satisfaction",
    description: "Discover why 98% of our clients are satisfied with our IT solutions. Read real testimonials from businesses we've transformed.",
    type: "website",
    url: "https://motshwaneloitconsulting.co.za/testimonial",
  },
};

export default function Testimonials() {
    return (
        <>
            <Layout>
                <SectionHeader title="Client Testimonials" group_page="98% Client Satisfaction - Hear From Our Partners" current_page="Testimonials" display="d-none" />
                <Section1 />
                <Section2 />
            </Layout>
        </>
    );
}
