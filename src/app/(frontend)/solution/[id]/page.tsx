import fs from 'fs';
import path from 'path';
import { notFound } from 'next/navigation';
import Layout from '@/components/layout/Layout';
import SolutionBanner from '@/components/sections/solution-details/SolutionBanner';
import SolutionAbout from '@/components/sections/solution-details/SolutionAbout';
import SolutionFeatures from '@/components/sections/solution-details/SolutionFeatures';
import SolutionSpecifications from '@/components/sections/solution-details/SolutionSpecifications';
import SolutionBenefits from '@/components/sections/solution-details/SolutionBenefits';
import SolutionRequestForm from '@/components/sections/solution-details/SolutionRequestForm';
import SolutionGallery from '@/components/sections/solution-details/SolutionGallery';
import type { Metadata } from 'next';

export async function generateStaticParams() {
  const dir = path.join(process.cwd(), 'src/data/products');
  const files = fs.readdirSync(dir);
  return files.map((file) => ({ id: file.replace('.json', '') }));
}

export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  try {
    const filePath = path.join(process.cwd(), 'src/data/products', `${params.id}.json`);
    const solutionData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
    
    return {
      title: `${solutionData.pageBanner.pageTitle} | Motshwanelo IT Consulting`,
      description: solutionData.pageBanner.description,
      openGraph: {
        title: solutionData.pageBanner.pageTitle,
        description: solutionData.pageBanner.description,
        type: 'website',
        url: `https://motshwaneloitconsulting.co.za/solution/${params.id}`,
        images: solutionData.about?.image?.src ? [solutionData.about.image.src] : [],
      },
    };
  } catch (error) {
    return {
      title: 'Solution Not Found | Motshwanelo IT Consulting',
      description: 'The requested solution could not be found.',
    };
  }
}

export default function SolutionDetailsPage({ params }: { params: { id: string } }) {
  try {
    const filePath = path.join(process.cwd(), 'src/data/products', `${params.id}.json`);
    const solutionData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));

    return (
      <Layout>
        <SolutionBanner banner={solutionData.pageBanner} />
        <SolutionAbout about={solutionData.about} />
        <SolutionFeatures features={solutionData.features} />
        {solutionData.specifications && <SolutionSpecifications specifications={solutionData.specifications} />}
        {solutionData.benefits && <SolutionBenefits benefits={solutionData.benefits} />}
        {solutionData.applications && <SolutionBenefits benefits={{title: "Use Cases", items: solutionData.applications.items}} />}
        <SolutionRequestForm requestForm={solutionData.requestForm} />
        {solutionData.gallery && <SolutionGallery gallery={solutionData.gallery} />}
      </Layout>
    );
  } catch (error) {
    notFound();
  }
}
