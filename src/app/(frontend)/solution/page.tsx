import fs from 'fs';
import path from 'path';
import Layout from '@/components/layout/Layout';
import SolutionsGrid from '@/components/sections/solutions/SolutionsGrid';
import SectionHeader from '@/components/layout/SectionHeader';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: "Our Solutions | Motshwanelo IT Consulting - Innovative Technology Products",
  description: "Explore our comprehensive range of innovative technology solutions including DiGiM Digital Signage, NetEco6000 Management Platform, and Fusion Module Data Centers.",
  keywords: ["IT Solutions", "Technology Products", "Digital Signage", "Data Center Solutions", "Infrastructure Management", "Innovation"],
  openGraph: {
    title: "Our Solutions | Innovative Technology Products",
    description: "Discover our cutting-edge technology solutions designed to transform your business operations and drive digital excellence.",
    type: "website",
    url: "https://motshwaneloitconsulting.co.za/solution",
  },
};

// Helper to get all solutions
function getAllSolutions() {
  const dir = path.join(process.cwd(), 'src/data/products');
  const files = fs.readdirSync(dir).filter(f => f.endsWith('.json'));
  return files.map(file => {
    const id = file.replace('.json', '');
    const data = JSON.parse(fs.readFileSync(path.join(dir, file), 'utf-8'));
    return { id, ...data };
  });
}

export default function SolutionsPage() {
  const solutions = getAllSolutions();
  return (
    <Layout>
      <SectionHeader 
        title="Our Technology Solutions" 
        group_page="Innovative Products for Digital Transformation" 
        current_page="Solutions" 
        display="d-none" 
      />
      <SolutionsGrid solutions={solutions} />
    </Layout>
  );
}
