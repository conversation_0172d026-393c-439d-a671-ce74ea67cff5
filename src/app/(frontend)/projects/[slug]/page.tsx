import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getProjectBySlug } from '@/lib/services/projects';
import { BlockRenderer } from '@/components/content-blocks';
import Link from 'next/link';
import { ArrowLeft, Calendar, MapPin, DollarSign, Building, Tag } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface ProjectPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: ProjectPageProps): Promise<Metadata> {
  const project = await getProjectBySlug(params.slug);

  if (!project || project.status !== 'PUBLISHED') {
    return {
      title: 'Project Not Found',
    };
  }

  return {
    title: project.seoTitle || project.title,
    description: project.seoDescription || project.excerpt,
    openGraph: {
      title: project.seoTitle || project.title,
      description: project.seoDescription || project.excerpt,
      images: project.featuredImage ? [project.featuredImage] : [],
    },
  };
}

export default async function ProjectPage({ params }: ProjectPageProps) {
  const project = await getProjectBySlug(params.slug);

  if (!project || project.status !== 'PUBLISHED') {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <Link 
            href="/projects" 
            className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Projects
          </Link>
        </div>
      </div>

      {/* Project Header */}
      <div className="bg-white">
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            <div className="mb-6">
              <div className="flex flex-wrap gap-2 mb-4">
                {project.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    <Tag className="mr-1 h-3 w-3" />
                    {tag}
                  </Badge>
                ))}
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                {project.title}
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed">
                {project.excerpt}
              </p>
            </div>

            {/* Project Meta */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 p-6 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <Building className="h-5 w-5 text-blue-600 mr-3" />
                <div>
                  <div className="text-sm text-gray-500">Client</div>
                  <div className="font-semibold">{project.clientName}</div>
                </div>
              </div>
              <div className="flex items-center">
                <MapPin className="h-5 w-5 text-blue-600 mr-3" />
                <div>
                  <div className="text-sm text-gray-500">Location</div>
                  <div className="font-semibold">{project.location}</div>
                </div>
              </div>
              <div className="flex items-center">
                <DollarSign className="h-5 w-5 text-blue-600 mr-3" />
                <div>
                  <div className="text-sm text-gray-500">Project Value</div>
                  <div className="font-semibold">{project.projectValue}</div>
                </div>
              </div>
              <div className="flex items-center">
                <Calendar className="h-5 w-5 text-blue-600 mr-3" />
                <div>
                  <div className="text-sm text-gray-500">Duration</div>
                  <div className="font-semibold">
                    {project.startDate && project.endDate 
                      ? `${new Date(project.startDate).getFullYear()} - ${new Date(project.endDate).getFullYear()}`
                      : 'N/A'
                    }
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Blocks */}
      {project.contentBlocks && project.contentBlocks.length > 0 ? (
        <BlockRenderer blocks={project.contentBlocks} />
      ) : (
        /* Fallback Content */
        <div className="bg-white">
          <div className="container mx-auto px-4 py-12">
            <div className="max-w-4xl mx-auto">
              {/* Main Content */}
              <div className="prose prose-lg max-w-none mb-12">
                <div dangerouslySetInnerHTML={{ __html: project.content || project.description || '' }} />
              </div>

              {/* Features */}
              {project.features && project.features.length > 0 && (
                <div className="mb-12">
                  <h2 className="text-3xl font-bold mb-8">Key Features</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {project.features.map((feature) => (
                      <div key={feature.id} className="flex items-start p-6 bg-gray-50 rounded-lg">
                        <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                          <span className="text-blue-600 font-bold">{feature.number}</span>
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
                          <p className="text-gray-600">{feature.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Timeline */}
              {project.timeline && project.timeline.length > 0 && (
                <div className="mb-12">
                  <h2 className="text-3xl font-bold mb-8">Project Timeline</h2>
                  <div className="space-y-6">
                    {project.timeline.map((phase) => (
                      <div key={phase.id} className="flex items-start">
                        <div className="flex-shrink-0 w-24 text-right mr-8">
                          <div className="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold">
                            {phase.phase}
                          </div>
                          <div className="text-sm text-gray-500 mt-1">{phase.duration}</div>
                        </div>
                        <div className="flex-shrink-0 w-4 h-4 bg-blue-600 rounded-full mt-2 mr-8"></div>
                        <div className="flex-grow">
                          <h3 className="text-xl font-semibold mb-2">{phase.title}</h3>
                          <p className="text-gray-600">{phase.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Technologies */}
              {project.technologies && project.technologies.length > 0 && (
                <div className="mb-12">
                  <h2 className="text-3xl font-bold mb-8">Technologies Used</h2>
                  <div className="flex flex-wrap gap-3">
                    {project.technologies.map((tech) => (
                      <Badge key={tech} variant="outline" className="px-3 py-1">
                        {tech}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Gallery */}
              {project.gallery && project.gallery.length > 0 && (
                <div className="mb-12">
                  <h2 className="text-3xl font-bold mb-8">Project Gallery</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {project.gallery.map((image) => (
                      <figure key={image.id} className="group">
                        <img 
                          src={image.src} 
                          alt={image.alt}
                          className="w-full h-64 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow"
                        />
                        {image.caption && (
                          <figcaption className="mt-2 text-sm text-gray-600">
                            {image.caption}
                          </figcaption>
                        )}
                      </figure>
                    ))}
                  </div>
                </div>
              )}

              {/* Stats */}
              {project.stats && project.stats.length > 0 && (
                <div className="mb-12">
                  <h2 className="text-3xl font-bold mb-8">Project Impact</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {project.stats.map((stat, index) => (
                      <div key={index} className="text-center p-6 bg-gray-50 rounded-lg">
                        <div className="text-4xl font-bold text-blue-600 mb-2">
                          {stat.number}
                        </div>
                        <div className="text-lg font-semibold">{stat.label}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* CTA Section */}
      <div className="bg-blue-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Start Your Project?</h2>
          <p className="text-xl mb-8 opacity-90">
            Let's discuss how we can help transform your infrastructure with innovative solutions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/contact"
              className="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Get In Touch
            </Link>
            <Link 
              href="/projects"
              className="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              View More Projects
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}