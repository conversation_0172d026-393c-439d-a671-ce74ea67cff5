import { Metadata } from 'next';
import { getAllProjects, getAllCategories } from '@/lib/services/projects';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MapPin, Calendar, Building, ArrowRight, Tag } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Our Projects - MITC',
  description: 'Explore our portfolio of successful infrastructure projects across Africa, from data centers to smart cities and digital transformation initiatives.',
};

export default async function ProjectsPage() {
  const projects = await getAllProjects();
  const categories = await getAllCategories();
  
  // Filter only published projects
  const publishedProjects = projects.filter(project => project.status === 'PUBLISHED');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">Our Projects</h1>
          <p className="text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto">
            Discover how we've transformed African infrastructure through innovative technology solutions, 
            delivering measurable impact across diverse sectors and communities.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            {categories.slice(0, 5).map((category) => (
              <Badge 
                key={category.id} 
                variant="secondary" 
                className="px-4 py-2 text-sm bg-white/20 text-white border-white/30"
              >
                {category.name} ({category.projectCount})
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">
                {publishedProjects.length}+
              </div>
              <div className="text-lg font-semibold">Completed Projects</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">17</div>
              <div className="text-lg font-semibold">Years Experience</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">
                {categories.length}+
              </div>
              <div className="text-lg font-semibold">Service Categories</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">100%</div>
              <div className="text-lg font-semibold">Client Satisfaction</div>
            </div>
          </div>
        </div>
      </div>

      {/* Projects Grid */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Featured Projects</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From enterprise data centers to smart city solutions, explore our portfolio of 
              transformative infrastructure projects that are shaping Africa's digital future.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {publishedProjects.map((project) => (
              <Card key={project.id} className="group hover:shadow-lg transition-shadow duration-300">
                <div className="relative overflow-hidden rounded-t-lg">
                  <img 
                    src={project.featuredImage || '/assets/img/project/default-project.jpg'} 
                    alt={project.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge 
                      variant="secondary" 
                      className="bg-white/90 text-gray-800"
                      style={{ backgroundColor: categories.find(c => c.slug === project.category)?.color + '20' }}
                    >
                      {categories.find(c => c.slug === project.category)?.name || 'Project'}
                    </Badge>
                  </div>
                </div>
                
                <CardHeader>
                  <CardTitle className="text-xl group-hover:text-blue-600 transition-colors">
                    <Link href={`/projects/${project.slug}`}>
                      {project.title}
                    </Link>
                  </CardTitle>
                  <CardDescription className="text-gray-600 line-clamp-2">
                    {project.excerpt}
                  </CardDescription>
                </CardHeader>
                
                <CardContent>
                  <div className="space-y-3 mb-4">
                    <div className="flex items-center text-sm text-gray-500">
                      <Building className="h-4 w-4 mr-2" />
                      {project.clientName}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <MapPin className="h-4 w-4 mr-2" />
                      {project.location}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="h-4 w-4 mr-2" />
                      {project.startDate && project.endDate ? (
                        `${new Date(project.startDate).getFullYear()} - ${new Date(project.endDate).getFullYear()}`
                      ) : (
                        'Date TBD'
                      )}
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    {project.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {project.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{project.tags.length - 3} more
                      </Badge>
                    )}
                  </div>

                  <Link href={`/projects/${project.slug}`}>
                    <Button variant="outline" className="w-full group-hover:bg-blue-600 group-hover:text-white transition-colors">
                      View Project Details
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>

          {publishedProjects.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg">No projects available at the moment.</div>
              <p className="text-gray-400 mt-2">Check back soon for updates on our latest work.</p>
            </div>
          )}
        </div>
      </div>

      {/* Categories Section */}
      <div className="bg-white py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Expertise</h2>
            <p className="text-xl text-gray-600">
              We specialize in diverse technology sectors, delivering comprehensive solutions 
              tailored to each industry's unique requirements.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category) => (
              <div 
                key={category.id} 
                className="p-6 rounded-lg border hover:shadow-md transition-shadow"
                style={{ borderColor: category.color + '40' }}
              >
                <div className="flex items-center mb-4">
                  <div 
                    className="w-4 h-4 rounded-full mr-3"
                    style={{ backgroundColor: category.color }}
                  />
                  <h3 className="text-lg font-semibold">{category.name}</h3>
                </div>
                <p className="text-gray-600 mb-4">{category.description}</p>
                <div className="flex items-center justify-between">
                  <Badge variant="secondary">
                    {category.projectCount} project{category.projectCount !== 1 ? 's' : ''}
                  </Badge>
                  <Tag className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Start Your Project?</h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Join our growing list of satisfied clients and let us help you transform your 
            infrastructure with innovative, reliable solutions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                Start Your Project
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="/services">
              <Button 
                size="lg" 
                variant="outline" 
                className="border-white text-white hover:bg-white hover:text-blue-600"
              >
                Explore Services
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}