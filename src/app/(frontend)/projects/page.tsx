import { Metadata } from 'next';
import { getAllProjects, getAllCategories } from '@/lib/services/projects';
import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Our Projects - Motshwanelo IT Consulting',
  description: 'Explore our portfolio of successful infrastructure projects across Africa, from data centers to smart cities and digital transformation initiatives.',
};

export default async function ProjectsPage() {
  const projects = await getAllProjects();
  const categories = await getAllCategories();

  // Filter only published projects
  const publishedProjects = projects.filter(project => project.status === 'PUBLISHED');

  return (
    <Layout>
      <SectionHeader
        title="Our Projects"
        group_page="Portfolio"
        current_page="Projects"
        display=""
      />

      {/* Hero Content Section */}
      <div className="projects-hero-content sp">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 m-auto text-center">
              <div className="heading1">
                <span className="span">Portfolio Showcase</span>
                <h2>Transforming African Infrastructure</h2>
                <div className="space16" />
                <p>Discover how we've transformed African infrastructure through innovative technology solutions, delivering measurable impact across diverse sectors and communities.</p>
                <div className="space30" />

                {/* Category Tags */}
                <div className="category-tags">
                  {categories.slice(0, 5).map((category) => (
                    <span key={category.id} className="category-tag">
                      {category.name} ({category.projectCount})
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="projects-stats-section sp">
        <div className="container">
          <div className="row">
            <div className="col-lg-3 col-md-6">
              <div className="stats-item text-center">
                <div className="stats-number">
                  <span className="counter">{publishedProjects.length}</span>+
                </div>
                <h4>Completed Projects</h4>
              </div>
              <div className="space30" />
            </div>
            <div className="col-lg-3 col-md-6">
              <div className="stats-item text-center">
                <div className="stats-number">
                  <span className="counter">17</span>
                </div>
                <h4>Years Experience</h4>
              </div>
              <div className="space30" />
            </div>
            <div className="col-lg-3 col-md-6">
              <div className="stats-item text-center">
                <div className="stats-number">
                  <span className="counter">{categories.length}</span>+
                </div>
                <h4>Service Categories</h4>
              </div>
              <div className="space30" />
            </div>
            <div className="col-lg-3 col-md-6">
              <div className="stats-item text-center">
                <div className="stats-number">
                  <span className="counter">100</span>%
                </div>
                <h4>Client Satisfaction</h4>
              </div>
              <div className="space30" />
            </div>
          </div>
        </div>
      </div>

      {/* Projects Grid Section */}
      <div className="projects-grid-section sp">
        <div className="container">
          <div className="row">
            <div className="col-lg-6 m-auto text-center">
              <div className="heading1">
                <span className="span">Featured Work</span>
                <h2>Our Project Portfolio</h2>
                <div className="space16" />
                <p>From enterprise data centers to smart city solutions, explore our portfolio of transformative infrastructure projects that are shaping Africa's digital future.</p>
              </div>
            </div>
          </div>
          <div className="space60" />

          <div className="row">
            {publishedProjects.map((project) => (
              <div key={project.id} className="col-lg-4 col-md-6">
                <div className="project-card">
                  <div className="project-image">
                    <div className="image overlay-anim">
                      <img
                        src={project.featuredImage || '/assets/img/project/default-project.jpg'}
                        alt={project.title}
                      />
                    </div>
                    <div className="project-category">
                      <span className="category-badge">
                        {categories.find(c => c.slug === project.category)?.name || 'Project'}
                      </span>
                    </div>
                  </div>

                  <div className="project-content">
                    <h3>
                      <Link href={`/projects/${project.slug}`}>
                        {project.title}
                      </Link>
                    </h3>
                    <div className="space16" />
                    <p className="project-excerpt">{project.excerpt}</p>
                    <div className="space20" />

                    <div className="project-meta">
                      <div className="meta-item">
                        <i className="fa-solid fa-building"></i>
                        <span>{project.clientName || 'Confidential'}</span>
                      </div>
                      <div className="meta-item">
                        <i className="fa-solid fa-map-marker-alt"></i>
                        <span>{project.location || 'Various'}</span>
                      </div>
                      <div className="meta-item">
                        <i className="fa-solid fa-calendar"></i>
                        <span>
                          {project.startDate && project.endDate ? (
                            `${new Date(project.startDate).getFullYear()} - ${new Date(project.endDate).getFullYear()}`
                          ) : (
                            'Ongoing'
                          )}
                        </span>
                      </div>
                    </div>

                    <div className="space20" />

                    {/* Tags */}
                    <div className="project-tags">
                      {project.tags.slice(0, 3).map((tag) => (
                        <span key={tag} className="tag-item">
                          {tag}
                        </span>
                      ))}
                      {project.tags.length > 3 && (
                        <span className="tag-item more-tags">
                          +{project.tags.length - 3} more
                        </span>
                      )}
                    </div>

                    <div className="space30" />

                    <Link className="theme-btn3" href={`/projects/${project.slug}`}>
                      View Project Details
                      <span>
                        <i className="fa-solid fa-arrow-right" />
                      </span>
                    </Link>
                  </div>
                </div>
                <div className="space30" />
              </div>
            ))}
          </div>

          {publishedProjects.length === 0 && (
            <div className="row">
              <div className="col-lg-8 m-auto text-center">
                <div className="no-projects-message">
                  <h3>No projects available at the moment.</h3>
                  <div className="space16" />
                  <p>Check back soon for updates on our latest work.</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Categories Section */}
      <div className="projects-categories-section sp">
        <div className="container">
          <div className="row">
            <div className="col-lg-6 m-auto text-center">
              <div className="heading1">
                <span className="span">Our Expertise</span>
                <h2>Service Categories</h2>
                <div className="space16" />
                <p>We specialize in diverse technology sectors, delivering comprehensive solutions tailored to each industry's unique requirements.</p>
              </div>
            </div>
          </div>
          <div className="space60" />

          <div className="row">
            {categories.map((category) => (
              <div key={category.id} className="col-lg-4 col-md-6">
                <div className="category-card">
                  <div className="category-icon">
                    <div
                      className="icon-circle"
                      style={{ backgroundColor: category.color }}
                    >
                      <i className="fa-solid fa-layer-group"></i>
                    </div>
                  </div>
                  <div className="category-content">
                    <h3>{category.name}</h3>
                    <div className="space16" />
                    <p>{category.description}</p>
                    <div className="space20" />
                    <div className="category-stats">
                      <span className="project-count">
                        <i className="fa-solid fa-folder"></i>
                        {category.projectCount} project{category.projectCount !== 1 ? 's' : ''}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="space30" />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="cta-section sp">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 m-auto text-center">
              <div className="heading1">
                <h2>Ready to Start Your Project?</h2>
                <div className="space16" />
                <p>Join our growing list of satisfied clients and let us help you transform your infrastructure with innovative, reliable solutions.</p>
                <div className="space30" />
                <div className="cta-buttons">
                  <Link className="theme-btn1" href="/contact">
                    Start Your Project
                    <span>
                      <i className="fa-solid fa-arrow-right" />
                    </span>
                  </Link>
                  <Link className="theme-btn2" href="/services">
                    Explore Services
                    <span>
                      <i className="fa-solid fa-arrow-right" />
                    </span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}