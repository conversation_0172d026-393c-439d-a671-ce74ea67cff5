import "/public/assets/css/bootstrap.min.css";
import "/public/assets/css/fontawesome.css";
import "/public/assets/css/magnific-popup.css";
import "/public/assets/css/nice-select.css";
import "/public/assets/css/slick-slider.css";
import "/public/assets/css/aos.css";
import "/public/assets/css/mobile-menu.css";
import "/public/assets/css/main.css";

import { AuthTestBanner } from "@/components/auth-test-banner";
import StructuredData from "@/components/seo/StructuredData";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title:
    "Motshwanelo IT Consulting | Leading IT Solutions Provider in South Africa",
  description:
    "Transform your business with Motshwanelo IT Consulting - South Africa's premier IT solutions provider. Specializing in Smart City infrastructure, Data Centre excellence, Software Development & Digital Transformation. 17+ years of proven expertise.",
  keywords: [
    "IT Consulting South Africa",
    "Smart City Solutions",
    "Data Centre Infrastructure", 
    "Software Development",
    "Digital Transformation",
    "ICT Solutions",
    "Technology Services",
    "Kempton Park IT Services"
  ],
  authors: [{ name: "Motshwanelo IT Consulting" }],
  creator: "Motshwanelo IT Consulting",
  publisher: "Motshwanelo IT Consulting",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_ZA",
    url: "https://motshwaneloitconsulting.co.za",
    title: "Motshwanelo IT Consulting | Leading IT Solutions Provider in South Africa",
    description: "Transform your business with cutting-edge IT solutions. 17+ years of expertise in Smart Cities, Data Centres & Digital Transformation.",
    siteName: "Motshwanelo IT Consulting",
  },
  twitter: {
    card: "summary_large_image",
    title: "Motshwanelo IT Consulting | Leading IT Solutions Provider in South Africa",
    description: "Transform your business with cutting-edge IT solutions. 17+ years of expertise in Smart Cities, Data Centres & Digital Transformation.",
    site: "@motshwaneloit",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="body">
      <StructuredData />
      {children}
    </div>
  );
}
