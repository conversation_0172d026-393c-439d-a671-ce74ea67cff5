import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";
import Section1 from "@/components/sections/blog/Section1";
import Section2 from "@/components/sections/about/Section3";
import { BlogListing } from "@/components/blog/blog-listing";
import { BlogService } from "@/lib/services/blog.service";
import type { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Industry Insights | Motshwanelo IT Consulting - Latest Technology Trends & News",
  description: "Stay updated with the latest IT trends, digital transformation insights, and technology news from Motshwanelo IT Consulting. Expert analysis on Smart Cities, Data Centres, and enterprise solutions.",
  keywords: ["IT Blog", "Technology News", "Digital Transformation", "Smart City Trends", "Data Centre News", "IT Industry Insights"],
  openGraph: {
    title: "Industry Insights | Latest Technology Trends & News",
    description: "Stay updated with expert insights on digital transformation, Smart Cities, Data Centres, and the latest IT industry trends.",
    type: "website",
    url: "https://motshwaneloitconsulting.co.za/blog",
  },
};

async function BlogContent() {
  try {
    const [postsResult, categories, tags] = await Promise.all([
      BlogService.getPosts({
        page: 1,
        perPage: 12,
        status: 'PUBLISHED'
      }),
      BlogService.getCategories(),
      BlogService.getTags()
    ])

    return (
      <div className="container mx-auto py-12">
        <BlogListing
          initialData={postsResult}
          categories={categories}
          tags={tags}
          itemsPerPage={12}
        />
      </div>
    )
  } catch (error) {
    console.error('Error loading blog data:', error)
    return (
      <div className="container mx-auto py-12 text-center">
        <h2 className="text-2xl font-bold mb-4">Unable to load blog posts</h2>
        <p className="text-muted-foreground">
          We're having trouble loading the blog posts. Please try again later.
        </p>
      </div>
    )
  }
}

function BlogSkeleton() {
  return (
    <div className="container mx-auto py-12">
      <div className="animate-pulse space-y-8">
        <div className="h-8 bg-gray-200 rounded w-1/4 mx-auto"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="border rounded-lg overflow-hidden">
              <div className="h-48 bg-gray-200"></div>
              <div className="p-4 space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default function Blog() {
    return (
        <>
            <Layout>
                <SectionHeader title="Industry Insights & News" group_page="Latest Technology Trends from Our Experts" current_page="Blog" display="d-none" />

                {/* Enhanced Blog Listing */}
                <Suspense fallback={<BlogSkeleton />}>
                  <BlogContent />
                </Suspense>

                {/* Keep existing sections for backward compatibility */}
                <Section1 />
                <Section2 />
            </Layout>
        </>
    );
}
