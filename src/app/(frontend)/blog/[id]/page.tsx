import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";
import Section1 from "@/components/sections/blog-details/Section1";
import Section2 from "@/components/sections/blog-details/Section2";
import Section3 from "@/components/sections/about/Section3";
import { BlogPostDetailComponent, BlogPostDetailSkeleton } from "@/components/blog/blog-post-detail";
import { BlogService } from "@/lib/services/blog.service";
import { Suspense } from "react";
import { notFound } from "next/navigation";
import type { Metadata } from "next";

interface BlogPostPageProps {
  params: { id: string }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  try {
    const post = await BlogService.getPostBySlug(params.id)

    if (!post) {
      return {
        title: "Post Not Found",
        description: "The requested blog post could not be found."
      }
    }

    return {
      title: post.seoTitle || post.title,
      description: post.seoDescription || post.excerpt || "Read this blog post for insights and information.",
      keywords: post.seoKeywords,
      openGraph: {
        title: post.title,
        description: post.excerpt || "",
        type: "article",
        url: `${process.env.NEXT_PUBLIC_SITE_URL}/blog/${post.slug}`,
        images: post.featuredImage ? [{ url: post.featuredImage }] : [],
        publishedTime: post.publishedAt ? new Date(post.publishedAt).toISOString() : undefined,
        authors: [post.author.name || ""],
      },
      twitter: {
        card: "summary_large_image",
        title: post.title,
        description: post.excerpt || "",
        images: post.featuredImage ? [post.featuredImage] : [],
      }
    }
  } catch (error) {
    return {
      title: "Blog Post",
      description: "Read our latest blog post."
    }
  }
}

async function BlogPostContent({ slug }: { slug: string }) {
  try {
    const [post, relatedPosts] = await Promise.all([
      BlogService.getPostBySlug(slug),
      BlogService.getRelatedPosts(slug, 3).catch(() => [])
    ])

    if (!post) {
      notFound()
    }

    // Add related posts to the post object
    const postWithRelated = {
      ...post,
      relatedPosts: relatedPosts || []
    }

    return (
      <div className="container mx-auto py-12">
        <BlogPostDetailComponent post={postWithRelated} />
      </div>
    )
  } catch (error) {
    console.error('Error loading blog post:', error)
    notFound()
  }
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
    return (
        <>
            <Layout>
                <SectionHeader
                  title="Blog Post"
                  group_page="Blog"
                  current_page="Article"
                  display="d-block"
                />

                {/* Enhanced Blog Post Detail */}
                <Suspense fallback={
                  <div className="container mx-auto py-12">
                    <BlogPostDetailSkeleton />
                  </div>
                }>
                  <BlogPostContent slug={params.id} />
                </Suspense>

                {/* Keep existing sections for backward compatibility */}
                <Section1 />
                <Section2 />
                <Section3 />
            </Layout>
        </>
    );
}
