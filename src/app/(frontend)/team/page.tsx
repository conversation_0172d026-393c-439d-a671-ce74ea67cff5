import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";
import Section1 from "@/components/sections/team/Section1";
import Section2 from "@/components/sections/about/Section3";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Our Expert Team | Motshwanelo IT Consulting - Meet Our Technology Professionals",
  description: "Meet the diverse team of technology professionals behind Motshwanelo IT Consulting. Our experienced experts bring decades of expertise in IT consulting, digital transformation, and innovative solutions across Africa.",
  keywords: ["IT Team", "Technology Professionals", "IT Experts", "Digital Transformation Team", "IT Consultants", "South Africa IT Team"],
  openGraph: {
    title: "Our Expert Team | Technology Professionals",
    description: "Meet our diverse team of technology professionals bringing decades of experience in IT consulting and digital transformation across Africa.",
    type: "website",
    url: "https://motshwaneloitconsulting.co.za/team",
  },
};

export default function Team() {
    return (
        <>
            <Layout>
                <SectionHeader title="Meet Our Expert Team" group_page="Technology Professionals Driving Digital Transformation" current_page="Team" display="d-none" />
                <Section1 />
                <Section2 />
            </Layout>
        </>
    );
}
