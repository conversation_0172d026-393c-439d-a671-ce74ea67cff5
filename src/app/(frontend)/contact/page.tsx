import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";
import Section1 from "@/components/sections/contact/Section1";
import Section2 from "@/components/sections/contact/Section2";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Contact Us | Motshwanelo IT Consulting - Start Your Digital Transformation",
  description: "Get in touch with Motshwanelo IT Consulting for your digital transformation needs. Located in Kempton Park, South Africa. Call 011 492 0992 <NAME_EMAIL>. Free consultation available.",
  keywords: ["Contact IT Consulting", "Kempton Park IT Services", "South Africa IT Support", "Digital Transformation Consultation", "IT Solutions Contact"],
  openGraph: {
    title: "Contact Us | Start Your Digital Transformation",
    description: "Ready to transform your business? Contact Motshwanelo IT Consulting for expert IT solutions and digital transformation services.",
    type: "website",
    url: "https://motshwaneloitconsulting.co.za/contact",
  },
};

export default function Contact() {
    return (
        <>
            <Layout>
                <SectionHeader title="Contact Our Digital Architects" group_page="Ready to Transform Your Business?" current_page="Contact" display="d-none" />
                <Section1 />
                <Section2 />
            </Layout>
        </>
    );
}
