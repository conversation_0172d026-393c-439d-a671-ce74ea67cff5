import { NextRequest } from 'next/server'
import { SEOService } from '@/lib/services/seo.service'

// This creates a dynamic robots.txt at /robots.txt
export async function GET(request: NextRequest) {
  try {
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com'
    const robotsContent = SEOService.generateRobotsTxt(siteUrl)

    return new Response(robotsContent, {
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'public, max-age=86400, s-maxage=86400'
      }
    })

  } catch (error) {
    console.error('Robots.txt generation error:', error)
    return new Response('Error generating robots.txt', { status: 500 })
  }
}
