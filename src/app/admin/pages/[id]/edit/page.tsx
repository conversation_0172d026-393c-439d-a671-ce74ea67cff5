'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { PageBuilder } from '@/components/admin/page-builder/page-builder'
import { LoadingSpinner } from '@/components/admin/ui/loading-spinner'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, AlertCircle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

export default function EditPageBuilderPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [page, setPage] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const pageId = params.id as string

  useEffect(() => {
    if (pageId) {
      fetchPage()
    }
  }, [pageId])

  const fetchPage = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/pages/${pageId}`)

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Page not found')
        }
        throw new Error('Failed to load page')
      }

      const data = await response.json()
      setPage(data.page)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load page'
      setError(errorMessage)
      console.error('Failed to fetch page:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (updatedPage: any) => {
    try {
      toast({
        title: 'Page saved',
        description: 'Your changes have been saved successfully.',
      })
    } catch (error) {
      toast({
        title: 'Save failed',
        description: 'Failed to save your changes. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handlePublish = async (updatedPage: any) => {
    try {
      toast({
        title: 'Page published',
        description: 'Your page is now live and visible to visitors.',
      })

      // Redirect to pages list after publishing
      setTimeout(() => {
        router.push('/admin/pages')
      }, 2000)
    } catch (error) {
      toast({
        title: 'Publish failed',
        description: 'Failed to publish your page. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handlePreview = (updatedPage: any) => {
    if (updatedPage?.slug) {
      window.open(`/${updatedPage.slug}?preview=true`, '_blank')
    }
  }

  const handleBackToPages = () => {
    router.push('/admin/pages')
  }

  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <LoadingSpinner size="lg" />
          <p className="text-muted-foreground">Loading page builder...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="h-screen flex items-center justify-center p-6">
        <Card className="max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              <span>Error Loading Page</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">{error}</p>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleBackToPages}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Pages
              </Button>
              <Button onClick={fetchPage}>
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!page) {
    return (
      <div className="h-screen flex items-center justify-center p-6">
        <Card className="max-w-md">
          <CardHeader>
            <CardTitle>Page Not Found</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              The page you're looking for doesn't exist or has been deleted.
            </p>
            <Button onClick={handleBackToPages}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Pages
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <PageBuilder
      pageId={pageId}
      initialPage={page}
      onSave={handleSave}
      onPublish={handlePublish}
      onPreview={handlePreview}
    />
  )
}