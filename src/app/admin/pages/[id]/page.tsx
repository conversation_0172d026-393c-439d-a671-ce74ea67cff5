'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import AwesomeLoadingComponent from '@/components/admin/ui/AwesomeLoadingComponent'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { PageRenderer } from '@/components/pages'
import {
  ArrowLeft,
  Edit,
  Eye,
  Copy,
  Trash2,
  Globe,
  Lock,
  Calendar,
  User,
  BarChart3,
  MessageSquare,
  Share2,
  Settings,
  History,
  ExternalLink
} from 'lucide-react'
import Link from 'next/link'
import { format } from 'date-fns'

interface PageData {
  id: string
  title: string
  slug: string
  excerpt?: string
  content?: string
  template: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  featuredImage?: string
  author: {
    id: string
    name: string
    image?: string
    bio?: string
  }
  category?: {
    id: string
    name: string
    color?: string
  }
  tags?: Array<{
    id: string
    name: string
    slug: string
    color?: string
  }>
  seo?: {
    title?: string
    description?: string
    keywords?: string[]
    noindex?: boolean
    nofollow?: boolean
  }
  blocks?: any[]
  settings?: {
    allowComments?: boolean
    showAuthor?: boolean
    showDate?: boolean
    showShare?: boolean
    showRelated?: boolean
  }
  _count: {
    views: number
    comments: number
    likes: number
  }
}

// Mock data for the page
const MOCK_PAGE_DATA: PageData = {
  id: '1',
  title: 'Welcome to Our Platform',
  slug: 'welcome',
  excerpt: 'Discover the power of our platform and how it can transform your business.',
  content: '<p>Welcome to our amazing platform! This is where great things happen.</p><p>Our platform offers cutting-edge solutions for modern businesses. With intuitive design and powerful features, we help you achieve your goals faster than ever before.</p>',
  template: 'landing',
  status: 'PUBLISHED',
  publishedAt: new Date('2024-01-15'),
  createdAt: new Date('2024-01-10'),
  updatedAt: new Date('2024-01-15'),
  featuredImage: '/images/welcome-hero.jpg',
  author: {
    id: '1',
    name: 'John Doe',
    image: '/avatars/john.jpg',
    bio: 'Senior Content Manager with 5+ years of experience in digital marketing and content strategy.'
  },
  category: {
    id: '1',
    name: 'Marketing',
    color: '#3b82f6'
  },
  tags: [
    { id: '1', name: 'Welcome', slug: 'welcome', color: '#10b981' },
    { id: '2', name: 'Platform', slug: 'platform', color: '#f59e0b' },
    { id: '3', name: 'Getting Started', slug: 'getting-started', color: '#ef4444' }
  ],
  seo: {
    title: 'Welcome to Our Platform - Get Started Today',
    description: 'Discover the power of our platform and how it can transform your business. Get started today!',
    keywords: ['platform', 'business', 'transformation'],
    noindex: false,
    nofollow: false,
  },
  blocks: [],
  settings: {
    allowComments: true,
    showAuthor: true,
    showDate: true,
    showShare: true,
    showRelated: true,
  },
  _count: {
    views: 1250,
    comments: 8,
    likes: 23
  }
}

export default function PageDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [pageData, setPageData] = useState<PageData | null>(null)
  const [activeTab, setActiveTab] = useState<'preview' | 'details' | 'analytics'>('preview')

  const pageId = params.id as string

  useEffect(() => {
    const loadPageData = async () => {
      try {
        setLoading(true)
        // In a real app, fetch from API
        // const response = await fetch(`/api/pages/${pageId}`)
        // const data = await response.json()

        // Using mock data for now
        const data = MOCK_PAGE_DATA
        setPageData(data)
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to load page data.',
          variant: 'destructive',
        })
      } finally {
        setLoading(false)
      }
    }

    if (pageId) {
      loadPageData()
    }
  }, [pageId, toast])

  const handleEdit = () => {
    router.push(`/admin/pages/${pageId}/edit`)
  }

  const handlePreview = () => {
    if (pageData) {
      window.open(`/${pageData.slug}`, '_blank')
    }
  }

  const handleDuplicate = async () => {
    try {
      // API call to duplicate page
      toast({
        title: 'Page duplicated',
        description: 'A copy of this page has been created.',
      })
      router.push('/admin/pages')
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to duplicate page.',
        variant: 'destructive',
      })
    }
  }

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this page? This action cannot be undone.')) {
      return
    }

    try {
      // API call to delete page
      toast({
        title: 'Page deleted',
        description: 'The page has been successfully deleted.',
      })
      router.push('/admin/pages')
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete page.',
        variant: 'destructive',
      })
    }
  }

  const getStatusBadge = (status: PageData['status']) => {
    const variants = {
      PUBLISHED: 'default',
      DRAFT: 'secondary',
      ARCHIVED: 'outline'
    } as const

    const colors = {
      PUBLISHED: 'text-green-700 bg-green-50 border-green-200',
      DRAFT: 'text-yellow-700 bg-yellow-50 border-yellow-200',
      ARCHIVED: 'text-gray-700 bg-gray-50 border-gray-200'
    }

    return (
      <Badge variant={variants[status]} className={colors[status]}>
        {status.toLowerCase()}
      </Badge>
    )
  }

  if (loading) {
    return <AwesomeLoadingComponent />
  }

  if (!pageData) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold mb-4">Page not found</h2>
        <p className="text-muted-foreground mb-6">
          The page you're looking for doesn't exist or has been deleted.
        </p>
        <Link href="/admin/pages">
          <Button>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Pages
          </Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/pages">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Pages
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{pageData.title}</h1>
            <p className="text-muted-foreground">
              /{pageData.slug} • Last updated {format(pageData.updatedAt, 'MMM dd, yyyy')}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handlePreview}>
            <Eye className="mr-2 h-4 w-4" />
            View Live
          </Button>
          <Button variant="outline" onClick={handleDuplicate}>
            <Copy className="mr-2 h-4 w-4" />
            Duplicate
          </Button>
          <Button onClick={handleEdit}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Page
          </Button>
        </div>
      </div>

      {/* Status and Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <div className="flex-1">
                {getStatusBadge(pageData.status)}
                <p className="text-xs text-muted-foreground mt-1">Status</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Eye className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-2xl font-bold">{pageData._count.views.toLocaleString()}</p>
                <p className="text-xs text-muted-foreground">Views</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-2xl font-bold">{pageData._count.comments}</p>
                <p className="text-xs text-muted-foreground">Comments</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Share2 className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-2xl font-bold">{pageData._count.likes}</p>
                <p className="text-xs text-muted-foreground">Likes</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">
                  {pageData.publishedAt ? format(pageData.publishedAt, 'MMM dd') : 'Not published'}
                </p>
                <p className="text-xs text-muted-foreground">Published</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('preview')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'preview'
              ? 'border-primary text-primary'
              : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
          >
            <Eye className="mr-2 h-4 w-4 inline" />
            Preview
          </button>
          <button
            onClick={() => setActiveTab('details')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'details'
              ? 'border-primary text-primary'
              : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
          >
            <Settings className="mr-2 h-4 w-4 inline" />
            Details
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'analytics'
              ? 'border-primary text-primary'
              : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
          >
            <BarChart3 className="mr-2 h-4 w-4 inline" />
            Analytics
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'preview' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Page Preview</CardTitle>
              <CardDescription>
                This is how your page will appear to visitors
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg overflow-hidden">
                <div className="bg-muted px-4 py-2 border-b flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="flex space-x-1">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      yoursite.com/{pageData.slug}
                    </span>
                  </div>
                  <Button variant="ghost" size="sm" onClick={handlePreview}>
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                </div>
                <div className="bg-white">
                  <PageRenderer
                    page={pageData}
                    template={pageData.template}
                    showComments={false}
                    showRelated={false}
                    showTOC={false}
                    showBreadcrumbs={false}
                    showShare={false}
                    showAuthor={pageData.settings?.showAuthor}
                    showMeta={pageData.settings?.showDate}
                    isPreview={true}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'details' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Details */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Page Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Title</label>
                  <p className="text-lg font-medium">{pageData.title}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">URL Slug</label>
                  <p className="text-sm font-mono bg-muted px-2 py-1 rounded">/{pageData.slug}</p>
                </div>
                {pageData.excerpt && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Excerpt</label>
                    <p className="text-sm">{pageData.excerpt}</p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Template</label>
                  <p className="text-sm capitalize">{pageData.template}</p>
                </div>
              </CardContent>
            </Card>

            {pageData.seo && (
              <Card>
                <CardHeader>
                  <CardTitle>SEO Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {pageData.seo.title && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">SEO Title</label>
                      <p className="text-sm">{pageData.seo.title}</p>
                    </div>
                  )}
                  {pageData.seo.description && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">SEO Description</label>
                      <p className="text-sm">{pageData.seo.description}</p>
                    </div>
                  )}
                  {pageData.seo.keywords && pageData.seo.keywords.length > 0 && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Keywords</label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {pageData.seo.keywords.map((keyword, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {keyword}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  <div className="flex space-x-4">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">No Index:</span>
                      <Badge variant={pageData.seo.noindex ? 'destructive' : 'secondary'}>
                        {pageData.seo.noindex ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">No Follow:</span>
                      <Badge variant={pageData.seo.nofollow ? 'destructive' : 'secondary'}>
                        {pageData.seo.nofollow ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {pageData.settings && (
              <Card>
                <CardHeader>
                  <CardTitle>Page Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Allow Comments</span>
                      <Badge variant={pageData.settings.allowComments ? 'default' : 'secondary'}>
                        {pageData.settings.allowComments ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Show Author</span>
                      <Badge variant={pageData.settings.showAuthor ? 'default' : 'secondary'}>
                        {pageData.settings.showAuthor ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Show Date</span>
                      <Badge variant={pageData.settings.showDate ? 'default' : 'secondary'}>
                        {pageData.settings.showDate ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Show Share</span>
                      <Badge variant={pageData.settings.showShare ? 'default' : 'secondary'}>
                        {pageData.settings.showShare ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Author */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-4 w-4" />
                  <span>Author</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start space-x-3">
                  <Avatar>
                    <AvatarImage src={pageData.author.image} alt={pageData.author.name} />
                    <AvatarFallback>
                      {pageData.author.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="font-medium">{pageData.author.name}</p>
                    {pageData.author.bio && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {pageData.author.bio}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Category */}
            {pageData.category && (
              <Card>
                <CardHeader>
                  <CardTitle>Category</CardTitle>
                </CardHeader>
                <CardContent>
                  <Badge
                    variant="outline"
                    style={{
                      borderColor: pageData.category.color,
                      color: pageData.category.color
                    }}
                  >
                    {pageData.category.name}
                  </Badge>
                </CardContent>
              </Card>
            )}

            {/* Tags */}
            {pageData.tags && pageData.tags.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Tags</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {pageData.tags.map((tag) => (
                      <Badge
                        key={tag.id}
                        variant="outline"
                        style={tag.color ? {
                          borderColor: tag.color,
                          color: tag.color
                        } : {}}
                      >
                        {tag.name}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Timestamps */}
            <Card>
              <CardHeader>
                <CardTitle>Timeline</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Created:</span>
                  <span>{format(pageData.createdAt, 'MMM dd, yyyy')}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Updated:</span>
                  <span>{format(pageData.updatedAt, 'MMM dd, yyyy')}</span>
                </div>
                {pageData.publishedAt && (
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Published:</span>
                    <span>{format(pageData.publishedAt, 'MMM dd, yyyy')}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-destructive">Danger Zone</CardTitle>
              </CardHeader>
              <CardContent>
                <Button
                  variant="destructive"
                  onClick={handleDelete}
                  className="w-full"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Page
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {activeTab === 'analytics' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Page Views</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{pageData._count.views.toLocaleString()}</div>
                <p className="text-sm text-muted-foreground">Total views</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Engagement</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{pageData._count.comments + pageData._count.likes}</div>
                <p className="text-sm text-muted-foreground">Comments + Likes</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Conversion Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">2.4%</div>
                <p className="text-sm text-muted-foreground">Avg. conversion</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Analytics Overview</CardTitle>
              <CardDescription>
                Detailed analytics would be displayed here with charts and graphs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-muted rounded-lg flex items-center justify-center">
                <p className="text-muted-foreground">Analytics charts would be rendered here</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}