'use client'

import { useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { PageBuilder } from '@/components/admin/page-builder/page-builder'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ArrowLeft, Plus, Loader2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { getTemplateList } from '@/components/templates/template-registry'

export default function PageBuilderPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  
  const [step, setStep] = useState<'setup' | 'builder'>('setup')
  const [loading, setLoading] = useState(false)
  const [newPage, setNewPage] = useState<any>(null)
  
  const templates = getTemplateList()
  
  const [setupData, setSetupData] = useState({
    title: '',
    slug: '',
    template: 'default',
    excerpt: ''
  })

  // Check if we should skip setup (e.g., from URL params)
  const skipSetup = searchParams.get('skip') === 'true'

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
  }

  const handleTitleChange = (title: string) => {
    setSetupData(prev => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(title)
    }))
  }

  const handleCreatePage = async () => {
    if (!setupData.title.trim()) {
      toast({
        title: 'Title required',
        description: 'Please enter a page title to continue.',
        variant: 'destructive'
      })
      return
    }

    try {
      setLoading(true)

      const response = await fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: setupData.title,
          slug: setupData.slug,
          template: setupData.template,
          excerpt: setupData.excerpt,
          status: 'DRAFT',
          content: '',
          blocks: []
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create page')
      }

      const { page } = await response.json()
      setNewPage(page)
      setStep('builder')
      
      toast({
        title: 'Page created',
        description: 'Your new page has been created. Start building!',
      })
    } catch (error) {
      console.error('Failed to create page:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create page',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (updatedPage: any) => {
    try {
      toast({
        title: 'Page saved',
        description: 'Your changes have been saved successfully.',
      })
    } catch (error) {
      toast({
        title: 'Save failed',
        description: 'Failed to save your changes. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handlePublish = async (updatedPage: any) => {
    try {
      toast({
        title: 'Page published',
        description: 'Your page is now live and visible to visitors.',
      })
      
      // Redirect to pages list after publishing
      setTimeout(() => {
        router.push('/admin/pages')
      }, 2000)
    } catch (error) {
      toast({
        title: 'Publish failed',
        description: 'Failed to publish your page. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handlePreview = (updatedPage: any) => {
    if (updatedPage?.slug) {
      window.open(`/${updatedPage.slug}?preview=true`, '_blank')
    }
  }

  if (skipSetup || step === 'builder') {
    if (!newPage) {
      // Create a minimal page object for new pages
      const defaultPage = {
        id: 'new',
        title: setupData.title || 'New Page',
        slug: setupData.slug || 'new-page',
        template: setupData.template || 'default',
        excerpt: setupData.excerpt || '',
        status: 'DRAFT',
        content: '',
        blocks: [],
        author: {
          id: '1',
          name: 'Admin',
          image: null
        },
        createdAt: new Date(),
        updatedAt: new Date()
      }

      return (
        <PageBuilder
          initialPage={defaultPage}
          onSave={handleSave}
          onPublish={handlePublish}
          onPreview={handlePreview}
        />
      )
    }

    return (
      <PageBuilder
        pageId={newPage.id}
        initialPage={newPage}
        onSave={handleSave}
        onPublish={handlePublish}
        onPreview={handlePreview}
      />
    )
  }

  return (
    <div className="container mx-auto py-6 max-w-2xl">
      <div className="flex items-center space-x-4 mb-6">
        <Button
          variant="ghost"
          onClick={() => router.push('/admin/pages')}
          className="p-2"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Create New Page</h1>
          <p className="text-muted-foreground">
            Set up your page details to start building
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Plus className="h-5 w-5" />
            <span>Page Setup</span>
          </CardTitle>
          <CardDescription>
            Configure your page settings before opening the visual builder
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <Label htmlFor="title">Page Title *</Label>
            <Input
              id="title"
              value={setupData.title}
              onChange={(e) => handleTitleChange(e.target.value)}
              placeholder="Enter your page title"
            />
          </div>

          <div>
            <Label htmlFor="slug">URL Slug *</Label>
            <Input
              id="slug"
              value={setupData.slug}
              onChange={(e) => setSetupData(prev => ({ ...prev, slug: e.target.value }))}
              placeholder="page-url-slug"
            />
            <p className="text-sm text-muted-foreground mt-1">
              URL: /{setupData.slug}
            </p>
          </div>

          <div>
            <Label htmlFor="template">Page Template</Label>
            <Select
              value={setupData.template}
              onValueChange={(value) => setSetupData(prev => ({ ...prev, template: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select template" />
              </SelectTrigger>
              <SelectContent>
                {templates.map((template) => (
                  <SelectItem key={template.id} value={template.id}>
                    <div className="flex flex-col">
                      <span>{template.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {template.description}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="excerpt">Page Description</Label>
            <Input
              id="excerpt"
              value={setupData.excerpt}
              onChange={(e) => setSetupData(prev => ({ ...prev, excerpt: e.target.value }))}
              placeholder="Brief description of your page"
            />
          </div>

          <div className="flex justify-between pt-4">
            <Button
              variant="outline"
              onClick={() => router.push('/admin/pages')}
            >
              Cancel
            </Button>
            
            <Button
              onClick={handleCreatePage}
              disabled={loading || !setupData.title.trim()}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Start Building
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
