import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { FileText, PenSquare, Users, <PERSON><PERSON>hart, Clock, Shield, User } from "lucide-react"
import { PageLayout } from "../../components/page-layout"
import { RecentActivity } from "../../components/recent-activity"
import { getCurrentUser } from "@/lib/auth"

const stats = [
  { title: "Total Pages", value: "24", icon: FileText, change: "+2.1%" },
  { title: "Active Blog Posts", value: "156", icon: PenSquare, change: "+12.5%" },
  { title: "Total Users", value: "1,234", icon: Users, change: "+5.2%" },
  { title: "Monthly Visitors", value: "8,754", icon: BarChart, change: "+18.3%" },
]

export default async function DashboardPage() {
  const user = await getCurrentUser()

  return (
    <PageLayout
      title="Admin Dashboard"
      description={`Welcome back, ${user?.name || 'Admin'}! Here's what's happening with your site.`}
      actions={
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Shield className="h-3 w-3" />
            {user?.role}
          </Badge>
          <Button>
            <Clock className="mr-2 h-4 w-4" />
            View Analytics
          </Button>
        </div>
      }
    >
      {/* Admin Info Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Admin Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-2 md:grid-cols-3">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Name</p>
              <p className="text-sm">{user?.name || 'Not set'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Email</p>
              <p className="text-sm">{user?.email}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Role</p>
              <Badge variant="outline">{user?.role}</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.change} from last month
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-6 grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <RecentActivity />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Admin Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="grid gap-2">
            <Button variant="outline" className="justify-start">
              <PenSquare className="mr-2 h-4 w-4" />
              New Blog Post
            </Button>
            <Button variant="outline" className="justify-start">
              <FileText className="mr-2 h-4 w-4" />
              Add New Page
            </Button>
            <Button variant="outline" className="justify-start">
              <Users className="mr-2 h-4 w-4" />
              Manage Users
            </Button>
            <Button variant="outline" className="justify-start">
              <Shield className="mr-2 h-4 w-4" />
              User Permissions
            </Button>
          </CardContent>
        </Card>
      </div>
    </PageLayout>
  )
}
