"use client"

"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from 'nextjs-toploader/app';
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Plus, Search, MoreHorizontal, PenSquare, Trash2, Eye, Calendar, Tag, Clock } from "lucide-react"
import { format } from "date-fns"
import { toast } from "sonner"

const posts = [
  {
    id: 1,
    title: 'Getting Started with Next.js 13',
    slug: 'getting-started-with-nextjs-13',
    status: 'published',
    publishedAt: '2023-05-15T10:30:00Z',
    author: 'Admin User',
    category: 'Tutorials',
    tags: ['Next.js', 'React', 'Web Development']
  },
  {
    id: 2,
    title: 'The Future of Web Development',
    slug: 'future-of-web-development',
    status: 'draft',
    publishedAt: '2023-05-10T14:20:00Z',
    author: 'Editor',
    category: 'Opinion',
    tags: ['Web Development', 'Trends']
  },
  {
    id: 3,
    title: 'Building Scalable APIs with GraphQL',
    slug: 'scalable-apis-graphql',
    status: 'published',
    publishedAt: '2023-05-05T09:15:00Z',
    author: 'Admin User',
    category: 'Tutorials',
    tags: ['GraphQL', 'API', 'Backend']
  },
]

export function BlogList() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const filteredPosts = posts.filter(post => 
    post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    post.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
    post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const handleViewPost = (id: number) => {
    router.push(`/admin/blog/${id}`)
  }

  const handleEditPost = (id: number, e: React.MouseEvent) => {
    e.stopPropagation()
    router.push(`/admin/blog/${id}/edit`)
  }

  const handleDeletePost = async (id: number, e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
      try {
        setIsLoading(true)
        // TODO: Replace with actual API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        toast.success('Post deleted successfully')
        // TODO: Refresh the posts list or remove the deleted post from state
      } catch (error) {
        console.error('Error deleting post:', error)
        toast.error('Failed to delete post')
      } finally {
        setIsLoading(false)
      }
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="relative w-full sm:max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search posts..."
            className="w-full pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button asChild>
          <Link href="/admin/blog/new">
            <Plus className="mr-2 h-4 w-4" />
            New Post
          </Link>
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Category</TableHead>
              <TableHead className="w-[200px]">Tags</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Published</TableHead>
              <TableHead className="w-10">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPosts.map((post) => (
              <TableRow 
                key={post.id} 
                className="cursor-pointer hover:bg-muted/50 transition-colors"
                onClick={() => handleViewPost(post.id)}
              >
                <TableCell className="font-medium">
                  <div className="flex flex-col">
                    <span className="hover:underline">{post.title}</span>
                    <span className="text-xs text-muted-foreground">
                      /blog/{post.slug}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {post.category}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1 max-w-[200px]">
                    {post.tags.slice(0, 2).map((tag, i) => (
                      <Badge key={i} variant="secondary" className="text-xs truncate">
                        {tag}
                      </Badge>
                    ))}
                    {post.tags.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{post.tags.length - 2} more
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={post.status === 'published' ? 'default' : 'outline'} className="capitalize">
                    {post.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Calendar className="mr-1 h-4 w-4 flex-shrink-0" />
                    <span>{format(new Date(post.publishedAt), 'MMM d, yyyy')}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex justify-end">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewPost(post.id)}>
                          <Eye className="mr-2 h-4 w-4" />
                          <span>View</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => handleEditPost(post.id, e)}>
                          <PenSquare className="mr-2 h-4 w-4" />
                          <span>Edit</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="text-destructive"
                          onClick={(e) => handleDeletePost(post.id, e)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          <span>Delete</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {filteredPosts.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  No posts found.
                  {searchQuery && (
                    <Button variant="ghost" size="sm" className="ml-2" onClick={() => setSearchQuery('')}>
                      Clear search
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      {/* Pagination - TODO: Implement actual pagination */}
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="text-sm text-muted-foreground">
          Showing <span className="font-medium">1</span> to <span className="font-medium">{filteredPosts.length}</span> of{' '}
          <span className="font-medium">{filteredPosts.length}</span> posts
        </div>
        <Button variant="outline" size="sm" disabled>
          Previous
        </Button>
        <Button variant="outline" size="sm" disabled>
          Next
        </Button>
      </div>
    </div>
  )
}
