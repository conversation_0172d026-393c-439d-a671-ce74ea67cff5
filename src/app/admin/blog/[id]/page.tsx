"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { format } from "date-fns"
import { ArrowLeft, Calendar, Clock, Edit, Eye, Tag, User } from "lucide-react"
import Link from "next/link"
import { notFound, useParams } from "next/navigation"
import { useState, useEffect } from "react"

interface Post {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  status: string;
  publishedAt: string;
  updatedAt: string;
  author: string;
  category: string;
  tags: string[];
  featuredImage: string;
  readTime: string;
  seo: {
    metaTitle: string;
    metaDescription: string;
    metaKeywords: string;
  };
}

// Mock data - replace with actual data fetching
async function getPost(id: string): Promise<Post | null> {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 500))
  
  const posts = [
    {
      id: '1',
      title: 'Getting Started with Next.js 13',
      slug: 'getting-started-with-nextjs-13',
      content: 'This is a comprehensive guide on getting started with Next.js 13 and its new features...',
      excerpt: 'Learn how to build modern web applications with Next.js 13',
      status: 'published',
      publishedAt: '2023-05-15T10:30:00Z',
      updatedAt: '2023-05-16T14:20:00Z',
      author: 'Admin User',
      category: 'Tutorials',
      tags: ['Next.js', 'React', 'Web Development'],
      featuredImage: '/images/blog/post-1.jpg',
      readTime: '5 min read',
      seo: {
        metaTitle: 'Getting Started with Next.js 13 - Complete Guide',
        metaDescription: 'Learn how to build modern web applications with Next.js 13',
        metaKeywords: 'next.js, react, web development, tutorial'
      }
    }
  ]

  const post = posts.find(post => post.id === id)
  if (!post) return null
  
  return post
}

export default function BlogPostDetails() {
  const params = useParams()
  const [post, setPost] = useState<Post | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchPost = async () => {
      try {
        setLoading(true)
        const postData = await getPost(params.id as string)
        if (!postData) {
          notFound()
        }
        setPost(postData)
      } catch (err: any) {
        setError(err.message || 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchPost()
  }, [params.id])

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error loading post: {error}</div>
  if (!post) return notFound()
  
  // Using non-null assertion operator (!) since we've already checked post is not null
  const safePost = post!

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Post</h1>
          <p className="text-sm text-gray-500">Manage your blog post content and settings</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" asChild>
            <Link href="/admin/blog" className="flex items-center">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Posts
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/admin/blog/${params.id}/edit`} className="flex items-center">
              <Edit className="mr-2 h-4 w-4" />
              Edit Post
            </Link>
          </Button>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Main Content - Left Column (2/3 width) */}
        <div className="lg:w-2/3 space-y-6">
          <Card className="shadow-sm">
            <CardHeader className="border-b bg-gray-50 px-6 py-4">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold">Post Content</h2>
                <Badge variant={safePost.status === 'published' ? 'default' : 'outline'} className="capitalize">
                  {safePost.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <article className="space-y-6">
                <h1 className="text-3xl font-bold text-gray-900">{safePost.title}</h1>
                
                <div className="aspect-video bg-gray-100 rounded-md overflow-hidden">
                  <div 
                    className="w-full h-full bg-cover bg-center" 
                    style={{ backgroundImage: `url(${safePost.featuredImage})` }} 
                  />
                </div>
                
                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 border-b pb-4">
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-1" />
                    <span>By {safePost.author}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    <time dateTime={safePost.publishedAt}>
                      {format(new Date(safePost.publishedAt), 'MMMM d, yyyy')}
                    </time>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    <span>{safePost.readTime}</span>
                  </div>
                </div>
                
                <div className="prose max-w-none">
                  <p className="text-gray-700">{safePost.content}</p>
                </div>
                
                <div className="flex flex-wrap gap-2 pt-4 border-t">
                  <Badge variant="secondary" className="text-sm font-normal">
                    {safePost.category}
                  </Badge>
                  {safePost.tags.map((tag, i) => (
                    <Badge key={i} variant="outline" className="text-sm font-normal">
                      <Tag className="h-3 w-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              </article>
            </CardContent>
            <CardFooter className="bg-gray-50 px-6 py-4 border-t flex justify-between items-center">
              <div className="text-sm text-gray-500">
                Last updated: {format(new Date(safePost.updatedAt), 'MMMM d, yyyy')}
              </div>
              <Button variant="outline" size="sm" asChild>
                <Link href={`/blog/${safePost.slug}`} target="_blank" className="flex items-center">
                  <Eye className="mr-2 h-4 w-4" />
                  View Live
                </Link>
              </Button>
            </CardFooter>
          </Card>
          
          {/* SEO Information */}
          <Card className="shadow-sm">
            <CardHeader className="border-b bg-gray-50 px-6 py-4">
              <h2 className="text-lg font-semibold">Search Engine Optimization</h2>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-1">Meta Title</h4>
                <p className="text-sm text-gray-600">{safePost.seo.metaTitle}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-1">Meta Description</h4>
                <p className="text-sm text-gray-600">{safePost.seo.metaDescription}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-1">Keywords</h4>
                <p className="text-sm text-gray-600">{safePost.seo.metaKeywords}</p>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Sidebar - Right Column (1/3 width) */}
        <div className="lg:w-1/3 space-y-6">
          {/* Status Card */}
          <Card className="shadow-sm">
            <CardHeader className="border-b bg-gray-50 px-6 py-4">
              <h2 className="text-lg font-semibold">Status</h2>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Visibility</p>
                  <p className="text-sm text-gray-600">
                    {safePost.status === 'published' ? 'Public' : 'Private'}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Publish Date</p>
                  <p className="text-sm text-gray-600">
                    {format(new Date(safePost.publishedAt), 'MMMM d, yyyy')}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Last Modified</p>
                  <p className="text-sm text-gray-600">
                    {format(new Date(safePost.updatedAt), 'MMMM d, yyyy')}
                  </p>
                </div>
              </div>
              <div className="mt-6 pt-6 border-t">
                <Button variant="outline" size="sm" className="w-full" asChild>
                  <Link href={`/admin/blog/${params.id}/edit`} className="flex items-center justify-center">
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Post
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
          
          {/* Sharing Card */}
          <Card className="shadow-sm">
            <CardHeader className="border-b bg-gray-50 px-6 py-4">
              <h2 className="text-lg font-semibold">Sharing</h2>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">Share this post</p>
                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="outline" size="sm" className="w-full">
                      <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.44 4.83c-.8.37-1.5.38-2.22.02.93-.56.98-.96 1.32-2.02-.88.52-1.86.9-2.9 1.1-.82-.88-2-1.43-3.3-1.43-2.5 0-4.55 2.04-4.55 4.54 0 .36.03.7.1 1.04-3.77-.2-7.12-2-9.36-4.75-.4.67-.6 1.45-.6 2.3 0 1.56.8 2.95 2 3.77-.74-.03-1.44-.23-2.05-.57v.06c0 2.2 1.56 4.03 3.64 4.44-.67.2-1.37.2-2.06.08.58 1.8 2.26 3.12 4.25 3.16-1.56 1.22-3.52 1.95-5.66 1.95-.36 0-.72-.02-1.08-.06 2 1.27 4.38 2.02 6.94 2.02 8.3 0 12.86-6.9 12.84-12.85.02-.24 0-.43 0-.65.84-.6 1.64-1.34 2.24-2.18z" />
                      </svg>
                      Twitter
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12c0 4.84 3.44 8.87 8 9.8V15H8v-3h2V9.5C10 7.57 11.57 6 13.5 6H16v3h-2c-.55 0-1 .45-1 1v2h3v3h-3v6.95c5.05-.5 9-4.76 9-9.95z" />
                      </svg>
                      Facebook
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.45 3.55c.56.56.55 1.43.13 2.1-1.7 2.08-3.9 3.78-6.4 4.95v5.9c0 1.1-.9 2-2 2s-2-.9-2-2v-5.9c-2.5-1.17-4.7-2.87-6.4-4.95-.42-.67-.43-1.54.13-2.1.56-.56 1.43-.55 2.1-.13 4.3 3.38 7.8 3.38 12.1 0 .67-.42 1.54-.43 2.1.13z" />
                      </svg>
                      Share
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.45 3.55c.56.56.55 1.43.13 2.1-1.7 2.08-3.9 3.78-6.4 4.95v5.9c0 1.1-.9 2-2 2s-2-.9-2-2v-5.9c-2.5-1.17-4.7-2.87-6.4-4.95-.42-.67-.43-1.54.13-2.1.56-.56 1.43-.55 2.1-.13 4.3 3.38 7.8 3.38 12.1 0 .67-.42 1.54-.43 2.1.13z" />
                      </svg>
                      Copy Link
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
