'use client'

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { EnhancedBlogForm } from "@/components/admin-blog/enhanced-blog-form"
import { ArrowLeft, Eye } from "lucide-react"
import Link from "next/link"
import { useToast } from "@/hooks/use-toast"
import { BlogCategory, BlogTag, UpdateBlogPostData, BlogPost } from "@/types/blog"
import { Skeleton } from "@/components/ui/skeleton"

export default function EditBlogPostPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [dataLoading, setDataLoading] = useState(true)
  const [categories, setCategories] = useState<BlogCategory[]>([])
  const [tags, setTags] = useState<BlogTag[]>([])
  const [post, setPost] = useState<BlogPost | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [postRes, categoriesRes, tagsRes] = await Promise.all([
          fetch(`/api/blog/${params.id}?includeUnpublished=true`),
          fetch('/api/blog/categories'),
          fetch('/api/blog/tags')
        ])

        const [postData, categoriesData, tagsData] = await Promise.all([
          postRes.json(),
          categoriesRes.json(),
          tagsRes.json()
        ])

        if (postData.success) {
          setPost(postData.data)
        } else {
          toast({
            title: "Error",
            description: "Blog post not found",
            variant: "destructive",
          })
          router.push('/admin/blog')
          return
        }

        if (categoriesData.success) {
          setCategories(categoriesData.data)
        }

        if (tagsData.success) {
          setTags(tagsData.data)
        }
      } catch (error) {
        console.error('Error fetching data:', error)
        toast({
          title: "Error",
          description: "Failed to load blog post data",
          variant: "destructive",
        })
        router.push('/admin/blog')
      } finally {
        setDataLoading(false)
      }
    }

    if (params.id) {
      fetchData()
    }
  }, [params.id, toast, router])

  const handleSubmit = async (data: UpdateBlogPostData) => {
    setLoading(true)
    try {
      const response = await fetch(`/api/blog/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Success!",
          description: "Blog post updated successfully.",
        })
        router.push('/admin/blog')
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to update blog post",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error updating post:', error)
      toast({
        title: "Error",
        description: "Failed to update blog post",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    router.push('/admin/blog')
  }

  if (dataLoading) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-64 mt-2" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-48 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </div>
      </div>
    )
  }

  if (!post) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Blog post not found</h1>
          <p className="text-muted-foreground mt-2">
            The blog post you're looking for doesn't exist or has been deleted.
          </p>
          <Button asChild className="mt-4">
            <Link href="/admin/blog">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Posts
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Blog Post</h1>
            <p className="text-muted-foreground">
              Update your blog post content and settings
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" asChild>
              <Link href={`/blog/${post.slug}`} target="_blank">
                <Eye className="mr-2 h-4 w-4" />
                View Post
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/admin/blog">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Posts
              </Link>
            </Button>
          </div>
        </div>

        {/* Form */}
        <EnhancedBlogForm
          initialData={post}
          categories={categories}
          tags={tags}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={loading}
          mode="edit"
        />
      </div>
    </div>
  )
}
