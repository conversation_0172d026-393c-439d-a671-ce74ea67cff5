/* Import the editor styles */
@import '../../../components/editor/editor.css';

/* Additional blog editor specific styles */
.blog-content-editor {
  min-height: 400px; /* Taller editor for blog posts */
}

/* Ensure the editor fits well within the form layout */
.blog-content-editor .editor {
  width: 100%;
  margin-bottom: 1rem;
}

/* Enhance the toolbar appearance */
.blog-content-editor .toolbar {
  border-bottom: 1px solid hsl(var(--border));
  padding-bottom: 0.5rem;
  margin-bottom: 0.5rem;
}

/* Make images in the editor more prominent */
.blog-content-editor .ProseMirror img {
  display: block;
  margin: 1.5rem auto;
  max-width: 100%;
  height: auto;
}

/* Improve heading styles in the editor */
.blog-content-editor .ProseMirror h1 {
  font-size: 1.875rem;
  line-height: 2.25rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.blog-content-editor .ProseMirror h2 {
  font-size: 1.5rem;
  line-height: 2rem;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.blog-content-editor .ProseMirror h3 {
  font-size: 1.25rem;
  line-height: 1.75rem;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
}

/* Enhance blockquote styling */
.blog-content-editor .ProseMirror blockquote {
  border-left: 4px solid hsl(var(--primary));
  padding-left: 1rem;
  font-style: italic;
  margin: 1.5rem 0;
}