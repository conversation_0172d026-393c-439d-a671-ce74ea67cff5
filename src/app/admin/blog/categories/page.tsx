'use client'

import { CategoryManagement } from "@/components/admin-blog/category-management"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function CategoriesPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Blog Categories</h1>
            <p className="text-muted-foreground">
              Manage categories to organize your blog posts
            </p>
          </div>
          <Button variant="outline" asChild>
            <Link href="/admin/blog">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Blog
            </Link>
          </Button>
        </div>

        {/* Category Management */}
        <CategoryManagement />
      </div>
    </div>
  )
}
