import { Suspense } from "react"
import { BlogService } from "@/lib/services/blog.service"
import { EnhancedBlogList } from "@/components/admin-blog/enhanced-blog-list"
import { Skeleton } from "@/components/ui/skeleton"

async function BlogPageContent() {
  try {
    const [postsResult, categories] = await Promise.all([
      BlogService.getPosts({
        page: 1,
        perPage: 10,
        includeUnpublished: true,
        includeDrafts: true
      }),
      BlogService.getCategories()
    ])

    return (
      <EnhancedBlogList
        initialData={postsResult}
        categories={categories}
      />
    )
  } catch (error) {
    console.error('Error loading blog data:', error)
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Failed to load blog posts. Please try again.</p>
      </div>
    )
  }
}

function BlogPageSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-64 mt-2" />
        </div>
        <div className="flex space-x-2">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>
      <div className="flex space-x-4">
        <Skeleton className="h-10 flex-1" />
        <Skeleton className="h-10 w-32" />
        <Skeleton className="h-10 w-24" />
      </div>
      <div className="border rounded-md">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4 p-4 border-b last:border-b-0">
            <Skeleton className="h-12 w-16" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
            </div>
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-8 w-8" />
          </div>
        ))}
      </div>
    </div>
  )
}

export default function BlogPage() {
  return (
    <div className="container mx-auto py-6">
      <Suspense fallback={<BlogPageSkeleton />}>
        <BlogPageContent />
      </Suspense>
    </div>
  )
}
