"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { PageLayout } from "../../../components/page-layout"
import { Search, Upload, Folder, Image, File, Video, MoreHorizontal, Trash2, Download } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

const mediaItems = [
  {
    id: 1,
    name: 'office-meeting.jpg',
    type: 'image',
    size: '2.4 MB',
    uploaded: '2023-05-15T10:30:00Z',
    dimensions: '1920x1080',
    url: 'https://example.com/media/office-meeting.jpg'
  },
  {
    id: 2,
    name: 'product-demo.mp4',
    type: 'video',
    size: '15.7 MB',
    uploaded: '2023-05-10T14:20:00Z',
    duration: '2:45',
    url: 'https://example.com/media/product-demo.mp4'
  },
  {
    id: 3,
    name: 'presentation.pdf',
    type: 'document',
    size: '4.2 MB',
    uploaded: '2023-05-05T09:15:00Z',
    pages: '24',
    url: 'https://example.com/media/presentation.pdf'
  },
  {
    id: 4,
    name: 'company-logo.png',
    type: 'image',
    size: '1.1 MB',
    uploaded: '2023-04-28T16:45:00Z',
    dimensions: '800x800',
    url: 'https://example.com/media/company-logo.png'
  },
]

type MediaItem = typeof mediaItems[0]

const FileIcon = ({ type }: { type: string }) => {
  switch (type) {
    case 'image':
      return <Image className="h-5 w-5 text-blue-500" />
    case 'video':
      return <Video className="h-5 w-5 text-purple-500" />
    default:
      return <File className="h-5 w-5 text-gray-500" />
  }
}

export default function MediaPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedItems, setSelectedItems] = useState<number[]>([])

  const filteredItems = mediaItems.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const toggleSelectItem = (id: number) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    )
  }

  const selectAllItems = () => {
    if (selectedItems.length === filteredItems.length) {
      setSelectedItems([])
    } else {
      setSelectedItems(filteredItems.map(item => item.id))
    }
  }

  return (
    <PageLayout
      title="Media Library"
      description="Upload and manage your media files"
      actions={
        <Button>
          <Upload className="mr-2 h-4 w-4" />
          Upload Files
        </Button>
      }
    >
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row justify-between gap-4">
          <div className="relative w-full sm:max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search media..."
              className="w-full pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Folder className="h-4 w-4 mr-2" />
              Create Folder
            </Button>
          </div>
        </div>

        {selectedItems.length > 0 && (
          <div className="flex items-center justify-between rounded-md border bg-muted/50 p-3">
            <div className="text-sm text-muted-foreground">
              {selectedItems.length} {selectedItems.length === 1 ? 'item' : 'items'} selected
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
              <Button variant="outline" size="sm" className="text-destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </div>
          </div>
        )}

        <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
          {filteredItems.map((item) => (
            <div
              key={item.id}
              className={`group relative rounded-lg border p-3 transition-colors hover:bg-muted/50 ${selectedItems.includes(item.id) ? 'border-primary bg-muted/50' : ''
                }`}
              onClick={() => toggleSelectItem(item.id)}
            >
              <div className="flex flex-col items-center">
                <div className="mb-2 flex h-16 w-16 items-center justify-center rounded-md bg-muted">
                  <FileIcon type={item.type} />
                </div>
                <div className="w-full truncate text-center text-sm font-medium">
                  {item.name}
                </div>
                <div className="text-xs text-muted-foreground">
                  {item.size}
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-1 top-1 h-8 w-8 opacity-0 group-hover:opacity-100"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                    <span className="sr-only">More</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Download className="mr-2 h-4 w-4" />
                    <span>Download</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <span className="text-destructive">
                      <Trash2 className="mr-2 h-4 w-4" />
                      <span>Delete</span>
                    </span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ))}
        </div>
      </div>
    </PageLayout>
  )
}
