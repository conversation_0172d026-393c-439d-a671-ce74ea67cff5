"use client"

import { useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { HeaderEditor } from "@/components/site-header/header-editor"
import { useSiteHeaderEditor } from '@/hooks/use-site-header-editor'
import { useAdminHeader } from '@/hooks/use-admin-header'

export default function HeaderEditorPage() {
  const { setTitle, setActions, toggleEditMode } = useSiteHeaderEditor()
  const adminHeader = useAdminHeader()

  useEffect(() => {
    // Set the page title
    adminHeader.setTitle('Header Editor')
    
    // Set the edit mode to true by default on this page
    toggleEditMode()
    
    return () => {
      // Clean up when navigating away
      adminHeader.setTitle('')
    }
  }, [adminHeader, toggleEditMode])

  return (
    <div className="container py-6 space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold">Header Editor</h1>
        <p className="text-muted-foreground">
          Customize the site header appearance and components
        </p>
      </div>
      
      <HeaderEditor />
      
      <div className="bg-muted p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">How to Use the Header Editor</h2>
        
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium">Adding Components</h3>
            <p>You can add buttons to the left, center, or right sections of the header.</p>
            <ol className="list-decimal list-inside mt-2 space-y-1">
              <li>Enter the button text</li>
              <li>Select the position (left, center, right)</li>
              <li>Click "Add Button"</li>
            </ol>
          </div>
          
          <div>
            <h3 className="text-lg font-medium">Styling the Header</h3>
            <p>Customize the appearance of the header container:</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Background color</li>
              <li>Border color and width</li>
              <li>Height, padding, and margin</li>
              <li>Box shadow</li>
              <li>Custom CSS classes</li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-medium">Header Settings</h3>
            <p>Control basic header settings:</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Set a custom title (overrides breadcrumbs)</li>
              <li>Toggle header visibility</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}