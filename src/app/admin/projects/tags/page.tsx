"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Search, MoreHorizontal, Edit, Trash2, ArrowLeft, Tag } from "lucide-react";
import Link from "next/link";

// Mock data - replace with actual API calls
const mockTags = [
  {
    id: "1",
    name: "Data Center",
    slug: "data-center",
    color: "#3B82F6",
    projectCount: 8,
    createdAt: "2024-01-10",
  },
  {
    id: "2",
    name: "Infrastructure",
    slug: "infrastructure",
    color: "#10B981",
    projectCount: 12,
    createdAt: "2024-01-12",
  },
  {
    id: "3",
    name: "Cloud",
    slug: "cloud",
    color: "#8B5CF6",
    projectCount: 6,
    createdAt: "2024-01-15",
  },
  {
    id: "4",
    name: "Security",
    slug: "security",
    color: "#EF4444",
    projectCount: 9,
    createdAt: "2024-01-18",
  },
  {
    id: "5",
    name: "Network",
    slug: "network",
    color: "#F59E0B",
    projectCount: 7,
    createdAt: "2024-01-20",
  },
  {
    id: "6",
    name: "IoT",
    slug: "iot",
    color: "#06B6D4",
    projectCount: 4,
    createdAt: "2024-01-25",
  },
  {
    id: "7",
    name: "AI/ML",
    slug: "ai-ml",
    color: "#EC4899",
    projectCount: 3,
    createdAt: "2024-02-01",
  },
  {
    id: "8",
    name: "Monitoring",
    slug: "monitoring",
    color: "#84CC16",
    projectCount: 5,
    createdAt: "2024-02-05",
  },
];

export default function ProjectTagsPage() {
  const [tags, setTags] = useState(mockTags);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingTag, setEditingTag] = useState<any>(null);

  // Form state
  const [name, setName] = useState("");
  const [slug, setSlug] = useState("");
  const [color, setColor] = useState("#3B82F6");

  const filteredTags = tags.filter((tag) =>
    tag.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleNameChange = (value: string) => {
    setName(value);
    if (!editingTag) {
      setSlug(generateSlug(value));
    }
  };

  const resetForm = () => {
    setName("");
    setSlug("");
    setColor("#3B82F6");
    setEditingTag(null);
  };

  const handleSave = async () => {
    setLoading(true);
    
    const tagData = {
      name,
      slug,
      color,
    };

    try {
      if (editingTag) {
        // Update existing tag
        setTags(tags.map(tag => 
          tag.id === editingTag.id 
            ? { ...tag, ...tagData }
            : tag
        ));
      } else {
        // Create new tag
        const newTag = {
          id: Date.now().toString(),
          ...tagData,
          projectCount: 0,
          createdAt: new Date().toISOString().split('T')[0],
        };
        setTags([...tags, newTag]);
      }
      
      setIsDialogOpen(false);
      resetForm();
    } catch (error) {
      console.error("Error saving tag:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (tag: any) => {
    setEditingTag(tag);
    setName(tag.name);
    setSlug(tag.slug);
    setColor(tag.color);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    const tag = tags.find(t => t.id === id);
    if (tag && tag.projectCount > 0) {
      alert(`Cannot delete "${tag.name}" because it is used in ${tag.projectCount} project(s).`);
      return;
    }

    if (confirm("Are you sure you want to delete this tag?")) {
      setLoading(true);
      try {
        setTags(tags.filter(tag => tag.id !== id));
      } catch (error) {
        console.error("Error deleting tag:", error);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    resetForm();
  };

  const getPopularTags = () => {
    return [...tags].sort((a, b) => b.projectCount - a.projectCount).slice(0, 5);
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/projects">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Projects
            </Link>
          </Button>
          <div className="h-6 w-px bg-border" />
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Project Tags</h2>
            <p className="text-muted-foreground">
              Tag your projects for better organization and discovery
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => setIsDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                New Tag
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>
                  {editingTag ? "Edit Tag" : "Create New Tag"}
                </DialogTitle>
                <DialogDescription>
                  {editingTag 
                    ? "Update the tag information below."
                    : "Add a new tag to organize your projects."
                  }
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Tag Name *</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => handleNameChange(e.target.value)}
                    placeholder="Enter tag name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="slug">URL Slug *</Label>
                  <Input
                    id="slug"
                    value={slug}
                    onChange={(e) => setSlug(e.target.value)}
                    placeholder="tag-url-slug"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="color">Color</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="color"
                      type="color"
                      value={color}
                      onChange={(e) => setColor(e.target.value)}
                      className="w-16 h-10 p-1 border rounded"
                    />
                    <Input
                      value={color}
                      onChange={(e) => setColor(e.target.value)}
                      placeholder="#3B82F6"
                      className="flex-1"
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={handleDialogClose}>
                  Cancel
                </Button>
                <Button onClick={handleSave} disabled={loading || !name || !slug}>
                  {loading ? "Saving..." : editingTag ? "Update Tag" : "Create Tag"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tags</CardTitle>
            <Tag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tags.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tags.reduce((sum, tag) => sum + tag.projectCount, 0)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Most Popular</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tags.reduce((max, tag) => tag.projectCount > max.projectCount ? tag : max, tags[0])?.name || "N/A"}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unused Tags</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tags.filter(tag => tag.projectCount === 0).length}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>All Tags</CardTitle>
                  <CardDescription>
                    {filteredTags.length} tag{filteredTags.length !== 1 ? 's' : ''} found
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search tags..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8 w-[250px]"
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tag</TableHead>
                    <TableHead>Usage</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTags.map((tag) => (
                    <TableRow key={tag.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: tag.color }}
                          />
                          <div>
                            <div className="font-medium">{tag.name}</div>
                            <div className="text-sm text-muted-foreground">
                              /{tag.slug}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {tag.projectCount} project{tag.projectCount !== 1 ? 's' : ''}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(tag.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleEdit(tag)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDelete(tag.id)}
                              className="text-red-600"
                              disabled={tag.projectCount > 0}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Popular Tags</CardTitle>
              <CardDescription>
                Most frequently used tags
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {getPopularTags().map((tag, index) => (
                  <div key={tag.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="text-sm font-medium text-muted-foreground w-4">
                        {index + 1}.
                      </div>
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: tag.color }}
                      />
                      <span className="text-sm font-medium">{tag.name}</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {tag.projectCount}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Tag Cloud</CardTitle>
              <CardDescription>
                Visual representation of tag usage
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <Badge
                    key={tag.id}
                    variant="outline"
                    className="cursor-pointer hover:bg-secondary"
                    style={{
                      borderColor: tag.color,
                      fontSize: Math.max(10, Math.min(16, 10 + tag.projectCount * 0.5)) + 'px',
                    }}
                  >
                    {tag.name}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}