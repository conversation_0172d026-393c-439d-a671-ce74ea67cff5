import { NextRequest } from 'next/server'
import { SEOService } from '@/lib/services/seo.service'

// This creates a dynamic sitemap.xml at /sitemap.xml
export async function GET(request: NextRequest) {
  try {
    const sitemapEntries = await SEOService.generateSitemap()
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com'
    
    const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapEntries.map(entry => `  <url>
    <loc>${siteUrl}${entry.url}</loc>
    <lastmod>${entry.lastModified.toISOString()}</lastmod>
    <changefreq>${entry.changeFrequency}</changefreq>
    <priority>${entry.priority}</priority>
  </url>`).join('\n')}
</urlset>`

    return new Response(xmlContent, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600'
      }
    })

  } catch (error) {
    console.error('Sitemap generation error:', error)
    return new Response('Error generating sitemap', { status: 500 })
  }
}
