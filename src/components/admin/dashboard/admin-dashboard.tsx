'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { AdminDataTable } from '../data-table/admin-data-table'
import { 
  userColumns, 
  postColumns, 
  categoryColumns, 
  mediaColumns, 
  commentColumns,
  type User,
  type Post,
  type Category,
  type Media,
  type Comment
} from '../data-table/columns'
import { 
  UserForm,
  PostForm,
  CategoryForm,
  TagForm,
  MediaForm,
  PageForm,
  MenuForm,
  SettingsForm
} from '../forms'
import { useToast } from '@/hooks/use-toast'
import {
  Users,
  FileText,
  Folder,
  Image as ImageIcon,
  MessageSquare,
  Tag,
  Layout,
  Menu,
  Settings,
  BarChart3,
  TrendingUp,
  Eye,
  Clock,
  Plus,
  Edit,
  Trash2,
  Download,
  Upload,
  RefreshCw
} from 'lucide-react'

interface AdminDashboardProps {
  initialData?: {
    users?: User[]
    posts?: Post[]
    categories?: Category[]
    media?: Media[]
    comments?: Comment[]
    stats?: {
      totalUsers: number
      totalPosts: number
      totalPages: number
      totalComments: number
      totalMedia: number
      publishedPosts: number
      draftPosts: number
      pendingComments: number
    }
  }
  onDataChange?: (type: string, data: any) => void
}

export function AdminDashboard({ 
  initialData = {}, 
  onDataChange 
}: AdminDashboardProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState('overview')
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [formType, setFormType] = useState<string>('')
  const [editingItem, setEditingItem] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Mock data - replace with actual data fetching
  const stats = initialData.stats || {
    totalUsers: 0,
    totalPosts: 0,
    totalPages: 0,
    totalComments: 0,
    totalMedia: 0,
    publishedPosts: 0,
    draftPosts: 0,
    pendingComments: 0,
  }

  const users = initialData.users || []
  const posts = initialData.posts || []
  const categories = initialData.categories || []
  const media = initialData.media || []
  const comments = initialData.comments || []

  const handleAdd = (type: string) => {
    setFormType(type)
    setEditingItem(null)
    setIsFormOpen(true)
  }

  const handleEdit = (type: string, item: any) => {
    setFormType(type)
    setEditingItem(item)
    setIsFormOpen(true)
  }

  const handleDelete = async (type: string, items: any[]) => {
    try {
      setIsLoading(true)
      // Implement delete logic
      toast({
        title: 'Items deleted',
        description: `${items.length} ${type}(s) have been deleted.`,
      })
      onDataChange?.(type, { action: 'delete', items })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete items.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleFormSubmit = async (data: any) => {
    try {
      setIsLoading(true)
      // Implement form submission logic
      const action = editingItem ? 'update' : 'create'
      toast({
        title: 'Success!',
        description: `${formType} ${action}d successfully.`,
      })
      onDataChange?.(formType, { action, data, item: editingItem })
      setIsFormOpen(false)
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to ${editingItem ? 'update' : 'create'} ${formType}.`,
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleExport = async (type: string, items: any[]) => {
    try {
      // Implement export logic
      toast({
        title: 'Export started',
        description: `Exporting ${items.length} ${type}(s)...`,
      })
    } catch (error) {
      toast({
        title: 'Export failed',
        description: 'Failed to export data.',
        variant: 'destructive',
      })
    }
  }

  const handleImport = async (type: string) => {
    try {
      // Implement import logic
      toast({
        title: 'Import started',
        description: `Importing ${type} data...`,
      })
    } catch (error) {
      toast({
        title: 'Import failed',
        description: 'Failed to import data.',
        variant: 'destructive',
      })
    }
  }

  const handleRefresh = async (type: string) => {
    try {
      setIsLoading(true)
      // Implement refresh logic
      toast({
        title: 'Data refreshed',
        description: `${type} data has been refreshed.`,
      })
      onDataChange?.(type, { action: 'refresh' })
    } catch (error) {
      toast({
        title: 'Refresh failed',
        description: 'Failed to refresh data.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const renderForm = () => {
    const commonProps = {
      mode: editingItem ? 'edit' : 'create',
      initialData: editingItem,
      onSubmit: handleFormSubmit,
      onCancel: () => setIsFormOpen(false),
      isLoading,
    }

    switch (formType) {
      case 'user':
        return <UserForm {...commonProps} />
      case 'post':
        return <PostForm {...commonProps} categories={categories} authors={users} />
      case 'category':
        return <CategoryForm {...commonProps} categories={categories} />
      case 'tag':
        return <TagForm {...commonProps} />
      case 'media':
        return <MediaForm {...commonProps} />
      case 'page':
        return <PageForm {...commonProps} authors={users} />
      case 'menu':
        return <MenuForm {...commonProps} />
      case 'settings':
        return <SettingsForm {...commonProps} />
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your site content, users, and settings
          </p>
        </div>
        <Button onClick={() => handleRefresh('all')} disabled={isLoading}>
          <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh All
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-9">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="posts">Posts</TabsTrigger>
          <TabsTrigger value="pages">Pages</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="media">Media</TabsTrigger>
          <TabsTrigger value="comments">Comments</TabsTrigger>
          <TabsTrigger value="menus">Menus</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalUsers}</div>
                <p className="text-xs text-muted-foreground">
                  +2 from last month
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalPosts}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.publishedPosts} published, {stats.draftPosts} drafts
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Comments</CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalComments}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.pendingComments} pending approval
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Media Files</CardTitle>
                <ImageIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalMedia}</div>
                <p className="text-xs text-muted-foreground">
                  Images, videos, documents
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Recent Posts</CardTitle>
                <CardDescription>Latest blog posts and pages</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {posts.slice(0, 5).map((post) => (
                    <div key={post.id} className="flex items-center space-x-4">
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {post.title}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          by {post.author.name}
                        </p>
                      </div>
                      <Badge
                        variant={post.status === 'PUBLISHED' ? 'default' : 'secondary'}
                      >
                        {post.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Comments</CardTitle>
                <CardDescription>Latest user comments</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {comments.slice(0, 5).map((comment) => (
                    <div key={comment.id} className="flex items-start space-x-4">
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {comment.author.name}
                        </p>
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {comment.content}
                        </p>
                      </div>
                      <Badge
                        variant={comment.approved ? 'default' : 'secondary'}
                      >
                        {comment.approved ? 'Approved' : 'Pending'}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Users Tab */}
        <TabsContent value="users">
          <AdminDataTable
            columns={userColumns}
            data={users}
            title="User Management"
            description="Manage user accounts, roles, and permissions"
            searchKey="name"
            searchPlaceholder="Search users..."
            onAdd={() => handleAdd('user')}
            onEdit={(user) => handleEdit('user', user)}
            onDelete={(users) => handleDelete('user', users)}
            onExport={(users) => handleExport('user', users)}
            onImport={() => handleImport('user')}
            onRefresh={() => handleRefresh('user')}
            isLoading={isLoading}
            filters={[
              {
                key: 'role',
                label: 'Role',
                options: [
                  { label: 'Admin', value: 'ADMIN' },
                  { label: 'Editor', value: 'EDITOR' },
                  { label: 'Author', value: 'AUTHOR' },
                  { label: 'User', value: 'USER' },
                ],
              },
            ]}
          />
        </TabsContent>

        {/* Posts Tab */}
        <TabsContent value="posts">
          <AdminDataTable
            columns={postColumns}
            data={posts}
            title="Post Management"
            description="Manage blog posts, articles, and content"
            searchKey="title"
            searchPlaceholder="Search posts..."
            onAdd={() => handleAdd('post')}
            onEdit={(post) => handleEdit('post', post)}
            onDelete={(posts) => handleDelete('post', posts)}
            onExport={(posts) => handleExport('post', posts)}
            onImport={() => handleImport('post')}
            onRefresh={() => handleRefresh('post')}
            isLoading={isLoading}
            filters={[
              {
                key: 'status',
                label: 'Status',
                options: [
                  { label: 'Published', value: 'PUBLISHED' },
                  { label: 'Draft', value: 'DRAFT' },
                  { label: 'Archived', value: 'ARCHIVED' },
                  { label: 'Scheduled', value: 'SCHEDULED' },
                ],
              },
            ]}
          />
        </TabsContent>

        {/* Categories Tab */}
        <TabsContent value="categories">
          <AdminDataTable
            columns={categoryColumns}
            data={categories}
            title="Category Management"
            description="Organize content with categories and subcategories"
            searchKey="name"
            searchPlaceholder="Search categories..."
            onAdd={() => handleAdd('category')}
            onEdit={(category) => handleEdit('category', category)}
            onDelete={(categories) => handleDelete('category', categories)}
            onExport={(categories) => handleExport('category', categories)}
            onRefresh={() => handleRefresh('category')}
            isLoading={isLoading}
          />
        </TabsContent>

        {/* Media Tab */}
        <TabsContent value="media">
          <AdminDataTable
            columns={mediaColumns}
            data={media}
            title="Media Library"
            description="Manage images, videos, documents, and other files"
            searchKey="originalName"
            searchPlaceholder="Search media..."
            onAdd={() => handleAdd('media')}
            onEdit={(media) => handleEdit('media', media)}
            onDelete={(media) => handleDelete('media', media)}
            onExport={(media) => handleExport('media', media)}
            onImport={() => handleImport('media')}
            onRefresh={() => handleRefresh('media')}
            isLoading={isLoading}
            filters={[
              {
                key: 'type',
                label: 'Type',
                options: [
                  { label: 'Images', value: 'IMAGE' },
                  { label: 'Videos', value: 'VIDEO' },
                  { label: 'Audio', value: 'AUDIO' },
                  { label: 'Documents', value: 'DOCUMENT' },
                  { label: 'Other', value: 'OTHER' },
                ],
              },
            ]}
          />
        </TabsContent>

        {/* Comments Tab */}
        <TabsContent value="comments">
          <AdminDataTable
            columns={commentColumns}
            data={comments}
            title="Comment Management"
            description="Moderate and manage user comments"
            searchKey="content"
            searchPlaceholder="Search comments..."
            onEdit={(comment) => handleEdit('comment', comment)}
            onDelete={(comments) => handleDelete('comment', comments)}
            onExport={(comments) => handleExport('comment', comments)}
            onRefresh={() => handleRefresh('comment')}
            isLoading={isLoading}
            filters={[
              {
                key: 'approved',
                label: 'Status',
                options: [
                  { label: 'Approved', value: 'approved' },
                  { label: 'Pending', value: 'pending' },
                ],
              },
            ]}
            actions={[
              {
                label: 'Approve',
                icon: <Eye className="mr-2 h-4 w-4" />,
                onClick: (comments) => {
                  // Implement approve logic
                  toast({
                    title: 'Comments approved',
                    description: `${comments.length} comment(s) have been approved.`,
                  })
                },
              },
            ]}
          />
        </TabsContent>

        {/* Other tabs would follow similar patterns */}
        <TabsContent value="pages">
          <Card>
            <CardHeader>
              <CardTitle>Page Management</CardTitle>
              <CardDescription>Manage static pages and custom content</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Page management interface would go here...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="menus">
          <Card>
            <CardHeader>
              <CardTitle>Menu Management</CardTitle>
              <CardDescription>Create and manage navigation menus</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Menu management interface would go here...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <SettingsForm
            onSubmit={handleFormSubmit}
            isLoading={isLoading}
          />
        </TabsContent>
      </Tabs>

      {/* Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingItem ? 'Edit' : 'Create'} {formType}
            </DialogTitle>
          </DialogHeader>
          {renderForm()}
        </DialogContent>
      </Dialog>
    </div>
  )
}