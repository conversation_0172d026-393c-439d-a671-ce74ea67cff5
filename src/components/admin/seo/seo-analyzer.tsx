'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckCircle, 
  AlertCircle, 
  XCircle, 
  TrendingUp, 
  Search, 
  Eye,
  Share2,
  Lightbulb
} from 'lucide-react'

interface SEOAnalyzerProps {
  title?: string
  description?: string
  content?: string
  url: string
  keywords?: string[]
  image?: string
  type?: 'website' | 'article' | 'product' | 'profile'
}

interface SEOAnalysis {
  validation: {
    valid: boolean
    errors: string[]
  }
  score: {
    score: number
    suggestions: string[]
  }
  metaTags: Record<string, string>
  structuredData: any
  recommendations: string[]
}

export function SEOAnalyzer({
  title = '',
  description = '',
  content = '',
  url,
  keywords = [],
  image,
  type = 'website'
}: SEOAnalyzerProps) {
  const [analysis, setAnalysis] = useState<SEOAnalysis | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (title || description || content) {
      analyzeSEO()
    }
  }, [title, description, content, url, keywords, image, type])

  const analyzeSEO = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/seo/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          description,
          content,
          url,
          keywords,
          image,
          type
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to analyze SEO')
      }

      const data = await response.json()
      setAnalysis(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to analyze SEO')
    } finally {
      setLoading(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="h-5 w-5 text-green-600" />
    if (score >= 60) return <AlertCircle className="h-5 w-5 text-yellow-600" />
    return <XCircle className="h-5 w-5 text-red-600" />
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>SEO Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            <span>Analyzing SEO...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>SEO Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button onClick={analyzeSEO} className="mt-4">
            Retry Analysis
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (!analysis) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>SEO Analysis</span>
          </CardTitle>
          <CardDescription>
            Enter content to see SEO analysis and recommendations
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* SEO Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Search className="h-5 w-5" />
              <span>SEO Score</span>
            </div>
            {getScoreIcon(analysis.score.score)}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Score</span>
              <span className={`text-2xl font-bold ${getScoreColor(analysis.score.score)}`}>
                {analysis.score.score}/100
              </span>
            </div>
            <Progress value={analysis.score.score} className="h-2" />
            
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-sm text-muted-foreground">Title</div>
                <div className="font-medium">
                  {title.length > 0 ? (
                    <Badge variant={title.length <= 60 ? 'default' : 'destructive'}>
                      {title.length}/60
                    </Badge>
                  ) : (
                    <Badge variant="outline">Missing</Badge>
                  )}
                </div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Description</div>
                <div className="font-medium">
                  {description.length > 0 ? (
                    <Badge variant={description.length <= 160 ? 'default' : 'destructive'}>
                      {description.length}/160
                    </Badge>
                  ) : (
                    <Badge variant="outline">Missing</Badge>
                  )}
                </div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Image</div>
                <div className="font-medium">
                  <Badge variant={image ? 'default' : 'outline'}>
                    {image ? 'Set' : 'Missing'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Validation Errors */}
      {!analysis.validation.valid && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-600">
              <XCircle className="h-5 w-5" />
              <span>Validation Issues</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {analysis.validation.errors.map((error, index) => (
                <Alert key={index} variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      {analysis.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Lightbulb className="h-5 w-5" />
              <span>Recommendations</span>
            </CardTitle>
            <CardDescription>
              Improve your SEO with these suggestions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analysis.recommendations.map((recommendation, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-muted/50 rounded-lg">
                  <TrendingUp className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">{recommendation}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="h-5 w-5" />
            <span>Search Preview</span>
          </CardTitle>
          <CardDescription>
            How your page might appear in search results
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg p-4 bg-white">
            <div className="text-blue-600 text-lg hover:underline cursor-pointer">
              {title || 'Page Title'}
            </div>
            <div className="text-green-700 text-sm">
              {url}
            </div>
            <div className="text-gray-600 text-sm mt-1">
              {description || 'Page description will appear here...'}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Social Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Share2 className="h-5 w-5" />
            <span>Social Media Preview</span>
          </CardTitle>
          <CardDescription>
            How your page will look when shared on social media
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg overflow-hidden bg-white max-w-md">
            {image && (
              <div className="aspect-video bg-gray-200">
                <img
                  src={image}
                  alt={title}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            <div className="p-4">
              <div className="font-medium text-sm line-clamp-2">
                {title || 'Page Title'}
              </div>
              <div className="text-gray-600 text-xs mt-1 line-clamp-2">
                {description || 'Page description will appear here...'}
              </div>
              <div className="text-gray-500 text-xs mt-2">
                {new URL(url).hostname}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
