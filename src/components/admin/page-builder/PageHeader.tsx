'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { useState } from 'react'
import {
  ArrowLeft,
  Save,
  Eye,
  Globe,
  Undo,
  Redo,
  Loader2,
  Clock
} from 'lucide-react'
import Link from 'next/link'

interface PageHeaderProps {
  title: string
  slug?: string
  status?: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  hasUnsavedChanges: boolean
  isLoading: boolean
  canUndo: boolean
  canRedo: boolean
  onSave: () => Promise<void>
  onPublish?: () => Promise<void>
  onPreview?: () => void
  onUndo: () => void
  onRedo: () => void
}

export function PageHeader({
  title,
  slug,
  status = 'DRAFT',
  hasUnsavedChanges,
  isLoading,
  canUndo,
  canRedo,
  onSave,
  onPublish,
  onPreview,
  onUndo,
  onRedo
}: PageHeaderProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedTitle, setEditedTitle] = useState(title)

  const getStatusBadge = (status: string) => {
    const variants = {
      PUBLISHED: 'default',
      DRAFT: 'secondary',
      ARCHIVED: 'outline'
    } as const

    const colors = {
      PUBLISHED: 'text-green-700 bg-green-50 border-green-200',
      DRAFT: 'text-yellow-700 bg-yellow-50 border-yellow-200',
      ARCHIVED: 'text-gray-700 bg-gray-50 border-gray-200'
    }

    return (
      <Badge variant={variants[status as keyof typeof variants]} className={colors[status as keyof typeof colors]}>
        {status.toLowerCase()}
      </Badge>
    )
  }

  return (
    <div className="border-b bg-background sticky top-0 z-10">
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-4">
          <Link href="/admin/pages">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </Link>
          
          <div>
            {isEditing ? (
              <div className="flex items-center space-x-2">
                <Input
                  value={editedTitle}
                  onChange={(e) => setEditedTitle(e.target.value)}
                  className="text-xl font-bold h-10 w-[300px]"
                  autoFocus
                />
                <Button 
                  size="sm" 
                  onClick={() => {
                    // Handle title update
                    setIsEditing(false)
                  }}
                >
                  Save
                </Button>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  onClick={() => {
                    setEditedTitle(title)
                    setIsEditing(false)
                  }}
                >
                  Cancel
                </Button>
              </div>
            ) : (
              <h1 
                className="text-2xl font-bold tracking-tight cursor-pointer hover:text-primary transition-colors"
                onClick={() => setIsEditing(true)}
                title="Click to edit title"
              >
                {title || 'Untitled Page'}
              </h1>
            )}
            
            {slug && (
              <div className="flex items-center space-x-2 mt-1">
                <p className="text-sm text-muted-foreground">
                  /{slug}
                </p>
                {getStatusBadge(status)}
                {hasUnsavedChanges && (
                  <Badge variant="outline" className="text-amber-600 border-amber-200 bg-amber-50">
                    <Clock className="h-3 w-3 mr-1" />
                    Unsaved changes
                  </Badge>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1 mr-2">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={onUndo} 
              disabled={!canUndo || isLoading}
              title="Undo"
            >
              <Undo className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={onRedo} 
              disabled={!canRedo || isLoading}
              title="Redo"
            >
              <Redo className="h-4 w-4" />
            </Button>
          </div>
          
          <Button 
            variant="outline" 
            onClick={onPreview}
            disabled={isLoading}
          >
            <Eye className="mr-2 h-4 w-4" />
            Preview
          </Button>
          
          <Button
            variant="outline"
            onClick={onSave}
            disabled={isLoading || !hasUnsavedChanges}
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Save className="mr-2 h-4 w-4" />
            )}
            Save
          </Button>
          
          {onPublish && (
            <Button
              onClick={onPublish}
              disabled={isLoading}
            >
              <Globe className="mr-2 h-4 w-4" />
              Publish
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}