'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Monitor, 
  Tablet, 
  Smartphone, 
  ExternalLink, 
  RefreshCw,
  Eye,
  Settings
} from 'lucide-react'
import { TemplateRenderer } from '@/components/templates'
import { cn } from '@/lib/utils'

interface PagePreviewProps {
  page: any
  blocks: any[]
  viewMode: 'desktop' | 'tablet' | 'mobile'
  className?: string
}

interface PreviewFrameProps {
  page: any
  blocks: any[]
  viewMode: 'desktop' | 'tablet' | 'mobile'
  showFrame?: boolean
}

function PreviewFrame({ page, blocks, viewMode, showFrame = true }: PreviewFrameProps) {
  const [isLoading, setIsLoading] = useState(false)

  // Create a mock page object with blocks for the template renderer
  const previewPage = {
    ...page,
    blocks: blocks.map((block, index) => ({
      ...block,
      order: index
    }))
  }

  const getFrameStyles = () => {
    switch (viewMode) {
      case 'tablet':
        return {
          width: '768px',
          height: '1024px',
          maxWidth: '100%'
        }
      case 'mobile':
        return {
          width: '375px',
          height: '667px',
          maxWidth: '100%'
        }
      default:
        return {
          width: '100%',
          height: '100%'
        }
    }
  }

  const frameStyles = getFrameStyles()

  if (!page) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
            <Eye className="h-8 w-8 text-muted-foreground" />
          </div>
          <div>
            <h3 className="font-medium mb-2">No Page to Preview</h3>
            <p className="text-sm text-muted-foreground">
              Create a page to see the preview
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex items-center justify-center p-4">
      <div
        className={cn(
          "bg-white shadow-lg transition-all duration-300",
          showFrame && "border rounded-lg overflow-hidden",
          viewMode !== 'desktop' && "mx-auto"
        )}
        style={frameStyles}
      >
        {showFrame && viewMode !== 'desktop' && (
          <div className="bg-gray-900 text-white px-4 py-2 text-xs flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            </div>
            <div className="text-gray-400">
              {viewMode === 'tablet' ? 'iPad' : 'iPhone'} Preview
            </div>
          </div>
        )}
        
        <div className={cn(
          "h-full overflow-auto",
          viewMode !== 'desktop' && showFrame && "h-[calc(100%-2rem)]"
        )}>
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <RefreshCw className="h-6 w-6 animate-spin" />
            </div>
          ) : (
            <TemplateRenderer
              page={previewPage}
              isPreview={true}
              className="min-h-full"
            />
          )}
        </div>
      </div>
    </div>
  )
}

export function PagePreview({ page, blocks, viewMode, className }: PagePreviewProps) {
  const [refreshKey, setRefreshKey] = useState(0)
  const [showDeviceFrame, setShowDeviceFrame] = useState(true)

  // Force refresh when blocks change
  useEffect(() => {
    setRefreshKey(prev => prev + 1)
  }, [blocks, page])

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1)
  }

  const handleOpenInNewTab = () => {
    if (page?.slug) {
      window.open(`/${page.slug}?preview=true`, '_blank')
    }
  }

  const getViewModeIcon = () => {
    switch (viewMode) {
      case 'tablet':
        return <Tablet className="h-4 w-4" />
      case 'mobile':
        return <Smartphone className="h-4 w-4" />
      default:
        return <Monitor className="h-4 w-4" />
    }
  }

  const getViewModeLabel = () => {
    switch (viewMode) {
      case 'tablet':
        return 'Tablet (768px)'
      case 'mobile':
        return 'Mobile (375px)'
      default:
        return 'Desktop'
    }
  }

  return (
    <div className={cn("h-full flex flex-col bg-muted/30", className)}>
      {/* Preview Header */}
      <div className="border-b bg-background px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              {getViewModeIcon()}
              <span className="font-medium">{getViewModeLabel()}</span>
            </div>
            
            <Separator orientation="vertical" className="h-4" />
            
            <Badge variant="outline" className="text-xs">
              Live Preview
            </Badge>
            
            {page?.status && (
              <Badge 
                variant={page.status === 'PUBLISHED' ? 'default' : 'secondary'}
                className="text-xs"
              >
                {page.status}
              </Badge>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDeviceFrame(!showDeviceFrame)}
              className="text-xs"
            >
              <Settings className="h-3 w-3 mr-1" />
              {showDeviceFrame ? 'Hide Frame' : 'Show Frame'}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              className="text-xs"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Refresh
            </Button>

            {page?.slug && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleOpenInNewTab}
                className="text-xs"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Open
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-hidden">
        <PreviewFrame
          key={refreshKey}
          page={page}
          blocks={blocks}
          viewMode={viewMode}
          showFrame={showDeviceFrame && viewMode !== 'desktop'}
        />
      </div>

      {/* Preview Footer */}
      <div className="border-t bg-background px-4 py-2">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            <span>Blocks: {blocks.length}</span>
            {page?.template && (
              <span>Template: {page.template}</span>
            )}
            <span>Last updated: {new Date().toLocaleTimeString()}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Live</span>
          </div>
        </div>
      </div>
    </div>
  )
}
