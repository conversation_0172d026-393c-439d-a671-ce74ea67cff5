'use client'

import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { format } from 'date-fns'
import { Clock, RotateCcw } from 'lucide-react'

interface PageHistoryProps {
  history: any[]
  currentIndex: number
  onRestore: (index: number) => void
}

export function PageHistory({ history, currentIndex, onRestore }: PageHistoryProps) {
  if (!history || history.length === 0) {
    return (
      <div className="text-center py-8">
        <Clock className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p className="text-muted-foreground">No history available</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium mb-1">Page History</h3>
        <p className="text-sm text-muted-foreground">
          View and restore previous versions of this page
        </p>
      </div>
      
      <ScrollArea className="h-[400px] border rounded-md">
        <div className="p-4 space-y-2">
          {history.map((version, index) => {
            const isCurrentVersion = index === currentIndex
            const date = new Date()
            date.setMinutes(date.getMinutes() - (history.length - index) * 5)
            
            return (
              <div 
                key={index}
                className={`p-3 rounded-md border ${
                  isCurrentVersion 
                    ? 'border-primary bg-primary/5' 
                    : 'border-border hover:border-primary/50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">
                      {index === history.length - 1 
                        ? 'Current Version' 
                        : `Version ${index + 1}`}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {format(date, 'MMM d, yyyy h:mm a')}
                    </div>
                  </div>
                  
                  {!isCurrentVersion && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => onRestore(index)}
                    >
                      <RotateCcw className="h-3.5 w-3.5 mr-1" />
                      Restore
                    </Button>
                  )}
                </div>
                
                <div className="mt-2 text-sm">
                  <div className="flex flex-wrap gap-2">
                    {version.title && (
                      <span className="bg-muted px-2 py-1 rounded text-xs">
                        Title: {version.title.substring(0, 20)}{version.title.length > 20 ? '...' : ''}
                      </span>
                    )}
                    {version.blocks && (
                      <span className="bg-muted px-2 py-1 rounded text-xs">
                        Blocks: {version.blocks.length}
                      </span>
                    )}
                    {version.status && (
                      <span className="bg-muted px-2 py-1 rounded text-xs">
                        Status: {version.status.toLowerCase()}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </ScrollArea>
      
      <div className="text-xs text-muted-foreground">
        Note: Restoring a previous version will not delete your current version, but will create a new version based on the restored content.
      </div>
    </div>
  )
}