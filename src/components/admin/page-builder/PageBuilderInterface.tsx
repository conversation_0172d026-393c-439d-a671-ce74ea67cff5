'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'
import {
  ArrowLeft,
  Save,
  Eye,
  Settings,
  Image,
  Layers,
  Search,
  Calendar,
  Globe,
  PanelLeft,
  PanelRight,
  Undo,
  Redo,
  Copy,
  Trash2,
  Plus,
  MoveVertical
} from 'lucide-react'
import Link from 'next/link'
import { BlockEditor } from '@/components/admin/ui/block-editor'
import { SEOFields } from '@/components/admin/ui/seo-fields'
import { PageFormData } from '@/lib/schemas/page.schema'
import { PagePreview } from './PagePreview'
import { PageSettings } from './PageSettings'
import { PageHeader } from './PageHeader'
import { BlockLibrary } from './BlockLibrary'
import { BlockProperties } from './BlockProperties'
import { PageHistory } from './PageHistory'
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable'

interface PageBuilderInterfaceProps {
  initialData?: PageFormData
  isNew?: boolean
  onSave: (data: PageFormData) => Promise<void>
  onPublish?: (data: PageFormData) => Promise<void>
  onPreview?: (data: PageFormData) => void
}

export function PageBuilderInterface({
  initialData,
  isNew = false,
  onSave,
  onPublish,
  onPreview
}: PageBuilderInterfaceProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [pageData, setPageData] = useState<PageFormData>(initialData || {
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    template: 'default',
    status: 'DRAFT',
    blocks: [],
    seo: {
      title: '',
      description: '',
      keywords: [],
      noindex: false,
      nofollow: false,
    },
    settings: {
      allowComments: true,
      showAuthor: true,
      showDate: true,
      showShare: true,
      showRelated: true,
    },
  })
  
  const [activeTab, setActiveTab] = useState<'content' | 'seo' | 'settings' | 'history'>('content')
  const [showPreview, setShowPreview] = useState(false)
  const [showLeftPanel, setShowLeftPanel] = useState(true)
  const [showRightPanel, setShowRightPanel] = useState(true)
  const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [history, setHistory] = useState<PageFormData[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)

  // Initialize history with initial data
  useEffect(() => {
    if (initialData) {
      setHistory([initialData])
      setHistoryIndex(0)
    }
  }, [initialData])

  // Update page data and track history
  const updatePageData = (newData: Partial<PageFormData>) => {
    setPageData(prev => {
      const updated = { ...prev, ...newData }
      
      // Add to history if there are changes
      if (JSON.stringify(updated) !== JSON.stringify(prev)) {
        // Truncate future history if we're not at the latest point
        const newHistory = history.slice(0, historyIndex + 1)
        setHistory([...newHistory, updated])
        setHistoryIndex(newHistory.length)
        setHasUnsavedChanges(true)
      }
      
      return updated
    })
  }

  // Handle undo
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1)
      setPageData(history[historyIndex - 1])
      setHasUnsavedChanges(true)
    }
  }

  // Handle redo
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1)
      setPageData(history[historyIndex + 1])
      setHasUnsavedChanges(true)
    }
  }

  // Handle saving the page
  const handleSave = async () => {
    try {
      setLoading(true)
      await onSave(pageData)
      setHasUnsavedChanges(false)
      toast({
        title: 'Page saved',
        description: 'Your page has been saved successfully.',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save page. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // Handle publishing the page
  const handlePublish = async () => {
    try {
      setLoading(true)
      const publishData = {
        ...pageData,
        status: 'PUBLISHED',
        publishedAt: new Date(),
      }
      
      if (onPublish) {
        await onPublish(publishData)
      } else {
        await onSave(publishData)
      }
      
      setHasUnsavedChanges(false)
      toast({
        title: 'Page published',
        description: 'Your page has been published successfully.',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to publish page. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // Handle previewing the page
  const handlePreview = () => {
    if (onPreview) {
      onPreview(pageData)
    } else {
      setShowPreview(!showPreview)
    }
  }

  // Handle selecting a block
  const handleSelectBlock = (blockId: string) => {
    setSelectedBlockId(blockId)
  }

  // Handle updating a block
  const handleUpdateBlock = (blockId: string, blockData: any) => {
    updatePageData({
      blocks: pageData.blocks?.map(block => 
        block.id === blockId ? { ...block, ...blockData } : block
      )
    })
  }

  // Handle adding a block
  const handleAddBlock = (blockType: string) => {
    const newBlock = {
      id: `block-${Date.now()}`,
      type: blockType,
      content: {},
    }
    
    updatePageData({
      blocks: [...(pageData.blocks || []), newBlock]
    })
  }

  // Handle removing a block
  const handleRemoveBlock = (blockId: string) => {
    updatePageData({
      blocks: pageData.blocks?.filter(block => block.id !== blockId)
    })
    
    if (selectedBlockId === blockId) {
      setSelectedBlockId(null)
    }
  }

  // Handle reordering blocks
  const handleReorderBlocks = (startIndex: number, endIndex: number) => {
    const result = Array.from(pageData.blocks || [])
    const [removed] = result.splice(startIndex, 1)
    result.splice(endIndex, 0, removed)
    
    updatePageData({ blocks: result })
  }

  // Get the selected block
  const selectedBlock = selectedBlockId 
    ? pageData.blocks?.find(block => block.id === selectedBlockId)
    : null

  return (
    <div className="flex flex-col h-screen">
      {/* Header */}
      <PageHeader
        title={pageData.title || (isNew ? 'New Page' : 'Edit Page')}
        slug={pageData.slug}
        status={pageData.status}
        hasUnsavedChanges={hasUnsavedChanges}
        onSave={handleSave}
        onPublish={handlePublish}
        onPreview={handlePreview}
        isLoading={loading}
        canUndo={historyIndex > 0}
        canRedo={historyIndex < history.length - 1}
        onUndo={handleUndo}
        onRedo={handleRedo}
      />

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        {showPreview ? (
          <div className="h-full flex flex-col">
            <div className="bg-muted p-2 flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Preview Mode</span>
              <Button variant="outline" size="sm" onClick={() => setShowPreview(false)}>
                Exit Preview
              </Button>
            </div>
            <div className="flex-1 overflow-auto bg-background">
              <PagePreview pageData={pageData} />
            </div>
          </div>
        ) : (
          <ResizablePanelGroup direction="horizontal" className="h-full">
            {/* Left Panel - Block Library */}
            {showLeftPanel && (
              <>
                <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
                  <div className="h-full overflow-hidden flex flex-col">
                    <div className="p-4 border-b bg-muted/30 flex justify-between items-center">
                      <h3 className="text-sm font-medium">Block Library</h3>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => setShowLeftPanel(false)}
                      >
                        <PanelLeft className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex-1 overflow-auto p-4">
                      <BlockLibrary onAddBlock={handleAddBlock} />
                    </div>
                  </div>
                </ResizablePanel>
                <ResizableHandle withHandle />
              </>
            )}

            {/* Center Panel - Main Editor */}
            <ResizablePanel defaultSize={showLeftPanel && showRightPanel ? 60 : 80}>
              <div className="h-full flex flex-col">
                <div className="bg-muted/30 border-b">
                  <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
                    <div className="flex justify-between items-center px-4">
                      <TabsList>
                        <TabsTrigger value="content">
                          <Layers className="h-4 w-4 mr-2" />
                          Content
                        </TabsTrigger>
                        <TabsTrigger value="seo">
                          <Search className="h-4 w-4 mr-2" />
                          SEO
                        </TabsTrigger>
                        <TabsTrigger value="settings">
                          <Settings className="h-4 w-4 mr-2" />
                          Settings
                        </TabsTrigger>
                        <TabsTrigger value="history">
                          <Calendar className="h-4 w-4 mr-2" />
                          History
                        </TabsTrigger>
                      </TabsList>
                      
                      <div className="flex items-center space-x-2">
                        {!showLeftPanel && (
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            onClick={() => setShowLeftPanel(true)}
                            title="Show block library"
                          >
                            <PanelLeft className="h-4 w-4" />
                          </Button>
                        )}
                        {!showRightPanel && (
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            onClick={() => setShowRightPanel(true)}
                            title="Show properties panel"
                          >
                            <PanelRight className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </Tabs>
                </div>
                
                <div className="flex-1 overflow-auto">
                  <TabsContent value="content" className="h-full m-0">
                    <div className="h-full p-4">
                      {!showLeftPanel && (
                        <div className="mb-4 flex justify-between items-center">
                          <h3 className="text-lg font-medium">Page Content</h3>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => handleAddBlock('text')}
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Add Block
                          </Button>
                        </div>
                      )}
                      
                      {pageData.blocks && pageData.blocks.length > 0 ? (
                        <div className="space-y-4">
                          {pageData.blocks.map((block, index) => (
                            <Card 
                              key={block.id} 
                              className={`border ${selectedBlockId === block.id ? 'border-primary ring-1 ring-primary' : ''}`}
                            >
                              <CardContent className="p-0">
                                <div className="flex items-center justify-between bg-muted/50 px-3 py-2 border-b">
                                  <div className="flex items-center">
                                    <MoveVertical className="h-4 w-4 text-muted-foreground mr-2 cursor-move" />
                                    <span className="text-sm font-medium">{block.type.charAt(0).toUpperCase() + block.type.slice(1)} Block</span>
                                  </div>
                                  <div className="flex items-center space-x-1">
                                    <Button 
                                      variant="ghost" 
                                      size="icon" 
                                      onClick={() => handleSelectBlock(block.id)}
                                      className="h-8 w-8"
                                    >
                                      <Settings className="h-4 w-4" />
                                    </Button>
                                    <Button 
                                      variant="ghost" 
                                      size="icon" 
                                      onClick={() => {
                                        const newBlock = { ...block, id: `block-${Date.now()}` }
                                        updatePageData({
                                          blocks: [...pageData.blocks || [], newBlock]
                                        })
                                      }}
                                      className="h-8 w-8"
                                    >
                                      <Copy className="h-4 w-4" />
                                    </Button>
                                    <Button 
                                      variant="ghost" 
                                      size="icon" 
                                      onClick={() => handleRemoveBlock(block.id)}
                                      className="h-8 w-8 text-destructive hover:text-destructive"
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                                <div className="p-4" onClick={() => handleSelectBlock(block.id)}>
                                  {/* Render block preview based on type */}
                                  {block.type === 'text' && (
                                    <div dangerouslySetInnerHTML={{ __html: block.content?.text || 'Text content' }} />
                                  )}
                                  {block.type === 'image' && (
                                    <div className="flex justify-center">
                                      {block.content?.src ? (
                                        <img 
                                          src={block.content.src} 
                                          alt={block.content.alt || ''} 
                                          className="max-h-48 object-contain"
                                        />
                                      ) : (
                                        <div className="flex items-center justify-center bg-muted h-32 w-full rounded-md">
                                          <Image className="h-8 w-8 text-muted-foreground" />
                                        </div>
                                      )}
                                    </div>
                                  )}
                                  {/* Add more block type renderers as needed */}
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center h-full text-center p-8">
                          <Layers className="h-12 w-12 text-muted-foreground mb-4" />
                          <h3 className="text-lg font-medium mb-2">No content blocks yet</h3>
                          <p className="text-muted-foreground mb-4">
                            Start building your page by adding content blocks from the library
                          </p>
                          <Button onClick={() => handleAddBlock('text')}>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Your First Block
                          </Button>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="seo" className="h-full m-0">
                    <div className="p-6">
                      <SEOFields
                        title={pageData.seo?.title || ''}
                        description={pageData.seo?.description || ''}
                        keywords={pageData.seo?.keywords || []}
                        noindex={pageData.seo?.noindex || false}
                        nofollow={pageData.seo?.nofollow || false}
                        onChange={(seoData) => updatePageData({ seo: seoData })}
                      />
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="settings" className="h-full m-0">
                    <div className="p-6">
                      <PageSettings
                        settings={pageData.settings || {}}
                        onChange={(settingsData) => updatePageData({ settings: settingsData })}
                      />
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="history" className="h-full m-0">
                    <div className="p-6">
                      <PageHistory
                        history={history}
                        currentIndex={historyIndex}
                        onRestore={(index) => {
                          setHistoryIndex(index)
                          setPageData(history[index])
                          setHasUnsavedChanges(true)
                        }}
                      />
                    </div>
                  </TabsContent>
                </div>
              </div>
            </ResizablePanel>

            {/* Right Panel - Block Properties */}
            {showRightPanel && (
              <>
                <ResizableHandle withHandle />
                <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
                  <div className="h-full overflow-hidden flex flex-col">
                    <div className="p-4 border-b bg-muted/30 flex justify-between items-center">
                      <h3 className="text-sm font-medium">Properties</h3>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => setShowRightPanel(false)}
                      >
                        <PanelRight className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex-1 overflow-auto p-4">
                      {selectedBlock ? (
                        <BlockProperties
                          block={selectedBlock}
                          onChange={(data) => handleUpdateBlock(selectedBlock.id, data)}
                        />
                      ) : (
                        <div className="flex flex-col items-center justify-center h-full text-center p-4">
                          <Settings className="h-8 w-8 text-muted-foreground mb-2" />
                          <p className="text-muted-foreground">
                            Select a block to edit its properties
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </ResizablePanel>
              </>
            )}
          </ResizablePanelGroup>
        )}
      </div>
    </div>
  )
}