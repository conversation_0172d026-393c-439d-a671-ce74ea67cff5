# Page Builder System

A comprehensive visual page builder for creating and editing dynamic pages with drag-and-drop functionality, real-time preview, and advanced content management features.

## Features

### 🎨 Visual Block Editor
- **Drag & Drop Interface**: Intuitive block management with sortable components
- **Block Library**: Pre-built blocks including Hero, Text, Image, Gallery, Video, CTA, Features, Testimonials, and Contact Forms
- **Real-time Editing**: Live preview updates as you modify content
- **Block Configuration**: Dynamic form panels for editing block properties

### 📱 Responsive Design
- **Multi-device Preview**: Desktop, tablet, and mobile viewport modes
- **Responsive Controls**: Switch between device views instantly
- **Frame Preview**: Device-specific preview frames for accurate representation

### ⚡ Advanced Features
- **Auto-save**: Automatic saving with visual indicators
- **Undo/Redo**: Full history management with keyboard shortcuts
- **Keyboard Shortcuts**: Comprehensive hotkey support for power users
- **Template System**: Multiple page templates with customizable layouts
- **SEO Integration**: Built-in SEO analyzer and meta tag management

### 🔧 Technical Features
- **Type Safety**: Full TypeScript implementation
- **Performance Optimized**: Efficient state management and rendering
- **Error Handling**: Comprehensive error boundaries and user feedback
- **Accessibility**: WCAG compliant with keyboard navigation support

## Components

### Core Components

#### `PageBuilder`
Main container component that orchestrates the entire page building experience.

```tsx
<PageBuilder
  pageId="page-123"
  initialPage={pageData}
  onSave={handleSave}
  onPublish={handlePublish}
  onPreview={handlePreview}
/>
```

#### `BlockLibrary`
Sidebar component displaying available block types with search and categorization.

#### `BlockEditor`
Main editing area where blocks are displayed and can be manipulated.

#### `BlockConfigPanel`
Dynamic configuration panel for editing selected block properties.

#### `PagePreview`
Real-time preview component with responsive viewport controls.

#### `PageSettingsPanel`
Comprehensive page metadata editor with SEO tools.

### Utility Components

#### `AutoSaveIndicator`
Visual indicator showing save status and connection state.

#### `KeyboardShortcuts`
Modal displaying available keyboard shortcuts and help information.

#### `ResponsivePreview`
Multi-device preview component showing all viewport sizes simultaneously.

## Usage

### Basic Implementation

```tsx
import { PageBuilder } from '@/components/admin/page-builder'

export default function EditPage() {
  const handleSave = async (page) => {
    // Save page logic
  }

  const handlePublish = async (page) => {
    // Publish page logic
  }

  return (
    <PageBuilder
      pageId="123"
      onSave={handleSave}
      onPublish={handlePublish}
    />
  )
}
```

### Custom Block Types

The system supports custom block types through the BlockService:

```tsx
import { BlockService } from '@/lib/services/block.service'

// Register a custom block type
BlockService.registerBlockType({
  id: 'custom-block',
  label: 'Custom Block',
  category: 'custom',
  schema: {
    title: { type: 'string', label: 'Title' },
    content: { type: 'richtext', label: 'Content' }
  },
  defaultData: {
    title: 'Default Title',
    content: '<p>Default content</p>'
  }
})
```

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+S` | Save page |
| `Ctrl+Z` | Undo |
| `Ctrl+Shift+Z` | Redo |
| `Ctrl+P` | Toggle preview |
| `Ctrl+D` | Duplicate selected block |
| `Delete` | Delete selected block |
| `Ctrl+↑` | Move block up |
| `Ctrl+↓` | Move block down |
| `Ctrl+1-4` | Quick add blocks |
| `?` | Show shortcuts help |

## API Integration

### Required Endpoints

The page builder expects these API endpoints to be available:

- `GET /api/pages/[id]` - Fetch page data
- `PUT /api/pages/[slug]` - Update page metadata
- `PUT /api/pages/[slug]/blocks` - Update page blocks
- `POST /api/pages` - Create new page

### Data Structure

```typescript
interface Page {
  id: string
  title: string
  slug: string
  template: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  blocks: Block[]
  // ... other page properties
}

interface Block {
  id: string
  type: string
  data: Record<string, any>
  order: number
}
```

## State Management

The page builder uses the `usePageBuilder` hook for state management:

```tsx
const {
  page,
  blocks,
  loading,
  saving,
  isDirty,
  addBlock,
  updateBlock,
  deleteBlock,
  reorderBlocks,
  savePage,
  undo,
  redo
} = usePageBuilder(pageId, initialPage)
```

## Styling

The page builder uses Tailwind CSS and shadcn/ui components. All components are fully customizable through CSS classes and theme variables.

## Performance Considerations

- **Lazy Loading**: Components are loaded on demand
- **Debounced Updates**: Auto-save and real-time updates are debounced
- **Optimized Rendering**: Only changed blocks are re-rendered
- **Memory Management**: Proper cleanup of event listeners and timers

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Accessibility

- Full keyboard navigation support
- Screen reader compatible
- ARIA labels and descriptions
- High contrast mode support
- Focus management

## Contributing

When adding new block types or features:

1. Follow the existing TypeScript patterns
2. Add proper error handling
3. Include accessibility features
4. Update documentation
5. Add tests for new functionality

## Troubleshooting

### Common Issues

**Auto-save not working**
- Check network connectivity
- Verify API endpoints are responding
- Check browser console for errors

**Blocks not rendering**
- Ensure block types are properly registered
- Check block data structure
- Verify template compatibility

**Performance issues**
- Reduce number of blocks per page
- Optimize images and media
- Check for memory leaks in custom components
