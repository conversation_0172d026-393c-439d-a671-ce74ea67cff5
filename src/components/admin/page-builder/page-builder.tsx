'use client'

import { useState, useEffect, useCallback } from 'react'
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent, closestCenter } from '@dnd-kit/core'
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { 
  Save, 
  Eye, 
  Settings, 
  Plus, 
  Undo, 
  Redo, 
  Monitor, 
  Tablet, 
  Smartphone,
  Layout,
  Loader2
} from 'lucide-react'
import { BlockLibrary } from './block-library'
import { BlockEditor } from './block-editor'
import { BlockConfigPanel } from './block-config-panel'
import { PagePreview } from './page-preview'
import { PageSettingsPanel } from './page-settings-panel'
import { AutoSaveIndicator } from './auto-save-indicator'
import { KeyboardShortcuts, useKeyboardShortcuts } from './keyboard-shortcuts'
import { usePageBuilder } from './hooks/use-page-builder'
import { BlockService, BlockType } from '@/lib/services/block.service'
import { useToast } from '@/hooks/use-toast'

interface PageBuilderProps {
  pageId?: string
  initialPage?: any
  onSave?: (page: any) => void
  onPublish?: (page: any) => void
  onPreview?: (page: any) => void
}

export function PageBuilder({
  pageId,
  initialPage,
  onSave,
  onPublish,
  onPreview
}: PageBuilderProps) {
  const { toast } = useToast()
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [showPreview, setShowPreview] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [showShortcuts, setShowShortcuts] = useState(false)
  const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null)
  const [draggedBlock, setDraggedBlock] = useState<any>(null)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  const {
    page,
    blocks,
    history,
    loading,
    saving,
    error,
    isDirty,
    addBlock,
    updateBlock,
    deleteBlock,
    reorderBlocks,
    updatePage,
    savePage,
    undo,
    redo,
    canUndo,
    canRedo
  } = usePageBuilder(pageId, initialPage)

  // Auto-save functionality
  useEffect(() => {
    if (isDirty && !saving) {
      const timer = setTimeout(() => {
        savePage()
      }, 2000) // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timer)
    }
  }, [isDirty, saving, savePage])

  // Keyboard shortcuts
  useKeyboardShortcuts({
    onSave: () => savePage(),
    onUndo: () => undo(),
    onRedo: () => redo(),
    onPreview: () => setShowPreview(!showPreview),
    onDuplicate: () => selectedBlockId && handleBlockDuplicate(selectedBlockId),
    onDelete: () => selectedBlockId && handleBlockDelete(selectedBlockId),
    onAddText: () => addBlock('text'),
    onAddImage: () => addBlock('image'),
    onAddHero: () => addBlock('hero'),
    onAddCTA: () => addBlock('cta'),
    onShowShortcuts: () => setShowShortcuts(true)
  })

  // Update last saved time
  useEffect(() => {
    if (!saving && !isDirty) {
      setLastSaved(new Date())
    }
  }, [saving, isDirty])

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    const block = blocks.find(b => b.id === active.id) || active.data.current
    setDraggedBlock(block)
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    setDraggedBlock(null)

    if (!over) return

    // Handle adding new block from library
    if (active.data.current?.isNewBlock) {
      const blockType = active.data.current.blockType
      const overIndex = blocks.findIndex(b => b.id === over.id)
      const insertIndex = overIndex >= 0 ? overIndex + 1 : blocks.length

      addBlock(blockType, insertIndex)
      return
    }

    // Handle reordering existing blocks
    if (active.id !== over.id) {
      const oldIndex = blocks.findIndex(b => b.id === active.id)
      const newIndex = blocks.findIndex(b => b.id === over.id)
      
      if (oldIndex !== -1 && newIndex !== -1) {
        reorderBlocks(oldIndex, newIndex)
      }
    }
  }

  const handleBlockSelect = (blockId: string) => {
    setSelectedBlockId(blockId)
  }

  const handleBlockUpdate = (blockId: string, data: any) => {
    updateBlock(blockId, data)
  }

  const handleBlockDelete = (blockId: string) => {
    deleteBlock(blockId)
    if (selectedBlockId === blockId) {
      setSelectedBlockId(null)
    }
  }

  const handleBlockDuplicate = (blockId: string) => {
    const block = blocks.find(b => b.id === blockId)
    if (block) {
      const index = blocks.findIndex(b => b.id === blockId)
      addBlock(block.type, index + 1, block.data)
    }
  }

  const handleSave = async () => {
    try {
      await savePage()
      onSave?.(page)
      toast({
        title: 'Page saved',
        description: 'Your changes have been saved successfully.',
      })
    } catch (error) {
      toast({
        title: 'Save failed',
        description: 'Failed to save your changes. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handlePublish = async () => {
    try {
      await updatePage({ status: 'PUBLISHED' })
      await savePage()
      onPublish?.(page)
      toast({
        title: 'Page published',
        description: 'Your page is now live and visible to visitors.',
      })
    } catch (error) {
      toast({
        title: 'Publish failed',
        description: 'Failed to publish your page. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handlePreviewClick = () => {
    setShowPreview(!showPreview)
    onPreview?.(page)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p className="text-muted-foreground">Loading page builder...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Card className="max-w-md">
          <CardHeader>
            <CardTitle className="text-red-600">Error Loading Page</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header Toolbar */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between px-6 py-3">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Layout className="h-5 w-5" />
              <h1 className="font-semibold">
                {page?.title || 'New Page'}
              </h1>
              <AutoSaveIndicator
                isDirty={isDirty}
                saving={saving}
                lastSaved={lastSaved || undefined}
                error={error}
                onManualSave={handleSave}
              />
            </div>
            
            <Separator orientation="vertical" className="h-6" />
            
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={undo}
                disabled={!canUndo}
              >
                <Undo className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={redo}
                disabled={!canRedo}
              >
                <Redo className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Responsive Preview Controls */}
            <div className="flex items-center space-x-1 border rounded-md p-1">
              <Button
                variant={viewMode === 'desktop' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('desktop')}
              >
                <Monitor className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'tablet' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('tablet')}
              >
                <Tablet className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'mobile' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('mobile')}
              >
                <Smartphone className="h-4 w-4" />
              </Button>
            </div>

            <Separator orientation="vertical" className="h-6" />

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(true)}
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handlePreviewClick}
            >
              <Eye className="h-4 w-4 mr-2" />
              {showPreview ? 'Edit' : 'Preview'}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleSave}
              disabled={saving || !isDirty}
            >
              {saving ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save
            </Button>

            <Button
              size="sm"
              onClick={handlePublish}
              disabled={saving}
            >
              Publish
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        <DndContext
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          {/* Block Library Sidebar */}
          {!showPreview && (
            <div className="w-80 border-r bg-muted/30 overflow-y-auto">
              <BlockLibrary onBlockSelect={addBlock} />
            </div>
          )}

          {/* Editor/Preview Area */}
          <div className="flex-1 flex">
            {showPreview ? (
              <PagePreview
                page={page}
                blocks={blocks}
                viewMode={viewMode}
                className="flex-1"
              />
            ) : (
              <div className="flex-1 overflow-y-auto">
                <BlockEditor
                  blocks={blocks}
                  selectedBlockId={selectedBlockId}
                  onBlockSelect={handleBlockSelect}
                  onBlockUpdate={handleBlockUpdate}
                  onBlockDelete={handleBlockDelete}
                  onBlockDuplicate={handleBlockDuplicate}
                  viewMode={viewMode}
                />
              </div>
            )}

            {/* Configuration Panel */}
            {!showPreview && selectedBlockId && (
              <div className="w-80 border-l bg-background overflow-y-auto">
                <BlockConfigPanel
                  block={blocks.find(b => b.id === selectedBlockId)}
                  onUpdate={(data) => handleBlockUpdate(selectedBlockId, data)}
                  onClose={() => setSelectedBlockId(null)}
                />
              </div>
            )}
          </div>

          {/* Drag Overlay */}
          <DragOverlay>
            {draggedBlock && (
              <div className="bg-background border rounded-lg p-4 shadow-lg">
                <div className="font-medium">{draggedBlock.type}</div>
              </div>
            )}
          </DragOverlay>
        </DndContext>
      </div>

      {/* Page Settings Modal */}
      {showSettings && (
        <PageSettingsPanel
          page={page}
          onUpdate={updatePage}
          onClose={() => setShowSettings(false)}
        />
      )}

      {/* Keyboard Shortcuts Modal */}
      <KeyboardShortcuts
        open={showShortcuts}
        onOpenChange={setShowShortcuts}
      />
    </div>
  )
}
