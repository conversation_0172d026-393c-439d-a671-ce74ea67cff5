// Legacy exports (if they exist)
export * from './PageBuilderInterface'
export * from './PageHeader'
export * from './BlockProperties'
export * from './PageSettings'
export * from './PageHistory'

// New page builder components
export { PageBuilder } from './page-builder'
export { BlockLibrary } from './block-library'
export { BlockEditor } from './block-editor'
export { BlockConfigPanel } from './block-config-panel'
export { PagePreview } from './page-preview'
export { ResponsivePreview } from './responsive-preview'
export { PageSettingsPanel } from './page-settings-panel'
export { AutoSaveIndicator } from './auto-save-indicator'
export { KeyboardShortcuts, useKeyboardShortcuts } from './keyboard-shortcuts'

// Hooks
export { usePageBuilder } from './hooks/use-page-builder'

// Types
export type {
  PageBuilderProps,
  PageData,
  BlockData,
  BlockType,
  BlockSchema,
  FieldSchema,
  BlockLibraryProps,
  BlockEditorProps,
  BlockConfigPanelProps,
  PagePreviewProps,
  PageSettingsPanelProps,
  AutoSaveIndicatorProps,
  KeyboardShortcutsProps,
  ViewMode,
  PageBuilderState,
  HistoryEntry,
  UsePageBuilderReturn,
  Template,
  SEOData,
  SEOScore,
  AnalyticsData,
  PageBuilderConfig
} from './types'