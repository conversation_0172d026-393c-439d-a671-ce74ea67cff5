'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { 
  Settings, 
  FileText, 
  Search, 
  Globe, 
  Eye, 
  Calendar,
  User,
  Tag,
  Image,
  Save,
  X
} from 'lucide-react'
import { SEOAnalyzer } from '../seo/seo-analyzer'
import { getTemplateList } from '@/components/templates/template-registry'
import { useToast } from '@/hooks/use-toast'

interface PageSettingsPanelProps {
  page: any
  onUpdate: (updates: any) => void
  onClose: () => void
}

export function PageSettingsPanel({ page, onUpdate, onClose }: PageSettingsPanelProps) {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    template: 'default',
    status: 'DRAFT',
    seoTitle: '',
    seoDescription: '',
    featuredImage: '',
    publishedAt: null,
    ...page
  })
  const [hasChanges, setHasChanges] = useState(false)
  const [activeTab, setActiveTab] = useState('general')

  const templates = getTemplateList()

  useEffect(() => {
    setFormData({
      title: '',
      slug: '',
      excerpt: '',
      content: '',
      template: 'default',
      status: 'DRAFT',
      seoTitle: '',
      seoDescription: '',
      featuredImage: '',
      publishedAt: null,
      ...page
    })
    setHasChanges(false)
  }, [page])

  const handleFieldChange = (field: string, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value }
      
      // Auto-generate slug from title
      if (field === 'title' && (!prev.slug || prev.slug === generateSlug(prev.title))) {
        newData.slug = generateSlug(value)
      }
      
      // Auto-generate SEO title from title
      if (field === 'title' && (!prev.seoTitle || prev.seoTitle === prev.title)) {
        newData.seoTitle = value
      }

      return newData
    })
    setHasChanges(true)
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
  }

  const handleSave = () => {
    onUpdate(formData)
    setHasChanges(false)
    toast({
      title: 'Settings saved',
      description: 'Page settings have been updated successfully.',
    })
  }

  const handleReset = () => {
    setFormData({
      title: '',
      slug: '',
      excerpt: '',
      content: '',
      template: 'default',
      status: 'DRAFT',
      seoTitle: '',
      seoDescription: '',
      featuredImage: '',
      publishedAt: null,
      ...page
    })
    setHasChanges(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PUBLISHED':
        return 'bg-green-100 text-green-800'
      case 'DRAFT':
        return 'bg-yellow-100 text-yellow-800'
      case 'ARCHIVED':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Page Settings</span>
            {hasChanges && (
              <Badge variant="secondary" className="ml-2">
                Unsaved changes
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            Configure page metadata, SEO settings, and publishing options
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 overflow-hidden">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="general" className="flex items-center space-x-1">
              <FileText className="h-4 w-4" />
              <span>General</span>
            </TabsTrigger>
            <TabsTrigger value="seo" className="flex items-center space-x-1">
              <Search className="h-4 w-4" />
              <span>SEO</span>
            </TabsTrigger>
            <TabsTrigger value="design" className="flex items-center space-x-1">
              <Eye className="h-4 w-4" />
              <span>Design</span>
            </TabsTrigger>
            <TabsTrigger value="publishing" className="flex items-center space-x-1">
              <Globe className="h-4 w-4" />
              <span>Publishing</span>
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-y-auto mt-4">
            <TabsContent value="general" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                  <CardDescription>
                    Essential page details and content
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="title">Page Title *</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => handleFieldChange('title', e.target.value)}
                      placeholder="Enter page title"
                    />
                  </div>

                  <div>
                    <Label htmlFor="slug">URL Slug *</Label>
                    <Input
                      id="slug"
                      value={formData.slug}
                      onChange={(e) => handleFieldChange('slug', e.target.value)}
                      placeholder="page-url-slug"
                    />
                    <p className="text-sm text-muted-foreground mt-1">
                      URL: /{formData.slug}
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="excerpt">Excerpt</Label>
                    <Textarea
                      id="excerpt"
                      value={formData.excerpt}
                      onChange={(e) => handleFieldChange('excerpt', e.target.value)}
                      placeholder="Brief description of the page"
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Featured Image</CardTitle>
                  <CardDescription>
                    Main image for the page
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="featuredImage">Image URL</Label>
                      <Input
                        id="featuredImage"
                        value={formData.featuredImage}
                        onChange={(e) => handleFieldChange('featuredImage', e.target.value)}
                        placeholder="https://example.com/image.jpg"
                      />
                    </div>
                    
                    {formData.featuredImage && (
                      <div className="mt-4">
                        <img
                          src={formData.featuredImage}
                          alt="Featured image preview"
                          className="w-full max-w-sm h-32 object-cover rounded border"
                        />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="seo" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">SEO Settings</CardTitle>
                  <CardDescription>
                    Optimize your page for search engines
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="seoTitle">SEO Title</Label>
                    <Input
                      id="seoTitle"
                      value={formData.seoTitle}
                      onChange={(e) => handleFieldChange('seoTitle', e.target.value)}
                      placeholder="SEO optimized title"
                    />
                    <p className="text-sm text-muted-foreground mt-1">
                      {formData.seoTitle.length}/60 characters
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="seoDescription">SEO Description</Label>
                    <Textarea
                      id="seoDescription"
                      value={formData.seoDescription}
                      onChange={(e) => handleFieldChange('seoDescription', e.target.value)}
                      placeholder="SEO meta description"
                      rows={3}
                    />
                    <p className="text-sm text-muted-foreground mt-1">
                      {formData.seoDescription.length}/160 characters
                    </p>
                  </div>
                </CardContent>
              </Card>

              <SEOAnalyzer
                title={formData.seoTitle || formData.title}
                description={formData.seoDescription || formData.excerpt}
                url={`/${formData.slug}`}
                image={formData.featuredImage}
                type="website"
              />
            </TabsContent>

            <TabsContent value="design" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Template & Design</CardTitle>
                  <CardDescription>
                    Choose how your page should be displayed
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="template">Page Template</Label>
                    <Select
                      value={formData.template}
                      onValueChange={(value) => handleFieldChange('template', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select template" />
                      </SelectTrigger>
                      <SelectContent>
                        {templates.map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            <div className="flex items-center space-x-2">
                              <span>{template.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {template.category}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    {formData.template && (
                      <div className="mt-2 p-3 bg-muted rounded-md">
                        <p className="text-sm">
                          {templates.find(t => t.id === formData.template)?.description}
                        </p>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {templates.find(t => t.id === formData.template)?.features.map((feature) => (
                            <Badge key={feature} variant="secondary" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="publishing" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Publishing Settings</CardTitle>
                  <CardDescription>
                    Control when and how your page is published
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="status">Publication Status</Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value) => handleFieldChange('status', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="DRAFT">
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                            <span>Draft</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="PUBLISHED">
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span>Published</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="ARCHIVED">
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                            <span>Archived</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="p-4 bg-muted rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">Current Status</h4>
                        <p className="text-sm text-muted-foreground">
                          {formData.status === 'PUBLISHED' 
                            ? 'This page is live and visible to visitors'
                            : formData.status === 'DRAFT'
                            ? 'This page is not yet published'
                            : 'This page is archived and not visible'
                          }
                        </p>
                      </div>
                      <Badge className={getStatusColor(formData.status)}>
                        {formData.status}
                      </Badge>
                    </div>
                  </div>

                  {page?.publishedAt && (
                    <div>
                      <Label>Published Date</Label>
                      <div className="flex items-center space-x-2 mt-1">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          {new Date(page.publishedAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  )}

                  {page?.author && (
                    <div>
                      <Label>Author</Label>
                      <div className="flex items-center space-x-2 mt-1">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{page.author.name}</span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>

        <div className="flex items-center justify-between pt-4 border-t">
          <Button variant="outline" onClick={handleReset} disabled={!hasChanges}>
            Reset Changes
          </Button>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={onClose}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={!hasChanges}>
              <Save className="h-4 w-4 mr-2" />
              Save Settings
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
