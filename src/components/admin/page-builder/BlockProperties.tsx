'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { RichTextEditor } from '@/components/admin/ui/rich-text-editor'
import { ImageUpload } from '@/components/admin'
import {
  Palette,
  Type,
  Layout,
  Settings,
  Image as ImageIcon,
  Link,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify
} from 'lucide-react'

interface BlockPropertiesProps {
  block: any
  onChange: (data: any) => void
}

export function BlockProperties({ block, onChange }: BlockPropertiesProps) {
  const [activeTab, setActiveTab] = useState<'content' | 'style' | 'advanced'>('content')

  if (!block) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-4">
        <Settings className="h-8 w-8 text-muted-foreground mb-2" />
        <p className="text-muted-foreground">
          Select a block to edit its properties
        </p>
      </div>
    )
  }

  // Render different property editors based on block type
  const renderContentProperties = () => {
    switch (block.type) {
      case 'text':
        return (
          <div className="space-y-4">
            <div>
              <Label>Text Content</Label>
              <RichTextEditor
                content={block.content?.text || ''}
                onChange={(value) => onChange({ content: { ...block.content, text: value } })}
              />
            </div>
          </div>
        )
        
      case 'heading':
        return (
          <div className="space-y-4">
            <div>
              <Label>Heading Text</Label>
              <Input
                value={block.content?.text || ''}
                onChange={(e) => onChange({ content: { ...block.content, text: e.target.value } })}
                placeholder="Enter heading text..."
              />
            </div>
            <div>
              <Label>Heading Level</Label>
              <Select
                value={block.content?.level?.toString() || '2'}
                onValueChange={(value) => onChange({ content: { ...block.content, level: parseInt(value) } })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select heading level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">H1 - Main Heading</SelectItem>
                  <SelectItem value="2">H2 - Section Heading</SelectItem>
                  <SelectItem value="3">H3 - Subsection Heading</SelectItem>
                  <SelectItem value="4">H4 - Minor Heading</SelectItem>
                  <SelectItem value="5">H5 - Small Heading</SelectItem>
                  <SelectItem value="6">H6 - Tiny Heading</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Label>Add ID for anchor links</Label>
              <Switch
                checked={!!block.content?.id}
                onCheckedChange={(checked) => onChange({ 
                  content: { 
                    ...block.content, 
                    id: checked ? (block.content?.id || block.content?.text?.toLowerCase().replace(/\s+/g, '-') || '') : undefined 
                  } 
                })}
              />
            </div>
            {block.content?.id && (
              <div>
                <Label>ID</Label>
                <Input
                  value={block.content.id}
                  onChange={(e) => onChange({ content: { ...block.content, id: e.target.value } })}
                  placeholder="heading-id"
                />
              </div>
            )}
          </div>
        )
        
      case 'image':
        return (
          <div className="space-y-4">
            <div>
              <Label>Image</Label>
              <ImageUpload
                value={block.content?.src || ''}
                onChange={(url) => onChange({ content: { ...block.content, src: url } })}
              />
            </div>
            <div>
              <Label>Alt Text</Label>
              <Input
                value={block.content?.alt || ''}
                onChange={(e) => onChange({ content: { ...block.content, alt: e.target.value } })}
                placeholder="Image description for accessibility..."
              />
            </div>
            <div>
              <Label>Caption</Label>
              <Input
                value={block.content?.caption || ''}
                onChange={(e) => onChange({ content: { ...block.content, caption: e.target.value } })}
                placeholder="Optional image caption..."
              />
            </div>
            <div>
              <Label>Alignment</Label>
              <div className="flex space-x-1 mt-1.5">
                <Button
                  type="button"
                  size="sm"
                  variant={block.content?.alignment === 'left' ? 'default' : 'outline'}
                  onClick={() => onChange({ content: { ...block.content, alignment: 'left' } })}
                >
                  <AlignLeft className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  size="sm"
                  variant={block.content?.alignment === 'center' ? 'default' : 'outline'}
                  onClick={() => onChange({ content: { ...block.content, alignment: 'center' } })}
                >
                  <AlignCenter className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  size="sm"
                  variant={block.content?.alignment === 'right' ? 'default' : 'outline'}
                  onClick={() => onChange({ content: { ...block.content, alignment: 'right' } })}
                >
                  <AlignRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div>
              <Label>Link (Optional)</Label>
              <Input
                value={block.content?.link || ''}
                onChange={(e) => onChange({ content: { ...block.content, link: e.target.value } })}
                placeholder="https://..."
              />
            </div>
          </div>
        )
        
      case 'quote':
        return (
          <div className="space-y-4">
            <div>
              <Label>Quote Text</Label>
              <Textarea
                value={block.content?.text || ''}
                onChange={(e) => onChange({ content: { ...block.content, text: e.target.value } })}
                placeholder="Enter quote text..."
                rows={4}
              />
            </div>
            <div>
              <Label>Author</Label>
              <Input
                value={block.content?.author || ''}
                onChange={(e) => onChange({ content: { ...block.content, author: e.target.value } })}
                placeholder="Quote author..."
              />
            </div>
            <div>
              <Label>Source</Label>
              <Input
                value={block.content?.source || ''}
                onChange={(e) => onChange({ content: { ...block.content, source: e.target.value } })}
                placeholder="Source publication or context..."
              />
            </div>
          </div>
        )
        
      // Add more block type editors as needed
      
      default:
        return (
          <div className="space-y-4">
            <p className="text-muted-foreground text-sm">
              Properties for {block.type} blocks are not yet implemented.
            </p>
          </div>
        )
    }
  }

  const renderStyleProperties = () => {
    return (
      <div className="space-y-4">
        <div>
          <Label>Margin</Label>
          <div className="grid grid-cols-2 gap-4 mt-1.5">
            <div>
              <Label className="text-xs">Top</Label>
              <Select
                value={block.styles?.marginTop || '0'}
                onValueChange={(value) => onChange({ styles: { ...block.styles, marginTop: value } })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Top margin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">None</SelectItem>
                  <SelectItem value="0.5rem">Extra Small</SelectItem>
                  <SelectItem value="1rem">Small</SelectItem>
                  <SelectItem value="1.5rem">Medium</SelectItem>
                  <SelectItem value="2rem">Large</SelectItem>
                  <SelectItem value="3rem">Extra Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Bottom</Label>
              <Select
                value={block.styles?.marginBottom || '0'}
                onValueChange={(value) => onChange({ styles: { ...block.styles, marginBottom: value } })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Bottom margin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">None</SelectItem>
                  <SelectItem value="0.5rem">Extra Small</SelectItem>
                  <SelectItem value="1rem">Small</SelectItem>
                  <SelectItem value="1.5rem">Medium</SelectItem>
                  <SelectItem value="2rem">Large</SelectItem>
                  <SelectItem value="3rem">Extra Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        
        <div>
          <Label>Padding</Label>
          <div className="grid grid-cols-2 gap-4 mt-1.5">
            <div>
              <Label className="text-xs">Horizontal</Label>
              <Select
                value={block.styles?.paddingX || '0'}
                onValueChange={(value) => onChange({ styles: { ...block.styles, paddingX: value } })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Horizontal padding" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">None</SelectItem>
                  <SelectItem value="0.5rem">Extra Small</SelectItem>
                  <SelectItem value="1rem">Small</SelectItem>
                  <SelectItem value="1.5rem">Medium</SelectItem>
                  <SelectItem value="2rem">Large</SelectItem>
                  <SelectItem value="3rem">Extra Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Vertical</Label>
              <Select
                value={block.styles?.paddingY || '0'}
                onValueChange={(value) => onChange({ styles: { ...block.styles, paddingY: value } })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Vertical padding" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">None</SelectItem>
                  <SelectItem value="0.5rem">Extra Small</SelectItem>
                  <SelectItem value="1rem">Small</SelectItem>
                  <SelectItem value="1.5rem">Medium</SelectItem>
                  <SelectItem value="2rem">Large</SelectItem>
                  <SelectItem value="3rem">Extra Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        
        <Separator />
        
        <div>
          <Label>Background</Label>
          <div className="grid grid-cols-2 gap-4 mt-1.5">
            <div>
              <Label className="text-xs">Color</Label>
              <Select
                value={block.styles?.backgroundColor || 'transparent'}
                onValueChange={(value) => onChange({ styles: { ...block.styles, backgroundColor: value } })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Background color" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="transparent">None</SelectItem>
                  <SelectItem value="#f8fafc">Light Gray</SelectItem>
                  <SelectItem value="#f1f5f9">Gray</SelectItem>
                  <SelectItem value="#e2e8f0">Dark Gray</SelectItem>
                  <SelectItem value="#dbeafe">Light Blue</SelectItem>
                  <SelectItem value="#bfdbfe">Blue</SelectItem>
                  <SelectItem value="#dcfce7">Light Green</SelectItem>
                  <SelectItem value="#bbf7d0">Green</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Rounded</Label>
              <Select
                value={block.styles?.borderRadius || '0'}
                onValueChange={(value) => onChange({ styles: { ...block.styles, borderRadius: value } })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Border radius" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">None</SelectItem>
                  <SelectItem value="0.25rem">Small</SelectItem>
                  <SelectItem value="0.5rem">Medium</SelectItem>
                  <SelectItem value="0.75rem">Large</SelectItem>
                  <SelectItem value="1rem">Extra Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        
        <div>
          <Label>Border</Label>
          <div className="grid grid-cols-2 gap-4 mt-1.5">
            <div>
              <Label className="text-xs">Style</Label>
              <Select
                value={block.styles?.borderStyle || 'none'}
                onValueChange={(value) => onChange({ styles: { ...block.styles, borderStyle: value } })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Border style" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="solid">Solid</SelectItem>
                  <SelectItem value="dashed">Dashed</SelectItem>
                  <SelectItem value="dotted">Dotted</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Width</Label>
              <Select
                value={block.styles?.borderWidth || '1px'}
                onValueChange={(value) => onChange({ styles: { ...block.styles, borderWidth: value } })}
                disabled={block.styles?.borderStyle === 'none' || !block.styles?.borderStyle}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Border width" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1px">Thin</SelectItem>
                  <SelectItem value="2px">Medium</SelectItem>
                  <SelectItem value="4px">Thick</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const renderAdvancedProperties = () => {
    return (
      <div className="space-y-4">
        <div>
          <Label>Custom CSS Class</Label>
          <Input
            value={block.styles?.className || ''}
            onChange={(e) => onChange({ styles: { ...block.styles, className: e.target.value } })}
            placeholder="custom-class-name"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Add custom CSS classes separated by spaces
          </p>
        </div>
        
        <div>
          <Label>HTML ID</Label>
          <Input
            value={block.styles?.id || ''}
            onChange={(e) => onChange({ styles: { ...block.styles, id: e.target.value } })}
            placeholder="custom-id"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Unique identifier for this block
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Switch
            checked={!!block.styles?.hidden}
            onCheckedChange={(checked) => onChange({ styles: { ...block.styles, hidden: checked } })}
            id="hidden-toggle"
          />
          <Label htmlFor="hidden-toggle">Hide on page</Label>
        </div>
        
        <div className="flex items-center space-x-2">
          <Switch
            checked={!!block.styles?.hideOnMobile}
            onCheckedChange={(checked) => onChange({ styles: { ...block.styles, hideOnMobile: checked } })}
            id="hide-mobile-toggle"
          />
          <Label htmlFor="hide-mobile-toggle">Hide on mobile devices</Label>
        </div>
        
        <div className="flex items-center space-x-2">
          <Switch
            checked={!!block.styles?.hideOnDesktop}
            onCheckedChange={(checked) => onChange({ styles: { ...block.styles, hideOnDesktop: checked } })}
            id="hide-desktop-toggle"
          />
          <Label htmlFor="hide-desktop-toggle">Hide on desktop devices</Label>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      <div className="mb-4">
        <h3 className="text-lg font-medium">
          {block.type.charAt(0).toUpperCase() + block.type.slice(1)} Block
        </h3>
        <p className="text-sm text-muted-foreground">
          Configure the properties for this block
        </p>
      </div>
      
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="flex-1 flex flex-col">
        <TabsList className="grid grid-cols-3">
          <TabsTrigger value="content">
            <Type className="h-4 w-4 mr-2" />
            Content
          </TabsTrigger>
          <TabsTrigger value="style">
            <Palette className="h-4 w-4 mr-2" />
            Style
          </TabsTrigger>
          <TabsTrigger value="advanced">
            <Settings className="h-4 w-4 mr-2" />
            Advanced
          </TabsTrigger>
        </TabsList>
        
        <ScrollArea className="flex-1 mt-4">
          <TabsContent value="content" className="m-0">
            {renderContentProperties()}
          </TabsContent>
          
          <TabsContent value="style" className="m-0">
            {renderStyleProperties()}
          </TabsContent>
          
          <TabsContent value="advanced" className="m-0">
            {renderAdvancedProperties()}
          </TabsContent>
        </ScrollArea>
      </Tabs>
    </div>
  )
}