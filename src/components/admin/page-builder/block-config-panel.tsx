'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  X, 
  Settings, 
  Image, 
  Type, 
  Palette, 
  Link,
  Plus,
  Trash2,
  Upload
} from 'lucide-react'
import { BlockService } from '@/lib/services/block.service'
import { cn } from '@/lib/utils'

interface BlockConfigPanelProps {
  block: any
  onUpdate: (data: any) => void
  onClose: () => void
}

interface FieldProps {
  field: any
  value: any
  onChange: (value: any) => void
  fieldKey: string
}

function TextField({ field, value, onChange }: FieldProps) {
  return (
    <div className="space-y-2">
      <Label>{field.label}</Label>
      <Input
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
      />
      {field.description && (
        <p className="text-xs text-muted-foreground">{field.description}</p>
      )}
    </div>
  )
}

function TextareaField({ field, value, onChange }: FieldProps) {
  return (
    <div className="space-y-2">
      <Label>{field.label}</Label>
      <Textarea
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
        rows={field.rows || 4}
      />
      {field.description && (
        <p className="text-xs text-muted-foreground">{field.description}</p>
      )}
    </div>
  )
}

function RichTextareaField({ field, value, onChange }: FieldProps) {
  return (
    <div className="space-y-2">
      <Label>{field.label}</Label>
      <Textarea
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
        rows={6}
        className="font-mono text-sm"
      />
      <p className="text-xs text-muted-foreground">
        HTML supported. {field.description}
      </p>
    </div>
  )
}

function SelectField({ field, value, onChange }: FieldProps) {
  return (
    <div className="space-y-2">
      <Label>{field.label}</Label>
      <Select value={value || field.default} onValueChange={onChange}>
        <SelectTrigger>
          <SelectValue placeholder={`Select ${field.label.toLowerCase()}`} />
        </SelectTrigger>
        <SelectContent>
          {field.options.map((option: any) => (
            <SelectItem key={option} value={option}>
              {typeof option === 'string' ? option : option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {field.description && (
        <p className="text-xs text-muted-foreground">{field.description}</p>
      )}
    </div>
  )
}

function BooleanField({ field, value, onChange }: FieldProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="space-y-0.5">
        <Label>{field.label}</Label>
        {field.description && (
          <p className="text-xs text-muted-foreground">{field.description}</p>
        )}
      </div>
      <Switch
        checked={value || field.default || false}
        onCheckedChange={onChange}
      />
    </div>
  )
}

function NumberField({ field, value, onChange }: FieldProps) {
  return (
    <div className="space-y-2">
      <Label>{field.label}</Label>
      <Input
        type="number"
        value={value || field.default || ''}
        onChange={(e) => onChange(Number(e.target.value))}
        min={field.min}
        max={field.max}
        step={field.step || 1}
      />
      {field.description && (
        <p className="text-xs text-muted-foreground">{field.description}</p>
      )}
    </div>
  )
}

function ImageField({ field, value, onChange }: FieldProps) {
  return (
    <div className="space-y-2">
      <Label>{field.label}</Label>
      <div className="space-y-2">
        <Input
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder="Enter image URL or upload"
        />
        <Button variant="outline" size="sm" className="w-full">
          <Upload className="h-4 w-4 mr-2" />
          Upload Image
        </Button>
        {value && (
          <div className="mt-2">
            <img
              src={value}
              alt="Preview"
              className="w-full h-32 object-cover rounded border"
            />
          </div>
        )}
      </div>
      {field.description && (
        <p className="text-xs text-muted-foreground">{field.description}</p>
      )}
    </div>
  )
}

function ArrayField({ field, value, onChange, fieldKey }: FieldProps) {
  const items = value || []

  const addItem = () => {
    const newItem = field.items ? 
      Object.keys(field.items).reduce((acc: any, key) => {
        acc[key] = field.items[key].default || ''
        return acc
      }, {}) : ''
    
    onChange([...items, newItem])
  }

  const updateItem = (index: number, newValue: any) => {
    const newItems = [...items]
    newItems[index] = newValue
    onChange(newItems)
  }

  const removeItem = (index: number) => {
    const newItems = items.filter((_: any, i: number) => i !== index)
    onChange(newItems)
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label>{field.label}</Label>
        <Button variant="outline" size="sm" onClick={addItem}>
          <Plus className="h-3 w-3 mr-1" />
          Add
        </Button>
      </div>
      
      <div className="space-y-3">
        {items.map((item: any, index: number) => (
          <Card key={index} className="p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Item {index + 1}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeItem(index)}
                className="h-6 w-6 p-0 text-red-600"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
            
            {field.items ? (
              <div className="space-y-3">
                {Object.entries(field.items).map(([key, subField]: [string, any]) => (
                  <FieldRenderer
                    key={key}
                    field={subField}
                    value={item[key]}
                    onChange={(newValue) => {
                      const newItem = { ...item, [key]: newValue }
                      updateItem(index, newItem)
                    }}
                    fieldKey={`${fieldKey}.${index}.${key}`}
                  />
                ))}
              </div>
            ) : (
              <Input
                value={item}
                onChange={(e) => updateItem(index, e.target.value)}
                placeholder={`Enter ${field.label.toLowerCase()} item`}
              />
            )}
          </Card>
        ))}
      </div>
      
      {field.description && (
        <p className="text-xs text-muted-foreground">{field.description}</p>
      )}
    </div>
  )
}

function FieldRenderer({ field, value, onChange, fieldKey }: FieldProps) {
  switch (field.type) {
    case 'string':
      return <TextField field={field} value={value} onChange={onChange} fieldKey={fieldKey} />
    case 'textarea':
      return <TextareaField field={field} value={value} onChange={onChange} fieldKey={fieldKey} />
    case 'richtext':
      return <RichTextareaField field={field} value={value} onChange={onChange} fieldKey={fieldKey} />
    case 'select':
      return <SelectField field={field} value={value} onChange={onChange} fieldKey={fieldKey} />
    case 'boolean':
      return <BooleanField field={field} value={value} onChange={onChange} fieldKey={fieldKey} />
    case 'number':
      return <NumberField field={field} value={value} onChange={onChange} fieldKey={fieldKey} />
    case 'image':
      return <ImageField field={field} value={value} onChange={onChange} fieldKey={fieldKey} />
    case 'array':
      return <ArrayField field={field} value={value} onChange={onChange} fieldKey={fieldKey} />
    default:
      return <TextField field={field} value={value} onChange={onChange} fieldKey={fieldKey} />
  }
}

export function BlockConfigPanel({ block, onUpdate, onClose }: BlockConfigPanelProps) {
  const [data, setData] = useState(block?.data || {})
  const [hasChanges, setHasChanges] = useState(false)

  const blockType = BlockService.getBlockType(block?.type)

  useEffect(() => {
    setData(block?.data || {})
    setHasChanges(false)
  }, [block])

  const handleFieldChange = (fieldKey: string, value: any) => {
    const newData = { ...data, [fieldKey]: value }
    setData(newData)
    setHasChanges(true)
    
    // Auto-update with debounce
    const timer = setTimeout(() => {
      onUpdate(newData)
      setHasChanges(false)
    }, 500)

    return () => clearTimeout(timer)
  }

  const handleSave = () => {
    onUpdate(data)
    setHasChanges(false)
  }

  const handleReset = () => {
    setData(block?.data || {})
    setHasChanges(false)
  }

  if (!block || !blockType) {
    return (
      <div className="h-full flex items-center justify-center p-6">
        <div className="text-center">
          <Settings className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">
            Select a block to configure its properties
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold">{blockType.label}</h3>
            <p className="text-sm text-muted-foreground">{blockType.description}</p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex items-center space-x-2 mt-2">
          <Badge variant="outline">{blockType.category}</Badge>
          {hasChanges && (
            <Badge variant="secondary">Unsaved changes</Badge>
          )}
        </div>
      </div>

      {/* Configuration Form */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-6">
          {Object.entries(blockType.schema).map(([fieldKey, field]: [string, any]) => (
            <FieldRenderer
              key={fieldKey}
              field={field}
              value={data[fieldKey]}
              onChange={(value) => handleFieldChange(fieldKey, value)}
              fieldKey={fieldKey}
            />
          ))}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-4 border-t">
        <div className="flex items-center justify-between">
          <Button variant="outline" onClick={handleReset} disabled={!hasChanges}>
            Reset
          </Button>
          <Button onClick={handleSave} disabled={!hasChanges}>
            Apply Changes
          </Button>
        </div>
      </div>
    </div>
  )
}
