'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { useToast } from '@/hooks/use-toast'
import { BlockService } from '@/lib/services/block.service'

interface PageBuilderState {
  page: any
  blocks: any[]
  history: any[]
  historyIndex: number
  loading: boolean
  saving: boolean
  error: string | null
  isDirty: boolean
}

interface HistoryEntry {
  page: any
  blocks: any[]
  timestamp: number
  action: string
}

export function usePageBuilder(pageId?: string, initialPage?: any) {
  const { toast } = useToast()
  const [state, setState] = useState<PageBuilderState>({
    page: initialPage || null,
    blocks: [],
    history: [],
    historyIndex: -1,
    loading: !!pageId && !initialPage,
    saving: false,
    error: null,
    isDirty: false
  })

  const saveTimeoutRef = useRef<NodeJS.Timeout>(null)

  // Load page data
  useEffect(() => {
    if (pageId && !initialPage) {
      loadPage(pageId)
    } else if (initialPage) {
      setState(prev => ({
        ...prev,
        page: initialPage,
        blocks: initialPage.blocks || [],
        loading: false
      }))
      addToHistory(initialPage, initialPage.blocks || [], 'initial')
    }
  }, [pageId, initialPage])

  const loadPage = async (id: string) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))

      const response = await fetch(`/api/pages/${id}`)
      if (!response.ok) {
        throw new Error('Failed to load page')
      }

      const data = await response.json()
      setState(prev => ({
        ...prev,
        page: data.page,
        blocks: data.page.blocks || [],
        loading: false
      }))

      addToHistory(data.page, data.page.blocks || [], 'load')
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load page'
      }))
    }
  }

  const addToHistory = useCallback((page: any, blocks: any[], action: string) => {
    setState(prev => {
      const newHistory = prev.history.slice(0, prev.historyIndex + 1)
      newHistory.push({
        page: JSON.parse(JSON.stringify(page)),
        blocks: JSON.parse(JSON.stringify(blocks)),
        timestamp: Date.now(),
        action
      })

      // Limit history to 50 entries
      if (newHistory.length > 50) {
        newHistory.shift()
      }

      return {
        ...prev,
        history: newHistory,
        historyIndex: newHistory.length - 1
      }
    })
  }, [])

  const updateState = useCallback((updates: Partial<PageBuilderState>, action: string) => {
    setState(prev => {
      const newState = { ...prev, ...updates, isDirty: true }
      
      if (updates.page || updates.blocks) {
        addToHistory(
          updates.page || prev.page,
          updates.blocks || prev.blocks,
          action
        )
      }

      return newState
    })
  }, [addToHistory])

  const addBlock = useCallback((blockType: string, index?: number, data?: any) => {
    const blockTypeConfig = BlockService.getBlockType(blockType)
    if (!blockTypeConfig) {
      toast({
        title: 'Error',
        description: 'Invalid block type',
        variant: 'destructive'
      })
      return
    }

    const newBlock = {
      id: `block_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: blockType,
      data: data || blockTypeConfig.defaultData,
      order: index !== undefined ? index : state.blocks.length
    }

    setState(prev => {
      const newBlocks = [...prev.blocks]
      if (index !== undefined) {
        newBlocks.splice(index, 0, newBlock)
        // Update order for subsequent blocks
        newBlocks.forEach((block, i) => {
          block.order = i
        })
      } else {
        newBlocks.push(newBlock)
      }

      const newState = {
        ...prev,
        blocks: newBlocks,
        isDirty: true
      }

      addToHistory(prev.page, newBlocks, `add_${blockType}`)
      return newState
    })
  }, [state.blocks, toast, addToHistory])

  const updateBlock = useCallback((blockId: string, data: any) => {
    setState(prev => {
      const newBlocks = prev.blocks.map(block =>
        block.id === blockId ? { ...block, data: { ...block.data, ...data } } : block
      )

      const newState = {
        ...prev,
        blocks: newBlocks,
        isDirty: true
      }

      addToHistory(prev.page, newBlocks, 'update_block')
      return newState
    })
  }, [addToHistory])

  const deleteBlock = useCallback((blockId: string) => {
    setState(prev => {
      const newBlocks = prev.blocks
        .filter(block => block.id !== blockId)
        .map((block, index) => ({ ...block, order: index }))

      const newState = {
        ...prev,
        blocks: newBlocks,
        isDirty: true
      }

      addToHistory(prev.page, newBlocks, 'delete_block')
      return newState
    })
  }, [addToHistory])

  const reorderBlocks = useCallback((fromIndex: number, toIndex: number) => {
    setState(prev => {
      const newBlocks = [...prev.blocks]
      const [movedBlock] = newBlocks.splice(fromIndex, 1)
      newBlocks.splice(toIndex, 0, movedBlock)

      // Update order property
      newBlocks.forEach((block, index) => {
        block.order = index
      })

      const newState = {
        ...prev,
        blocks: newBlocks,
        isDirty: true
      }

      addToHistory(prev.page, newBlocks, 'reorder_blocks')
      return newState
    })
  }, [addToHistory])

  const updatePage = useCallback((updates: any) => {
    setState(prev => {
      const newPage = { ...prev.page, ...updates }
      const newState = {
        ...prev,
        page: newPage,
        isDirty: true
      }

      addToHistory(newPage, prev.blocks, 'update_page')
      return newState
    })
  }, [addToHistory])

  const savePage = useCallback(async () => {
    if (!state.page || state.saving) return

    try {
      setState(prev => ({ ...prev, saving: true }))

      // Save page metadata
      const pageResponse = await fetch(`/api/pages/${state.page.slug}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(state.page)
      })

      if (!pageResponse.ok) {
        throw new Error('Failed to save page')
      }

      // Save blocks
      const blocksResponse = await fetch(`/api/pages/${state.page.slug}/blocks`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ blocks: state.blocks })
      })

      if (!blocksResponse.ok) {
        throw new Error('Failed to save blocks')
      }

      setState(prev => ({
        ...prev,
        saving: false,
        isDirty: false
      }))

      toast({
        title: 'Saved',
        description: 'Page saved successfully'
      })
    } catch (error) {
      setState(prev => ({ ...prev, saving: false }))
      toast({
        title: 'Save failed',
        description: error instanceof Error ? error.message : 'Failed to save page',
        variant: 'destructive'
      })
      throw error
    }
  }, [state.page, state.blocks, state.saving, toast])

  const undo = useCallback(() => {
    if (state.historyIndex > 0) {
      setState(prev => {
        const newIndex = prev.historyIndex - 1
        const historyEntry = prev.history[newIndex]
        
        return {
          ...prev,
          page: historyEntry.page,
          blocks: historyEntry.blocks,
          historyIndex: newIndex,
          isDirty: newIndex !== 0
        }
      })
    }
  }, [state.historyIndex])

  const redo = useCallback(() => {
    if (state.historyIndex < state.history.length - 1) {
      setState(prev => {
        const newIndex = prev.historyIndex + 1
        const historyEntry = prev.history[newIndex]
        
        return {
          ...prev,
          page: historyEntry.page,
          blocks: historyEntry.blocks,
          historyIndex: newIndex,
          isDirty: true
        }
      })
    }
  }, [state.historyIndex, state.history.length])

  // Auto-save functionality
  useEffect(() => {
    if (state.isDirty && !state.saving) {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current)
      }

      saveTimeoutRef.current = setTimeout(() => {
        savePage().catch(() => {
          // Auto-save failed, but don't show error toast
          console.warn('Auto-save failed')
        })
      }, 3000) // Auto-save after 3 seconds of inactivity
    }

    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current)
      }
    }
  }, [state.isDirty, state.saving, savePage])

  return {
    page: state.page,
    blocks: state.blocks,
    history: state.history,
    loading: state.loading,
    saving: state.saving,
    error: state.error,
    isDirty: state.isDirty,
    canUndo: state.historyIndex > 0,
    canRedo: state.historyIndex < state.history.length - 1,
    addBlock,
    updateBlock,
    deleteBlock,
    reorderBlocks,
    updatePage,
    savePage,
    undo,
    redo
  }
}
