// Page Builder Types

export interface PageBuilderProps {
  pageId?: string
  initialPage?: PageData
  onSave?: (page: PageData) => void
  onPublish?: (page: PageData) => void
  onPreview?: (page: PageData) => void
}

export interface PageData {
  id: string
  title: string
  slug: string
  template: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  content?: string
  excerpt?: string
  seoTitle?: string
  seoDescription?: string
  featuredImage?: string
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  author: {
    id: string
    name: string
    image?: string
  }
  blocks: BlockData[]
}

export interface BlockData {
  id: string
  type: string
  data: Record<string, any>
  order: number
}

export interface BlockType {
  id: string
  label: string
  description: string
  category: string
  icon?: React.ReactNode
  schema: BlockSchema
  defaultData: Record<string, any>
  preview?: React.ComponentType<{ data: any }>
}

export interface BlockSchema {
  [key: string]: FieldSchema
}

export interface FieldSchema {
  type: 'string' | 'textarea' | 'richtext' | 'number' | 'boolean' | 'select' | 'image' | 'array'
  label: string
  description?: string
  placeholder?: string
  default?: any
  required?: boolean
  min?: number
  max?: number
  step?: number
  options?: string[] | { value: string; label: string }[]
  rows?: number
  items?: BlockSchema // For array fields
}

export interface BlockLibraryProps {
  onBlockSelect: (blockType: string, index?: number, data?: any) => void
}

export interface BlockEditorProps {
  blocks: BlockData[]
  selectedBlockId: string | null
  onBlockSelect: (blockId: string) => void
  onBlockUpdate: (blockId: string, data: any) => void
  onBlockDelete: (blockId: string) => void
  onBlockDuplicate: (blockId: string) => void
  viewMode: ViewMode
}

export interface BlockConfigPanelProps {
  block?: BlockData
  onUpdate: (data: any) => void
  onClose: () => void
}

export interface PagePreviewProps {
  page: PageData
  blocks: BlockData[]
  viewMode: ViewMode
  className?: string
}

export interface PageSettingsPanelProps {
  page: PageData
  onUpdate: (updates: Partial<PageData>) => void
  onClose: () => void
}

export interface AutoSaveIndicatorProps {
  isDirty: boolean
  saving: boolean
  lastSaved?: Date
  error?: string | null
  onManualSave?: () => void
  className?: string
}

export interface KeyboardShortcutsProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export type ViewMode = 'desktop' | 'tablet' | 'mobile'

export interface PageBuilderState {
  page: PageData | null
  blocks: BlockData[]
  history: HistoryEntry[]
  historyIndex: number
  loading: boolean
  saving: boolean
  error: string | null
  isDirty: boolean
}

export interface HistoryEntry {
  page: PageData
  blocks: BlockData[]
  timestamp: number
  action: string
}

export interface UsePageBuilderReturn {
  page: PageData | null
  blocks: BlockData[]
  history: HistoryEntry[]
  loading: boolean
  saving: boolean
  error: string | null
  isDirty: boolean
  canUndo: boolean
  canRedo: boolean
  addBlock: (blockType: string, index?: number, data?: any) => void
  updateBlock: (blockId: string, data: any) => void
  deleteBlock: (blockId: string) => void
  reorderBlocks: (fromIndex: number, toIndex: number) => void
  updatePage: (updates: Partial<PageData>) => void
  savePage: () => Promise<void>
  undo: () => void
  redo: () => void
}

export interface DragItem {
  id: string
  type: string
  isNewBlock?: boolean
  blockType?: string
}

export interface DropResult {
  draggedId: string
  targetId: string
  position: 'before' | 'after'
}

// Template Types
export interface Template {
  id: string
  name: string
  description: string
  category: string
  features: string[]
  preview?: string
  component: React.ComponentType<{ page: PageData; isPreview?: boolean }>
}

// SEO Types
export interface SEOData {
  title: string
  description: string
  url: string
  image?: string
  type: string
  siteName?: string
  locale?: string
}

export interface SEOScore {
  score: number
  maxScore: number
  issues: SEOIssue[]
  suggestions: string[]
}

export interface SEOIssue {
  type: 'error' | 'warning' | 'info'
  message: string
  field?: string
}

// Analytics Types
export interface AnalyticsData {
  pageViews: number
  uniqueViews: number
  avgTimeOnPage: number
  bounceRate: number
  conversionRate?: number
}

// Error Types
export interface PageBuilderError {
  code: string
  message: string
  details?: any
}

// Event Types
export interface BlockEvent {
  type: 'add' | 'update' | 'delete' | 'reorder'
  blockId: string
  data?: any
  timestamp: number
}

export interface PageEvent {
  type: 'save' | 'publish' | 'preview' | 'settings'
  data?: any
  timestamp: number
}

// Configuration Types
export interface PageBuilderConfig {
  autoSave: boolean
  autoSaveInterval: number
  maxHistoryEntries: number
  enableKeyboardShortcuts: boolean
  enableAnalytics: boolean
  defaultTemplate: string
  allowedBlockTypes: string[]
  customBlockTypes: BlockType[]
}

// API Types
export interface CreatePageRequest {
  title: string
  slug: string
  template: string
  excerpt?: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  content?: string
  blocks?: BlockData[]
}

export interface UpdatePageRequest {
  title?: string
  slug?: string
  template?: string
  excerpt?: string
  status?: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  content?: string
  seoTitle?: string
  seoDescription?: string
  featuredImage?: string
}

export interface UpdateBlocksRequest {
  blocks: BlockData[]
}

export interface PageResponse {
  page: PageData
  relatedPages?: PageData[]
}

export interface PagesResponse {
  pages: PageData[]
  total: number
  page: number
  limit: number
}
