'use client'

import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { DatePicker } from '@/components/ui/date-picker'
import { Checkbox } from '@/components/ui/checkbox'

interface PageSettingsProps {
  settings: {
    allowComments?: boolean
    showAuthor?: boolean
    showDate?: boolean
    showShare?: boolean
    showRelated?: boolean
    showTOC?: boolean
    showBreadcrumbs?: boolean
    featuredInSidebar?: boolean
    featuredOnHomepage?: boolean
    [key: string]: any
  }
  onChange: (settings: any) => void
}

export function PageSettings({ settings, onChange }: PageSettingsProps) {
  const handleChange = (key: string, value: any) => {
    onChange({ ...settings, [key]: value })
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Display Settings</CardTitle>
          <CardDescription>
            Configure how the page content is displayed
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="show-author">Show Author</Label>
              <p className="text-sm text-muted-foreground">
                Display the author information on the page
              </p>
            </div>
            <Switch
              id="show-author"
              checked={settings.showAuthor ?? true}
              onCheckedChange={(checked) => handleChange('showAuthor', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="show-date">Show Date</Label>
              <p className="text-sm text-muted-foreground">
                Display the publication date on the page
              </p>
            </div>
            <Switch
              id="show-date"
              checked={settings.showDate ?? true}
              onCheckedChange={(checked) => handleChange('showDate', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="show-share">Show Share Buttons</Label>
              <p className="text-sm text-muted-foreground">
                Display social sharing buttons on the page
              </p>
            </div>
            <Switch
              id="show-share"
              checked={settings.showShare ?? true}
              onCheckedChange={(checked) => handleChange('showShare', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="show-toc">Show Table of Contents</Label>
              <p className="text-sm text-muted-foreground">
                Display a table of contents for the page
              </p>
            </div>
            <Switch
              id="show-toc"
              checked={settings.showTOC ?? false}
              onCheckedChange={(checked) => handleChange('showTOC', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="show-breadcrumbs">Show Breadcrumbs</Label>
              <p className="text-sm text-muted-foreground">
                Display breadcrumb navigation on the page
              </p>
            </div>
            <Switch
              id="show-breadcrumbs"
              checked={settings.showBreadcrumbs ?? false}
              onCheckedChange={(checked) => handleChange('showBreadcrumbs', checked)}
            />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Interaction Settings</CardTitle>
          <CardDescription>
            Configure how users can interact with the page
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="allow-comments">Allow Comments</Label>
              <p className="text-sm text-muted-foreground">
                Enable commenting on this page
              </p>
            </div>
            <Switch
              id="allow-comments"
              checked={settings.allowComments ?? true}
              onCheckedChange={(checked) => handleChange('allowComments', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="show-related">Show Related Content</Label>
              <p className="text-sm text-muted-foreground">
                Display related pages at the bottom
              </p>
            </div>
            <Switch
              id="show-related"
              checked={settings.showRelated ?? true}
              onCheckedChange={(checked) => handleChange('showRelated', checked)}
            />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Featured Content</CardTitle>
          <CardDescription>
            Configure where this page appears as featured content
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="featured-sidebar">Featured in Sidebar</Label>
              <p className="text-sm text-muted-foreground">
                Show this page in the sidebar featured content
              </p>
            </div>
            <Switch
              id="featured-sidebar"
              checked={settings.featuredInSidebar ?? false}
              onCheckedChange={(checked) => handleChange('featuredInSidebar', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="featured-homepage">Featured on Homepage</Label>
              <p className="text-sm text-muted-foreground">
                Show this page in the homepage featured content
              </p>
            </div>
            <Switch
              id="featured-homepage"
              checked={settings.featuredOnHomepage ?? false}
              onCheckedChange={(checked) => handleChange('featuredOnHomepage', checked)}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}