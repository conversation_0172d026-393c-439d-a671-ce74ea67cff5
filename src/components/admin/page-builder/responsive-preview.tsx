'use client'

import { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  Monitor, 
  Tablet, 
  Smartphone, 
  Grid,
  Maximize2,
  ExternalLink
} from 'lucide-react'
import { PagePreview } from './page-preview'
import { cn } from '@/lib/utils'

interface ResponsivePreviewProps {
  page: any
  blocks: any[]
  className?: string
}

interface DevicePreviewProps {
  page: any
  blocks: any[]
  device: 'desktop' | 'tablet' | 'mobile'
  size?: 'small' | 'medium' | 'large'
}

function DevicePreview({ page, blocks, device, size = 'medium' }: DevicePreviewProps) {
  const getDeviceStyles = () => {
    const baseStyles = "bg-white border rounded-lg overflow-hidden shadow-sm"
    
    switch (device) {
      case 'desktop':
        return {
          className: cn(baseStyles, {
            'w-full h-48': size === 'small',
            'w-full h-64': size === 'medium',
            'w-full h-80': size === 'large'
          }),
          width: '100%'
        }
      case 'tablet':
        return {
          className: cn(baseStyles, {
            'w-32 h-40': size === 'small',
            'w-40 h-52': size === 'medium',
            'w-48 h-64': size === 'large'
          }),
          width: '768px'
        }
      case 'mobile':
        return {
          className: cn(baseStyles, {
            'w-20 h-36': size === 'small',
            'w-24 h-44': size === 'medium',
            'w-32 h-56': size === 'large'
          }),
          width: '375px'
        }
    }
  }

  const deviceStyles = getDeviceStyles()
  
  const getDeviceIcon = () => {
    switch (device) {
      case 'tablet':
        return <Tablet className="h-3 w-3" />
      case 'mobile':
        return <Smartphone className="h-3 w-3" />
      default:
        return <Monitor className="h-3 w-3" />
    }
  }

  const getDeviceLabel = () => {
    switch (device) {
      case 'tablet':
        return 'Tablet'
      case 'mobile':
        return 'Mobile'
      default:
        return 'Desktop'
    }
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-1 text-xs text-muted-foreground">
          {getDeviceIcon()}
          <span>{getDeviceLabel()}</span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={() => {
            // Open in new tab with specific viewport
            if (page?.slug) {
              window.open(`/${page.slug}?preview=true&device=${device}`, '_blank')
            }
          }}
        >
          <ExternalLink className="h-3 w-3" />
        </Button>
      </div>
      
      <div className={deviceStyles.className}>
        <div className="h-full overflow-hidden">
          <PagePreview
            page={page}
            blocks={blocks}
            viewMode={device}
            className="h-full"
          />
        </div>
      </div>
    </div>
  )
}

export function ResponsivePreview({ page, blocks, className }: ResponsivePreviewProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'tabs'>('grid')
  const [selectedDevice, setSelectedDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')

  return (
    <div className={cn("h-full flex flex-col", className)}>
      {/* Header */}
      <div className="border-b bg-background px-4 py-3">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold">Responsive Preview</h3>
            <p className="text-sm text-muted-foreground">
              See how your page looks across different devices
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4 mr-1" />
              Grid
            </Button>
            <Button
              variant={viewMode === 'tabs' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('tabs')}
            >
              <Maximize2 className="h-4 w-4 mr-1" />
              Focus
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4">
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
            {/* Desktop Preview */}
            <div className="lg:col-span-2">
              <DevicePreview
                page={page}
                blocks={blocks}
                device="desktop"
                size="large"
              />
            </div>
            
            {/* Mobile and Tablet Previews */}
            <div className="space-y-6">
              <DevicePreview
                page={page}
                blocks={blocks}
                device="tablet"
                size="medium"
              />
              
              <DevicePreview
                page={page}
                blocks={blocks}
                device="mobile"
                size="medium"
              />
            </div>
          </div>
        ) : (
          <Tabs value={selectedDevice} onValueChange={(value: any) => setSelectedDevice(value)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="desktop" className="flex items-center space-x-2">
                <Monitor className="h-4 w-4" />
                <span>Desktop</span>
              </TabsTrigger>
              <TabsTrigger value="tablet" className="flex items-center space-x-2">
                <Tablet className="h-4 w-4" />
                <span>Tablet</span>
              </TabsTrigger>
              <TabsTrigger value="mobile" className="flex items-center space-x-2">
                <Smartphone className="h-4 w-4" />
                <span>Mobile</span>
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="desktop" className="mt-4 h-[calc(100%-3rem)]">
              <PagePreview
                page={page}
                blocks={blocks}
                viewMode="desktop"
                className="h-full"
              />
            </TabsContent>
            
            <TabsContent value="tablet" className="mt-4 h-[calc(100%-3rem)]">
              <PagePreview
                page={page}
                blocks={blocks}
                viewMode="tablet"
                className="h-full"
              />
            </TabsContent>
            
            <TabsContent value="mobile" className="mt-4 h-[calc(100%-3rem)]">
              <PagePreview
                page={page}
                blocks={blocks}
                viewMode="mobile"
                className="h-full"
              />
            </TabsContent>
          </Tabs>
        )}
      </div>

      {/* Footer Stats */}
      <div className="border-t bg-background px-4 py-2">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            <span>Total Blocks: {blocks.length}</span>
            <span>Template: {page?.template || 'default'}</span>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <Monitor className="h-3 w-3" />
              <span>1920px+</span>
            </div>
            <div className="flex items-center space-x-1">
              <Tablet className="h-3 w-3" />
              <span>768px</span>
            </div>
            <div className="flex items-center space-x-1">
              <Smartphone className="h-3 w-3" />
              <span>375px</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
