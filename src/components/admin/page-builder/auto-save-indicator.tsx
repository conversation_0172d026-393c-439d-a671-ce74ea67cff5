'use client'

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Save, 
  Check, 
  AlertCircle, 
  Loader2,
  Clock,
  Wifi,
  WifiOff
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface AutoSaveIndicatorProps {
  isDirty: boolean
  saving: boolean
  lastSaved?: Date
  error?: string | null
  onManualSave?: () => void
  className?: string
}

export function AutoSaveIndicator({
  isDirty,
  saving,
  lastSaved,
  error,
  onManualSave,
  className
}: AutoSaveIndicatorProps) {
  const [isOnline, setIsOnline] = useState(true)
  const [timeAgo, setTimeAgo] = useState('')

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Update time ago
  useEffect(() => {
    if (!lastSaved) return

    const updateTimeAgo = () => {
      const now = new Date()
      const diff = now.getTime() - lastSaved.getTime()
      const seconds = Math.floor(diff / 1000)
      const minutes = Math.floor(seconds / 60)
      const hours = Math.floor(minutes / 60)

      if (seconds < 60) {
        setTimeAgo('just now')
      } else if (minutes < 60) {
        setTimeAgo(`${minutes}m ago`)
      } else if (hours < 24) {
        setTimeAgo(`${hours}h ago`)
      } else {
        setTimeAgo(lastSaved.toLocaleDateString())
      }
    }

    updateTimeAgo()
    const interval = setInterval(updateTimeAgo, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [lastSaved])

  const getStatus = () => {
    if (!isOnline) {
      return {
        icon: <WifiOff className="h-3 w-3" />,
        text: 'Offline',
        variant: 'destructive' as const,
        description: 'Changes will be saved when connection is restored'
      }
    }

    if (error) {
      return {
        icon: <AlertCircle className="h-3 w-3" />,
        text: 'Save failed',
        variant: 'destructive' as const,
        description: error
      }
    }

    if (saving) {
      return {
        icon: <Loader2 className="h-3 w-3 animate-spin" />,
        text: 'Saving...',
        variant: 'secondary' as const,
        description: 'Your changes are being saved'
      }
    }

    if (isDirty) {
      return {
        icon: <Clock className="h-3 w-3" />,
        text: 'Unsaved changes',
        variant: 'secondary' as const,
        description: 'Changes will be auto-saved shortly'
      }
    }

    return {
      icon: <Check className="h-3 w-3" />,
      text: 'Saved',
      variant: 'default' as const,
      description: lastSaved ? `Last saved ${timeAgo}` : 'All changes saved'
    }
  }

  const status = getStatus()

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <Badge variant={status.variant} className="flex items-center space-x-1">
        {status.icon}
        <span className="text-xs">{status.text}</span>
      </Badge>

      {lastSaved && (
        <span className="text-xs text-muted-foreground">
          {timeAgo}
        </span>
      )}

      {(error || (!isOnline && isDirty)) && onManualSave && (
        <Button
          variant="outline"
          size="sm"
          onClick={onManualSave}
          disabled={saving || !isOnline}
          className="h-6 px-2 text-xs"
        >
          <Save className="h-3 w-3 mr-1" />
          Retry
        </Button>
      )}
    </div>
  )
}
