'use client'

import { useState } from 'react'
import { useDraggable } from '@dnd-kit/core'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Search, 
  Plus,
  LayoutDashboard,
  Type,
  Image,
  Images,
  Play,
  MousePointer,
  Grid,
  Quote,
  Mail,
  Palette,
  Zap
} from 'lucide-react'
import { BlockService, BlockType } from '@/lib/services/block.service'

interface BlockLibraryProps {
  onBlockSelect: (blockType: string, index?: number, data?: any) => void
}

interface DraggableBlockProps {
  blockType: BlockType
  onSelect: (blockType: string) => void
}

function DraggableBlock({ blockType, onSelect }: DraggableBlockProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `new-${blockType.id}`,
    data: {
      isNewBlock: true,
      blockType: blockType.id,
    },
  })

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined

  const getBlockIcon = (blockId: string) => {
    switch (blockId) {
      case 'hero':
        return <LayoutDashboard className="h-5 w-5" />
      case 'text':
        return <Type className="h-5 w-5" />
      case 'image':
        return <Image className="h-5 w-5" />
      case 'gallery':
        return <Images className="h-5 w-5" />
      case 'video':
        return <Play className="h-5 w-5" />
      case 'cta':
        return <MousePointer className="h-5 w-5" />
      case 'features':
        return <Grid className="h-5 w-5" />
      case 'testimonial':
        return <Quote className="h-5 w-5" />
      case 'contact':
        return <Mail className="h-5 w-5" />
      default:
        return <Palette className="h-5 w-5" />
    }
  }

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={`cursor-grab hover:shadow-md transition-shadow ${
        isDragging ? 'opacity-50' : ''
      }`}
      {...listeners}
      {...attributes}
    >
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <div className="p-2 bg-primary/10 rounded-md text-primary">
            {getBlockIcon(blockType.id)}
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm">{blockType.label}</h4>
            <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
              {blockType.description}
            </p>
            <Badge variant="outline" className="mt-2 text-xs">
              {blockType.category}
            </Badge>
          </div>
        </div>
        <Button
          size="sm"
          variant="ghost"
          className="w-full mt-3"
          onClick={() => onSelect(blockType.id)}
        >
          <Plus className="h-3 w-3 mr-1" />
          Add Block
        </Button>
      </CardContent>
    </Card>
  )
}

export function BlockLibrary({ onBlockSelect }: BlockLibraryProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  const blockTypes = BlockService.getBlockTypes()
  const blocksByCategory = BlockService.getBlockTypesByCategory()

  // Filter blocks based on search and category
  const filteredBlocks = blockTypes.filter(block => {
    const matchesSearch = block.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         block.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || block.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const categories = [
    { id: 'all', label: 'All Blocks', icon: <Palette className="h-4 w-4" /> },
    { id: 'layout', label: 'Layout', icon: <LayoutDashboard className="h-4 w-4" /> },
    { id: 'content', label: 'Content', icon: <Type className="h-4 w-4" /> },
    { id: 'media', label: 'Media', icon: <Image className="h-4 w-4" /> },
    { id: 'marketing', label: 'Marketing', icon: <Zap className="h-4 w-4" /> },
    { id: 'forms', label: 'Forms', icon: <Mail className="h-4 w-4" /> },
  ]

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b">
        <h2 className="font-semibold mb-3">Block Library</h2>
        
        {/* Search */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search blocks..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
          <TabsList className="grid w-full grid-cols-2 h-auto">
            {categories.slice(0, 2).map((category) => (
              <TabsTrigger
                key={category.id}
                value={category.id}
                className="flex items-center space-x-1 text-xs"
              >
                {category.icon}
                <span>{category.label}</span>
              </TabsTrigger>
            ))}
          </TabsList>
          
          <div className="mt-2 grid grid-cols-2 gap-1">
            {categories.slice(2).map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="justify-start text-xs h-8"
              >
                {category.icon}
                <span className="ml-1">{category.label}</span>
              </Button>
            ))}
          </div>
        </Tabs>
      </div>

      {/* Block List */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-3">
          {filteredBlocks.length > 0 ? (
            filteredBlocks.map((blockType) => (
              <DraggableBlock
                key={blockType.id}
                blockType={blockType}
                onSelect={onBlockSelect}
              />
            ))
          ) : (
            <div className="text-center py-8">
              <div className="text-muted-foreground mb-2">No blocks found</div>
              <p className="text-sm text-muted-foreground">
                Try adjusting your search or category filter
              </p>
            </div>
          )}
        </div>

        {/* Quick Add Section */}
        {searchQuery === '' && selectedCategory === 'all' && (
          <div className="mt-8 pt-6 border-t">
            <h3 className="font-medium text-sm mb-3">Quick Add</h3>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBlockSelect('text')}
                className="justify-start"
              >
                <Type className="h-3 w-3 mr-2" />
                Text
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBlockSelect('image')}
                className="justify-start"
              >
                <Image className="h-3 w-3 mr-2" />
                Image
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBlockSelect('hero')}
                className="justify-start"
              >
                <LayoutDashboard className="h-3 w-3 mr-2" />
                Hero
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBlockSelect('cta')}
                className="justify-start"
              >
                <MousePointer className="h-3 w-3 mr-2" />
                CTA
              </Button>
            </div>
          </div>
        )}

        {/* Tips Section */}
        <div className="mt-8 pt-6 border-t">
          <h3 className="font-medium text-sm mb-3">Tips</h3>
          <div className="space-y-2 text-xs text-muted-foreground">
            <p>• Drag blocks to add them to your page</p>
            <p>• Click the + button for quick insertion</p>
            <p>• Use Ctrl+Z to undo changes</p>
            <p>• Auto-save keeps your work safe</p>
          </div>
        </div>
      </div>
    </div>
  )
}
