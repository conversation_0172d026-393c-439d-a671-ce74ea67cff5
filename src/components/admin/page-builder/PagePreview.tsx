'use client'

import { Card, CardContent } from '@/components/ui/card'
import { PageRenderer } from '@/components/pages'

interface PagePreviewProps {
  pageData: any
}

export function PagePreview({ pageData }: PagePreviewProps) {
  if (!pageData) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-muted-foreground">No page data to preview</p>
      </div>
    )
  }

  // If we have a PageRenderer component, use it
  if (typeof PageRenderer === 'function') {
    return (
      <div className="max-w-5xl mx-auto p-6">
        <PageRenderer
          page={pageData}
          template={pageData.template}
          showComments={pageData.settings?.allowComments}
          showRelated={pageData.settings?.showRelated}
          showTOC={pageData.settings?.showTOC}
          showBreadcrumbs={pageData.settings?.showBreadcrumbs}
          showShare={pageData.settings?.showShare}
          showAuthor={pageData.settings?.showAuthor}
          showMeta={pageData.settings?.showDate}
          isPreview={true}
        />
      </div>
    )
  }

  // Fallback preview if <PERSON><PERSON><PERSON><PERSON> is not available
  return (
    <div className="max-w-5xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{pageData.title || 'Untitled Page'}</h1>
        {pageData.excerpt && (
          <p className="text-lg text-muted-foreground">{pageData.excerpt}</p>
        )}
      </div>

      {pageData.featuredImage && (
        <div className="mb-8">
          <img 
            src={pageData.featuredImage} 
            alt={pageData.title} 
            className="w-full h-auto rounded-lg"
          />
        </div>
      )}

      {pageData.blocks && pageData.blocks.length > 0 ? (
        <div className="space-y-6">
          {pageData.blocks.map((block: any) => (
            <Card key={block.id} className="overflow-hidden">
              <CardContent className="p-4">
                {block.type === 'text' && (
                  <div dangerouslySetInnerHTML={{ __html: block.content?.text || 'Text content' }} />
                )}
                {block.type === 'heading' && (
                  <div>
                    {block.content?.level === 1 && <h1 className="text-3xl font-bold">{block.content.text}</h1>}
                    {block.content?.level === 2 && <h2 className="text-2xl font-bold">{block.content.text}</h2>}
                    {block.content?.level === 3 && <h3 className="text-xl font-bold">{block.content.text}</h3>}
                    {block.content?.level === 4 && <h4 className="text-lg font-bold">{block.content.text}</h4>}
                    {block.content?.level === 5 && <h5 className="text-base font-bold">{block.content.text}</h5>}
                    {block.content?.level === 6 && <h6 className="text-sm font-bold">{block.content.text}</h6>}
                  </div>
                )}
                {block.type === 'image' && (
                  <div className={`flex justify-${block.content?.alignment || 'center'}`}>
                    <figure>
                      <img 
                        src={block.content?.src} 
                        alt={block.content?.alt || ''} 
                        className="max-w-full h-auto"
                      />
                      {block.content?.caption && (
                        <figcaption className="text-sm text-muted-foreground mt-2 text-center">
                          {block.content.caption}
                        </figcaption>
                      )}
                    </figure>
                  </div>
                )}
                {block.type === 'quote' && (
                  <blockquote className="border-l-4 border-primary pl-4 italic">
                    <p className="text-lg">{block.content?.text}</p>
                    {(block.content?.author || block.content?.source) && (
                      <footer className="text-sm text-muted-foreground mt-2">
                        {block.content.author && <span>— {block.content.author}</span>}
                        {block.content.source && (
                          <span>{block.content.author ? `, ${block.content.source}` : block.content.source}</span>
                        )}
                      </footer>
                    )}
                  </blockquote>
                )}
                {/* Add more block type renderers as needed */}
                {!['text', 'heading', 'image', 'quote'].includes(block.type) && (
                  <div className="p-4 border border-dashed border-muted-foreground rounded-md text-center">
                    <p className="text-muted-foreground">
                      {block.type.charAt(0).toUpperCase() + block.type.slice(1)} Block
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border border-dashed border-muted-foreground rounded-md">
          <p className="text-muted-foreground">No content blocks in this page</p>
        </div>
      )}
    </div>
  )
}