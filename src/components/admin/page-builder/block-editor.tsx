'use client'

import { useState } from 'react'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  GripVertical, 
  Settings, 
  Copy, 
  Trash2, 
  Eye, 
  EyeOff,
  ChevronUp,
  ChevronDown
} from 'lucide-react'
import { BlockRenderer } from '@/components/pages/blocks/block-renderer'
import { BlockService } from '@/lib/services/block.service'
import { cn } from '@/lib/utils'

interface BlockEditorProps {
  blocks: any[]
  selectedBlockId: string | null
  onBlockSelect: (blockId: string) => void
  onBlockUpdate: (blockId: string, data: any) => void
  onBlockDelete: (blockId: string) => void
  onBlockDuplicate: (blockId: string) => void
  viewMode: 'desktop' | 'tablet' | 'mobile'
}

interface SortableBlockProps {
  block: any
  index: number
  isSelected: boolean
  onSelect: () => void
  onUpdate: (data: any) => void
  onDelete: () => void
  onDuplicate: () => void
  onMoveUp: () => void
  onMoveDown: () => void
  canMoveUp: boolean
  canMoveDown: boolean
  viewMode: 'desktop' | 'tablet' | 'mobile'
}

function SortableBlock({
  block,
  index,
  isSelected,
  onSelect,
  onUpdate,
  onDelete,
  onDuplicate,
  onMoveUp,
  onMoveDown,
  canMoveUp,
  canMoveDown,
  viewMode
}: SortableBlockProps) {
  const [isVisible, setIsVisible] = useState(true)
  const [isHovered, setIsHovered] = useState(false)

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: block.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const blockType = BlockService.getBlockType(block.type)

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "group relative",
        isDragging && "opacity-50",
        isSelected && "ring-2 ring-primary ring-offset-2"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Block Controls Overlay */}
      {(isHovered || isSelected) && (
        <div className="absolute -top-10 left-0 right-0 z-10 flex items-center justify-between bg-background border rounded-t-md px-3 py-1">
          <div className="flex items-center space-x-2">
            <div
              className="cursor-grab hover:cursor-grabbing"
              {...attributes}
              {...listeners}
            >
              <GripVertical className="h-4 w-4 text-muted-foreground" />
            </div>
            <Badge variant="outline" className="text-xs">
              {blockType?.label || block.type}
            </Badge>
            <span className="text-xs text-muted-foreground">#{index + 1}</span>
          </div>

          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(!isVisible)}
              className="h-6 w-6 p-0"
            >
              {isVisible ? (
                <Eye className="h-3 w-3" />
              ) : (
                <EyeOff className="h-3 w-3" />
              )}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onMoveUp}
              disabled={!canMoveUp}
              className="h-6 w-6 p-0"
            >
              <ChevronUp className="h-3 w-3" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onMoveDown}
              disabled={!canMoveDown}
              className="h-6 w-6 p-0"
            >
              <ChevronDown className="h-3 w-3" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onSelect}
              className="h-6 w-6 p-0"
            >
              <Settings className="h-3 w-3" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onDuplicate}
              className="h-6 w-6 p-0"
            >
              <Copy className="h-3 w-3" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onDelete}
              className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}

      {/* Block Content */}
      <Card
        className={cn(
          "cursor-pointer transition-all",
          isSelected && "ring-2 ring-primary ring-offset-2",
          !isVisible && "opacity-50",
          isHovered && "shadow-md"
        )}
        onClick={onSelect}
      >
        <CardContent className="p-0">
          {isVisible ? (
            <div className={cn(
              "transition-all",
              viewMode === 'tablet' && "max-w-2xl mx-auto",
              viewMode === 'mobile' && "max-w-sm mx-auto"
            )}>
              <BlockRenderer
                block={block}
                isPreview={true}
                isEditing={true}
              />
            </div>
          ) : (
            <div className="p-8 text-center text-muted-foreground">
              <EyeOff className="h-8 w-8 mx-auto mb-2" />
              <p className="text-sm">Block hidden</p>
              <p className="text-xs">Click the eye icon to show</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export function BlockEditor({
  blocks,
  selectedBlockId,
  onBlockSelect,
  onBlockUpdate,
  onBlockDelete,
  onBlockDuplicate,
  viewMode
}: BlockEditorProps) {
  const handleMoveUp = (index: number) => {
    if (index > 0) {
      // This would be handled by the parent component's reorder function
      // For now, we'll just trigger a reorder event
      const event = new CustomEvent('blockReorder', {
        detail: { from: index, to: index - 1 }
      })
      window.dispatchEvent(event)
    }
  }

  const handleMoveDown = (index: number) => {
    if (index < blocks.length - 1) {
      const event = new CustomEvent('blockReorder', {
        detail: { from: index, to: index + 1 }
      })
      window.dispatchEvent(event)
    }
  }

  if (blocks.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center space-y-4 max-w-md">
          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
            <Settings className="h-8 w-8 text-muted-foreground" />
          </div>
          <div>
            <h3 className="font-medium mb-2">Start Building Your Page</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Drag blocks from the library on the left to start creating your page content.
            </p>
            <div className="space-y-2 text-xs text-muted-foreground">
              <p>• Add a Hero block to create an impactful header</p>
              <p>• Use Text blocks for your main content</p>
              <p>• Include Images and CTAs to engage visitors</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      "min-h-full",
      viewMode === 'tablet' && "px-8",
      viewMode === 'mobile' && "px-4"
    )}>
      <div className="max-w-none mx-auto py-8">
        <div className="space-y-6">
          {blocks.map((block, index) => (
            <SortableBlock
              key={block.id}
              block={block}
              index={index}
              isSelected={selectedBlockId === block.id}
              onSelect={() => onBlockSelect(block.id)}
              onUpdate={(data) => onBlockUpdate(block.id, data)}
              onDelete={() => onBlockDelete(block.id)}
              onDuplicate={() => onBlockDuplicate(block.id)}
              onMoveUp={() => handleMoveUp(index)}
              onMoveDown={() => handleMoveDown(index)}
              canMoveUp={index > 0}
              canMoveDown={index < blocks.length - 1}
              viewMode={viewMode}
            />
          ))}
        </div>

        {/* Add Block Prompt */}
        <div className="mt-8 p-8 border-2 border-dashed border-muted-foreground/25 rounded-lg text-center">
          <p className="text-sm text-muted-foreground">
            Drag a block here to add it to the end of your page
          </p>
        </div>
      </div>
    </div>
  )
}
