'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import {
  Search,
  Type,
  Image,
  FileText,
  Layout,
  Grid,
  List,
  Video,
  Music,
  Map,
  Table,
  BarChart,
  Code,
  Quote,
  Columns,
  Rows,
  FormInput,
  MessageSquare,
  Users,
  CreditCard,
  HelpCircle,
  Mail
} from 'lucide-react'

interface BlockLibraryProps {
  onAddBlock: (blockType: string) => void
}

interface BlockCategory {
  id: string
  name: string
  icon: React.ReactNode
  blocks: BlockType[]
}

interface BlockType {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  preview?: string
}

export function BlockLibrary({ onAddBlock }: BlockLibraryProps) {
  const [searchQuery, setSearchQuery] = useState('')

  const blockCategories: BlockCategory[] = [
    {
      id: 'text',
      name: 'Text',
      icon: <Type className="h-4 w-4" />,
      blocks: [
        {
          id: 'text',
          name: 'Text',
          description: 'Regular text content with formatting options',
          icon: <Type className="h-4 w-4" />,
        },
        {
          id: 'heading',
          name: 'Heading',
          description: 'Section heading with different levels',
          icon: <FileText className="h-4 w-4" />,
        },
        {
          id: 'quote',
          name: 'Quote',
          description: 'Blockquote for testimonials or citations',
          icon: <Quote className="h-4 w-4" />,
        },
      ]
    },
    {
      id: 'media',
      name: 'Media',
      icon: <Image className="h-4 w-4" />,
      blocks: [
        {
          id: 'image',
          name: 'Image',
          description: 'Single image with caption and alignment options',
          icon: <Image className="h-4 w-4" />,
        },
        {
          id: 'gallery',
          name: 'Gallery',
          description: 'Multiple images in a grid or carousel',
          icon: <Grid className="h-4 w-4" />,
        },
        {
          id: 'video',
          name: 'Video',
          description: 'Embedded video from YouTube, Vimeo, etc.',
          icon: <Video className="h-4 w-4" />,
        },
        {
          id: 'audio',
          name: 'Audio',
          description: 'Audio player with controls',
          icon: <Music className="h-4 w-4" />,
        },
      ]
    },
    {
      id: 'layout',
      name: 'Layout',
      icon: <Layout className="h-4 w-4" />,
      blocks: [
        {
          id: 'columns',
          name: 'Columns',
          description: 'Multi-column layout with responsive options',
          icon: <Columns className="h-4 w-4" />,
        },
        {
          id: 'container',
          name: 'Container',
          description: 'Content container with styling options',
          icon: <Layout className="h-4 w-4" />,
        },
        {
          id: 'divider',
          name: 'Divider',
          description: 'Horizontal separator with styling options',
          icon: <Rows className="h-4 w-4" />,
        },
        {
          id: 'spacer',
          name: 'Spacer',
          description: 'Adjustable vertical spacing',
          icon: <Rows className="h-4 w-4" />,
        },
      ]
    },
    {
      id: 'content',
      name: 'Content',
      icon: <FileText className="h-4 w-4" />,
      blocks: [
        {
          id: 'list',
          name: 'List',
          description: 'Ordered or unordered list',
          icon: <List className="h-4 w-4" />,
        },
        {
          id: 'table',
          name: 'Table',
          description: 'Data table with formatting options',
          icon: <Table className="h-4 w-4" />,
        },
        {
          id: 'code',
          name: 'Code',
          description: 'Code snippet with syntax highlighting',
          icon: <Code className="h-4 w-4" />,
        },
        {
          id: 'map',
          name: 'Map',
          description: 'Interactive map with location marker',
          icon: <Map className="h-4 w-4" />,
        },
        {
          id: 'chart',
          name: 'Chart',
          description: 'Data visualization chart',
          icon: <BarChart className="h-4 w-4" />,
        },
      ]
    },
    {
      id: 'interactive',
      name: 'Interactive',
      icon: <FormInput className="h-4 w-4" />,
      blocks: [
        {
          id: 'form',
          name: 'Form',
          description: 'Contact or subscription form',
          icon: <FormInput className="h-4 w-4" />,
        },
        {
          id: 'button',
          name: 'Button',
          description: 'Call-to-action button with styling options',
          icon: <FormInput className="h-4 w-4" />,
        },
        {
          id: 'accordion',
          name: 'Accordion',
          description: 'Collapsible content sections',
          icon: <List className="h-4 w-4" />,
        },
        {
          id: 'tabs',
          name: 'Tabs',
          description: 'Tabbed content interface',
          icon: <Layout className="h-4 w-4" />,
        },
      ]
    },
    {
      id: 'sections',
      name: 'Sections',
      icon: <Layout className="h-4 w-4" />,
      blocks: [
        {
          id: 'hero',
          name: 'Hero',
          description: 'Full-width hero section with background',
          icon: <Layout className="h-4 w-4" />,
        },
        {
          id: 'features',
          name: 'Features',
          description: 'Feature highlights with icons',
          icon: <Grid className="h-4 w-4" />,
        },
        {
          id: 'testimonial',
          name: 'Testimonial',
          description: 'Customer testimonial with avatar',
          icon: <MessageSquare className="h-4 w-4" />,
        },
        {
          id: 'team',
          name: 'Team',
          description: 'Team member profiles with photos',
          icon: <Users className="h-4 w-4" />,
        },
        {
          id: 'pricing',
          name: 'Pricing',
          description: 'Pricing table with features',
          icon: <CreditCard className="h-4 w-4" />,
        },
        {
          id: 'faq',
          name: 'FAQ',
          description: 'Frequently asked questions',
          icon: <HelpCircle className="h-4 w-4" />,
        },
        {
          id: 'contact',
          name: 'Contact',
          description: 'Contact information with icons',
          icon: <Mail className="h-4 w-4" />,
        },
      ]
    },
  ]

  // Filter blocks based on search query
  const filteredCategories = searchQuery
    ? blockCategories.map(category => ({
        ...category,
        blocks: category.blocks.filter(block =>
          block.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          block.description.toLowerCase().includes(searchQuery.toLowerCase())
        )
      })).filter(category => category.blocks.length > 0)
    : blockCategories

  // Flatten all blocks for "All" tab
  const allBlocks = blockCategories.flatMap(category => category.blocks)
  const filteredAllBlocks = searchQuery
    ? allBlocks.filter(block =>
        block.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        block.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : allBlocks

  return (
    <div className="flex flex-col h-full">
      <div className="mb-4 relative">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search blocks..."
          className="pl-8"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      
      <Tabs defaultValue="all" className="flex-1 flex flex-col">
        <TabsList className="grid grid-cols-4 h-9 mb-4">
          <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
          <TabsTrigger value="text" className="text-xs">Text</TabsTrigger>
          <TabsTrigger value="media" className="text-xs">Media</TabsTrigger>
          <TabsTrigger value="layout" className="text-xs">Layout</TabsTrigger>
        </TabsList>
        
        <ScrollArea className="flex-1">
          <TabsContent value="all" className="m-0">
            <div className="grid grid-cols-1 gap-2">
              {filteredAllBlocks.map((block) => (
                <BlockItem
                  key={block.id}
                  block={block}
                  onAdd={() => onAddBlock(block.id)}
                />
              ))}
              {filteredAllBlocks.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No blocks match your search
                </div>
              )}
            </div>
          </TabsContent>
          
          {blockCategories.map((category) => (
            <TabsContent key={category.id} value={category.id} className="m-0">
              <div className="grid grid-cols-1 gap-2">
                {category.blocks
                  .filter(block => 
                    !searchQuery || 
                    block.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    block.description.toLowerCase().includes(searchQuery.toLowerCase())
                  )
                  .map((block) => (
                    <BlockItem
                      key={block.id}
                      block={block}
                      onAdd={() => onAddBlock(block.id)}
                    />
                  ))}
                {category.blocks.filter(block => 
                  !searchQuery || 
                  block.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                  block.description.toLowerCase().includes(searchQuery.toLowerCase())
                ).length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No blocks match your search
                  </div>
                )}
              </div>
            </TabsContent>
          ))}
        </ScrollArea>
      </Tabs>
    </div>
  )
}

interface BlockItemProps {
  block: BlockType
  onAdd: () => void
}

function BlockItem({ block, onAdd }: BlockItemProps) {
  return (
    <Button
      variant="outline"
      className="h-auto py-3 px-4 justify-start text-left flex items-start gap-3 hover:border-primary"
      onClick={onAdd}
    >
      <div className="mt-0.5 bg-muted rounded-md p-1.5">
        {block.icon}
      </div>
      <div>
        <div className="font-medium">{block.name}</div>
        <div className="text-xs text-muted-foreground mt-1">{block.description}</div>
      </div>
    </Button>
  )
}