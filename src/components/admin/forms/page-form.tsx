'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Form, FormField } from '@/components/ui/form'
import { useToast } from '@/hooks/use-toast'
import { FormFieldWrapper, FormSection, FormGrid } from './shared/form-field-wrapper'
import { SlugInput } from './shared/slug-input'
import { DateTimePicker } from './shared/date-time-picker'
import { CreatePageSchema, UpdatePageSchema, type CreatePageInput, type UpdatePageInput } from '@/lib/schemas'
import { FileText, Save, Eye, Settings, Layout, Code } from 'lucide-react'
import { useState } from 'react'

interface PageFormProps {
  initialData?: Partial<UpdatePageInput>
  mode?: 'create' | 'edit'
  onSubmit?: (data: CreatePageInput | UpdatePageInput) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  templates?: Array<{ id: string; name: string; description?: string }>
  authors?: Array<{ id: string; name: string }>
}

const DEFAULT_TEMPLATES = [
  { id: 'default', name: 'Default', description: 'Standard page layout' },
  { id: 'landing', name: 'Landing Page', description: 'Marketing landing page' },
  { id: 'about', name: 'About Page', description: 'Company/personal about page' },
  { id: 'contact', name: 'Contact Page', description: 'Contact form and information' },
  { id: 'services', name: 'Services', description: 'Services showcase page' },
  { id: 'portfolio', name: 'Portfolio', description: 'Work showcase page' },
  { id: 'blank', name: 'Blank', description: 'Empty page with no default content' },
]

export function PageForm({
  initialData,
  mode = 'create',
  onSubmit,
  onCancel,
  isLoading = false,
  templates = DEFAULT_TEMPLATES,
  authors = [],
}: PageFormProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState('content')

  const form = useForm({
    resolver: zodResolver(mode === 'create' ? CreatePageSchema : UpdatePageSchema),
    defaultValues: {
      title: initialData?.title || '',
      slug: initialData?.slug || '',
      content: initialData?.content || '',
      template: initialData?.template || 'default',
      status: initialData?.status || 'DRAFT',
      publishedAt: initialData?.publishedAt,
      seoTitle: initialData?.seoTitle || '',
      seoDescription: initialData?.seoDescription || '',
      authorId: initialData?.authorId || '',
    },
  })

  const watchedTitle = form.watch('title')
  const watchedStatus = form.watch('status')
  const watchedTemplate = form.watch('template')

  const handleSubmit = async (data: any) => {
    try {
      if (onSubmit) {
        await onSubmit(data)
        toast({
          title: 'Success!',
          description: `Page ${mode === 'create' ? 'created' : 'updated'} successfully.`,
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to ${mode === 'create' ? 'create' : 'update'} page. Please try again.`,
        variant: 'destructive',
      })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PUBLISHED': return 'bg-green-100 text-green-800'
      case 'DRAFT': return 'bg-gray-100 text-gray-800'
      case 'ARCHIVED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTemplateDescription = (templateId: string) => {
    const template = templates.find(t => t.id === templateId)
    return template?.description || ''
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {mode === 'create' ? 'Create New Page' : 'Edit Page'}
          </CardTitle>
          <CardDescription>
            {mode === 'create' 
              ? 'Create a new page with custom content and layout.'
              : 'Update your page content and settings.'
            }
          </CardDescription>
        </CardHeader>
      </Card>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Page Content</CardTitle>
                </CardHeader>
                <CardContent>
                  <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="content">Content</TabsTrigger>
                      <TabsTrigger value="seo">SEO</TabsTrigger>
                      <TabsTrigger value="advanced">Advanced</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="content" className="space-y-6 mt-6">
                      <FormField
                        control={form.control}
                        name="title"
                        render={({ field }) => (
                          <FormFieldWrapper
                            label="Page Title"
                            required
                            description="The main title of your page"
                          >
                            <Input
                              placeholder="Enter page title..."
                              {...field}
                              disabled={isLoading}
                              className="text-lg"
                            />
                          </FormFieldWrapper>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="slug"
                        render={({ field }) => (
                          <FormFieldWrapper
                            label="URL Slug"
                            required
                            description="The URL path for this page"
                          >
                            <SlugInput
                              value={field.value}
                              onChange={field.onChange}
                              sourceValue={watchedTitle}
                              prefix="/"
                              disabled={isLoading}
                            />
                          </FormFieldWrapper>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="content"
                        render={({ field }) => (
                          <FormFieldWrapper
                            label="Page Content"
                            description="The main content of your page"
                          >
                            <Textarea
                              placeholder="Start writing your page content..."
                              className="min-h-[400px]"
                              {...field}
                              disabled={isLoading}
                            />
                          </FormFieldWrapper>
                        )}
                      />
                    </TabsContent>
                    
                    <TabsContent value="seo" className="space-y-6 mt-6">
                      <FormField
                        control={form.control}
                        name="seoTitle"
                        render={({ field }) => (
                          <FormFieldWrapper
                            label="SEO Title"
                            description="Title for search engines (max 60 characters)"
                          >
                            <Input
                              placeholder="SEO optimized title..."
                              {...field}
                              disabled={isLoading}
                              maxLength={60}
                            />
                            <div className="text-xs text-muted-foreground text-right">
                              {field.value?.length || 0}/60
                            </div>
                          </FormFieldWrapper>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="seoDescription"
                        render={({ field }) => (
                          <FormFieldWrapper
                            label="SEO Description"
                            description="Description for search engines (max 160 characters)"
                          >
                            <Textarea
                              placeholder="SEO optimized description..."
                              className="min-h-[100px]"
                              {...field}
                              disabled={isLoading}
                              maxLength={160}
                            />
                            <div className="text-xs text-muted-foreground text-right">
                              {field.value?.length || 0}/160
                            </div>
                          </FormFieldWrapper>
                        )}
                      />

                      {/* SEO Preview */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Search Preview</label>
                        <div className="border rounded-lg p-4 bg-muted/30">
                          <div className="space-y-1">
                            <h3 className="text-blue-600 text-lg font-medium">
                              {form.watch('seoTitle') || form.watch('title') || 'Page Title'}
                            </h3>
                            <p className="text-green-700 text-sm">
                              {window.location.origin}/{form.watch('slug') || 'page-slug'}
                            </p>
                            <p className="text-gray-600 text-sm">
                              {form.watch('seoDescription') || 'Page description will appear here...'}
                            </p>
                          </div>
                        </div>
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="advanced" className="space-y-6 mt-6">
                      <FormField
                        control={form.control}
                        name="template"
                        render={({ field }) => (
                          <FormFieldWrapper
                            label="Page Template"
                            description="Choose a template that defines the page layout and structure"
                          >
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                              disabled={isLoading}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {templates.map((template) => (
                                  <SelectItem key={template.id} value={template.id}>
                                    <div className="flex flex-col">
                                      <span>{template.name}</span>
                                      {template.description && (
                                        <span className="text-xs text-muted-foreground">
                                          {template.description}
                                        </span>
                                      )}
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormFieldWrapper>
                        )}
                      />

                      {/* Template Preview */}
                      {watchedTemplate && (
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Template Preview</label>
                          <div className="border rounded-lg p-4 bg-muted/30">
                            <div className="flex items-center gap-2 mb-2">
                              <Layout className="h-4 w-4" />
                              <span className="font-medium">
                                {templates.find(t => t.id === watchedTemplate)?.name}
                              </span>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {getTemplateDescription(watchedTemplate)}
                            </p>
                          </div>
                        </div>
                      )}
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Publishing Options */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    Publishing
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormFieldWrapper
                        label="Status"
                        required
                      >
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                          disabled={isLoading}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="DRAFT">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-gray-400" />
                                Draft
                              </div>
                            </SelectItem>
                            <SelectItem value="PUBLISHED">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-green-400" />
                                Published
                              </div>
                            </SelectItem>
                            <SelectItem value="ARCHIVED">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-red-400" />
                                Archived
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </FormFieldWrapper>
                    )}
                  />

                  {watchedStatus === 'PUBLISHED' && (
                    <FormField
                      control={form.control}
                      name="publishedAt"
                      render={({ field }) => (
                        <FormFieldWrapper
                          label="Publish Date"
                          description="When this page was/will be published"
                        >
                          <DateTimePicker
                            value={field.value}
                            onChange={field.onChange}
                            disabled={isLoading}
                          />
                        </FormFieldWrapper>
                      )}
                    />
                  )}

                  <FormField
                    control={form.control}
                    name="authorId"
                    render={({ field }) => (
                      <FormFieldWrapper
                        label="Author"
                        required
                      >
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                          disabled={isLoading}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select author" />
                          </SelectTrigger>
                          <SelectContent>
                            {authors.map((author) => (
                              <SelectItem key={author.id} value={author.id}>
                                {author.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormFieldWrapper>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Page Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Page Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Current Template</label>
                    <div className="flex items-center gap-2 p-2 bg-muted rounded">
                      <Layout className="h-4 w-4" />
                      <span className="text-sm">
                        {templates.find(t => t.id === watchedTemplate)?.name || 'Default'}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Page URL</label>
                    <div className="p-2 bg-muted rounded text-sm font-mono">
                      {window.location.origin}/{form.watch('slug') || 'page-slug'}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => {
                      // Preview functionality
                      window.open(`/preview/${form.watch('slug')}`, '_blank')
                    }}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Page
                  </Button>
                  
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('seo')}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    SEO Settings
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Action Buttons */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(watchedStatus)}>
                    {watchedStatus}
                  </Badge>
                  {watchedStatus === 'PUBLISHED' && form.watch('publishedAt') && (
                    <span className="text-sm text-muted-foreground">
                      Published {new Date(form.watch('publishedAt')).toLocaleDateString()}
                    </span>
                  )}
                </div>
                
                <div className="flex gap-3">
                  {onCancel && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={onCancel}
                      disabled={isLoading}
                    >
                      Cancel
                    </Button>
                  )}
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => form.setValue('status', 'DRAFT')}
                    disabled={isLoading}
                  >
                    Save as Draft
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    <Save className="h-4 w-4 mr-2" />
                    {isLoading ? 'Saving...' : mode === 'create' ? 'Create Page' : 'Update Page'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </form>
      </Form>
    </div>
  )
}