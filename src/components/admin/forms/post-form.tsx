'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Form, FormField } from '@/components/ui/form'
import { useToast } from '@/hooks/use-toast'
import { FormFieldWrapper, FormSection, FormGrid } from './shared/form-field-wrapper'
import { ImageUpload } from './shared/image-upload'
import { SlugInput } from './shared/slug-input'
import { DateTimePicker } from './shared/date-time-picker'
import { CreatePostSchema, UpdatePostSchema, type CreatePostInput, type UpdatePostInput } from '@/lib/schemas'
import { FileText, Save, Eye, Calendar, Tag, Folder, Settings } from 'lucide-react'
import { useState } from 'react'

interface PostFormProps {
  initialData?: Partial<UpdatePostInput>
  mode?: 'create' | 'edit'
  onSubmit?: (data: CreatePostInput | UpdatePostInput) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  categories?: Array<{ id: string; name: string }>
  tags?: Array<{ id: string; name: string }>
  authors?: Array<{ id: string; name: string }>
}

export function PostForm({
  initialData,
  mode = 'create',
  onSubmit,
  onCancel,
  isLoading = false,
  categories = [],
  tags = [],
  authors = [],
}: PostFormProps) {
  const { toast } = useToast()
  const [selectedTags, setSelectedTags] = useState<string[]>(initialData?.tagIds || [])
  const [isScheduled, setIsScheduled] = useState(!!initialData?.scheduledAt)

  const form = useForm({
    resolver: zodResolver(mode === 'create' ? CreatePostSchema : UpdatePostSchema),
    defaultValues: {
      title: initialData?.title || '',
      slug: initialData?.slug || '',
      excerpt: initialData?.excerpt || '',
      content: initialData?.content || '',
      featuredImage: initialData?.featuredImage || '',
      status: initialData?.status || 'DRAFT',
      publishedAt: initialData?.publishedAt,
      scheduledAt: initialData?.scheduledAt,
      seoTitle: initialData?.seoTitle || '',
      seoDescription: initialData?.seoDescription || '',
      authorId: initialData?.authorId || '',
      categoryId: initialData?.categoryId || '',
      tagIds: initialData?.tagIds || [],
    },
  })

  const watchedTitle = form.watch('title')
  const watchedStatus = form.watch('status')

  const handleSubmit = async (data: any) => {
    try {
      const submitData = {
        ...data,
        tagIds: selectedTags,
        ...(isScheduled && data.scheduledAt && { scheduledAt: data.scheduledAt }),
        ...(!isScheduled && { scheduledAt: undefined }),
      }

      if (onSubmit) {
        await onSubmit(submitData)
        toast({
          title: 'Success!',
          description: `Post ${mode === 'create' ? 'created' : 'updated'} successfully.`,
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to ${mode === 'create' ? 'create' : 'update'} post. Please try again.`,
        variant: 'destructive',
      })
    }
  }

  const handleTagToggle = (tagId: string) => {
    setSelectedTags(prev => 
      prev.includes(tagId) 
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PUBLISHED': return 'bg-green-100 text-green-800'
      case 'DRAFT': return 'bg-gray-100 text-gray-800'
      case 'ARCHIVED': return 'bg-red-100 text-red-800'
      case 'SCHEDULED': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {mode === 'create' ? 'Create New Post' : 'Edit Post'}
          </CardTitle>
          <CardDescription>
            {mode === 'create' 
              ? 'Create a new blog post with content, SEO settings, and publishing options.'
              : 'Update your blog post content and settings.'
            }
          </CardDescription>
        </CardHeader>
      </Card>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Content</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormFieldWrapper
                        label="Title"
                        required
                        description="The main title of your post"
                      >
                        <Input
                          placeholder="Enter post title..."
                          {...field}
                          disabled={isLoading}
                          className="text-lg"
                        />
                      </FormFieldWrapper>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="slug"
                    render={({ field }) => (
                      <FormFieldWrapper
                        label="URL Slug"
                        required
                        description="The URL-friendly version of the title"
                      >
                        <SlugInput
                          value={field.value}
                          onChange={field.onChange}
                          sourceValue={watchedTitle}
                          prefix="/blog/"
                          disabled={isLoading}
                        />
                      </FormFieldWrapper>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="excerpt"
                    render={({ field }) => (
                      <FormFieldWrapper
                        label="Excerpt"
                        description="A brief summary of the post (optional)"
                      >
                        <Textarea
                          placeholder="Write a brief summary of your post..."
                          className="min-h-[100px]"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormFieldWrapper>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="content"
                    render={({ field }) => (
                      <FormFieldWrapper
                        label="Content"
                        description="The main content of your post"
                      >
                        <Textarea
                          placeholder="Start writing your post content..."
                          className="min-h-[400px]"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormFieldWrapper>
                    )}
                  />
                </CardContent>
              </Card>

              {/* SEO Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    SEO Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="seoTitle"
                    render={({ field }) => (
                      <FormFieldWrapper
                        label="SEO Title"
                        description="Title for search engines (max 60 characters)"
                      >
                        <Input
                          placeholder="SEO optimized title..."
                          {...field}
                          disabled={isLoading}
                          maxLength={60}
                        />
                        <div className="text-xs text-muted-foreground text-right">
                          {field.value?.length || 0}/60
                        </div>
                      </FormFieldWrapper>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="seoDescription"
                    render={({ field }) => (
                      <FormFieldWrapper
                        label="SEO Description"
                        description="Description for search engines (max 160 characters)"
                      >
                        <Textarea
                          placeholder="SEO optimized description..."
                          className="min-h-[80px]"
                          {...field}
                          disabled={isLoading}
                          maxLength={160}
                        />
                        <div className="text-xs text-muted-foreground text-right">
                          {field.value?.length || 0}/160
                        </div>
                      </FormFieldWrapper>
                    )}
                  />
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Publishing Options */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    Publishing
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormFieldWrapper
                        label="Status"
                        required
                      >
                        <Select
                          value={field.value}
                          onValueChange={(value) => {
                            field.onChange(value)
                            setIsScheduled(value === 'SCHEDULED')
                          }}
                          disabled={isLoading}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="DRAFT">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-gray-400" />
                                Draft
                              </div>
                            </SelectItem>
                            <SelectItem value="PUBLISHED">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-green-400" />
                                Published
                              </div>
                            </SelectItem>
                            <SelectItem value="SCHEDULED">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-blue-400" />
                                Scheduled
                              </div>
                            </SelectItem>
                            <SelectItem value="ARCHIVED">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-red-400" />
                                Archived
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </FormFieldWrapper>
                    )}
                  />

                  {watchedStatus === 'PUBLISHED' && (
                    <FormField
                      control={form.control}
                      name="publishedAt"
                      render={({ field }) => (
                        <FormFieldWrapper
                          label="Publish Date"
                          description="When this post was/will be published"
                        >
                          <DateTimePicker
                            value={field.value}
                            onChange={field.onChange}
                            disabled={isLoading}
                          />
                        </FormFieldWrapper>
                      )}
                    />
                  )}

                  {watchedStatus === 'SCHEDULED' && (
                    <FormField
                      control={form.control}
                      name="scheduledAt"
                      render={({ field }) => (
                        <FormFieldWrapper
                          label="Schedule Date"
                          description="When this post should be published"
                          required
                        >
                          <DateTimePicker
                            value={field.value}
                            onChange={field.onChange}
                            disabled={isLoading}
                          />
                        </FormFieldWrapper>
                      )}
                    />
                  )}

                  <FormField
                    control={form.control}
                    name="authorId"
                    render={({ field }) => (
                      <FormFieldWrapper
                        label="Author"
                        required
                      >
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                          disabled={isLoading}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select author" />
                          </SelectTrigger>
                          <SelectContent>
                            {authors.map((author) => (
                              <SelectItem key={author.id} value={author.id}>
                                {author.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormFieldWrapper>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Featured Image */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Featured Image</CardTitle>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="featuredImage"
                    render={({ field }) => (
                      <ImageUpload
                        value={field.value}
                        onChange={field.onChange}
                        disabled={isLoading}
                      />
                    )}
                  />
                </CardContent>
              </Card>

              {/* Categories */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Folder className="h-4 w-4" />
                    Category
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="categoryId"
                    render={({ field }) => (
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isLoading}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">No category</SelectItem>
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Tags */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Tag className="h-4 w-4" />
                    Tags
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex flex-wrap gap-2">
                      {selectedTags.map((tagId) => {
                        const tag = tags.find(t => t.id === tagId)
                        return tag ? (
                          <Badge
                            key={tagId}
                            variant="secondary"
                            className="cursor-pointer"
                            onClick={() => handleTagToggle(tagId)}
                          >
                            {tag.name} ×
                          </Badge>
                        ) : null
                      })}
                    </div>
                    
                    <div className="border rounded-lg p-3 max-h-48 overflow-y-auto">
                      <div className="space-y-2">
                        {tags.map((tag) => (
                          <label
                            key={tag.id}
                            className="flex items-center space-x-2 cursor-pointer hover:bg-muted/50 p-1 rounded"
                          >
                            <input
                              type="checkbox"
                              checked={selectedTags.includes(tag.id)}
                              onChange={() => handleTagToggle(tag.id)}
                              className="rounded"
                            />
                            <span className="text-sm">{tag.name}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Action Buttons */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(watchedStatus)}>
                    {watchedStatus}
                  </Badge>
                  {watchedStatus === 'SCHEDULED' && form.watch('scheduledAt') && (
                    <span className="text-sm text-muted-foreground">
                      Scheduled for {new Date(form.watch('scheduledAt')).toLocaleDateString()}
                    </span>
                  )}
                </div>
                
                <div className="flex gap-3">
                  {onCancel && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={onCancel}
                      disabled={isLoading}
                    >
                      Cancel
                    </Button>
                  )}
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => form.setValue('status', 'DRAFT')}
                    disabled={isLoading}
                  >
                    Save as Draft
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    <Save className="h-4 w-4 mr-2" />
                    {isLoading ? 'Saving...' : mode === 'create' ? 'Create Post' : 'Update Post'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </form>
      </Form>
    </div>
  )
}