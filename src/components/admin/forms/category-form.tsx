'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormField } from '@/components/ui/form'
import { useToast } from '@/hooks/use-toast'
import { FormFieldWrapper, FormSection, FormGrid } from './shared/form-field-wrapper'
import { ImageUpload } from './shared/image-upload'
import { SlugInput } from './shared/slug-input'
import { ColorPicker } from './shared/color-picker'
import { CreateCategorySchema, UpdateCategorySchema, type CreateCategoryInput, type UpdateCategoryInput } from '@/lib/schemas'
import { Folder, Save, FolderPlus, Palette } from 'lucide-react'

interface CategoryFormProps {
  initialData?: Partial<UpdateCategoryInput>
  mode?: 'create' | 'edit'
  onSubmit?: (data: CreateCategoryInput | UpdateCategoryInput) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  categories?: Array<{ id: string; name: string; parentId?: string }>
  currentUserId?: string
}

export function CategoryForm({
  initialData,
  mode = 'create',
  onSubmit,
  onCancel,
  isLoading = false,
  categories = [],
  currentUserId = '',
}: CategoryFormProps) {
  const { toast } = useToast()

  const form = useForm({
    resolver: zodResolver(mode === 'create' ? CreateCategorySchema : UpdateCategorySchema),
    defaultValues: {
      name: initialData?.name || '',
      slug: initialData?.slug || '',
      description: initialData?.description || '',
      color: initialData?.color || '#3B82F6',
      image: initialData?.image || '',
      parentId: initialData?.parentId || '',
      createdBy: initialData?.createdBy || currentUserId,
    },
  })

  const watchedName = form.watch('name')

  // Filter out current category and its children to prevent circular references
  const availableParentCategories = categories.filter(cat => {
    if (mode === 'edit' && initialData?.id) {
      return cat.id !== initialData.id && !isChildCategory(cat.id, initialData.id, categories)
    }
    return true
  })

  const handleSubmit = async (data: any) => {
    try {
      if (onSubmit) {
        await onSubmit(data)
        toast({
          title: 'Success!',
          description: `Category ${mode === 'create' ? 'created' : 'updated'} successfully.`,
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to ${mode === 'create' ? 'create' : 'update'} category. Please try again.`,
        variant: 'destructive',
      })
    }
  }

  // Helper function to check if a category is a child of another
  const isChildCategory = (categoryId: string, parentId: string, categories: any[]): boolean => {
    const category = categories.find(cat => cat.id === categoryId)
    if (!category) return false
    if (category.parentId === parentId) return true
    if (category.parentId) {
      return isChildCategory(category.parentId, parentId, categories)
    }
    return false
  }

  // Build category hierarchy for display
  const buildCategoryHierarchy = (categories: any[], parentId: string | null = null, level = 0): any[] => {
    return categories
      .filter(cat => cat.parentId === parentId)
      .map(cat => ({
        ...cat,
        level,
        children: buildCategoryHierarchy(categories, cat.id, level + 1)
      }))
  }

  const hierarchicalCategories = buildCategoryHierarchy(availableParentCategories, null)

  const renderCategoryOption = (category: any): React.ReactNode => {
    const indent = '  '.repeat(category.level)
    return (
      <SelectItem key={category.id} value={category.id}>
        {indent}{category.name}
      </SelectItem>
    )
  }

  const renderCategoryOptions = (categories: any[]): React.ReactNode[] => {
    const options: React.ReactNode[] = []
    
    const addOptions = (cats: any[]) => {
      cats.forEach(cat => {
        options.push(renderCategoryOption(cat))
        if (cat.children && cat.children.length > 0) {
          addOptions(cat.children)
        }
      })
    }
    
    addOptions(categories)
    return options
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FolderPlus className="h-5 w-5" />
          {mode === 'create' ? 'Create New Category' : 'Edit Category'}
        </CardTitle>
        <CardDescription>
          {mode === 'create' 
            ? 'Create a new category to organize your content.'
            : 'Update category information and settings.'
          }
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
            <FormSection title="Basic Information">
              <FormGrid columns={2}>
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="Category Name"
                      required
                      description="The display name for this category"
                    >
                      <Input
                        placeholder="Technology"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormFieldWrapper>
                  )}
                />

                <FormField
                  control={form.control}
                  name="slug"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="URL Slug"
                      required
                      description="The URL-friendly version of the name"
                    >
                      <SlugInput
                        value={field.value}
                        onChange={field.onChange}
                        sourceValue={watchedName}
                        prefix="/category/"
                        disabled={isLoading}
                      />
                    </FormFieldWrapper>
                  )}
                />
              </FormGrid>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormFieldWrapper
                    label="Description"
                    description="A brief description of this category (optional)"
                  >
                    <Textarea
                      placeholder="Describe what this category is about..."
                      className="min-h-[100px]"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormFieldWrapper>
                )}
              />
            </FormSection>

            <FormSection title="Appearance">
              <FormGrid columns={2}>
                <FormField
                  control={form.control}
                  name="color"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="Category Color"
                      description="Choose a color to represent this category"
                    >
                      <ColorPicker
                        value={field.value}
                        onChange={field.onChange}
                        disabled={isLoading}
                      />
                    </FormFieldWrapper>
                  )}
                />

                <FormField
                  control={form.control}
                  name="parentId"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="Parent Category"
                      description="Select a parent category to create a hierarchy"
                    >
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isLoading}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="No parent category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">No parent category</SelectItem>
                          {renderCategoryOptions(hierarchicalCategories)}
                        </SelectContent>
                      </Select>
                    </FormFieldWrapper>
                  )}
                />
              </FormGrid>

              <FormField
                control={form.control}
                name="image"
                render={({ field }) => (
                  <FormFieldWrapper
                    label="Category Image"
                    description="Upload an image to represent this category (optional)"
                  >
                    <ImageUpload
                      value={field.value}
                      onChange={field.onChange}
                      disabled={isLoading}
                      className="max-w-md"
                    />
                  </FormFieldWrapper>
                )}
              />
            </FormSection>

            {/* Preview Section */}
            <FormSection title="Preview">
              <div className="border rounded-lg p-4 bg-muted/30">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: form.watch('color') || '#3B82F6' }}
                  />
                  <div>
                    <h4 className="font-medium">
                      {form.watch('name') || 'Category Name'}
                    </h4>
                    {form.watch('description') && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {form.watch('description')}
                      </p>
                    )}
                    <p className="text-xs text-muted-foreground mt-1">
                      URL: /category/{form.watch('slug') || 'category-slug'}
                    </p>
                  </div>
                </div>
              </div>
            </FormSection>

            <div className="flex justify-end space-x-4 pt-6 border-t">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isLoading}>
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? 'Saving...' : mode === 'create' ? 'Create Category' : 'Save Changes'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}