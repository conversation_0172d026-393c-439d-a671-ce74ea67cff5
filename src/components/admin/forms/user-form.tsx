'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormField } from '@/components/ui/form'
import { useToast } from '@/hooks/use-toast'
import { FormFieldWrapper, FormSection, FormGrid } from './shared/form-field-wrapper'
import { ImageUpload } from './shared/image-upload'
import { CreateUserSchema, UpdateUserSchema, UserProfileSchema, type CreateUserInput, type UpdateUserInput, type UserProfileInput } from '@/lib/schemas'
import { Eye, EyeOff, Save, UserPlus } from 'lucide-react'
import { useState } from 'react'

interface UserFormProps {
  initialData?: Partial<UpdateUserInput>
  mode?: 'create' | 'edit' | 'profile'
  onSubmit?: (data: CreateUserInput | UpdateUserInput | UserProfileInput) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
}

export function UserForm({
  initialData,
  mode = 'create',
  onSubmit,
  onCancel,
  isLoading = false,
}: UserFormProps) {
  const { toast } = useToast()
  const [showPassword, setShowPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const getSchema = () => {
    switch (mode) {
      case 'create':
        return CreateUserSchema
      case 'edit':
        return UpdateUserSchema
      case 'profile':
        return UserProfileSchema
      default:
        return CreateUserSchema
    }
  }

  const form = useForm({
    resolver: zodResolver(getSchema()),
    defaultValues: {
      name: initialData?.name || '',
      email: initialData?.email || '',
      password: '',
      role: initialData?.role || 'USER',
      image: initialData?.image || '',
      ...(mode === 'profile' && {
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      }),
    },
  })

  const handleSubmit = async (data: any) => {
    try {
      if (onSubmit) {
        await onSubmit(data)
        toast({
          title: 'Success!',
          description: `User ${mode === 'create' ? 'created' : 'updated'} successfully.`,
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to ${mode === 'create' ? 'create' : 'update'} user. Please try again.`,
        variant: 'destructive',
      })
    }
  }

  const getTitle = () => {
    switch (mode) {
      case 'create':
        return 'Create New User'
      case 'edit':
        return 'Edit User'
      case 'profile':
        return 'Edit Profile'
      default:
        return 'User Form'
    }
  }

  const getDescription = () => {
    switch (mode) {
      case 'create':
        return 'Add a new user to the system with their basic information and role.'
      case 'edit':
        return 'Update user information and permissions.'
      case 'profile':
        return 'Update your personal information and change your password.'
      default:
        return ''
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserPlus className="h-5 w-5" />
          {getTitle()}
        </CardTitle>
        <CardDescription>{getDescription()}</CardDescription>
      </CardHeader>
      
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
            <FormSection title="Basic Information">
              <FormGrid columns={2}>
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="Full Name"
                      required
                      description="The user's display name"
                    >
                      <Input
                        placeholder="John Doe"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormFieldWrapper>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="Email Address"
                      required
                      description="Used for login and notifications"
                    >
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormFieldWrapper>
                  )}
                />
              </FormGrid>

              <FormField
                control={form.control}
                name="image"
                render={({ field }) => (
                  <FormFieldWrapper
                    label="Profile Picture"
                    description="Upload a profile picture for the user"
                  >
                    <ImageUpload
                      value={field.value}
                      onChange={field.onChange}
                      disabled={isLoading}
                      className="max-w-md"
                    />
                  </FormFieldWrapper>
                )}
              />
            </FormSection>

            {mode !== 'profile' && (
              <FormSection title="Role & Permissions">
                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="User Role"
                      required
                      description="Determines what the user can access and modify"
                    >
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isLoading}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USER">User</SelectItem>
                          <SelectItem value="AUTHOR">Author</SelectItem>
                          <SelectItem value="EDITOR">Editor</SelectItem>
                          <SelectItem value="ADMIN">Admin</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormFieldWrapper>
                  )}
                />
              </FormSection>
            )}

            <FormSection 
              title={mode === 'profile' ? 'Change Password' : 'Password'}
              description={mode === 'profile' ? 'Leave blank to keep current password' : undefined}
            >
              {mode === 'profile' && (
                <FormField
                  control={form.control}
                  name="currentPassword"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="Current Password"
                      description="Enter your current password to change it"
                    >
                      <div className="relative">
                        <Input
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Enter current password"
                          {...field}
                          disabled={isLoading}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </FormFieldWrapper>
                  )}
                />
              )}

              <FormGrid columns={mode === 'profile' ? 2 : 1}>
                <FormField
                  control={form.control}
                  name={mode === 'profile' ? 'newPassword' : 'password'}
                  render={({ field }) => (
                    <FormFieldWrapper
                      label={mode === 'profile' ? 'New Password' : 'Password'}
                      required={mode === 'create'}
                      description={
                        mode === 'create' 
                          ? 'Must be at least 8 characters long'
                          : mode === 'edit'
                          ? 'Leave blank to keep current password'
                          : 'Must be at least 8 characters long'
                      }
                    >
                      <div className="relative">
                        <Input
                          type={showNewPassword ? 'text' : 'password'}
                          placeholder={mode === 'profile' ? 'Enter new password' : 'Enter password'}
                          {...field}
                          disabled={isLoading}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                        >
                          {showNewPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </FormFieldWrapper>
                  )}
                />

                {mode === 'profile' && (
                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormFieldWrapper
                        label="Confirm New Password"
                        description="Re-enter your new password"
                      >
                        <div className="relative">
                          <Input
                            type={showConfirmPassword ? 'text' : 'password'}
                            placeholder="Confirm new password"
                            {...field}
                            disabled={isLoading}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          >
                            {showConfirmPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </FormFieldWrapper>
                    )}
                  />
                )}
              </FormGrid>
            </FormSection>

            <div className="flex justify-end space-x-4 pt-6 border-t">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isLoading}>
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? 'Saving...' : mode === 'create' ? 'Create User' : 'Save Changes'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}