'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Form, FormField } from '@/components/ui/form'
import { useToast } from '@/hooks/use-toast'
import { FormFieldWrapper, FormSection, FormGrid } from './shared/form-field-wrapper'
import { 
  CreateMenuSchema, 
  UpdateMenuSchema, 
  CreateMenuItemSchema,
  UpdateMenuItemSchema,
  type CreateMenuInput, 
  type UpdateMenuInput,
  type CreateMenuItemInput,
  type UpdateMenuItemInput
} from '@/lib/schemas'
import { 
  Menu, 
  Save, 
  Plus, 
  Edit, 
  Trash2, 
  GripVertical, 
  ExternalLink,
  ChevronRight,
  ChevronDown,
  Link as LinkIcon
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface MenuItem {
  id: string
  label: string
  url?: string
  target: string
  order: number
  parentId?: string
  children?: MenuItem[]
}

interface MenuFormProps {
  initialData?: Partial<UpdateMenuInput>
  mode?: 'create' | 'edit'
  onSubmit?: (data: CreateMenuInput | UpdateMenuInput) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  menuItems?: MenuItem[]
  onMenuItemsChange?: (items: MenuItem[]) => void
}

export function MenuForm({
  initialData,
  mode = 'create',
  onSubmit,
  onCancel,
  isLoading = false,
  menuItems = [],
  onMenuItemsChange,
}: MenuFormProps) {
  const { toast } = useToast()
  const [items, setItems] = useState<MenuItem[]>(menuItems)
  const [editingItem, setEditingItem] = useState<MenuItem | null>(null)
  const [isItemDialogOpen, setIsItemDialogOpen] = useState(false)

  const form = useForm({
    resolver: zodResolver(mode === 'create' ? CreateMenuSchema : UpdateMenuSchema),
    defaultValues: {
      name: initialData?.name || '',
      location: initialData?.location || '',
    },
  })

  const handleSubmit = async (data: any) => {
    try {
      if (onSubmit) {
        await onSubmit(data)
        toast({
          title: 'Success!',
          description: `Menu ${mode === 'create' ? 'created' : 'updated'} successfully.`,
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to ${mode === 'create' ? 'create' : 'update'} menu. Please try again.`,
        variant: 'destructive',
      })
    }
  }

  const addMenuItem = () => {
    setEditingItem(null)
    setIsItemDialogOpen(true)
  }

  const editMenuItem = (item: MenuItem) => {
    setEditingItem(item)
    setIsItemDialogOpen(true)
  }

  const deleteMenuItem = (itemId: string) => {
    const newItems = items.filter(item => item.id !== itemId && item.parentId !== itemId)
    setItems(newItems)
    onMenuItemsChange?.(newItems)
  }

  const saveMenuItem = (itemData: CreateMenuItemInput | UpdateMenuItemInput) => {
    if (editingItem) {
      // Update existing item
      const newItems = items.map(item => 
        item.id === editingItem.id 
          ? { ...item, ...itemData }
          : item
      )
      setItems(newItems)
      onMenuItemsChange?.(newItems)
    } else {
      // Add new item
      const newItem: MenuItem = {
        id: `temp-${Date.now()}`,
        label: itemData.label,
        url: itemData.url,
        target: itemData.target || '_self',
        order: itemData.order || items.length,
        parentId: itemData.parentId,
      }
      const newItems = [...items, newItem]
      setItems(newItems)
      onMenuItemsChange?.(newItems)
    }
    setIsItemDialogOpen(false)
  }

  // Build hierarchical structure
  const buildHierarchy = (items: MenuItem[], parentId?: string): MenuItem[] => {
    return items
      .filter(item => item.parentId === parentId)
      .sort((a, b) => a.order - b.order)
      .map(item => ({
        ...item,
        children: buildHierarchy(items, item.id)
      }))
  }

  const hierarchicalItems = buildHierarchy(items)

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Menu className="h-5 w-5" />
            {mode === 'create' ? 'Create New Menu' : 'Edit Menu'}
          </CardTitle>
          <CardDescription>
            {mode === 'create' 
              ? 'Create a new navigation menu with custom items and structure.'
              : 'Update menu information and manage menu items.'
            }
          </CardDescription>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Menu Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Menu Settings</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="Menu Name"
                      required
                      description="A unique name to identify this menu"
                    >
                      <Input
                        placeholder="Main Navigation"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormFieldWrapper>
                  )}
                />

                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="Menu Location"
                      description="Where this menu will be displayed"
                    >
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isLoading}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select location" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="header">Header</SelectItem>
                          <SelectItem value="footer">Footer</SelectItem>
                          <SelectItem value="sidebar">Sidebar</SelectItem>
                          <SelectItem value="mobile">Mobile Menu</SelectItem>
                          <SelectItem value="custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormFieldWrapper>
                  )}
                />

                <div className="flex justify-end space-x-4 pt-4 border-t">
                  {onCancel && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={onCancel}
                      disabled={isLoading}
                    >
                      Cancel
                    </Button>
                  )}
                  <Button type="submit" disabled={isLoading}>
                    <Save className="h-4 w-4 mr-2" />
                    {isLoading ? 'Saving...' : mode === 'create' ? 'Create Menu' : 'Save Changes'}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Menu Items */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Menu Items</CardTitle>
              <Button onClick={addMenuItem} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {hierarchicalItems.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Menu className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No menu items yet</p>
                <p className="text-sm">Click "Add Item" to get started</p>
              </div>
            ) : (
              <div className="space-y-2">
                {hierarchicalItems.map((item) => (
                  <MenuItemComponent
                    key={item.id}
                    item={item}
                    level={0}
                    onEdit={editMenuItem}
                    onDelete={deleteMenuItem}
                  />
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Menu Item Dialog */}
      <MenuItemDialog
        isOpen={isItemDialogOpen}
        onClose={() => setIsItemDialogOpen(false)}
        onSave={saveMenuItem}
        editingItem={editingItem}
        parentItems={items.filter(item => !item.parentId)}
      />
    </div>
  )
}

interface MenuItemComponentProps {
  item: MenuItem
  level: number
  onEdit: (item: MenuItem) => void
  onDelete: (itemId: string) => void
}

function MenuItemComponent({ item, level, onEdit, onDelete }: MenuItemComponentProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  const hasChildren = item.children && item.children.length > 0

  return (
    <div className="space-y-2">
      <div 
        className={cn(
          "flex items-center gap-2 p-3 border rounded-lg hover:bg-muted/50 transition-colors",
          level > 0 && "ml-6 border-l-2 border-l-primary/20"
        )}
      >
        <div className="flex items-center gap-2 flex-1">
          {hasChildren && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          )}
          
          <GripVertical className="h-4 w-4 text-muted-foreground cursor-move" />
          
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <span className="font-medium">{item.label}</span>
              {item.target === '_blank' && (
                <ExternalLink className="h-3 w-3 text-muted-foreground" />
              )}
            </div>
            {item.url && (
              <p className="text-xs text-muted-foreground">{item.url}</p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(item)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(item.id)}
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {hasChildren && isExpanded && (
        <div className="space-y-2">
          {item.children!.map((child) => (
            <MenuItemComponent
              key={child.id}
              item={child}
              level={level + 1}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
        </div>
      )}
    </div>
  )
}

interface MenuItemDialogProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: CreateMenuItemInput | UpdateMenuItemInput) => void
  editingItem: MenuItem | null
  parentItems: MenuItem[]
}

function MenuItemDialog({ isOpen, onClose, onSave, editingItem, parentItems }: MenuItemDialogProps) {
  const form = useForm({
    resolver: zodResolver(editingItem ? UpdateMenuItemSchema : CreateMenuItemSchema),
    defaultValues: {
      label: editingItem?.label || '',
      url: editingItem?.url || '',
      target: editingItem?.target || '_self',
      order: editingItem?.order || 0,
      parentId: editingItem?.parentId || '',
    },
  })

  const handleSubmit = (data: any) => {
    onSave(data)
    form.reset()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {editingItem ? 'Edit Menu Item' : 'Add Menu Item'}
          </DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="label"
              render={({ field }) => (
                <FormFieldWrapper
                  label="Label"
                  required
                  description="The text displayed for this menu item"
                >
                  <Input
                    placeholder="Home"
                    {...field}
                  />
                </FormFieldWrapper>
              )}
            />

            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormFieldWrapper
                  label="URL"
                  description="The link destination (optional)"
                >
                  <Input
                    placeholder="/about"
                    {...field}
                  />
                </FormFieldWrapper>
              )}
            />

            <FormGrid columns={2}>
              <FormField
                control={form.control}
                name="target"
                render={({ field }) => (
                  <FormFieldWrapper
                    label="Target"
                    description="How the link opens"
                  >
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_self">Same Window</SelectItem>
                        <SelectItem value="_blank">New Window</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormFieldWrapper>
                )}
              />

              <FormField
                control={form.control}
                name="order"
                render={({ field }) => (
                  <FormFieldWrapper
                    label="Order"
                    description="Display order"
                  >
                    <Input
                      type="number"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    />
                  </FormFieldWrapper>
                )}
              />
            </FormGrid>

            <FormField
              control={form.control}
              name="parentId"
              render={({ field }) => (
                <FormFieldWrapper
                  label="Parent Item"
                  description="Make this a sub-menu item"
                >
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="No parent (top level)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">No parent (top level)</SelectItem>
                      {parentItems.map((item) => (
                        <SelectItem key={item.id} value={item.id}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormFieldWrapper>
              )}
            />

            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit">
                <Save className="h-4 w-4 mr-2" />
                {editingItem ? 'Update' : 'Add'} Item
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}