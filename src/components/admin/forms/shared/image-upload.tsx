'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Upload, Link, X, Image as ImageIcon, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ImageUploadProps {
  value?: string
  onChange: (value: string) => void
  onRemove?: () => void
  disabled?: boolean
  className?: string
  accept?: string[]
  maxSize?: number
  placeholder?: string
  showUrlInput?: boolean
  showMediaLibrary?: boolean
}

export function ImageUpload({
  value,
  onChange,
  onRemove,
  disabled = false,
  className,
  accept = ['image/*'],
  maxSize = 5 * 1024 * 1024, // 5MB
  placeholder = 'Upload an image',
  showUrlInput = true,
  showMediaLibrary = true,
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [urlInput, setUrlInput] = useState('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return

    const file = acceptedFiles[0]
    setIsUploading(true)

    try {
      // Here you would implement your file upload logic
      // For now, we'll create a temporary URL
      const url = URL.createObjectURL(file)
      onChange(url)
    } catch (error) {
      console.error('Upload failed:', error)
    } finally {
      setIsUploading(false)
    }
  }, [onChange])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: accept.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
    maxSize,
    multiple: false,
    disabled: disabled || isUploading,
  })

  const handleUrlSubmit = () => {
    if (urlInput.trim()) {
      onChange(urlInput.trim())
      setUrlInput('')
      setIsDialogOpen(false)
    }
  }

  const handleRemove = () => {
    if (onRemove) {
      onRemove()
    } else {
      onChange('')
    }
  }

  if (value) {
    return (
      <div className={cn('relative group', className)}>
        <div className="relative aspect-video rounded-lg border overflow-hidden bg-muted">
          <img
            src={value}
            alt="Uploaded image"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <div className="flex gap-2">
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button size="sm" variant="secondary">
                    <Upload className="h-4 w-4 mr-2" />
                    Change
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Change Image</DialogTitle>
                  </DialogHeader>
                  <ImageUploadDialog
                    onSelect={onChange}
                    onClose={() => setIsDialogOpen(false)}
                    showUrlInput={showUrlInput}
                    showMediaLibrary={showMediaLibrary}
                  />
                </DialogContent>
              </Dialog>
              <Button size="sm" variant="destructive" onClick={handleRemove}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('space-y-4', className)}>
      <div
        {...getRootProps()}
        className={cn(
          'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',
          isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25',
          disabled && 'opacity-50 cursor-not-allowed',
          'hover:border-primary/50 hover:bg-primary/5'
        )}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center gap-2">
          {isUploading ? (
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          ) : (
            <Upload className="h-8 w-8 text-muted-foreground" />
          )}
          <div className="text-sm">
            <span className="font-medium text-primary">
              {isUploading ? 'Uploading...' : 'Click to upload'}
            </span>
            <span className="text-muted-foreground"> or drag and drop</span>
          </div>
          <p className="text-xs text-muted-foreground">
            {accept.join(', ')} up to {Math.round(maxSize / 1024 / 1024)}MB
          </p>
        </div>
      </div>

      {(showUrlInput || showMediaLibrary) && (
        <div className="flex gap-2">
          {showUrlInput && (
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Link className="h-4 w-4 mr-2" />
                  Add URL
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Image URL</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="image-url">Image URL</Label>
                    <Input
                      id="image-url"
                      placeholder="https://example.com/image.jpg"
                      value={urlInput}
                      onChange={(e) => setUrlInput(e.target.value)}
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setUrlInput('')}>
                      Cancel
                    </Button>
                    <Button onClick={handleUrlSubmit}>
                      Add Image
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
          {showMediaLibrary && (
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <ImageIcon className="h-4 w-4 mr-2" />
                  Media Library
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl">
                <DialogHeader>
                  <DialogTitle>Media Library</DialogTitle>
                </DialogHeader>
                <MediaLibraryGrid onSelect={onChange} />
              </DialogContent>
            </Dialog>
          )}
        </div>
      )}
    </div>
  )
}

interface ImageUploadDialogProps {
  onSelect: (url: string) => void
  onClose: () => void
  showUrlInput?: boolean
  showMediaLibrary?: boolean
}

function ImageUploadDialog({
  onSelect,
  onClose,
  showUrlInput = true,
  showMediaLibrary = true,
}: ImageUploadDialogProps) {
  const [urlInput, setUrlInput] = useState('')

  const handleUrlSubmit = () => {
    if (urlInput.trim()) {
      onSelect(urlInput.trim())
      onClose()
    }
  }

  return (
    <Tabs defaultValue="upload" className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="upload">Upload</TabsTrigger>
        {showUrlInput && <TabsTrigger value="url">URL</TabsTrigger>}
        {showMediaLibrary && <TabsTrigger value="library">Library</TabsTrigger>}
      </TabsList>
      
      <TabsContent value="upload" className="space-y-4">
        <ImageUpload
          onChange={onSelect}
          showUrlInput={false}
          showMediaLibrary={false}
        />
      </TabsContent>
      
      {showUrlInput && (
        <TabsContent value="url" className="space-y-4">
          <div>
            <Label htmlFor="image-url">Image URL</Label>
            <Input
              id="image-url"
              placeholder="https://example.com/image.jpg"
              value={urlInput}
              onChange={(e) => setUrlInput(e.target.value)}
            />
          </div>
          <Button onClick={handleUrlSubmit} className="w-full">
            Add Image
          </Button>
        </TabsContent>
      )}
      
      {showMediaLibrary && (
        <TabsContent value="library">
          <MediaLibraryGrid onSelect={onSelect} />
        </TabsContent>
      )}
    </Tabs>
  )
}

interface MediaLibraryGridProps {
  onSelect: (url: string) => void
}

function MediaLibraryGrid({ onSelect }: MediaLibraryGridProps) {
  // This would be replaced with actual media library data
  const mockImages = [
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
    'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300&h=200&fit=crop',
    'https://images.unsplash.com/photo-1501594907352-04cda38ebc29?w=300&h=200&fit=crop',
    'https://images.unsplash.com/photo-1542273917363-3b1817f69a2d?w=300&h=200&fit=crop',
  ]

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
      {mockImages.map((src, index) => (
        <div
          key={index}
          className="aspect-square rounded-lg overflow-hidden border cursor-pointer hover:ring-2 hover:ring-primary transition-all"
          onClick={() => onSelect(src)}
        >
          <img
            src={src}
            alt={`Media ${index + 1}`}
            className="w-full h-full object-cover"
          />
        </div>
      ))}
    </div>
  )
}