'use client'

import { useState } from 'react'
import { format } from 'date-fns'
import { Calendar as CalendarIcon, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { cn } from '@/lib/utils'

interface DateTimePickerProps {
  value?: Date
  onChange: (date: Date | undefined) => void
  disabled?: boolean
  className?: string
  placeholder?: string
  showTime?: boolean
  dateFormat?: string
  timeFormat?: string
}

export function DateTimePicker({
  value,
  onChange,
  disabled = false,
  className,
  placeholder = 'Pick a date',
  showTime = true,
  dateFormat = 'PPP',
  timeFormat = 'HH:mm',
}: DateTimePickerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [timeValue, setTimeValue] = useState(
    value ? format(value, 'HH:mm') : '09:00'
  )

  const handleDateSelect = (date: Date | undefined) => {
    if (!date) {
      onChange(undefined)
      return
    }

    if (showTime && value) {
      // Preserve the time when changing date
      const newDate = new Date(date)
      newDate.setHours(value.getHours())
      newDate.setMinutes(value.getMinutes())
      onChange(newDate)
    } else if (showTime) {
      // Set default time if no previous time
      const [hours, minutes] = timeValue.split(':').map(Number)
      const newDate = new Date(date)
      newDate.setHours(hours)
      newDate.setMinutes(minutes)
      onChange(newDate)
    } else {
      onChange(date)
    }
  }

  const handleTimeChange = (time: string) => {
    setTimeValue(time)
    
    if (value) {
      const [hours, minutes] = time.split(':').map(Number)
      const newDate = new Date(value)
      newDate.setHours(hours)
      newDate.setMinutes(minutes)
      onChange(newDate)
    }
  }

  const formatDisplayValue = () => {
    if (!value) return placeholder
    
    if (showTime) {
      return `${format(value, dateFormat)} at ${format(value, timeFormat)}`
    }
    
    return format(value, dateFormat)
  }

  return (
    <div className={className}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-start text-left font-normal',
              !value && 'text-muted-foreground'
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {formatDisplayValue()}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          {showTime ? (
            <Tabs defaultValue="date" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="date">Date</TabsTrigger>
                <TabsTrigger value="time">Time</TabsTrigger>
              </TabsList>
              
              <TabsContent value="date" className="p-3">
                <Calendar
                  mode="single"
                  selected={value}
                  onSelect={handleDateSelect}
                  initialFocus
                />
              </TabsContent>
              
              <TabsContent value="time" className="p-3 space-y-3">
                <div className="space-y-2">
                  <Label htmlFor="time-input">Time</Label>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <Input
                      id="time-input"
                      type="time"
                      value={timeValue}
                      onChange={(e) => handleTimeChange(e.target.value)}
                      className="flex-1"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTimeChange('09:00')}
                  >
                    9:00 AM
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTimeChange('12:00')}
                  >
                    12:00 PM
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTimeChange('15:00')}
                  >
                    3:00 PM
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTimeChange('18:00')}
                  >
                    6:00 PM
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          ) : (
            <div className="p-3">
              <Calendar
                mode="single"
                selected={value}
                onSelect={handleDateSelect}
                initialFocus
              />
            </div>
          )}
        </PopoverContent>
      </Popover>
    </div>
  )
}

interface DateRangePickerProps {
  from?: Date
  to?: Date
  onFromChange: (date: Date | undefined) => void
  onToChange: (date: Date | undefined) => void
  disabled?: boolean
  className?: string
  placeholder?: string
}

export function DateRangePicker({
  from,
  to,
  onFromChange,
  onToChange,
  disabled = false,
  className,
  placeholder = 'Pick a date range',
}: DateRangePickerProps) {
  const [isOpen, setIsOpen] = useState(false)

  const formatDisplayValue = () => {
    if (!from && !to) return placeholder
    if (from && !to) return `From ${format(from, 'PPP')}`
    if (!from && to) return `Until ${format(to, 'PPP')}`
    if (from && to) return `${format(from, 'PPP')} - ${format(to, 'PPP')}`
    return placeholder
  }

  return (
    <div className={className}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-start text-left font-normal',
              !from && !to && 'text-muted-foreground'
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {formatDisplayValue()}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-3 space-y-3">
            <div>
              <Label className="text-sm font-medium">From Date</Label>
              <Calendar
                mode="single"
                selected={from}
                onSelect={onFromChange}
                className="mt-1"
              />
            </div>
            
            <div>
              <Label className="text-sm font-medium">To Date</Label>
              <Calendar
                mode="single"
                selected={to}
                onSelect={onToChange}
                className="mt-1"
                disabled={(date) => from ? date < from : false}
              />
            </div>
            
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  onFromChange(undefined)
                  onToChange(undefined)
                }}
              >
                Clear
              </Button>
              <Button
                size="sm"
                onClick={() => setIsOpen(false)}
              >
                Done
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}