'use client'

import { ReactNode } from 'react'
import { cn } from '@/lib/utils'
import {
  FormControl,
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'

interface FormFieldWrapperProps {
  label?: string
  description?: string
  required?: boolean
  className?: string
  children: ReactNode
  error?: string
}

export function FormFieldWrapper({
  label,
  description,
  required = false,
  className,
  children,
  error,
}: FormFieldWrapperProps) {
  return (
    <FormItem className={cn('space-y-2', className)}>
      {label && (
        <FormLabel className={cn(required && 'after:content-["*"] after:ml-0.5 after:text-red-500')}>
          {label}
        </FormLabel>
      )}
      <FormControl>
        {children}
      </FormControl>
      {description && (
        <FormDescription>
          {description}
        </FormDescription>
      )}
      <FormMessage />
    </FormItem>
  )
}

interface FormSectionProps {
  title: string
  description?: string
  children: ReactNode
  className?: string
}

export function FormSection({
  title,
  description,
  children,
  className,
}: FormSectionProps) {
  return (
    <div className={cn('space-y-6', className)}>
      <div className="space-y-2">
        <h3 className="text-lg font-medium">{title}</h3>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
      <div className="space-y-4">
        {children}
      </div>
    </div>
  )
}

interface FormGridProps {
  children: ReactNode
  columns?: 1 | 2 | 3 | 4
  className?: string
}

export function FormGrid({
  children,
  columns = 2,
  className,
}: FormGridProps) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  }

  return (
    <div className={cn('grid gap-4', gridCols[columns], className)}>
      {children}
    </div>
  )
}