'use client'

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { RefreshCw } from 'lucide-react'
import { cn } from '@/lib/utils'

interface SlugInputProps {
  value: string
  onChange: (value: string) => void
  sourceValue?: string
  prefix?: string
  disabled?: boolean
  className?: string
  placeholder?: string
  autoGenerate?: boolean
}

export function SlugInput({
  value,
  onChange,
  sourceValue,
  prefix = '/',
  disabled = false,
  className,
  placeholder = 'slug',
  autoGenerate = true,
}: SlugInputProps) {
  const [isManuallyEdited, setIsManuallyEdited] = useState(false)

  const generateSlug = (text: string): string => {
    return text
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/--+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
  }

  useEffect(() => {
    if (autoGenerate && sourceValue && !isManuallyEdited) {
      const newSlug = generateSlug(sourceValue)
      if (newSlug !== value) {
        onChange(newSlug)
      }
    }
  }, [sourceValue, autoGenerate, isManuallyEdited, value, onChange])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = generateSlug(e.target.value)
    onChange(newValue)
    setIsManuallyEdited(true)
  }

  const handleRegenerate = () => {
    if (sourceValue) {
      const newSlug = generateSlug(sourceValue)
      onChange(newSlug)
      setIsManuallyEdited(false)
    }
  }

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <div className="flex flex-1 items-center">
        <div className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground text-sm">
          {prefix}
        </div>
        <Input
          value={value}
          onChange={handleChange}
          placeholder={placeholder}
          disabled={disabled}
          className="rounded-l-none"
        />
      </div>
      {sourceValue && autoGenerate && (
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleRegenerate}
          disabled={disabled}
          title="Regenerate slug from title"
        >
          <RefreshCw className="h-4 w-4" />
        </Button>
      )}
    </div>
  )
}