'use client'

import { useMemo } from 'react'
import { useForm, DefaultValues } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Form, FormField } from '@/components/ui/form'
import { useToast } from '@/hooks/use-toast'
import { FormFieldWrapper, FormSection, FormGrid } from './shared/form-field-wrapper'
import { SlugInput } from './shared/slug-input'
import { ColorPicker } from './shared/color-picker'
import { CreateTagSchema, UpdateTagSchema, type CreateTagInput, type UpdateTagInput } from '@/lib/schemas'
import { Tag, Save, Plus } from 'lucide-react'

interface TagFormProps {
  initialData?: Partial<UpdateTagInput>
  mode?: 'create' | 'edit'
  onSubmit?: (data: CreateTagInput | UpdateTagInput) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  currentUserId?: string
}

export function TagForm({
  initialData,
  mode = 'create',
  onSubmit,
  onCancel,
  isLoading = false,
  currentUserId = '',
}: TagFormProps) {
  const { toast } = useToast()

  const defaultValues = useMemo(() => {
    if (mode === 'create') {
      return {
        name: initialData?.name || '',
        slug: initialData?.slug || '',
        color: initialData?.color || '#3B82F6',
        createdBy: initialData?.createdBy || currentUserId,
      };
    } else { // mode === 'edit'
      return {
        id: initialData?.id || '', // id is required for UpdateTagSchema
        name: initialData?.name,
        slug: initialData?.slug,
        color: initialData?.color || '#3B82F6',
        createdBy: initialData?.createdBy,
      };
    }
  }, [mode, initialData, currentUserId]);

  // Use conditional types based on mode to ensure type safety
  type FormValues = typeof mode extends 'create' ? CreateTagInput : UpdateTagInput;
  
  const form = useForm<FormValues>({
    resolver: zodResolver(mode === 'create' 
      ? CreateTagSchema as any 
      : UpdateTagSchema as any),
    defaultValues: defaultValues as DefaultValues<FormValues>,
  });

  // Watch fields that exist in both types, with proper type handling
  const watchedName = form.watch('name') as string | undefined
  const watchedColor = form.watch('color') as string | undefined

  const handleSubmit = async (data: FormValues) => {
    try {
      if (onSubmit) {
        await onSubmit(data)
        toast({
          title: 'Success!',
          description: `Tag ${mode === 'create' ? 'created' : 'updated'} successfully.`,
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to ${mode === 'create' ? 'create' : 'update'} tag. Please try again.`,
        variant: 'destructive',
      })
    }
  }

  const popularTagColors = [
    '#3B82F6', // Blue
    '#10B981', // Green
    '#F59E0B', // Yellow
    '#EF4444', // Red
    '#8B5CF6', // Purple
    '#06B6D4', // Cyan
    '#F97316', // Orange
    '#84CC16', // Lime
    '#EC4899', // Pink
    '#6B7280', // Gray
  ]

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Plus className="h-5 w-5" />
          {mode === 'create' ? 'Create New Tag' : 'Edit Tag'}
        </CardTitle>
        <CardDescription>
          {mode === 'create' 
            ? 'Create a new tag to categorize your content.'
            : 'Update tag information and appearance.'
          }
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
            <FormSection title="Basic Information">
              <FormGrid columns={1}>
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="Tag Name"
                      required
                      description="The display name for this tag"
                    >
                      <Input
                        placeholder="React"
                        {...field}
                        disabled={isLoading}
                        maxLength={50}
                      />
                    </FormFieldWrapper>
                  )}
                />

                <FormField
                  control={form.control}
                  name="slug"
                  render={({ field }) => (
                    <FormFieldWrapper
                      label="URL Slug"
                      required
                      description="The URL-friendly version of the name"
                    >
                      <SlugInput
                        value={field.value}
                        onChange={field.onChange}
                        sourceValue={watchedName}
                        prefix="/tag/"
                        disabled={isLoading}
                      />
                    </FormFieldWrapper>
                  )}
                />
              </FormGrid>
            </FormSection>

            <FormSection title="Appearance">
              <FormField
                control={form.control}
                name="color"
                render={({ field }) => (
                  <FormFieldWrapper
                    label="Tag Color"
                    description="Choose a color to represent this tag"
                  >
                    <ColorPicker
                      value={field.value}
                      onChange={field.onChange}
                      disabled={isLoading}
                      presetColors={popularTagColors}
                    />
                  </FormFieldWrapper>
                )}
              />

              {/* Color Presets */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Popular Tag Colors</label>
                <div className="flex flex-wrap gap-2">
                  {popularTagColors.map((color) => (
                    <button
                      key={color}
                      type="button"
                      className="w-8 h-8 rounded-full border-2 hover:scale-110 transition-transform"
                      style={{ 
                        backgroundColor: color,
                        borderColor: watchedColor === color ? '#000' : 'transparent'
                      }}
                      onClick={() => form.setValue('color' as keyof FormValues, color)}
                      disabled={isLoading}
                      title={color}
                    />
                  ))}
                </div>
              </div>
            </FormSection>

            {/* Preview Section */}
            <FormSection title="Preview">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Tag Badge Preview</label>
                  <div className="flex gap-2">
                    <Badge 
                      style={{ 
                        backgroundColor: watchedColor || '#3B82F6',
                        color: getContrastColor(watchedColor || '#3B82F6')
                      }}
                    >
                      {watchedName || 'Tag Name'}
                    </Badge>
                    <Badge 
                      variant="outline"
                      style={{ 
                        borderColor: watchedColor || '#3B82F6',
                        color: watchedColor || '#3B82F6'
                      }}
                    >
                      {watchedName || 'Tag Name'}
                    </Badge>
                  </div>
                </div>

                <div className="border rounded-lg p-4 bg-muted/30">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: watchedColor || '#3B82F6' }}
                      />
                      <span className="font-medium">
                        {watchedName || 'Tag Name'}
                      </span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      URL: /tag/{(form.watch('slug') as string | undefined) || 'tag-slug'}
                    </p>
                  </div>
                </div>
              </div>
            </FormSection>

            <div className="flex justify-end space-x-4 pt-6 border-t">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isLoading}>
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? 'Saving...' : mode === 'create' ? 'Create Tag' : 'Save Changes'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

// Helper function to determine if text should be light or dark based on background color
function getContrastColor(hexColor: string): string {
  // Remove # if present
  const hex = hexColor.replace('#', '')
  
  // Convert to RGB
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  
  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
  
  // Return black or white based on luminance
  return luminance > 0.5 ? '#000000' : '#FFFFFF'
}