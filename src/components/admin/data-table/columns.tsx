'use client'

import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { format } from 'date-fns'
import { ArrowUpDown, ExternalLink, Eye, FileText, Image as ImageIcon, Video, Music, File } from 'lucide-react'

// User columns
export interface User {
  id: string
  name: string
  email: string
  role: 'ADMIN' | 'EDITOR' | 'AUTHOR' | 'USER'
  image?: string
  emailVerified?: Date
  createdAt: Date
  updatedAt: Date
  _count?: {
    posts: number
    pages: number
  }
}

export const userColumns: ColumnDef<User>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-auto p-0 hover:bg-transparent"
      >
        User
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const user = row.original
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.image} alt={user.name} />
            <AvatarFallback>
              {user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{user.name}</div>
            <div className="text-sm text-muted-foreground">{user.email}</div>
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: 'role',
    header: 'Role',
    cell: ({ row }) => {
      const role = row.getValue('role') as string
      const roleColors = {
        ADMIN: 'bg-red-100 text-red-800',
        EDITOR: 'bg-blue-100 text-blue-800',
        AUTHOR: 'bg-green-100 text-green-800',
        USER: 'bg-gray-100 text-gray-800',
      }
      return (
        <Badge className={roleColors[role as keyof typeof roleColors]}>
          {role}
        </Badge>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'emailVerified',
    header: 'Status',
    cell: ({ row }) => {
      const verified = row.getValue('emailVerified')
      return (
        <Badge variant={verified ? 'default' : 'secondary'}>
          {verified ? 'Verified' : 'Unverified'}
        </Badge>
      )
    },
  },
  {
    accessorKey: '_count',
    header: 'Content',
    cell: ({ row }) => {
      const count = row.getValue('_count') as { posts: number; pages: number } | undefined
      if (!count) return <span className="text-muted-foreground">-</span>
      return (
        <div className="text-sm">
          <div>{count.posts} posts</div>
          <div className="text-muted-foreground">{count.pages} pages</div>
        </div>
      )
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-auto p-0 hover:bg-transparent"
      >
        Created
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const date = row.getValue('createdAt') as Date
      return <div className="text-sm">{format(date, 'MMM dd, yyyy')}</div>
    },
  },
]

// Post columns
export interface Post {
  id: string
  title: string
  slug: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED' | 'SCHEDULED'
  featuredImage?: string
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  author: {
    name: string
    image?: string
  }
  category?: {
    name: string
    color?: string
  }
  _count?: {
    comments: number
  }
}

export const postColumns: ColumnDef<Post>[] = [
  {
    accessorKey: 'title',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-auto p-0 hover:bg-transparent"
      >
        Post
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const post = row.original
      return (
        <div className="flex items-center space-x-3">
          {post.featuredImage ? (
            <img
              src={post.featuredImage}
              alt={post.title}
              className="h-10 w-10 rounded object-cover"
            />
          ) : (
            <div className="h-10 w-10 rounded bg-muted flex items-center justify-center">
              <FileText className="h-4 w-4 text-muted-foreground" />
            </div>
          )}
          <div className="min-w-0 flex-1">
            <div className="font-medium truncate">{post.title}</div>
            <div className="text-sm text-muted-foreground">/{post.slug}</div>
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status') as string
      const statusColors = {
        PUBLISHED: 'bg-green-100 text-green-800',
        DRAFT: 'bg-gray-100 text-gray-800',
        ARCHIVED: 'bg-red-100 text-red-800',
        SCHEDULED: 'bg-blue-100 text-blue-800',
      }
      return (
        <Badge className={statusColors[status as keyof typeof statusColors]}>
          {status}
        </Badge>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'author',
    header: 'Author',
    cell: ({ row }) => {
      const author = row.getValue('author') as { name: string; image?: string }
      return (
        <div className="flex items-center space-x-2">
          <Avatar className="h-6 w-6">
            <AvatarImage src={author.image} alt={author.name} />
            <AvatarFallback className="text-xs">
              {author.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <span className="text-sm">{author.name}</span>
        </div>
      )
    },
  },
  {
    accessorKey: 'category',
    header: 'Category',
    cell: ({ row }) => {
      const category = row.getValue('category') as { name: string; color?: string } | undefined
      if (!category) return <span className="text-muted-foreground">-</span>
      return (
        <div className="flex items-center space-x-2">
          {category.color && (
            <div
              className="h-3 w-3 rounded-full"
              style={{ backgroundColor: category.color }}
            />
          )}
          <span className="text-sm">{category.name}</span>
        </div>
      )
    },
  },
  {
    accessorKey: '_count',
    header: 'Comments',
    cell: ({ row }) => {
      const count = row.getValue('_count') as { comments: number } | undefined
      return <span className="text-sm">{count?.comments || 0}</span>
    },
  },
  {
    accessorKey: 'publishedAt',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-auto p-0 hover:bg-transparent"
      >
        Published
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const date = row.getValue('publishedAt') as Date | undefined
      if (!date) return <span className="text-muted-foreground">-</span>
      return <div className="text-sm">{format(date, 'MMM dd, yyyy')}</div>
    },
  },
]

// Category columns
export interface Category {
  id: string
  name: string
  slug: string
  description?: string
  color?: string
  image?: string
  parentId?: string
  createdAt: Date
  _count?: {
    posts: number
    children: number
  }
  parent?: {
    name: string
  }
}

export const categoryColumns: ColumnDef<Category>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-auto p-0 hover:bg-transparent"
      >
        Category
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const category = row.original
      return (
        <div className="flex items-center space-x-3">
          {category.image ? (
            <img
              src={category.image}
              alt={category.name}
              className="h-8 w-8 rounded object-cover"
            />
          ) : (
            <div
              className="h-8 w-8 rounded flex items-center justify-center"
              style={{ backgroundColor: category.color || '#e5e7eb' }}
            >
              <span className="text-xs font-medium text-white">
                {category.name.charAt(0).toUpperCase()}
              </span>
            </div>
          )}
          <div>
            <div className="font-medium">{category.name}</div>
            <div className="text-sm text-muted-foreground">/{category.slug}</div>
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: 'parent',
    header: 'Parent',
    cell: ({ row }) => {
      const parent = row.getValue('parent') as { name: string } | undefined
      return parent ? (
        <span className="text-sm">{parent.name}</span>
      ) : (
        <span className="text-muted-foreground">-</span>
      )
    },
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => {
      const description = row.getValue('description') as string | undefined
      return description ? (
        <div className="max-w-[200px] truncate text-sm">{description}</div>
      ) : (
        <span className="text-muted-foreground">-</span>
      )
    },
  },
  {
    accessorKey: '_count',
    header: 'Posts',
    cell: ({ row }) => {
      const count = row.getValue('_count') as { posts: number; children: number } | undefined
      return (
        <div className="text-sm">
          <div>{count?.posts || 0} posts</div>
          {(count?.children || 0) > 0 && (
            <div className="text-muted-foreground">{count?.children} subcategories</div>
          )}
        </div>
      )
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-auto p-0 hover:bg-transparent"
      >
        Created
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const date = row.getValue('createdAt') as Date
      return <div className="text-sm">{format(date, 'MMM dd, yyyy')}</div>
    },
  },
]

// Media columns
export interface Media {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  width?: number
  height?: number
  url: string
  thumbnailUrl?: string
  alt?: string
  type: 'IMAGE' | 'VIDEO' | 'AUDIO' | 'DOCUMENT' | 'OTHER'
  createdAt: Date
  uploader: {
    name: string
  }
}

export const mediaColumns: ColumnDef<Media>[] = [
  {
    accessorKey: 'filename',
    header: 'File',
    cell: ({ row }) => {
      const media = row.original
      const getIcon = () => {
        switch (media.type) {
          case 'IMAGE':
            return <ImageIcon className="h-4 w-4" />
          case 'VIDEO':
            return <Video className="h-4 w-4" />
          case 'AUDIO':
            return <Music className="h-4 w-4" />
          default:
            return <File className="h-4 w-4" />
        }
      }

      return (
        <div className="flex items-center space-x-3">
          {media.type === 'IMAGE' && media.thumbnailUrl ? (
            <img
              src={media.thumbnailUrl}
              alt={media.alt || media.originalName}
              className="h-10 w-10 rounded object-cover"
            />
          ) : (
            <div className="h-10 w-10 rounded bg-muted flex items-center justify-center">
              {getIcon()}
            </div>
          )}
          <div className="min-w-0 flex-1">
            <div className="font-medium truncate">{media.originalName}</div>
            <div className="text-sm text-muted-foreground">{media.filename}</div>
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: 'type',
    header: 'Type',
    cell: ({ row }) => {
      const type = row.getValue('type') as string
      const typeColors = {
        IMAGE: 'bg-green-100 text-green-800',
        VIDEO: 'bg-blue-100 text-blue-800',
        AUDIO: 'bg-purple-100 text-purple-800',
        DOCUMENT: 'bg-orange-100 text-orange-800',
        OTHER: 'bg-gray-100 text-gray-800',
      }
      return (
        <Badge className={typeColors[type as keyof typeof typeColors]}>
          {type}
        </Badge>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'size',
    header: 'Size',
    cell: ({ row }) => {
      const size = row.getValue('size') as number
      const formatSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes'
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
      }
      return <span className="text-sm">{formatSize(size)}</span>
    },
  },
  {
    accessorKey: 'dimensions',
    header: 'Dimensions',
    cell: ({ row }) => {
      const media = row.original
      if (media.width && media.height) {
        return <span className="text-sm">{media.width} × {media.height}</span>
      }
      return <span className="text-muted-foreground">-</span>
    },
  },
  {
    accessorKey: 'uploader',
    header: 'Uploaded by',
    cell: ({ row }) => {
      const uploader = row.getValue('uploader') as { name: string }
      return <span className="text-sm">{uploader.name}</span>
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-auto p-0 hover:bg-transparent"
      >
        Uploaded
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const date = row.getValue('createdAt') as Date
      return <div className="text-sm">{format(date, 'MMM dd, yyyy')}</div>
    },
  },
  {
    id: 'preview',
    header: 'Preview',
    cell: ({ row }) => {
      const media = row.original
      return (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => window.open(media.url, '_blank')}
        >
          <ExternalLink className="h-4 w-4" />
        </Button>
      )
    },
  },
]

// Comment columns
export interface Comment {
  id: string
  content: string
  approved: boolean
  createdAt: Date
  author: {
    name: string
    email: string
    image?: string
  }
  post: {
    title: string
    slug: string
  }
  parent?: {
    id: string
  }
  _count?: {
    replies: number
  }
}

export const commentColumns: ColumnDef<Comment>[] = [
  {
    accessorKey: 'content',
    header: 'Comment',
    cell: ({ row }) => {
      const comment = row.original
      return (
        <div className="space-y-2">
          <div className="max-w-[300px] text-sm">
            {comment.content.length > 100
              ? `${comment.content.substring(0, 100)}...`
              : comment.content}
          </div>
          <div className="flex items-center space-x-2">
            <Avatar className="h-5 w-5">
              <AvatarImage src={comment.author.image} alt={comment.author.name} />
              <AvatarFallback className="text-xs">
                {comment.author.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <span className="text-xs text-muted-foreground">{comment.author.name}</span>
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: 'post',
    header: 'Post',
    cell: ({ row }) => {
      const post = row.getValue('post') as { title: string; slug: string }
      return (
        <div className="max-w-[200px]">
          <div className="font-medium truncate text-sm">{post.title}</div>
          <div className="text-xs text-muted-foreground">/{post.slug}</div>
        </div>
      )
    },
  },
  {
    accessorKey: 'approved',
    header: 'Status',
    cell: ({ row }) => {
      const approved = row.getValue('approved') as boolean
      return (
        <Badge variant={approved ? 'default' : 'secondary'}>
          {approved ? 'Approved' : 'Pending'}
        </Badge>
      )
    },
    filterFn: (row, id, value) => {
      const approved = row.getValue(id) as boolean
      return value.includes(approved ? 'approved' : 'pending')
    },
  },
  {
    accessorKey: 'parent',
    header: 'Type',
    cell: ({ row }) => {
      const parent = row.getValue('parent') as { id: string } | undefined
      return (
        <Badge variant="outline">
          {parent ? 'Reply' : 'Comment'}
        </Badge>
      )
    },
  },
  {
    accessorKey: '_count',
    header: 'Replies',
    cell: ({ row }) => {
      const count = row.getValue('_count') as { replies: number } | undefined
      return <span className="text-sm">{count?.replies || 0}</span>
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-auto p-0 hover:bg-transparent"
      >
        Date
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const date = row.getValue('createdAt') as Date
      return <div className="text-sm">{format(date, 'MMM dd, yyyy')}</div>
    },
  },
]