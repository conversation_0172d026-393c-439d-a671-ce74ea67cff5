'use client'

import { X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import { useState } from 'react'

interface BulkActionsProps {
  selectedCount: number
  actions: Array<{
    value: string
    label: string
    destructive?: boolean
  }>
  onAction: (action: string) => void
  onClear: () => void
  className?: string
}

export function BulkActions({
  selectedCount,
  actions,
  onAction,
  onClear,
  className,
}: BulkActionsProps) {
  const [selectedAction, setSelectedAction] = useState<string>('')

  const handleActionChange = (value: string) => {
    setSelectedAction(value)
  }

  const handleApplyAction = () => {
    if (selectedAction) {
      onAction(selectedAction)
      setSelectedAction('')
    }
  }

  return (
    <Card className={className}>
      <CardContent className="p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Badge variant="secondary" className="px-3 py-1.5">
              {selectedCount} selected
            </Badge>
            
            <Select value={selectedAction} onValueChange={handleActionChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select action..." />
              </SelectTrigger>
              <SelectContent>
                {actions.map((action) => (
                  <SelectItem 
                    key={action.value} 
                    value={action.value}
                    className={action.destructive ? "text-destructive" : ""}
                  >
                    {action.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button 
              size="sm" 
              onClick={handleApplyAction}
              disabled={!selectedAction}
            >
              Apply
            </Button>
          </div>
          
          <Button variant="ghost" size="sm" onClick={onClear}>
            <X className="h-4 w-4 mr-2" />
            Clear selection
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}