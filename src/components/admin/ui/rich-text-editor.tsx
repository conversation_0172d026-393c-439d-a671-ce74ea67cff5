'use client';

import { useState, useRef } from 'react';
import { Bold, Italic, Underline, List, ListOrdered, Link, Image, AlignLeft, AlignCenter, AlignRight, Heading1, Heading2, Quote, Code, Undo, Redo } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  minHeight?: string;
  className?: string;
}

export const RichTextEditor: React.FC<RichTextEditorProps> = ({ 
  content, 
  onChange, 
  placeholder = 'Start typing...',
  minHeight = '300px',
  className
}) => {
  const [activeTab, setActiveTab] = useState<string>('edit');
  const [selection, setSelection] = useState<{ start: number; end: number }>({ start: 0, end: 0 });
  const editorRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Track selection in the editor
  const saveSelection = () => {
    if (textareaRef.current) {
      setSelection({
        start: textareaRef.current.selectionStart,
        end: textareaRef.current.selectionEnd
      });
    }
  };

  // Apply formatting to selected text
  const applyFormatting = (prefix: string, suffix: string = prefix) => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const selectedText = content.substring(start, end);
      
      const newContent = 
        content.substring(0, start) + 
        prefix + selectedText + suffix + 
        content.substring(end);
      
      onChange(newContent);
      
      // Set cursor position after formatting
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(
          start + prefix.length, 
          end + prefix.length
        );
      }, 0);
    }
  };

  // Format handlers
  const formatBold = () => applyFormatting('**');
  const formatItalic = () => applyFormatting('*');
  const formatUnderline = () => applyFormatting('<u>', '</u>');
  const formatH1 = () => applyFormatting('# ');
  const formatH2 = () => applyFormatting('## ');
  const formatQuote = () => applyFormatting('> ');
  const formatCode = () => applyFormatting('```\\n', '\\n```');
  const formatLink = () => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const selectedText = content.substring(start, end);
      
      const linkText = selectedText || 'Link text';
      const newContent = 
        content.substring(0, start) + 
        `[${linkText}](https://example.com)` + 
        content.substring(end);
      
      onChange(newContent);
    }
  };
  
  const formatImage = () => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      
      const newContent = 
        content.substring(0, start) + 
        '![Alt text](https://example.com/image.jpg)' + 
        content.substring(start);
      
      onChange(newContent);
    }
  };
  
  const formatList = () => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const selectedText = content.substring(start, end);
      
      // If there's selected text, format each line
      if (selectedText) {
        const lines = selectedText.split('\\n');
        const formattedLines = lines.map(line => `- ${line}`).join('\\n');
        
        const newContent = 
          content.substring(0, start) + 
          formattedLines + 
          content.substring(end);
        
        onChange(newContent);
      } else {
        // No selection, just insert a list item
        const newContent = 
          content.substring(0, start) + 
          '- ' + 
          content.substring(start);
        
        onChange(newContent);
      }
    }
  };
  
  const formatOrderedList = () => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const selectedText = content.substring(start, end);
      
      // If there's selected text, format each line
      if (selectedText) {
        const lines = selectedText.split('\\n');
        const formattedLines = lines.map((line, index) => `${index + 1}. ${line}`).join('\\n');
        
        const newContent = 
          content.substring(0, start) + 
          formattedLines + 
          content.substring(end);
        
        onChange(newContent);
      } else {
        // No selection, just insert a list item
        const newContent = 
          content.substring(0, start) + 
          '1. ' + 
          content.substring(start);
        
        onChange(newContent);
      }
    }
  };

  // Simple markdown to HTML conversion for preview
  const markdownToHtml = (markdown: string) => {
    let html = markdown
      // Headers
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      // Bold
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Italic
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Lists
      .replace(/^\s*- (.*$)/gm, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/g, '<ul>$1</ul>')
      .replace(/^\s*\d+\. (.*$)/gm, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/g, '<ol>$1</ol>')
      // Links
      .replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank">$1</a>')
      // Images
      .replace(/!\[(.*?)\]\((.*?)\)/g, '<img src="$2" alt="$1" style="max-width: 100%;" />')
      // Blockquotes
      .replace(/^\> (.*$)/gm, '<blockquote>$1</blockquote>')
      // Code blocks
      .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
      // Line breaks
      .replace(/\n/g, '<br />');
    
    return html;
  };

  return (
    <div className={cn("border rounded-md", className)}>
      {/* Toolbar */}
      <div className="flex flex-wrap items-center gap-1 p-2 border-b bg-muted/50">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={formatBold} 
          className="h-8 w-8 p-0"
          title="Bold"
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={formatItalic} 
          className="h-8 w-8 p-0"
          title="Italic"
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={formatUnderline} 
          className="h-8 w-8 p-0"
          title="Underline"
        >
          <Underline className="h-4 w-4" />
        </Button>
        <div className="w-px h-6 bg-border mx-1" />
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={formatH1} 
          className="h-8 w-8 p-0"
          title="Heading 1"
        >
          <Heading1 className="h-4 w-4" />
        </Button>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={formatH2} 
          className="h-8 w-8 p-0"
          title="Heading 2"
        >
          <Heading2 className="h-4 w-4" />
        </Button>
        <div className="w-px h-6 bg-border mx-1" />
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={formatList} 
          className="h-8 w-8 p-0"
          title="Bullet List"
        >
          <List className="h-4 w-4" />
        </Button>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={formatOrderedList} 
          className="h-8 w-8 p-0"
          title="Numbered List"
        >
          <ListOrdered className="h-4 w-4" />
        </Button>
        <div className="w-px h-6 bg-border mx-1" />
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={formatLink} 
          className="h-8 w-8 p-0"
          title="Insert Link"
        >
          <Link className="h-4 w-4" />
        </Button>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={formatImage} 
          className="h-8 w-8 p-0"
          title="Insert Image"
        >
          <Image className="h-4 w-4" />
        </Button>
        <div className="w-px h-6 bg-border mx-1" />
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={formatQuote} 
          className="h-8 w-8 p-0"
          title="Blockquote"
        >
          <Quote className="h-4 w-4" />
        </Button>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={formatCode} 
          className="h-8 w-8 p-0"
          title="Code Block"
        >
          <Code className="h-4 w-4" />
        </Button>
      </div>

      {/* Editor/Preview Tabs */}
      <Tabs defaultValue="edit" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="edit">Edit</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>
        <TabsContent value="edit" className="p-0">
          <textarea
            ref={textareaRef}
            className="w-full p-4 font-mono text-sm resize-y outline-none"
            style={{ minHeight }}
            value={content}
            onChange={(e) => onChange(e.target.value)}
            onSelect={saveSelection}
            placeholder={placeholder}
          />
        </TabsContent>
        <TabsContent value="preview" className="p-4 prose max-w-none">
          <div 
            dangerouslySetInnerHTML={{ __html: markdownToHtml(content) }} 
            className="prose max-w-none"
            style={{ minHeight }}
          />
        </TabsContent>
      </Tabs>

      {/* Character count */}
      <div className="p-2 text-xs text-muted-foreground border-t">
        {content.length} characters
      </div>
    </div>
  );
};
