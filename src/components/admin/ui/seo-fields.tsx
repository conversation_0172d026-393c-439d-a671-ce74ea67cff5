'use client';

import React from 'react';
import { Control } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';

interface SEOFieldsProps {
  control: Control<any>;
  title: string;
}

export const SEOFields: React.FC<SEOFieldsProps> = ({ control, title }) => {
  return (
    <div className="space-y-6">
      <FormField
        control={control}
        name="seo.title"
        render={({ field }) => (
          <FormItem>
            <FormLabel>SEO Title</FormLabel>
            <FormControl>
              <Input {...field} placeholder={title} />
            </FormControl>
            <FormDescription>Recommended length: 50-60 characters.</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="seo.description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Meta Description</FormLabel>
            <FormControl>
              <Textarea {...field} placeholder="Enter meta description..." />
            </FormControl>
            <FormDescription>Recommended length: 150-160 characters.</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
       <div className="flex space-x-8">
        <FormField
          control={control}
          name="seo.noindex"
          render={({ field }) => (
            <FormItem className="flex items-center space-x-2">
              <FormControl>
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
              <FormLabel>No Index</FormLabel>
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="seo.nofollow"
          render={({ field }) => (
            <FormItem className="flex items-center space-x-2">
              <FormControl>
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
              <FormLabel>No Follow</FormLabel>
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};
