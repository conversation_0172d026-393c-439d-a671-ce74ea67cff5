'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';

const AwesomeLoadingComponent = () => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
      <motion.div
        animate={{
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 1.5,
          ease: 'easeInOut',
          repeat: Infinity,
        }}
      >
        <Image
          src="/assets/img/logo/logo_icon.svg"
          alt="Loading..."
          width={60}
          height={60}
          priority
        />
      </motion.div>
    </div>
  );
};

export default AwesomeLoadingComponent;
