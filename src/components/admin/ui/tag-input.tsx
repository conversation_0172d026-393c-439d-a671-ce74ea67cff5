'use client'

import { useState, useRef } from 'react'
import { X, Plus } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { cn } from '@/lib/utils'

export interface Tag {
  id: string
  name: string
  color?: string
}

interface TagInputProps {
  tags: Tag[]
  selectedTags: Tag[]
  onTagSelect: (tag: Tag) => void
  onTagRemove: (tagId: string) => void
  onCreateTag?: (name: string) => void
  placeholder?: string
  disabled?: boolean
  maxTags?: number
  className?: string
}

export function TagInput({
  tags,
  selectedTags = [],
  onTagSelect,
  onTagRemove,
  onCreateTag,
  placeholder = 'Select tags...',
  disabled = false,
  maxTags,
  className,
}: TagInputProps) {
  const [open, setOpen] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)

  const handleSelect = (tag: Tag) => {
    if (maxTags && selectedTags.length >= maxTags) {
      return
    }
    
    if (!selectedTags.some(t => t.id === tag.id)) {
      onTagSelect(tag)
    }
    
    setInputValue('')
    setOpen(false)
  }

  const handleCreateTag = () => {
    if (!inputValue.trim() || !onCreateTag) return
    
    onCreateTag(inputValue.trim())
    setInputValue('')
  }

  const filteredTags = tags.filter(tag => 
    !selectedTags.some(selectedTag => selectedTag.id === tag.id) &&
    tag.name.toLowerCase().includes(inputValue.toLowerCase())
  )

  const canCreateNew = onCreateTag && 
    inputValue.trim() !== '' && 
    !tags.some(tag => tag.name.toLowerCase() === inputValue.toLowerCase())

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex flex-wrap gap-2 mb-2">
        {selectedTags.map(tag => (
          <Badge 
            key={tag.id} 
            variant="secondary"
            className={cn(
              "px-3 py-1 text-sm",
              tag.color && `bg-opacity-10 bg-${tag.color}-100 text-${tag.color}-700 border-${tag.color}-200`
            )}
          >
            {tag.name}
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 ml-2 hover:bg-transparent"
              onClick={() => onTagRemove(tag.id)}
              disabled={disabled}
            >
              <X className="h-3 w-3" />
              <span className="sr-only">Remove {tag.name}</span>
            </Button>
          </Badge>
        ))}
      </div>
      
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={disabled || (maxTags !== undefined && selectedTags.length >= maxTags)}
          >
            {placeholder}
            <Plus className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput 
              placeholder="Search tags..." 
              value={inputValue}
              onValueChange={setInputValue}
              ref={inputRef}
              className="h-9"
            />
            <CommandList>
              <CommandEmpty>
                {canCreateNew ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start px-2 py-1.5 text-sm"
                    onClick={handleCreateTag}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Create "{inputValue}"
                  </Button>
                ) : (
                  <p className="py-3 px-4 text-sm text-muted-foreground">No tags found.</p>
                )}
              </CommandEmpty>
              <CommandGroup>
                {filteredTags.map(tag => (
                  <CommandItem
                    key={tag.id}
                    value={tag.name}
                    onSelect={() => handleSelect(tag)}
                  >
                    <Badge 
                      variant="outline" 
                      className={cn(
                        "mr-2",
                        tag.color && `border-${tag.color}-200 text-${tag.color}-700`
                      )}
                    >
                      {tag.name}
                    </Badge>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}

// Simplified version that works with string arrays instead of Tag objects
interface SimpleTagInputProps {
  availableTags?: string[]
  selectedTags: string[]
  onTagSelect: (tag: string) => void
  onTagRemove: (tag: string) => void
  onCreateTag?: (tag: string) => void
  placeholder?: string
  disabled?: boolean
  maxTags?: number
  className?: string
}

export function SimpleTagInput({
  availableTags = [],
  selectedTags,
  onTagSelect,
  onTagRemove,
  onCreateTag,
  placeholder = 'Select tags...',
  disabled = false,
  maxTags,
  className,
}: SimpleTagInputProps) {
  const [open, setOpen] = useState(false)
  const [inputValue, setInputValue] = useState('')

  const handleSelect = (tag: string) => {
    if (maxTags && selectedTags.length >= maxTags) {
      return
    }
    
    if (!selectedTags.includes(tag)) {
      onTagSelect(tag)
    }
    
    setInputValue('')
    setOpen(false)
  }

  const handleCreateTag = () => {
    if (!inputValue.trim() || !onCreateTag) return
    
    onCreateTag(inputValue.trim())
    setInputValue('')
  }

  const filteredTags = availableTags.filter(tag => 
    !selectedTags.includes(tag) &&
    tag.toLowerCase().includes(inputValue.toLowerCase())
  )

  const canCreateNew = onCreateTag && 
    inputValue.trim() !== '' && 
    !availableTags.some(tag => tag.toLowerCase() === inputValue.toLowerCase())

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex flex-wrap gap-2 mb-2">
        {selectedTags.map(tag => (
          <Badge key={tag} variant="secondary" className="px-3 py-1 text-sm">
            {tag}
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 ml-2 hover:bg-transparent"
              onClick={() => onTagRemove(tag)}
              disabled={disabled}
            >
              <X className="h-3 w-3" />
              <span className="sr-only">Remove {tag}</span>
            </Button>
          </Badge>
        ))}
      </div>
      
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={disabled || (maxTags !== undefined && selectedTags.length >= maxTags)}
          >
            {placeholder}
            <Plus className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput 
              placeholder="Search tags..." 
              value={inputValue}
              onValueChange={setInputValue}
              className="h-9"
            />
            <CommandList>
              <CommandEmpty>
                {canCreateNew ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start px-2 py-1.5 text-sm"
                    onClick={handleCreateTag}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Create "{inputValue}"
                  </Button>
                ) : (
                  <p className="py-3 px-4 text-sm text-muted-foreground">No tags found.</p>
                )}
              </CommandEmpty>
              <CommandGroup>
                {filteredTags.map(tag => (
                  <CommandItem
                    key={tag}
                    value={tag}
                    onSelect={() => handleSelect(tag)}
                  >
                    {tag}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}