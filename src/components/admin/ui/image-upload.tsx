'use client';

import { useState, useRef } from 'react';
import { Upload, X, Image as ImageIcon, Link as LinkIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

interface ImageUploadProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  maxSizeMB?: number;
  aspectRatio?: string;
  allowedTypes?: string[];
}

export const ImageUpload: React.FC<ImageUploadProps> = ({ 
  value, 
  onChange, 
  placeholder = 'Enter image URL or upload a file...',
  className,
  maxSizeMB = 5,
  aspectRatio,
  allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
}) => {
  const [activeTab, setActiveTab] = useState<string>(value && value.startsWith('http') ? 'url' : 'upload');
  const [dragActive, setDragActive] = useState<boolean>(false);
  const [urlInput, setUrlInput] = useState<string>(value && value.startsWith('http') ? value : '');
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file upload
  const handleFileUpload = (file: File) => {
    setError(null);
    
    // Validate file type
    if (!allowedTypes.includes(file.type)) {
      setError(`Invalid file type. Allowed types: ${allowedTypes.map(t => t.replace('image/', '')).join(', ')}`);
      return;
    }
    
    // Validate file size
    if (file.size > maxSizeMB * 1024 * 1024) {
      setError(`File size exceeds the maximum allowed size (${maxSizeMB}MB)`);
      return;
    }
    
    // In a real application, you would upload the file to your server or cloud storage
    // For this example, we'll use a FileReader to create a data URL
    setIsUploading(true);
    
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      onChange(result);
      setIsUploading(false);
    };
    reader.onerror = () => {
      setError('Failed to read the file');
      setIsUploading(false);
    };
    reader.readAsDataURL(file);
  };

  // Handle drag events
  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  // Handle drop event
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  };

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileUpload(e.target.files[0]);
    }
  };

  // Handle URL input change
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUrlInput(e.target.value);
  };

  // Handle URL submission
  const handleUrlSubmit = () => {
    setError(null);
    if (!urlInput) {
      setError('Please enter a URL');
      return;
    }
    
    // Basic URL validation
    try {
      new URL(urlInput);
      onChange(urlInput);
    } catch (err) {
      setError('Please enter a valid URL');
    }
  };

  // Handle image removal
  const handleRemoveImage = () => {
    onChange('');
    setUrlInput('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // For demo purposes, set some sample images
  const sampleImages = [
    '/assets/img/placeholders/cover-1.jpg',
    '/assets/img/placeholders/cover-2.jpg',
    '/assets/img/placeholders/cover-3.jpg',
    '/assets/img/placeholders/avatar-1.jpg',
    '/assets/img/placeholders/avatar-2.jpg',
    '/assets/img/placeholders/avatar-3.jpg',
  ];

  return (
    <div className={cn("space-y-4", className)}>
      {/* Preview area */}
      {value && (
        <div className="relative rounded-md overflow-hidden border bg-muted/20">
          <img 
            src={value} 
            alt="Preview" 
            className="w-full h-auto object-cover max-h-[300px]"
            style={aspectRatio ? { aspectRatio } : undefined}
          />
          <Button
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 rounded-full opacity-90"
            onClick={handleRemoveImage}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}
      
      {/* Upload interface */}
      {!value && (
        <Tabs defaultValue={activeTab} value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload">Upload</TabsTrigger>
            <TabsTrigger value="url">URL</TabsTrigger>
          </TabsList>
          
          <TabsContent value="upload" className="space-y-4">
            {/* File drop area */}
            <div
              className={cn(
                "border-2 border-dashed rounded-md p-6 flex flex-col items-center justify-center cursor-pointer transition-colors",
                dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/20 hover:border-muted-foreground/30",
                isUploading && "opacity-50 pointer-events-none"
              )}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="h-10 w-10 text-muted-foreground mb-2" />
              <p className="text-sm text-center font-medium">
                Drag and drop an image, or click to browse
              </p>
              <p className="text-xs text-muted-foreground mt-1 text-center">
                Supported formats: {allowedTypes.map(t => t.replace('image/', '')).join(', ')}
                <br />
                Max size: {maxSizeMB}MB
              </p>
              <input
                ref={fileInputRef}
                type="file"
                accept={allowedTypes.join(',')}
                className="hidden"
                onChange={handleFileInputChange}
                disabled={isUploading}
              />
            </div>
            
            {/* Sample images */}
            <div className="space-y-2">
              <Label>Sample Images</Label>
              <div className="grid grid-cols-3 gap-2">
                {sampleImages.map((img, index) => (
                  <button
                    key={index}
                    type="button"
                    className="border rounded-md overflow-hidden hover:border-primary transition-colors"
                    onClick={() => onChange(img)}
                  >
                    <img src={img} alt={`Sample ${index + 1}`} className="w-full h-20 object-cover" />
                  </button>
                ))}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="url" className="space-y-4">
            <div className="flex space-x-2">
              <div className="flex-1">
                <Input
                  type="url"
                  value={urlInput}
                  onChange={handleUrlChange}
                  placeholder={placeholder}
                  className="flex-1"
                />
              </div>
              <Button onClick={handleUrlSubmit} type="button">
                <LinkIcon className="h-4 w-4 mr-2" />
                Set URL
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              Enter the URL of an image from the web
            </p>
          </TabsContent>
        </Tabs>
      )}
      
      {/* Error message */}
      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
    </div>
  );
};
