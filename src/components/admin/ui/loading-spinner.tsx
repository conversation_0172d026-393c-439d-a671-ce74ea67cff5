'use client';

import { cva, type VariantProps } from 'class-variance-authority';

const spinnerVariants = cva('animate-spin rounded-full border-solid border-current border-t-transparent',
  {
    variants: {
      size: {
        sm: 'h-4 w-4 border-2',
        md: 'h-8 w-8 border-4',
        lg: 'h-12 w-12 border-4',
      },
    },
    defaultVariants: {
      size: 'md',
    },
  }
);

interface LoadingSpinnerProps extends VariantProps<typeof spinnerVariants> {}

export const LoadingSpinner = ({ size }: LoadingSpinnerProps) => {
  return <div className={spinnerVariants({ size })} />;
};
