'use client';

import { useState } from 'react';
import { PlusCircle, Trash2, GripVertical, ChevronUp, ChevronDown, <PERSON><PERSON>s, Copy, Edit, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { RichTextEditor } from './rich-text-editor';
import { ImageUpload } from './image-upload';

// Block types
export type BlockType = 
  | 'heading'
  | 'paragraph'
  | 'image'
  | 'gallery'
  | 'video'
  | 'quote'
  | 'callout'
  | 'button'
  | 'divider'
  | 'spacer'
  | 'columns'
  | 'list'
  | 'html';

// Base block interface
export interface Block {
  id: string;
  type: BlockType;
  settings?: Record<string, any>;
}

// Specific block interfaces
export interface HeadingBlock extends Block {
  type: 'heading';
  content: string;
  settings?: {
    level: 1 | 2 | 3 | 4 | 5 | 6;
    alignment?: 'left' | 'center' | 'right';
  };
}

export interface ParagraphBlock extends Block {
  type: 'paragraph';
  content: string;
  settings?: {
    alignment?: 'left' | 'center' | 'right' | 'justify';
    size?: 'small' | 'medium' | 'large';
  };
}

export interface ImageBlock extends Block {
  type: 'image';
  url: string;
  alt?: string;
  settings?: {
    width?: 'small' | 'medium' | 'large' | 'full';
    alignment?: 'left' | 'center' | 'right';
    caption?: string;
    link?: string;
  };
}

export interface GalleryBlock extends Block {
  type: 'gallery';
  images: Array<{
    url: string;
    alt?: string;
    caption?: string;
  }>;
  settings?: {
    columns?: 2 | 3 | 4;
    gap?: 'small' | 'medium' | 'large';
  };
}

export interface VideoBlock extends Block {
  type: 'video';
  url: string;
  settings?: {
    autoplay?: boolean;
    loop?: boolean;
    muted?: boolean;
    controls?: boolean;
    width?: 'small' | 'medium' | 'large' | 'full';
  };
}

export interface QuoteBlock extends Block {
  type: 'quote';
  content: string;
  citation?: string;
  settings?: {
    style?: 'simple' | 'bordered' | 'large';
  };
}

export interface CalloutBlock extends Block {
  type: 'callout';
  content: string;
  settings?: {
    type?: 'info' | 'warning' | 'error' | 'success';
    icon?: string;
  };
}

export interface ButtonBlock extends Block {
  type: 'button';
  text: string;
  url: string;
  settings?: {
    style?: 'primary' | 'secondary' | 'outline' | 'ghost';
    size?: 'small' | 'medium' | 'large';
    alignment?: 'left' | 'center' | 'right';
    openInNewTab?: boolean;
  };
}

export interface DividerBlock extends Block {
  type: 'divider';
  settings?: {
    style?: 'solid' | 'dashed' | 'dotted';
    width?: 'narrow' | 'medium' | 'wide' | 'full';
  };
}

export interface SpacerBlock extends Block {
  type: 'spacer';
  settings?: {
    height?: 'small' | 'medium' | 'large' | 'custom';
    customHeight?: number;
  };
}

export interface ColumnsBlock extends Block {
  type: 'columns';
  columns: Block[][];
  settings?: {
    count?: 2 | 3 | 4;
    gap?: 'small' | 'medium' | 'large';
    stackOnMobile?: boolean;
  };
}

export interface ListBlock extends Block {
  type: 'list';
  items: string[];
  settings?: {
    type?: 'bullet' | 'numbered' | 'check';
    spacing?: 'tight' | 'normal' | 'loose';
  };
}

export interface HtmlBlock extends Block {
  type: 'html';
  content: string;
}

// Union type for all block types
export type BlockUnion = 
  | HeadingBlock
  | ParagraphBlock
  | ImageBlock
  | GalleryBlock
  | VideoBlock
  | QuoteBlock
  | CalloutBlock
  | ButtonBlock
  | DividerBlock
  | SpacerBlock
  | ColumnsBlock
  | ListBlock
  | HtmlBlock;

// Block templates for adding new blocks
const blockTemplates = {
  heading: {
    type: 'heading',
    content: '',
    settings: {
      level: 2,
      alignment: 'left'
    }
  } as Omit<HeadingBlock, 'id'>,
  paragraph: {
    type: 'paragraph',
    content: '',
    settings: {
      alignment: 'left',
      size: 'medium'
    }
  } as Omit<ParagraphBlock, 'id'>,
  image: {
    type: 'image',
    url: '',
    alt: '',
    settings: {
      width: 'full',
      alignment: 'center'
    }
  } as Omit<ImageBlock, 'id'>,
  gallery: {
    type: 'gallery',
    images: [],
    settings: {
      columns: 3,
      gap: 'medium'
    }
  } as Omit<GalleryBlock, 'id'>,
  video: {
    type: 'video',
    url: '',
    settings: {
      autoplay: false,
      loop: false,
      muted: false,
      controls: true,
      width: 'full'
    }
  } as Omit<VideoBlock, 'id'>,
  quote: {
    type: 'quote',
    content: '',
    citation: '',
    settings: {
      style: 'simple'
    }
  } as Omit<QuoteBlock, 'id'>,
  callout: {
    type: 'callout',
    content: '',
    settings: {
      type: 'info',
      icon: 'info'
    }
  } as Omit<CalloutBlock, 'id'>,
  button: {
    type: 'button',
    text: 'Click me',
    url: '#',
    settings: {
      style: 'primary',
      size: 'medium',
      alignment: 'center',
      openInNewTab: false
    }
  } as Omit<ButtonBlock, 'id'>,
  divider: {
    type: 'divider',
    settings: {
      style: 'solid',
      width: 'full'
    }
  } as Omit<DividerBlock, 'id'>,
  spacer: {
    type: 'spacer',
    settings: {
      height: 'medium'
    }
  } as Omit<SpacerBlock, 'id'>,
  columns: {
    type: 'columns',
    columns: [[], []],
    settings: {
      count: 2,
      gap: 'medium',
      stackOnMobile: true
    }
  } as Omit<ColumnsBlock, 'id'>,
  list: {
    type: 'list',
    items: ['Item 1', 'Item 2', 'Item 3'],
    settings: {
      type: 'bullet',
      spacing: 'normal'
    }
  } as Omit<ListBlock, 'id'>,
  html: {
    type: 'html',
    content: '<!-- Custom HTML here -->'
  } as Omit<HtmlBlock, 'id'>
} as const;

// Helper to generate a unique ID
const generateId = () => Math.random().toString(36).substring(2, 9);

// Block editor component
interface BlockEditorProps {
  blocks: BlockUnion[];
  onChange: (blocks: BlockUnion[]) => void;
  className?: string;
}

export const BlockEditor: React.FC<BlockEditorProps> = ({ 
  blocks, 
  onChange,
  className
}) => {
  const [activeBlockId, setActiveBlockId] = useState<string | null>(null);
  const [showBlockSelector, setShowBlockSelector] = useState(false);
  const [blockSelectorPosition, setBlockSelectorPosition] = useState<number | null>(null);
  const [editMode, setEditMode] = useState<'edit' | 'preview'>('edit');

  // Add a new block
  const addBlock = (type: BlockType, position?: number) => {
    const newBlock = {
      ...blockTemplates[type],
      id: generateId()
    } as BlockUnion;
    
    const newBlocks = [...blocks];
    if (position !== undefined) {
      newBlocks.splice(position, 0, newBlock);
    } else {
      newBlocks.push(newBlock);
    }
    
    onChange(newBlocks as BlockUnion[]);
    setActiveBlockId(newBlock.id);
    setShowBlockSelector(false);
  };

  // Update a block
  const updateBlock = (id: string, updates: Partial<BlockUnion>) => {
    const newBlocks = blocks.map(block => 
      block.id === id ? { ...block, ...updates } as BlockUnion : block
    );
    onChange(newBlocks as BlockUnion[]);
  };

  // Delete a block
  const deleteBlock = (id: string) => {
    const newBlocks = blocks.filter(block => block.id !== id);
    onChange(newBlocks as BlockUnion[]);
    setActiveBlockId(null);
  };

  // Duplicate a block
  const duplicateBlock = (id: string) => {
    const blockToDuplicate = blocks.find(block => block.id === id);
    if (!blockToDuplicate) return;
    
    const duplicatedBlock = {
      ...blockToDuplicate,
      id: generateId()
    } as BlockUnion;
    
    const index = blocks.findIndex(block => block.id === id);
    const newBlocks = [...blocks];
    newBlocks.splice(index + 1, 0, duplicatedBlock);
    
    onChange(newBlocks as BlockUnion[]);
  };

  // Move a block up
  const moveBlockUp = (id: string) => {
    const index = blocks.findIndex(block => block.id === id);
    if (index <= 0) return;
    
    const newBlocks = [...blocks];
    const temp = newBlocks[index];
    newBlocks[index] = newBlocks[index - 1];
    newBlocks[index - 1] = temp;
    
    onChange(newBlocks as BlockUnion[]);
  };

  // Move a block down
  const moveBlockDown = (id: string) => {
    const index = blocks.findIndex(block => block.id === id);
    if (index >= blocks.length - 1) return;
    
    const newBlocks = [...blocks];
    const temp = newBlocks[index];
    newBlocks[index] = newBlocks[index + 1];
    newBlocks[index + 1] = temp;
    
    onChange(newBlocks as BlockUnion[]);
  };

  // Manual reordering instead of drag and drop
  const reorderBlocks = (sourceIndex: number, destinationIndex: number) => {
    if (sourceIndex === destinationIndex) return;
    
    const newBlocks = [...blocks];
    const [removed] = newBlocks.splice(sourceIndex, 1);
    newBlocks.splice(destinationIndex, 0, removed);
    
    onChange(newBlocks as BlockUnion[]);
  };

  // Show block selector at a specific position
  const showBlockSelectorAt = (position: number) => {
    setBlockSelectorPosition(position);
    setShowBlockSelector(true);
  };

  // Render block content based on type
  const renderBlockContent = (block: BlockUnion) => {
    const isActive = activeBlockId === block.id;
    
    switch (block.type) {
      case 'heading':
        return (
          <div className="w-full">
            {isActive ? (
              <div className="space-y-2">
                <Input
                  value={(block as HeadingBlock).content}
                  onChange={(e) => updateBlock(block.id, { content: e.target.value })}
                  placeholder="Heading text..."
                  className="font-bold text-xl"
                />
                <div className="flex items-center gap-4">
                  <div className="space-y-1">
                    <Label htmlFor={`heading-level-${block.id}`}>Level</Label>
                    <Select
                      value={String((block as HeadingBlock).settings?.level || 2)}
                      onValueChange={(value) => updateBlock(block.id, { 
                        settings: { 
                          ...(block as HeadingBlock).settings,
                          level: Number(value) as 1 | 2 | 3 | 4 | 5 | 6
                        } 
                      })}
                    >
                      <SelectTrigger id={`heading-level-${block.id}`} className="w-24">
                        <SelectValue placeholder="Level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">H1</SelectItem>
                        <SelectItem value="2">H2</SelectItem>
                        <SelectItem value="3">H3</SelectItem>
                        <SelectItem value="4">H4</SelectItem>
                        <SelectItem value="5">H5</SelectItem>
                        <SelectItem value="6">H6</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor={`heading-align-${block.id}`}>Alignment</Label>
                    <Select
                      value={(block as HeadingBlock).settings?.alignment || 'left'}
                      onValueChange={(value) => updateBlock(block.id, { 
                        settings: { 
                          ...(block as HeadingBlock).settings,
                          alignment: value as 'left' | 'center' | 'right'
                        } 
                      })}
                    >
                      <SelectTrigger id={`heading-align-${block.id}`} className="w-24">
                        <SelectValue placeholder="Align" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            ) : (
              <div 
                className={cn(
                  "cursor-pointer py-2",
                  (block as HeadingBlock).settings?.alignment === 'center' && "text-center",
                  (block as HeadingBlock).settings?.alignment === 'right' && "text-right"
                )}
                onClick={() => setActiveBlockId(block.id)}
              >
                {(block as HeadingBlock).settings?.level === 1 && (
                  <h1 className="text-3xl font-bold">{(block as HeadingBlock).content || "Heading 1"}</h1>
                )}
                {(block as HeadingBlock).settings?.level === 2 && (
                  <h2 className="text-2xl font-bold">{(block as HeadingBlock).content || "Heading 2"}</h2>
                )}
                {(block as HeadingBlock).settings?.level === 3 && (
                  <h3 className="text-xl font-bold">{(block as HeadingBlock).content || "Heading 3"}</h3>
                )}
                {(block as HeadingBlock).settings?.level === 4 && (
                  <h4 className="text-lg font-bold">{(block as HeadingBlock).content || "Heading 4"}</h4>
                )}
                {(block as HeadingBlock).settings?.level === 5 && (
                  <h5 className="text-base font-bold">{(block as HeadingBlock).content || "Heading 5"}</h5>
                )}
                {(block as HeadingBlock).settings?.level === 6 && (
                  <h6 className="text-sm font-bold">{(block as HeadingBlock).content || "Heading 6"}</h6>
                )}
              </div>
            )}
          </div>
        );
        
      case 'paragraph':
        return (
          <div className="w-full">
            {isActive ? (
              <div className="space-y-2">
                <RichTextEditor
                  content={(block as ParagraphBlock).content}
                  onChange={(content) => updateBlock(block.id, { content })}
                  placeholder="Start typing paragraph content..."
                />
                <div className="flex items-center gap-4">
                  <div className="space-y-1">
                    <Label htmlFor={`paragraph-align-${block.id}`}>Alignment</Label>
                    <Select
                      value={(block as ParagraphBlock).settings?.alignment || 'left'}
                      onValueChange={(value) => updateBlock(block.id, { 
                        settings: { 
                          ...(block as ParagraphBlock).settings,
                          alignment: value as 'left' | 'center' | 'right' | 'justify'
                        } 
                      })}
                    >
                      <SelectTrigger id={`paragraph-align-${block.id}`} className="w-24">
                        <SelectValue placeholder="Align" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                        <SelectItem value="justify">Justify</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor={`paragraph-size-${block.id}`}>Size</Label>
                    <Select
                      value={(block as ParagraphBlock).settings?.size || 'medium'}
                      onValueChange={(value) => updateBlock(block.id, { 
                        settings: { 
                          ...(block as ParagraphBlock).settings,
                          size: value as 'small' | 'medium' | 'large'
                        } 
                      })}
                    >
                      <SelectTrigger id={`paragraph-size-${block.id}`} className="w-24">
                        <SelectValue placeholder="Size" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">Small</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="large">Large</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            ) : (
              <div 
                className={cn(
                  "cursor-pointer py-2",
                  (block as ParagraphBlock).settings?.alignment === 'center' && "text-center",
                  (block as ParagraphBlock).settings?.alignment === 'right' && "text-right",
                  (block as ParagraphBlock).settings?.alignment === 'justify' && "text-justify",
                  (block as ParagraphBlock).settings?.size === 'small' && "text-sm",
                  (block as ParagraphBlock).settings?.size === 'large' && "text-lg"
                )}
                onClick={() => setActiveBlockId(block.id)}
              >
                {(block as ParagraphBlock).content || "Click to edit this paragraph..."}
              </div>
            )}
          </div>
        );
        
      case 'image':
        return (
          <div className="w-full">
            {isActive ? (
              <div className="space-y-4">
                <ImageUpload
                  value={(block as ImageBlock).url}
                  onChange={(url) => updateBlock(block.id, { url })}
                  placeholder="Enter image URL or upload..."
                />
                <div className="space-y-2">
                  <Label htmlFor={`image-alt-${block.id}`}>Alt Text</Label>
                  <Input
                    id={`image-alt-${block.id}`}
                    value={(block as ImageBlock).alt || ''}
                    onChange={(e) => updateBlock(block.id, { alt: e.target.value })}
                    placeholder="Describe the image for accessibility..."
                  />
                </div>
                <div className="flex items-center gap-4">
                  <div className="space-y-1">
                    <Label htmlFor={`image-width-${block.id}`}>Width</Label>
                    <Select
                      value={(block as ImageBlock).settings?.width || 'full'}
                      onValueChange={(value) => updateBlock(block.id, { 
                        settings: { 
                          ...(block as ImageBlock).settings,
                          width: value as 'small' | 'medium' | 'large' | 'full'
                        } 
                      })}
                    >
                      <SelectTrigger id={`image-width-${block.id}`} className="w-24">
                        <SelectValue placeholder="Width" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">Small</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="large">Large</SelectItem>
                        <SelectItem value="full">Full</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor={`image-align-${block.id}`}>Alignment</Label>
                    <Select
                      value={(block as ImageBlock).settings?.alignment || 'center'}
                      onValueChange={(value) => updateBlock(block.id, { 
                        settings: { 
                          ...(block as ImageBlock).settings,
                          alignment: value as 'left' | 'center' | 'right'
                        } 
                      })}
                    >
                      <SelectTrigger id={`image-align-${block.id}`} className="w-24">
                        <SelectValue placeholder="Align" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor={`image-caption-${block.id}`}>Caption</Label>
                  <Input
                    id={`image-caption-${block.id}`}
                    value={(block as ImageBlock).settings?.caption || ''}
                    onChange={(e) => updateBlock(block.id, { 
                      settings: { 
                        ...(block as ImageBlock).settings,
                        caption: e.target.value
                      } 
                    })}
                    placeholder="Image caption (optional)..."
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor={`image-link-${block.id}`}>Link URL</Label>
                  <Input
                    id={`image-link-${block.id}`}
                    value={(block as ImageBlock).settings?.link || ''}
                    onChange={(e) => updateBlock(block.id, { 
                      settings: { 
                        ...(block as ImageBlock).settings,
                        link: e.target.value
                      } 
                    })}
                    placeholder="Link URL (optional)..."
                  />
                </div>
              </div>
            ) : (
              <div 
                className={cn(
                  "cursor-pointer py-2",
                  (block as ImageBlock).settings?.alignment === 'center' && "flex justify-center",
                  (block as ImageBlock).settings?.alignment === 'right' && "flex justify-end"
                )}
                onClick={() => setActiveBlockId(block.id)}
              >
                <div className={cn(
                  "overflow-hidden rounded-md border",
                  (block as ImageBlock).settings?.width === 'small' && "max-w-xs",
                  (block as ImageBlock).settings?.width === 'medium' && "max-w-md",
                  (block as ImageBlock).settings?.width === 'large' && "max-w-lg",
                  (block as ImageBlock).settings?.width === 'full' && "w-full"
                )}>
                  {(block as ImageBlock).url ? (
                    <>
                      <img 
                        src={(block as ImageBlock).url} 
                        alt={(block as ImageBlock).alt || ''} 
                        className="w-full h-auto object-cover"
                      />
                      {(block as ImageBlock).settings?.caption && (
                        <div className="p-2 text-sm text-center text-muted-foreground">
                          {(block as ImageBlock).settings?.caption}
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="flex items-center justify-center h-40 bg-muted">
                      <p className="text-muted-foreground">Click to add an image</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        );
        
      // For simplicity, we'll implement just a few block types fully
      // Other block types would follow similar patterns
      
      case 'button':
        return (
          <div className="w-full">
            {isActive ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor={`button-text-${block.id}`}>Button Text</Label>
                  <Input
                    id={`button-text-${block.id}`}
                    value={(block as ButtonBlock).text}
                    onChange={(e) => updateBlock(block.id, { text: e.target.value })}
                    placeholder="Button text..."
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor={`button-url-${block.id}`}>URL</Label>
                  <Input
                    id={`button-url-${block.id}`}
                    value={(block as ButtonBlock).url}
                    onChange={(e) => updateBlock(block.id, { url: e.target.value })}
                    placeholder="https://example.com"
                  />
                </div>
                <div className="flex items-center gap-4">
                  <div className="space-y-1">
                    <Label htmlFor={`button-style-${block.id}`}>Style</Label>
                    <Select
                      value={(block as ButtonBlock).settings?.style || 'primary'}
                      onValueChange={(value) => updateBlock(block.id, { 
                        settings: { 
                          ...(block as ButtonBlock).settings,
                          style: value as 'primary' | 'secondary' | 'outline' | 'ghost'
                        } 
                      })}
                    >
                      <SelectTrigger id={`button-style-${block.id}`} className="w-28">
                        <SelectValue placeholder="Style" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="primary">Primary</SelectItem>
                        <SelectItem value="secondary">Secondary</SelectItem>
                        <SelectItem value="outline">Outline</SelectItem>
                        <SelectItem value="ghost">Ghost</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor={`button-size-${block.id}`}>Size</Label>
                    <Select
                      value={(block as ButtonBlock).settings?.size || 'medium'}
                      onValueChange={(value) => updateBlock(block.id, { 
                        settings: { 
                          ...(block as ButtonBlock).settings,
                          size: value as 'small' | 'medium' | 'large'
                        } 
                      })}
                    >
                      <SelectTrigger id={`button-size-${block.id}`} className="w-28">
                        <SelectValue placeholder="Size" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">Small</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="large">Large</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="space-y-1">
                    <Label htmlFor={`button-align-${block.id}`}>Alignment</Label>
                    <Select
                      value={(block as ButtonBlock).settings?.alignment || 'center'}
                      onValueChange={(value) => updateBlock(block.id, { 
                        settings: { 
                          ...(block as ButtonBlock).settings,
                          alignment: value as 'left' | 'center' | 'right'
                        } 
                      })}
                    >
                      <SelectTrigger id={`button-align-${block.id}`} className="w-28">
                        <SelectValue placeholder="Alignment" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id={`button-newtab-${block.id}`}
                      checked={(block as ButtonBlock).settings?.openInNewTab || false}
                      onCheckedChange={(checked) => updateBlock(block.id, { 
                        settings: { 
                          ...(block as ButtonBlock).settings,
                          openInNewTab: checked
                        } 
                      })}
                    />
                    <Label htmlFor={`button-newtab-${block.id}`}>Open in new tab</Label>
                  </div>
                </div>
              </div>
            ) : (
              <div 
                className={cn(
                  "cursor-pointer py-2",
                  (block as ButtonBlock).settings?.alignment === 'center' && "flex justify-center",
                  (block as ButtonBlock).settings?.alignment === 'right' && "flex justify-end"
                )}
                onClick={() => setActiveBlockId(block.id)}
              >
                <Button
                  variant={(block as ButtonBlock).settings?.style || 'primary'}
                  size={(block as ButtonBlock).settings?.size || 'medium'}
                  className="pointer-events-none"
                >
                  {(block as ButtonBlock).text || "Button"}
                </Button>
              </div>
            )}
          </div>
        );
        
      // For other block types, we'll use simplified placeholders
      default:
        return (
          <div 
            className="w-full cursor-pointer py-2 px-4 border border-dashed rounded-md bg-muted/50 text-center"
            onClick={() => setActiveBlockId(block.id)}
          >
            <p className="text-muted-foreground">{block.type.charAt(0).toUpperCase() + block.type.slice(1)} Block</p>
            <p className="text-xs text-muted-foreground">Click to edit</p>
          </div>
        );
    }
  };

  // Render block with controls
  const renderBlock = (block: BlockUnion, index: number) => {
    const isActive = activeBlockId === block.id;
    
    return (
      <div 
        className={cn(
          "relative group border rounded-md transition-all",
          isActive ? "border-primary shadow-sm" : "border-transparent hover:border-muted"
        )}
      >
        {/* Block controls */}
        {editMode === 'edit' && (
          <div className={cn(
            "absolute -left-10 top-0 bottom-0 flex flex-col items-center justify-center opacity-0 transition-opacity",
            (isActive || blocks.length <= 5) && "opacity-100",
            "group-hover:opacity-100"
          )}>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => showBlockSelectorAt(index)}
              title="Add block above"
            >
              <PlusCircle className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => moveBlockUp(block.id)}
              disabled={index === 0}
              title="Move up"
            >
              <ChevronUp className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => moveBlockDown(block.id)}
              disabled={index === blocks.length - 1}
              title="Move down"
            >
              <ChevronDown className="h-4 w-4" />
            </Button>
          </div>
        )}
        
        {/* Block content */}
        <div className="p-4">
          {renderBlockContent(block)}
        </div>
        
        {/* Block toolbar */}
        {editMode === 'edit' && (
          <div className={cn(
            "absolute top-2 right-2 flex items-center gap-1 opacity-0 transition-opacity",
            isActive && "opacity-100",
            "group-hover:opacity-100"
          )}>
            <Button
              variant="ghost"
              size="sm"
              className="h-7 w-7 p-0"
              onClick={() => duplicateBlock(block.id)}
              title="Duplicate"
            >
              <Copy className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-7 w-7 p-0"
              onClick={() => deleteBlock(block.id)}
              title="Delete"
            >
              <Trash2 className="h-3.5 w-3.5" />
            </Button>
          </div>
        )}
        
        {/* Add block button below */}
        {editMode === 'edit' && index === blocks.length - 1 && (
          <div className="flex justify-center -mb-4">
            <Button
              variant="outline"
              size="sm"
              className="h-8 rounded-full"
              onClick={() => showBlockSelectorAt(index + 1)}
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              Add Block
            </Button>
          </div>
        )}
      </div>
    );
  };

  // Block selector dialog
  const BlockSelector = ({ position }: { position: number }) => {
    const blockTypes: Array<{ type: BlockType; label: string; description: string }> = [
      { type: 'heading', label: 'Heading', description: 'Section heading with multiple levels' },
      { type: 'paragraph', label: 'Paragraph', description: 'Text content with formatting options' },
      { type: 'image', label: 'Image', description: 'Single image with caption and link options' },
      { type: 'gallery', label: 'Gallery', description: 'Multiple images in a grid layout' },
      { type: 'video', label: 'Video', description: 'Embed a video from URL' },
      { type: 'quote', label: 'Quote', description: 'Highlighted quote with optional citation' },
      { type: 'callout', label: 'Callout', description: 'Highlighted information box' },
      { type: 'button', label: 'Button', description: 'Call to action button' },
      { type: 'divider', label: 'Divider', description: 'Visual separator between sections' },
      { type: 'spacer', label: 'Spacer', description: 'Add vertical space between blocks' },
      { type: 'columns', label: 'Columns', description: 'Multi-column layout' },
      { type: 'list', label: 'List', description: 'Bulleted or numbered list' },
      { type: 'html', label: 'Custom HTML', description: 'Add custom HTML code' }
    ];
    
    return (
      <Dialog open={showBlockSelector} onOpenChange={setShowBlockSelector}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Block</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-2 py-4">
            {blockTypes.map((blockType) => (
              <Button
                key={blockType.type}
                variant="outline"
                className="h-auto flex flex-col items-start p-4 justify-start"
                onClick={() => addBlock(blockType.type, position)}
              >
                <span className="font-medium">{blockType.label}</span>
                <span className="text-xs text-muted-foreground mt-1">{blockType.description}</span>
              </Button>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Editor controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Tabs value={editMode} onValueChange={(value) => setEditMode(value as 'edit' | 'preview')}>
            <TabsList>
              <TabsTrigger value="edit">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </TabsTrigger>
              <TabsTrigger value="preview">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        
        {editMode === 'edit' && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => showBlockSelectorAt(blocks.length)}
          >
            <PlusCircle className="h-4 w-4 mr-2" />
            Add Block
          </Button>
        )}
      </div>
      
      {/* Empty state */}
      {blocks.length === 0 && (
        <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-md">
          <p className="text-muted-foreground mb-4">No content blocks yet</p>
          <Button onClick={() => showBlockSelectorAt(0)}>
            <PlusCircle className="h-4 w-4 mr-2" />
            Add Your First Block
          </Button>
        </div>
      )}
      
      {/* Block list */}
      {blocks.length > 0 && (
        <div className="space-y-4">
          {editMode === 'edit' ? (
            <div className="space-y-4">
              {blocks.map((block, index) => (
                <div key={block.id} className="group">
                  {renderBlock(block, index)}
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-4 border rounded-md p-6">
              {blocks.map((block) => (
                <div key={block.id}>
                  {renderBlockContent(block)}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
      
      {/* Block selector dialog */}
      {showBlockSelector && blockSelectorPosition !== null && (
        <BlockSelector position={blockSelectorPosition} />
      )}
    </div>
  );
};
