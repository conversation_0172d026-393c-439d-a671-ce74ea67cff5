import React from 'react';

interface PageBannerProps {
  pageTitle: string;
  breadTitle: string;
  description: string;
  type: string;
}

const PageBanner: React.FC<PageBannerProps> = ({ pageTitle, breadTitle, description, type }) => {
  return (
    <section className="common-hero" style={{ backgroundImage: "url(/assets/img/hero/hero-bg.jpg)" }}>
      <div className="container">
        <div className="row">
          <div className="col-lg-12">
            <div className="main-heading text-center">
              <h1 className="text-center">{pageTitle}</h1>
              <div className="pages-intro text-center">
                <a href="/">Home</a>
                <span>/</span>
                <p>{breadTitle}</p>
              </div>
              <p className="text-center">{description}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PageBanner;