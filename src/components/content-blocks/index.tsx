import React from 'react';
import { cn } from '@/lib/utils';

// Base block interface
export interface ContentBlock {
  id: string;
  type: string;
  data: any;
  order?: number;
}

// Hero Block
export interface HeroBlockData {
  title: string;
  subtitle?: string;
  description: string;
  backgroundImage?: string;
  ctaText?: string;
  ctaLink?: string;
}

export const HeroBlock: React.FC<{ data: HeroBlockData; className?: string }> = ({ 
  data, 
  className 
}) => (
  <section 
    className={cn(
      "relative py-20 px-4 text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white",
      className
    )}
    style={data.backgroundImage ? {
      backgroundImage: `linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url(${data.backgroundImage})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center'
    } : {}}
  >
    <div className="container mx-auto max-w-4xl">
      {data.subtitle && (
        <p className="text-lg mb-4 opacity-90">{data.subtitle}</p>
      )}
      <h1 className="text-4xl md:text-6xl font-bold mb-6">{data.title}</h1>
      <p className="text-xl mb-8 opacity-90">{data.description}</p>
      {data.ctaText && data.ctaLink && (
        <a 
          href={data.ctaLink}
          className="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
        >
          {data.ctaText}
        </a>
      )}
    </div>
  </section>
);

// Text Block
export interface TextBlockData {
  content: string; // TipTap HTML content
  alignment?: 'left' | 'center' | 'right';
}

export const TextBlock: React.FC<{ data: TextBlockData; className?: string }> = ({ 
  data, 
  className 
}) => (
  <div 
    className={cn(
      "prose prose-lg max-w-none",
      data.alignment === 'center' && "text-center",
      data.alignment === 'right' && "text-right",
      className
    )}
    dangerouslySetInnerHTML={{ __html: data.content }}
  />
);

// Image Block
export interface ImageBlockData {
  src: string;
  alt: string;
  caption?: string;
  width?: number;
  height?: number;
  alignment?: 'left' | 'center' | 'right';
}

export const ImageBlock: React.FC<{ data: ImageBlockData; className?: string }> = ({ 
  data, 
  className 
}) => (
  <figure 
    className={cn(
      "my-8",
      data.alignment === 'center' && "text-center",
      data.alignment === 'right' && "text-right",
      className
    )}
  >
    <img 
      src={data.src} 
      alt={data.alt}
      width={data.width}
      height={data.height}
      className="max-w-full h-auto rounded-lg shadow-lg"
    />
    {data.caption && (
      <figcaption className="mt-2 text-sm text-gray-600 italic">
        {data.caption}
      </figcaption>
    )}
  </figure>
);

// Gallery Block
export interface GalleryBlockData {
  images: Array<{
    src: string;
    alt: string;
    caption?: string;
  }>;
  columns?: 2 | 3 | 4;
}

export const GalleryBlock: React.FC<{ data: GalleryBlockData; className?: string }> = ({ 
  data, 
  className 
}) => (
  <div 
    className={cn(
      "grid gap-4 my-8",
      data.columns === 2 && "grid-cols-1 md:grid-cols-2",
      data.columns === 3 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
      data.columns === 4 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
      !data.columns && "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
      className
    )}
  >
    {data.images.map((image, index) => (
      <figure key={index} className="group">
        <img 
          src={image.src} 
          alt={image.alt}
          className="w-full h-64 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow"
        />
        {image.caption && (
          <figcaption className="mt-2 text-sm text-gray-600">
            {image.caption}
          </figcaption>
        )}
      </figure>
    ))}
  </div>
);

// Stats Block
export interface StatsBlockData {
  title?: string;
  stats: Array<{
    number: string;
    label: string;
    description?: string;
  }>;
  layout?: 'horizontal' | 'grid';
}

export const StatsBlock: React.FC<{ data: StatsBlockData; className?: string }> = ({ 
  data, 
  className 
}) => (
  <section className={cn("py-12", className)}>
    {data.title && (
      <h2 className="text-3xl font-bold text-center mb-12">{data.title}</h2>
    )}
    <div 
      className={cn(
        "grid gap-8",
        data.layout === 'horizontal' ? "grid-cols-1 md:grid-cols-4" : "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
      )}
    >
      {data.stats.map((stat, index) => (
        <div key={index} className="text-center">
          <div className="text-4xl md:text-5xl font-bold text-blue-600 mb-2">
            {stat.number}
          </div>
          <div className="text-lg font-semibold mb-1">{stat.label}</div>
          {stat.description && (
            <div className="text-gray-600 text-sm">{stat.description}</div>
          )}
        </div>
      ))}
    </div>
  </section>
);

// Features Block
export interface FeaturesBlockData {
  title?: string;
  features: Array<{
    icon?: string;
    title: string;
    description: string;
  }>;
  layout?: 'grid' | 'list';
}

export const FeaturesBlock: React.FC<{ data: FeaturesBlockData; className?: string }> = ({ 
  data, 
  className 
}) => (
  <section className={cn("py-12", className)}>
    {data.title && (
      <h2 className="text-3xl font-bold text-center mb-12">{data.title}</h2>
    )}
    <div 
      className={cn(
        "grid gap-8",
        data.layout === 'list' ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
      )}
    >
      {data.features.map((feature, index) => (
        <div key={index} className="flex flex-col items-center text-center p-6 bg-white rounded-lg shadow-md">
          {feature.icon && (
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <i className={`${feature.icon} text-2xl text-blue-600`}></i>
            </div>
          )}
          <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
          <p className="text-gray-600">{feature.description}</p>
        </div>
      ))}
    </div>
  </section>
);

// Timeline Block
export interface TimelineBlockData {
  title?: string;
  phases: Array<{
    phase: string;
    duration: string;
    title: string;
    description: string;
    status?: 'completed' | 'current' | 'upcoming';
  }>;
}

export const TimelineBlock: React.FC<{ data: TimelineBlockData; className?: string }> = ({ 
  data, 
  className 
}) => (
  <section className={cn("py-12", className)}>
    {data.title && (
      <h2 className="text-3xl font-bold text-center mb-12">{data.title}</h2>
    )}
    <div className="max-w-4xl mx-auto">
      {data.phases.map((phase, index) => (
        <div key={index} className="flex items-start mb-8 last:mb-0">
          <div className="flex-shrink-0 w-24 text-right mr-8">
            <div className={cn(
              "inline-block px-3 py-1 rounded-full text-sm font-semibold",
              phase.status === 'completed' && "bg-green-100 text-green-800",
              phase.status === 'current' && "bg-blue-100 text-blue-800",
              phase.status === 'upcoming' && "bg-gray-100 text-gray-800",
              !phase.status && "bg-gray-100 text-gray-800"
            )}>
              {phase.phase}
            </div>
            <div className="text-sm text-gray-500 mt-1">{phase.duration}</div>
          </div>
          <div className="flex-shrink-0 w-4 h-4 bg-blue-600 rounded-full mt-2 mr-8"></div>
          <div className="flex-grow">
            <h3 className="text-xl font-semibold mb-2">{phase.title}</h3>
            <p className="text-gray-600">{phase.description}</p>
          </div>
        </div>
      ))}
    </div>
  </section>
);

// CTA Block
export interface CTABlockData {
  title: string;
  description: string;
  primaryButton?: {
    text: string;
    link: string;
  };
  secondaryButton?: {
    text: string;
    link: string;
  };
  backgroundColor?: string;
}

export const CTABlock: React.FC<{ data: CTABlockData; className?: string }> = ({ 
  data, 
  className 
}) => (
  <section 
    className={cn(
      "py-16 px-4 text-center text-white",
      className
    )}
    style={{ backgroundColor: data.backgroundColor || '#1e40af' }}
  >
    <div className="container mx-auto max-w-4xl">
      <h2 className="text-3xl md:text-4xl font-bold mb-6">{data.title}</h2>
      <p className="text-xl mb-8 opacity-90">{data.description}</p>
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        {data.primaryButton && (
          <a 
            href={data.primaryButton.link}
            className="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            {data.primaryButton.text}
          </a>
        )}
        {data.secondaryButton && (
          <a 
            href={data.secondaryButton.link}
            className="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
          >
            {data.secondaryButton.text}
          </a>
        )}
      </div>
    </div>
  </section>
);

// Block Renderer
export const BlockRenderer: React.FC<{ 
  blocks: ContentBlock[]; 
  className?: string 
}> = ({ blocks, className }) => {
  const sortedBlocks = blocks.sort((a, b) => (a.order || 0) - (b.order || 0));

  return (
    <div className={className}>
      {sortedBlocks.map((block) => {
        switch (block.type) {
          case 'hero':
            return <HeroBlock key={block.id} data={block.data} />;
          case 'text':
            return <TextBlock key={block.id} data={block.data} />;
          case 'image':
            return <ImageBlock key={block.id} data={block.data} />;
          case 'gallery':
            return <GalleryBlock key={block.id} data={block.data} />;
          case 'stats':
            return <StatsBlock key={block.id} data={block.data} />;
          case 'features':
            return <FeaturesBlock key={block.id} data={block.data} />;
          case 'timeline':
            return <TimelineBlock key={block.id} data={block.data} />;
          case 'cta':
            return <CTABlock key={block.id} data={block.data} />;
          default:
            return null;
        }
      })}
    </div>
  );
};