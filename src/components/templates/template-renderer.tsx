import React from 'react'
import { getTemplate, PageData } from './template-registry'
import { DefaultTemplate } from './default-template'

interface TemplateRendererProps {
  page: PageData
  isPreview?: boolean
  className?: string
}

export function TemplateRenderer({ page, isPreview = false, className }: TemplateRendererProps) {
  const template = getTemplate(page.template)
  
  if (!template) {
    console.warn(`Template "${page.template}" not found, falling back to default`)
    return <DefaultTemplate page={page} isPreview={isPreview} className={className} />
  }

  const TemplateComponent = template.component

  return (
    <div className={className}>
      <TemplateComponent 
        page={page} 
        isPreview={isPreview}
        templateSettings={template.settings}
      />
    </div>
  )
}
