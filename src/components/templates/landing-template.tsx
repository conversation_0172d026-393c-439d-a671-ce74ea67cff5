import React from 'react'
import { PageData } from './template-registry'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { ArrowRight, Check, Star, Quote } from 'lucide-react'
import { BlockRenderer } from '@/components/pages/blocks/block-renderer'

interface LandingTemplateProps {
  page: PageData
  isPreview?: boolean
  templateSettings?: Record<string, any>
  className?: string
}

export function LandingTemplate({ 
  page, 
  isPreview = false, 
  templateSettings = {},
  className 
}: LandingTemplateProps) {
  const {
    showHero = true,
    showFeatures = true,
    showTestimonials = true,
    showCTA = true
  } = templateSettings

  return (
    <div className={`min-h-screen ${className || ''}`}>
      {/* Hero Section */}
      {showHero && (
        <section className="relative bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-20 lg:py-32">
          <div className="container mx-auto px-4">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-8">
                <div className="space-y-4">
                  <Badge variant="outline" className="w-fit">
                    {page.template} Template
                  </Badge>
                  <h1 className="text-4xl lg:text-6xl font-bold tracking-tight">
                    {page.title}
                  </h1>
                  {page.excerpt && (
                    <p className="text-xl text-muted-foreground leading-relaxed">
                      {page.excerpt}
                    </p>
                  )}
                </div>
                
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button size="lg" className="text-lg px-8">
                    Get Started
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                  <Button variant="outline" size="lg" className="text-lg px-8">
                    Learn More
                  </Button>
                </div>

                {/* Social Proof */}
                <div className="flex items-center space-x-6 pt-8">
                  <div className="text-center">
                    <div className="text-2xl font-bold">10K+</div>
                    <div className="text-sm text-muted-foreground">Happy Customers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">99%</div>
                    <div className="text-sm text-muted-foreground">Satisfaction Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">24/7</div>
                    <div className="text-sm text-muted-foreground">Support</div>
                  </div>
                </div>
              </div>

              {/* Hero Image */}
              <div className="relative">
                {page.featuredImage ? (
                  <img
                    src={page.featuredImage}
                    alt={page.title}
                    className="w-full h-auto rounded-2xl shadow-2xl"
                  />
                ) : (
                  <div className="w-full h-96 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl flex items-center justify-center">
                    <div className="text-center space-y-2">
                      <div className="text-6xl">🚀</div>
                      <p className="text-muted-foreground">Hero Image Placeholder</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Features Section */}
      {showFeatures && (
        <section className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="text-center space-y-4 mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold">Why Choose Us?</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Discover the features that make our solution stand out from the competition.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: "⚡",
                  title: "Lightning Fast",
                  description: "Optimized for speed and performance with cutting-edge technology."
                },
                {
                  icon: "🔒",
                  title: "Secure & Reliable",
                  description: "Enterprise-grade security with 99.9% uptime guarantee."
                },
                {
                  icon: "🎨",
                  title: "Beautiful Design",
                  description: "Modern, responsive design that looks great on all devices."
                },
                {
                  icon: "📱",
                  title: "Mobile First",
                  description: "Built with mobile users in mind for the best experience."
                },
                {
                  icon: "🔧",
                  title: "Easy to Use",
                  description: "Intuitive interface that anyone can master in minutes."
                },
                {
                  icon: "💬",
                  title: "24/7 Support",
                  description: "Round-the-clock customer support whenever you need help."
                }
              ].map((feature, index) => (
                <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="text-4xl mb-4">{feature.icon}</div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Content Blocks */}
      {page.blocks && page.blocks.length > 0 && (
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="space-y-16">
              {page.blocks.map((block) => (
                <BlockRenderer
                  key={block.id}
                  block={block}
                  isPreview={isPreview}
                />
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Main Content */}
      {page.content && (
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div 
                className="prose prose-lg max-w-none"
                dangerouslySetInnerHTML={{ __html: page.content }}
              />
            </div>
          </div>
        </section>
      )}

      {/* Testimonials */}
      {showTestimonials && (
        <section className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="text-center space-y-4 mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold">What Our Customers Say</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Don't just take our word for it. Here's what real customers have to say.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  name: "Sarah Johnson",
                  role: "CEO, TechCorp",
                  avatar: "/placeholder-user.jpg",
                  rating: 5,
                  content: "This solution has transformed our business. The results speak for themselves."
                },
                {
                  name: "Michael Chen",
                  role: "Marketing Director",
                  avatar: "/placeholder-user.jpg",
                  rating: 5,
                  content: "Outstanding support and incredible features. Highly recommended!"
                },
                {
                  name: "Emily Davis",
                  role: "Startup Founder",
                  avatar: "/placeholder-user.jpg",
                  rating: 5,
                  content: "Easy to use and powerful. Exactly what we needed to scale our business."
                }
              ].map((testimonial, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center space-x-1 mb-4">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <Quote className="h-8 w-8 text-muted-foreground mb-4" />
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-muted-foreground italic">"{testimonial.content}"</p>
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                        <AvatarFallback>
                          {testimonial.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-semibold">{testimonial.name}</div>
                        <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Call to Action */}
      {showCTA && (
        <section className="py-20 bg-primary text-primary-foreground">
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-3xl mx-auto space-y-8">
              <h2 className="text-3xl lg:text-4xl font-bold">
                Ready to Get Started?
              </h2>
              <p className="text-xl opacity-90">
                Join thousands of satisfied customers and transform your business today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" variant="secondary" className="text-lg px-8">
                  Start Free Trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button size="lg" variant="outline" className="text-lg px-8 border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
                  Contact Sales
                </Button>
              </div>
              <p className="text-sm opacity-75">
                No credit card required • 14-day free trial • Cancel anytime
              </p>
            </div>
          </div>
        </section>
      )}
    </div>
  )
}
