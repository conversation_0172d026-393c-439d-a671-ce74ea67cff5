import React from 'react'
import { PageData } from './template-registry'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { MapPin, Mail, Phone, Linkedin, Twitter, Github } from 'lucide-react'
import { BlockRenderer } from '@/components/pages/blocks/block-renderer'

interface AboutTemplateProps {
  page: PageData
  isPreview?: boolean
  templateSettings?: Record<string, any>
  className?: string
}

export function AboutTemplate({ 
  page, 
  isPreview = false, 
  templateSettings = {},
  className 
}: AboutTemplateProps) {
  const {
    showTeam = true,
    showTimeline = true,
    showValues = true,
    showStats = true
  } = templateSettings

  return (
    <div className={`min-h-screen ${className || ''}`}>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <Badge variant="outline" className="w-fit mx-auto">
              About Us
            </Badge>
            <h1 className="text-4xl lg:text-6xl font-bold tracking-tight">
              {page.title}
            </h1>
            {page.excerpt && (
              <p className="text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto">
                {page.excerpt}
              </p>
            )}
            
            {page.featuredImage && (
              <div className="mt-12">
                <img
                  src={page.featuredImage}
                  alt={page.title}
                  className="w-full max-w-4xl mx-auto rounded-2xl shadow-2xl"
                />
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      {showStats && (
        <section className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { number: "10+", label: "Years Experience" },
                { number: "500+", label: "Projects Completed" },
                { number: "50+", label: "Team Members" },
                { number: "99%", label: "Client Satisfaction" }
              ].map((stat, index) => (
                <Card key={index} className="text-center">
                  <CardContent className="pt-6">
                    <div className="text-4xl font-bold text-primary mb-2">{stat.number}</div>
                    <div className="text-muted-foreground">{stat.label}</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Main Content */}
      {page.content && (
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div 
                className="prose prose-lg max-w-none"
                dangerouslySetInnerHTML={{ __html: page.content }}
              />
            </div>
          </div>
        </section>
      )}

      {/* Content Blocks */}
      {page.blocks && page.blocks.length > 0 && (
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="space-y-16">
              {page.blocks.map((block) => (
                <BlockRenderer
                  key={block.id}
                  block={block}
                  isPreview={isPreview}
                />
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Values Section */}
      {showValues && (
        <section className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="text-center space-y-4 mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold">Our Values</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                The principles that guide everything we do and shape our company culture.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: "🎯",
                  title: "Excellence",
                  description: "We strive for excellence in everything we do, never settling for mediocrity."
                },
                {
                  icon: "🤝",
                  title: "Integrity",
                  description: "We conduct business with honesty, transparency, and ethical practices."
                },
                {
                  icon: "💡",
                  title: "Innovation",
                  description: "We embrace new ideas and technologies to stay ahead of the curve."
                },
                {
                  icon: "🌟",
                  title: "Quality",
                  description: "We deliver high-quality solutions that exceed our clients' expectations."
                },
                {
                  icon: "🚀",
                  title: "Growth",
                  description: "We believe in continuous learning and improvement for our team and clients."
                },
                {
                  icon: "❤️",
                  title: "Passion",
                  description: "We are passionate about what we do and it shows in our work."
                }
              ].map((value, index) => (
                <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="text-4xl mb-4">{value.icon}</div>
                    <CardTitle className="text-xl">{value.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base">
                      {value.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Timeline Section */}
      {showTimeline && (
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center space-y-4 mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold">Our Journey</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Key milestones in our company's growth and evolution.
              </p>
            </div>

            <div className="max-w-4xl mx-auto">
              <div className="space-y-8">
                {[
                  {
                    year: "2020",
                    title: "Company Founded",
                    description: "Started with a vision to transform the industry with innovative solutions."
                  },
                  {
                    year: "2021",
                    title: "First Major Client",
                    description: "Secured our first enterprise client and delivered exceptional results."
                  },
                  {
                    year: "2022",
                    title: "Team Expansion",
                    description: "Grew our team to 25+ talented professionals across multiple disciplines."
                  },
                  {
                    year: "2023",
                    title: "International Expansion",
                    description: "Expanded operations to serve clients across multiple countries."
                  },
                  {
                    year: "2024",
                    title: "Innovation Award",
                    description: "Recognized for our innovative approach and outstanding client satisfaction."
                  }
                ].map((milestone, index) => (
                  <div key={index} className="flex gap-6">
                    <div className="flex flex-col items-center">
                      <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">
                        {milestone.year.slice(-2)}
                      </div>
                      {index < 4 && <div className="w-0.5 h-16 bg-border mt-4"></div>}
                    </div>
                    <div className="flex-1 pb-8">
                      <div className="text-sm text-muted-foreground mb-1">{milestone.year}</div>
                      <h3 className="text-xl font-semibold mb-2">{milestone.title}</h3>
                      <p className="text-muted-foreground">{milestone.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Team Section */}
      {showTeam && (
        <section className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="text-center space-y-4 mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold">Meet Our Team</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                The talented individuals who make our success possible.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {[
                {
                  name: "John Smith",
                  role: "CEO & Founder",
                  avatar: "/placeholder-user.jpg",
                  bio: "Visionary leader with 15+ years of industry experience.",
                  social: {
                    linkedin: "#",
                    twitter: "#",
                    email: "<EMAIL>"
                  }
                },
                {
                  name: "Sarah Johnson",
                  role: "CTO",
                  avatar: "/placeholder-user.jpg",
                  bio: "Technology expert passionate about innovation and scalability.",
                  social: {
                    linkedin: "#",
                    github: "#",
                    email: "<EMAIL>"
                  }
                },
                {
                  name: "Michael Chen",
                  role: "Head of Design",
                  avatar: "/placeholder-user.jpg",
                  bio: "Creative designer focused on user experience and beautiful interfaces.",
                  social: {
                    linkedin: "#",
                    twitter: "#",
                    email: "<EMAIL>"
                  }
                },
                {
                  name: "Emily Davis",
                  role: "Marketing Director",
                  avatar: "/placeholder-user.jpg",
                  bio: "Marketing strategist with a passion for brand building and growth.",
                  social: {
                    linkedin: "#",
                    twitter: "#",
                    email: "<EMAIL>"
                  }
                }
              ].map((member, index) => (
                <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <Avatar className="h-24 w-24 mx-auto mb-4">
                      <AvatarImage src={member.avatar} alt={member.name} />
                      <AvatarFallback className="text-lg">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <CardTitle className="text-xl">{member.name}</CardTitle>
                    <CardDescription className="text-primary font-medium">
                      {member.role}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-muted-foreground">{member.bio}</p>
                    <div className="flex justify-center space-x-2">
                      {member.social.linkedin && (
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Linkedin className="h-4 w-4" />
                        </Button>
                      )}
                      {member.social.twitter && (
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Twitter className="h-4 w-4" />
                        </Button>
                      )}
                      {member.social.github && (
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Github className="h-4 w-4" />
                        </Button>
                      )}
                      {member.social.email && (
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Mail className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Contact CTA */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <Card className="max-w-4xl mx-auto text-center">
            <CardHeader>
              <CardTitle className="text-3xl">Want to Work With Us?</CardTitle>
              <CardDescription className="text-lg">
                We're always looking for talented individuals and exciting partnerships.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="text-lg px-8">
                  Join Our Team
                </Button>
                <Button variant="outline" size="lg" className="text-lg px-8">
                  Partner With Us
                </Button>
              </div>
              <div className="flex justify-center space-x-6 text-sm text-muted-foreground">
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4" />
                  <span>New York, NY</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4" />
                  <span>+****************</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  )
}
