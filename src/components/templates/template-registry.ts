import { ComponentType } from 'react'
import { DefaultTemplate } from './default-template'
import { LandingTemplate } from './landing-template'
import { AboutTemplate } from './about-template'
import { ContactTemplate } from './contact-template'
import { BlogTemplate } from './blog-template'

export interface TemplateConfig {
  id: string
  name: string
  description: string
  category: string
  preview?: string
  component: ComponentType<any>
  features: string[]
  settings?: Record<string, any>
}

export interface PageData {
  id: string
  title: string
  slug: string
  content?: string
  excerpt?: string
  template: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  seoTitle?: string
  seoDescription?: string
  featuredImage?: string
  author: {
    id: string
    name: string
    image?: string
    bio?: string
  }
  blocks?: Array<{
    id: string
    type: string
    data: any
    order: number
  }>
  _count?: {
    views: number
    comments: number
    likes: number
  }
}

// Template registry
const TEMPLATES: Record<string, TemplateConfig> = {
  default: {
    id: 'default',
    name: 'Default',
    description: 'Standard page layout with content and sidebar',
    category: 'basic',
    component: DefaultTemplate,
    features: ['Content area', 'Sidebar', 'SEO optimized', 'Responsive'],
    settings: {
      showSidebar: true,
      showBreadcrumbs: true,
      showAuthor: true,
      showDate: true
    }
  },
  landing: {
    id: 'landing',
    name: 'Landing Page',
    description: 'Marketing-focused landing page with hero section and CTAs',
    category: 'marketing',
    component: LandingTemplate,
    features: ['Hero section', 'Call-to-action', 'Features grid', 'Testimonials'],
    settings: {
      showHero: true,
      showFeatures: true,
      showTestimonials: true,
      showCTA: true
    }
  },
  about: {
    id: 'about',
    name: 'About Page',
    description: 'Company or personal about page with team and story sections',
    category: 'company',
    component: AboutTemplate,
    features: ['Story section', 'Team grid', 'Timeline', 'Values'],
    settings: {
      showTeam: true,
      showTimeline: true,
      showValues: true,
      showStats: true
    }
  },
  contact: {
    id: 'contact',
    name: 'Contact Page',
    description: 'Contact page with form, map, and contact information',
    category: 'utility',
    component: ContactTemplate,
    features: ['Contact form', 'Map integration', 'Contact info', 'Office locations'],
    settings: {
      showForm: true,
      showMap: true,
      showOffices: true,
      showSocial: true
    }
  },
  blog: {
    id: 'blog',
    name: 'Blog Post',
    description: 'Blog post layout with content, author info, and related posts',
    category: 'content',
    component: BlogTemplate,
    features: ['Article content', 'Author bio', 'Related posts', 'Comments'],
    settings: {
      showAuthor: true,
      showRelated: true,
      showComments: true,
      showTags: true
    }
  }
}

/**
 * Get a template by ID
 */
export function getTemplate(templateId: string): TemplateConfig | null {
  return TEMPLATES[templateId] || null
}

/**
 * Get all available templates
 */
export function getTemplateList(): TemplateConfig[] {
  return Object.values(TEMPLATES)
}

/**
 * Get templates by category
 */
export function getTemplatesByCategory(): Record<string, TemplateConfig[]> {
  const categories: Record<string, TemplateConfig[]> = {}
  
  Object.values(TEMPLATES).forEach(template => {
    if (!categories[template.category]) {
      categories[template.category] = []
    }
    categories[template.category].push(template)
  })

  return categories
}

/**
 * Get template categories
 */
export function getTemplateCategories(): string[] {
  const categories = new Set<string>()
  Object.values(TEMPLATES).forEach(template => {
    categories.add(template.category)
  })
  return Array.from(categories)
}

/**
 * Check if a template exists
 */
export function templateExists(templateId: string): boolean {
  return templateId in TEMPLATES
}

/**
 * Get template features
 */
export function getTemplateFeatures(templateId: string): string[] {
  const template = getTemplate(templateId)
  return template?.features || []
}

/**
 * Get template settings
 */
export function getTemplateSettings(templateId: string): Record<string, any> {
  const template = getTemplate(templateId)
  return template?.settings || {}
}
