import React from 'react'
import { PageData } from './template-registry'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Calendar, Eye, MessageCircle, Clock } from 'lucide-react'
import { format } from 'date-fns'
import { BlockRenderer } from '@/components/pages/blocks/block-renderer'

interface DefaultTemplateProps {
  page: PageData
  isPreview?: boolean
  templateSettings?: Record<string, any>
  className?: string
}

export function DefaultTemplate({ 
  page, 
  isPreview = false, 
  templateSettings = {},
  className 
}: DefaultTemplateProps) {
  const {
    showSidebar = true,
    showBreadcrumbs = true,
    showAuthor = true,
    showDate = true
  } = templateSettings

  return (
    <div className={`container mx-auto px-4 py-8 ${className || ''}`}>
      {/* Breadcrumbs */}
      {showBreadcrumbs && (
        <nav className="mb-6 text-sm text-muted-foreground">
          <ol className="flex items-center space-x-2">
            <li><a href="/" className="hover:text-foreground">Home</a></li>
            <li>/</li>
            <li className="text-foreground">{page.title}</li>
          </ol>
        </nav>
      )}

      <div className={`grid gap-8 ${showSidebar ? 'lg:grid-cols-4' : 'lg:grid-cols-1'}`}>
        {/* Main Content */}
        <div className={showSidebar ? 'lg:col-span-3' : 'lg:col-span-1'}>
          <article className="space-y-6">
            {/* Header */}
            <header className="space-y-4">
              <h1 className="text-4xl font-bold tracking-tight">{page.title}</h1>
              
              {page.excerpt && (
                <p className="text-xl text-muted-foreground leading-relaxed">
                  {page.excerpt}
                </p>
              )}

              {/* Meta Information */}
              <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                {showAuthor && (
                  <div className="flex items-center space-x-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={page.author.image} alt={page.author.name} />
                      <AvatarFallback className="text-xs">
                        {page.author.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <span>By {page.author.name}</span>
                  </div>
                )}

                {showDate && page.publishedAt && (
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>{format(new Date(page.publishedAt), 'MMM dd, yyyy')}</span>
                  </div>
                )}

                {page._count && (
                  <>
                    <div className="flex items-center space-x-1">
                      <Eye className="h-4 w-4" />
                      <span>{page._count.views} views</span>
                    </div>
                    {page._count.comments > 0 && (
                      <div className="flex items-center space-x-1">
                        <MessageCircle className="h-4 w-4" />
                        <span>{page._count.comments} comments</span>
                      </div>
                    )}
                  </>
                )}

                {!isPreview && (
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>Updated {format(new Date(page.updatedAt), 'MMM dd, yyyy')}</span>
                  </div>
                )}
              </div>
            </header>

            {/* Featured Image */}
            {page.featuredImage && (
              <div className="aspect-video overflow-hidden rounded-lg">
                <img
                  src={page.featuredImage}
                  alt={page.title}
                  className="w-full h-full object-cover"
                />
              </div>
            )}

            {/* Content */}
            <div className="space-y-6">
              {/* Blocks */}
              {page.blocks && page.blocks.length > 0 ? (
                <div className="space-y-8">
                  {page.blocks.map((block) => (
                    <BlockRenderer
                      key={block.id}
                      block={block}
                      isPreview={isPreview}
                    />
                  ))}
                </div>
              ) : page.content ? (
                <div 
                  className="prose prose-lg max-w-none"
                  dangerouslySetInnerHTML={{ __html: page.content }}
                />
              ) : (
                <div className="text-center py-12 text-muted-foreground">
                  <p>No content available for this page.</p>
                </div>
              )}
            </div>

            {/* Author Bio */}
            {showAuthor && page.author.bio && (
              <Card className="mt-8">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-3">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={page.author.image} alt={page.author.name} />
                      <AvatarFallback>
                        {page.author.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="text-lg font-semibold">{page.author.name}</h3>
                      <p className="text-sm text-muted-foreground">Author</p>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{page.author.bio}</p>
                </CardContent>
              </Card>
            )}
          </article>
        </div>

        {/* Sidebar */}
        {showSidebar && (
          <aside className="space-y-6">
            {/* Page Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Page Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Status</span>
                  <Badge variant={page.status === 'PUBLISHED' ? 'default' : 'secondary'}>
                    {page.status.toLowerCase()}
                  </Badge>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Template</span>
                  <Badge variant="outline">{page.template}</Badge>
                </div>

                {page._count && (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Views</span>
                      <span className="text-sm font-medium">{page._count.views}</span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Comments</span>
                      <span className="text-sm font-medium">{page._count.comments}</span>
                    </div>
                  </>
                )}

                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Created</span>
                  <span className="text-sm font-medium">
                    {format(new Date(page.createdAt), 'MMM dd, yyyy')}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            {!isPreview && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <button className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-muted transition-colors">
                    Share Page
                  </button>
                  <button className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-muted transition-colors">
                    Print Page
                  </button>
                  <button className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-muted transition-colors">
                    Report Issue
                  </button>
                </CardContent>
              </Card>
            )}

            {/* Table of Contents */}
            {page.content && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Table of Contents</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <a href="#introduction" className="block text-muted-foreground hover:text-foreground">
                      Introduction
                    </a>
                    <a href="#main-content" className="block text-muted-foreground hover:text-foreground">
                      Main Content
                    </a>
                    <a href="#conclusion" className="block text-muted-foreground hover:text-foreground">
                      Conclusion
                    </a>
                  </div>
                </CardContent>
              </Card>
            )}
          </aside>
        )}
      </div>
    </div>
  )
}
