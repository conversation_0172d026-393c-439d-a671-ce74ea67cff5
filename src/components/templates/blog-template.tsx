import React from 'react'
import { PageData } from './template-registry'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Calendar, Eye, MessageCircle, Clock, Share2, Bookmark, ThumbsUp, Tag } from 'lucide-react'
import { format } from 'date-fns'
import { BlockRenderer } from '@/components/pages/blocks/block-renderer'

interface BlogTemplateProps {
  page: PageData
  isPreview?: boolean
  templateSettings?: Record<string, any>
  className?: string
  relatedPages?: PageData[]
}

export function BlogTemplate({ 
  page, 
  isPreview = false, 
  templateSettings = {},
  className,
  relatedPages = []
}: BlogTemplateProps) {
  const {
    showAuthor = true,
    showRelated = true,
    showComments = true,
    showTags = true
  } = templateSettings

  // Mock tags if not provided
  const tags = page.tags || [
    { id: '1', name: 'Technology', slug: 'technology', color: '#3b82f6' },
    { id: '2', name: 'Innovation', slug: 'innovation', color: '#10b981' },
    { id: '3', name: 'Business', slug: 'business', color: '#f59e0b' }
  ]

  return (
    <div className={`min-h-screen ${className || ''}`}>
      <div className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            <article className="space-y-8">
              {/* Header */}
              <header className="space-y-6">
                {/* Tags */}
                {showTags && tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag) => (
                      <Badge 
                        key={tag.id} 
                        variant="outline"
                        style={{ borderColor: tag.color, color: tag.color }}
                      >
                        <Tag className="mr-1 h-3 w-3" />
                        {tag.name}
                      </Badge>
                    ))}
                  </div>
                )}

                <h1 className="text-4xl lg:text-5xl font-bold tracking-tight leading-tight">
                  {page.title}
                </h1>
                
                {page.excerpt && (
                  <p className="text-xl text-muted-foreground leading-relaxed">
                    {page.excerpt}
                  </p>
                )}

                {/* Meta Information */}
                <div className="flex flex-wrap items-center gap-6 py-4 border-y">
                  {showAuthor && (
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={page.author.image} alt={page.author.name} />
                        <AvatarFallback>
                          {page.author.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{page.author.name}</div>
                        <div className="text-sm text-muted-foreground">Author</div>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    {page.publishedAt && (
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>{format(new Date(page.publishedAt), 'MMM dd, yyyy')}</span>
                      </div>
                    )}

                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>5 min read</span>
                    </div>

                    {page._count && (
                      <>
                        <div className="flex items-center space-x-1">
                          <Eye className="h-4 w-4" />
                          <span>{page._count.views} views</span>
                        </div>
                        {page._count.comments > 0 && (
                          <div className="flex items-center space-x-1">
                            <MessageCircle className="h-4 w-4" />
                            <span>{page._count.comments} comments</span>
                          </div>
                        )}
                      </>
                    )}
                  </div>

                  {/* Action Buttons */}
                  {!isPreview && (
                    <div className="flex items-center space-x-2 ml-auto">
                      <Button variant="ghost" size="sm">
                        <Share2 className="h-4 w-4 mr-1" />
                        Share
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Bookmark className="h-4 w-4 mr-1" />
                        Save
                      </Button>
                      <Button variant="ghost" size="sm">
                        <ThumbsUp className="h-4 w-4 mr-1" />
                        Like
                      </Button>
                    </div>
                  )}
                </div>
              </header>

              {/* Featured Image */}
              {page.featuredImage && (
                <div className="aspect-video overflow-hidden rounded-lg">
                  <img
                    src={page.featuredImage}
                    alt={page.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}

              {/* Content */}
              <div className="space-y-8">
                {/* Blocks */}
                {page.blocks && page.blocks.length > 0 ? (
                  <div className="space-y-8">
                    {page.blocks.map((block) => (
                      <BlockRenderer
                        key={block.id}
                        block={block}
                        isPreview={isPreview}
                      />
                    ))}
                  </div>
                ) : page.content ? (
                  <div 
                    className="prose prose-lg max-w-none"
                    dangerouslySetInnerHTML={{ __html: page.content }}
                  />
                ) : (
                  <div className="text-center py-12 text-muted-foreground">
                    <p>No content available for this article.</p>
                  </div>
                )}
              </div>

              {/* Author Bio */}
              {showAuthor && page.author.bio && (
                <Card className="mt-12">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-4">
                      <Avatar className="h-16 w-16">
                        <AvatarImage src={page.author.image} alt={page.author.name} />
                        <AvatarFallback className="text-lg">
                          {page.author.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="text-xl font-semibold">{page.author.name}</h3>
                        <p className="text-muted-foreground">Author</p>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground mb-4">{page.author.bio}</p>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        Follow
                      </Button>
                      <Button variant="ghost" size="sm">
                        View Profile
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Comments Section */}
              {showComments && !isPreview && (
                <Card className="mt-12">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <MessageCircle className="h-5 w-5" />
                      <span>Comments ({page._count?.comments || 0})</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Comment Form */}
                    <div className="space-y-4">
                      <h4 className="font-medium">Leave a comment</h4>
                      <div className="space-y-3">
                        <textarea
                          placeholder="Share your thoughts..."
                          className="w-full p-3 border rounded-md resize-none"
                          rows={4}
                        />
                        <Button>Post Comment</Button>
                      </div>
                    </div>

                    {/* Sample Comments */}
                    <div className="space-y-6 pt-6 border-t">
                      {[
                        {
                          author: "John Doe",
                          avatar: "/placeholder-user.jpg",
                          time: "2 hours ago",
                          content: "Great article! Really helpful insights on this topic."
                        },
                        {
                          author: "Jane Smith",
                          avatar: "/placeholder-user.jpg",
                          time: "1 day ago",
                          content: "Thanks for sharing this. Looking forward to more content like this."
                        }
                      ].map((comment, index) => (
                        <div key={index} className="flex space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={comment.avatar} alt={comment.author} />
                            <AvatarFallback className="text-xs">
                              {comment.author.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 space-y-1">
                            <div className="flex items-center space-x-2">
                              <span className="font-medium text-sm">{comment.author}</span>
                              <span className="text-xs text-muted-foreground">{comment.time}</span>
                            </div>
                            <p className="text-sm text-muted-foreground">{comment.content}</p>
                            <div className="flex space-x-2">
                              <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                                Reply
                              </Button>
                              <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                                Like
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </article>
          </div>

          {/* Sidebar */}
          <aside className="space-y-6">
            {/* Article Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Article Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Status</span>
                  <Badge variant={page.status === 'PUBLISHED' ? 'default' : 'secondary'}>
                    {page.status.toLowerCase()}
                  </Badge>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Template</span>
                  <Badge variant="outline">{page.template}</Badge>
                </div>

                {page._count && (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Views</span>
                      <span className="text-sm font-medium">{page._count.views}</span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Comments</span>
                      <span className="text-sm font-medium">{page._count.comments}</span>
                    </div>
                  </>
                )}

                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Published</span>
                  <span className="text-sm font-medium">
                    {page.publishedAt ? format(new Date(page.publishedAt), 'MMM dd, yyyy') : 'Draft'}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Table of Contents */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Table of Contents</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <a href="#introduction" className="block text-muted-foreground hover:text-foreground">
                    Introduction
                  </a>
                  <a href="#main-points" className="block text-muted-foreground hover:text-foreground">
                    Main Points
                  </a>
                  <a href="#examples" className="block text-muted-foreground hover:text-foreground">
                    Examples
                  </a>
                  <a href="#conclusion" className="block text-muted-foreground hover:text-foreground">
                    Conclusion
                  </a>
                </div>
              </CardContent>
            </Card>

            {/* Related Articles */}
            {showRelated && relatedPages.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Related Articles</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {relatedPages.slice(0, 3).map((relatedPage) => (
                    <div key={relatedPage.id} className="space-y-2">
                      <h4 className="font-medium text-sm leading-tight">
                        <a href={`/${relatedPage.slug}`} className="hover:text-primary">
                          {relatedPage.title}
                        </a>
                      </h4>
                      {relatedPage.excerpt && (
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {relatedPage.excerpt}
                        </p>
                      )}
                      <div className="text-xs text-muted-foreground">
                        {relatedPage.publishedAt && format(new Date(relatedPage.publishedAt), 'MMM dd, yyyy')}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Newsletter Signup */}
            {!isPreview && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Stay Updated</CardTitle>
                  <CardDescription>
                    Get the latest articles delivered to your inbox.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="w-full p-2 border rounded-md text-sm"
                  />
                  <Button className="w-full" size="sm">
                    Subscribe
                  </Button>
                  <p className="text-xs text-muted-foreground">
                    No spam. Unsubscribe anytime.
                  </p>
                </CardContent>
              </Card>
            )}
          </aside>
        </div>
      </div>
    </div>
  )
}
