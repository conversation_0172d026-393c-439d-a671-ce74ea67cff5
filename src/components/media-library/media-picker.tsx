'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Check, X } from 'lucide-react'

import { MediaLibrary } from './media-library'
import { useMediaSelection } from './hooks/use-media-selection'
import type { MediaPickerProps, MediaFile } from './types'

export function MediaPicker({
  isOpen,
  onClose,
  onSelect,
  config = {},
  initialSelection = []
}: MediaPickerProps) {
  const {
    allowMultiple = true,
    maxFiles = 10,
    allowedTypes = ['IMAGE', 'VIDEO', 'AUDIO', 'DOCUMENT', 'OTHER'],
    showUploader = true,
    showFolders = true,
    showSearch = true,
    showFilters = true,
    showBulkActions = false, // Usually disabled in picker mode
    defaultView = 'grid',
    gridSize = 'medium'
  } = config

  const {
    selectedFiles,
    selectFiles,
    deselectAll,
    hasSelection,
    getSelectionCount
  } = useMediaSelection({
    allowMultiple,
    maxSelection: maxFiles,
    allowedTypes,
    onSelectionChange: (files) => {
      // Optional: Real-time selection callback
    }
  })

  // Initialize with initial selection
  useEffect(() => {
    if (initialSelection.length > 0) {
      selectFiles(initialSelection)
    }
  }, [initialSelection, selectFiles])

  const handleSelect = () => {
    onSelect(selectedFiles)
    onClose()
  }

  const handleCancel = () => {
    deselectAll()
    onClose()
  }

  const canSelect = hasSelection() && getSelectionCount() <= (maxFiles || Infinity)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Select Media</span>
            {selectedFiles.length > 0 && (
              <Badge variant="secondary">
                {selectedFiles.length} selected
                {maxFiles && ` of ${maxFiles}`}
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            Choose files from your media library
            {allowedTypes.length < 5 && (
              <span className="ml-2">
                • Allowed types: {allowedTypes.join(', ')}
              </span>
            )}
            {maxFiles && (
              <span className="ml-2">
                • Max files: {maxFiles}
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <Separator />

        <div className="flex-1 overflow-hidden">
          <MediaLibrary
            config={{
              ...config,
              showBulkActions: false, // Disable bulk actions in picker
              allowMultiple,
              allowedTypes
            }}
            onFileSelect={(files) => {
              // Handle file selection from library
              if (allowMultiple) {
                selectFiles([...selectedFiles, ...files])
              } else {
                selectFiles(files)
              }
            }}
            className="h-full"
          />
        </div>

        {/* Selection Summary */}
        {selectedFiles.length > 0 && (
          <>
            <Separator />
            <div className="p-4 bg-muted/30 rounded-lg">
              <h4 className="text-sm font-medium mb-2">Selected Files</h4>
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                {selectedFiles.map((file) => (
                  <div
                    key={file.$id}
                    className="flex items-center gap-2 bg-background rounded-md px-2 py-1 text-sm"
                  >
                    {file.type === 'IMAGE' && file.thumbnailUrl ? (
                      <img
                        src={file.thumbnailUrl}
                        alt={file.alt || file.filename}
                        className="w-6 h-6 object-cover rounded"
                      />
                    ) : (
                      <div className="w-6 h-6 bg-muted rounded flex items-center justify-center text-xs">
                        {file.type.charAt(0)}
                      </div>
                    )}
                    <span className="truncate max-w-32">{file.filename}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0"
                      onClick={() => {
                        const newSelection = selectedFiles.filter(f => f.$id !== file.$id)
                        selectFiles(newSelection)
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        <DialogFooter className="flex justify-between">
          <div className="flex items-center gap-2">
            {selectedFiles.length > 0 && (
              <Button
                variant="outline"
                onClick={deselectAll}
                size="sm"
              >
                Clear Selection
              </Button>
            )}
            <span className="text-sm text-muted-foreground">
              {getSelectionCount()} selected
              {maxFiles && ` of ${maxFiles} max`}
            </span>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSelect}
              disabled={!canSelect}
            >
              <Check className="h-4 w-4 mr-2" />
              Select {selectedFiles.length > 0 ? `(${selectedFiles.length})` : ''}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Convenience hook for using the media picker
export function useMediaPicker() {
  const [isOpen, setIsOpen] = useState(false)
  const [config, setConfig] = useState<MediaPickerProps['config']>({})
  const [onSelectCallback, setOnSelectCallback] = useState<((files: MediaFile[]) => void) | null>(null)

  const openPicker = (
    pickerConfig: MediaPickerProps['config'] = {},
    onSelect: (files: MediaFile[]) => void
  ) => {
    setConfig(pickerConfig)
    setOnSelectCallback(() => onSelect)
    setIsOpen(true)
  }

  const closePicker = () => {
    setIsOpen(false)
    setConfig({})
    setOnSelectCallback(null)
  }

  const handleSelect = (files: MediaFile[]) => {
    onSelectCallback?.(files)
    closePicker()
  }

  return {
    isOpen,
    openPicker,
    closePicker,
    MediaPickerComponent: () => (
      <MediaPicker
        isOpen={isOpen}
        onClose={closePicker}
        onSelect={handleSelect}
        config={config}
      />
    )
  }
}