import { MediaType } from '@/lib/schemas/media.schema'

export interface MediaFile {
  $id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  width?: number
  height?: number
  url: string
  thumbnailUrl?: string
  alt?: string
  caption?: string
  type: MediaType
  uploadedBy: string
  folder?: string
  tags?: string[]
  $createdAt: string
  $updatedAt: string
}

export interface MediaFolder {
  id: string
  name: string
  path: string
  parentId?: string
  children?: MediaFolder[]
  fileCount: number
  $createdAt: string
  $updatedAt: string
}

export interface MediaLibraryConfig {
  allowedTypes?: MediaType[]
  maxFileSize?: number
  maxFiles?: number
  allowMultiple?: boolean
  showUploader?: boolean
  showFolders?: boolean
  showSearch?: boolean
  showFilters?: boolean
  showBulkActions?: boolean
  defaultView?: 'grid' | 'list'
  gridSize?: 'small' | 'medium' | 'large'
  enableDragDrop?: boolean
  enableCrop?: boolean
  enableResize?: boolean
}

export interface MediaSelection {
  selectedFiles: MediaFile[]
  selectedFolders: MediaFolder[]
  isMultiSelect: boolean
}

export interface MediaFilters {
  type?: MediaType
  folder?: string
  search?: string
  dateRange?: {
    from?: Date
    to?: Date
  }
  sizeRange?: {
    min?: number
    max?: number
  }
  tags?: string[]
  uploadedBy?: string
}

export interface MediaUploadProgress {
  fileId: string
  filename: string
  progress: number
  status: 'pending' | 'uploading' | 'processing' | 'success' | 'error'
  error?: string
}

export interface MediaLibraryState {
  files: MediaFile[]
  folders: MediaFolder[]
  currentFolder?: string
  selectedFiles: MediaFile[]
  selectedFolders: MediaFolder[]
  filters: MediaFilters
  view: 'grid' | 'list'
  gridSize: 'small' | 'medium' | 'large'
  isLoading: boolean
  error?: string
  uploadProgress: MediaUploadProgress[]
}

export interface MediaLibraryActions {
  // File operations
  loadFiles: (folderId?: string) => Promise<void>
  uploadFiles: (files: File[], folderId?: string) => Promise<void>
  deleteFiles: (fileIds: string[]) => Promise<void>
  updateFile: (fileId: string, updates: Partial<MediaFile>) => Promise<void>
  moveFiles: (fileIds: string[], targetFolderId: string) => Promise<void>
  
  // Folder operations
  loadFolders: () => Promise<void>
  createFolder: (name: string, parentId?: string) => Promise<void>
  deleteFolder: (folderId: string) => Promise<void>
  renameFolder: (folderId: string, name: string) => Promise<void>
  moveFolder: (folderId: string, targetParentId?: string) => Promise<void>
  
  // Selection
  selectFile: (file: MediaFile) => void
  selectFiles: (files: MediaFile[]) => void
  deselectFile: (fileId: string) => void
  deselectAll: () => void
  toggleFileSelection: (file: MediaFile) => void
  
  // Filters and search
  setFilters: (filters: Partial<MediaFilters>) => void
  clearFilters: () => void
  setSearch: (query: string) => void
  
  // View
  setView: (view: 'grid' | 'list') => void
  setGridSize: (size: 'small' | 'medium' | 'large') => void
  setCurrentFolder: (folderId?: string) => void
}

export interface MediaPickerProps {
  isOpen: boolean
  onClose: () => void
  onSelect: (files: MediaFile[]) => void
  config?: MediaLibraryConfig
  initialSelection?: MediaFile[]
}

export interface MediaUploaderProps {
  onUpload?: (files: MediaFile[]) => void
  onProgress?: (progress: MediaUploadProgress[]) => void
  config?: {
    allowedTypes?: string[]
    maxFileSize?: number
    maxFiles?: number
    folder?: string
    enableCrop?: boolean
    enableResize?: boolean
  }
}

export interface MediaGridProps {
  files: MediaFile[]
  selectedFiles: MediaFile[]
  onSelect: (file: MediaFile) => void
  onDeselect: (fileId: string) => void
  onToggleSelect: (file: MediaFile) => void
  view: 'grid' | 'list'
  gridSize: 'small' | 'medium' | 'large'
  isSelectable?: boolean
  isMultiSelect?: boolean
}

export interface MediaCardProps {
  file: MediaFile
  isSelected: boolean
  onSelect: (file: MediaFile) => void
  onDeselect: (fileId: string) => void
  onToggleSelect: (file: MediaFile) => void
  view: 'grid' | 'list'
  size: 'small' | 'medium' | 'large'
  isSelectable?: boolean
  showDetails?: boolean
}

export interface MediaPreviewProps {
  file: MediaFile
  isOpen: boolean
  onClose: () => void
  onNext?: () => void
  onPrevious?: () => void
  onDelete?: (fileId: string) => void
  onUpdate?: (fileId: string, updates: Partial<MediaFile>) => void
}

export interface MediaFiltersProps {
  filters: MediaFilters
  onFiltersChange: (filters: Partial<MediaFilters>) => void
  onClearFilters: () => void
  availableTypes?: MediaType[]
  availableFolders?: MediaFolder[]
  availableTags?: string[]
}

export interface MediaSearchProps {
  query: string
  onSearch: (query: string) => void
  placeholder?: string
  suggestions?: string[]
}

export interface MediaFoldersProps {
  folders: MediaFolder[]
  currentFolder?: string
  onFolderSelect: (folderId?: string) => void
  onFolderCreate: (name: string, parentId?: string) => void
  onFolderDelete: (folderId: string) => void
  onFolderRename: (folderId: string, name: string) => void
  onFolderMove: (folderId: string, targetParentId?: string) => void
  isEditable?: boolean
}

export interface MediaBulkActionsProps {
  selectedFiles: MediaFile[]
  selectedFolders: MediaFolder[]
  onDelete: (fileIds: string[], folderIds: string[]) => void
  onMove: (fileIds: string[], folderIds: string[], targetFolderId: string) => void
  onTag: (fileIds: string[], tags: string[]) => void
  onExport: (fileIds: string[]) => void
  availableFolders?: MediaFolder[]
  availableTags?: string[]
}