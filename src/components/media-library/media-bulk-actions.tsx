'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { 
  MoreHorizontal,
  Trash2,
  Move,
  Tag,
  Download,
  Copy,
  Archive,
  AlertTriangle
} from 'lucide-react'

import type { MediaBulkActionsProps } from './types'

export function MediaBulkActions({
  selectedFiles,
  selectedFolders,
  onDelete,
  onMove,
  onTag,
  onExport,
  availableFolders = [],
  availableTags = []
}: MediaBulkActionsProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showMoveDialog, setShowMoveDialog] = useState(false)
  const [showTagDialog, setShowTagDialog] = useState(false)
  const [targetFolderId, setTargetFolderId] = useState('')
  const [newTags, setNewTags] = useState<string[]>([])
  const [tagInput, setTagInput] = useState('')

  const totalSelected = selectedFiles.length + selectedFolders.length
  const fileIds = selectedFiles.map(f => f.$id)
  const folderIds = selectedFolders.map(f => f.id)

  const handleDelete = () => {
    onDelete(fileIds, folderIds)
    setShowDeleteDialog(false)
  }

  const handleMove = () => {
    if (targetFolderId) {
      onMove(fileIds, folderIds, targetFolderId)
      setShowMoveDialog(false)
      setTargetFolderId('')
    }
  }

  const handleAddTag = () => {
    if (tagInput.trim() && !newTags.includes(tagInput.trim())) {
      setNewTags([...newTags, tagInput.trim()])
      setTagInput('')
    }
  }

  const handleRemoveTag = (tag: string) => {
    setNewTags(newTags.filter(t => t !== tag))
  }

  const handleApplyTags = () => {
    if (newTags.length > 0) {
      onTag(fileIds, newTags)
      setShowTagDialog(false)
      setNewTags([])
    }
  }

  const handleExport = () => {
    onExport(fileIds)
  }

  if (totalSelected === 0) {
    return null
  }

  return (
    <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg border">
      <div className="flex items-center gap-2">
        <Badge variant="secondary">
          {totalSelected} selected
        </Badge>
        <span className="text-sm text-muted-foreground">
          {selectedFiles.length > 0 && `${selectedFiles.length} files`}
          {selectedFiles.length > 0 && selectedFolders.length > 0 && ', '}
          {selectedFolders.length > 0 && `${selectedFolders.length} folders`}
        </span>
      </div>

      <div className="flex items-center gap-2">
        {/* Quick Actions */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowMoveDialog(true)}
          disabled={totalSelected === 0}
        >
          <Move className="h-4 w-4 mr-2" />
          Move
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowTagDialog(true)}
          disabled={selectedFiles.length === 0}
        >
          <Tag className="h-4 w-4 mr-2" />
          Tag
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handleExport}
          disabled={selectedFiles.length === 0}
        >
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>

        {/* More Actions */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setShowMoveDialog(true)}>
              <Move className="h-4 w-4 mr-2" />
              Move to Folder
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => setShowTagDialog(true)}
              disabled={selectedFiles.length === 0}
            >
              <Tag className="h-4 w-4 mr-2" />
              Add Tags
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={handleExport}
              disabled={selectedFiles.length === 0}
            >
              <Download className="h-4 w-4 mr-2" />
              Download All
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Copy className="h-4 w-4 mr-2" />
              Copy URLs
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Archive className="h-4 w-4 mr-2" />
              Archive
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={() => setShowDeleteDialog(true)}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Selected
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button
          variant="destructive"
          size="sm"
          onClick={() => setShowDeleteDialog(true)}
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Delete
        </Button>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Confirm Deletion
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the selected items? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-2">
            {selectedFiles.length > 0 && (
              <div className="text-sm">
                <strong>{selectedFiles.length} files</strong> will be permanently deleted:
                <ul className="list-disc list-inside mt-1 text-muted-foreground max-h-32 overflow-y-auto">
                  {selectedFiles.slice(0, 5).map((file) => (
                    <li key={file.$id} className="truncate">
                      {file.filename}
                    </li>
                  ))}
                  {selectedFiles.length > 5 && (
                    <li>... and {selectedFiles.length - 5} more files</li>
                  )}
                </ul>
              </div>
            )}

            {selectedFolders.length > 0 && (
              <div className="text-sm">
                <strong>{selectedFolders.length} folders</strong> and their contents will be permanently deleted:
                <ul className="list-disc list-inside mt-1 text-muted-foreground">
                  {selectedFolders.map((folder) => (
                    <li key={folder.id} className="truncate">
                      {folder.name} ({folder.fileCount} files)
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
            >
              Delete {totalSelected} Item{totalSelected !== 1 ? 's' : ''}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Move Dialog */}
      <Dialog open={showMoveDialog} onOpenChange={setShowMoveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Move Items</DialogTitle>
            <DialogDescription>
              Select a destination folder for the selected items.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Destination Folder</Label>
              <Select value={targetFolderId} onValueChange={setTargetFolderId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a folder..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Root Folder</SelectItem>
                  {availableFolders.map((folder) => (
                    <SelectItem key={folder.id} value={folder.id}>
                      {folder.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="text-sm text-muted-foreground">
              Moving {totalSelected} item{totalSelected !== 1 ? 's' : ''}
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowMoveDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleMove}
              disabled={!targetFolderId}
            >
              Move Items
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Tag Dialog */}
      <Dialog open={showTagDialog} onOpenChange={setShowTagDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Tags</DialogTitle>
            <DialogDescription>
              Add tags to the selected files for better organization.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Add New Tag</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Enter tag name..."
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault()
                      handleAddTag()
                    }
                  }}
                />
                <Button
                  type="button"
                  onClick={handleAddTag}
                  disabled={!tagInput.trim()}
                >
                  Add
                </Button>
              </div>
            </div>

            {availableTags.length > 0 && (
              <div className="space-y-2">
                <Label>Existing Tags</Label>
                <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                  {availableTags.map((tag) => (
                    <Badge
                      key={tag}
                      variant={newTags.includes(tag) ? 'default' : 'outline'}
                      className="cursor-pointer"
                      onClick={() => {
                        if (newTags.includes(tag)) {
                          handleRemoveTag(tag)
                        } else {
                          setNewTags([...newTags, tag])
                        }
                      }}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {newTags.length > 0 && (
              <div className="space-y-2">
                <Label>Tags to Add</Label>
                <div className="flex flex-wrap gap-2">
                  {newTags.map((tag) => (
                    <Badge key={tag} variant="secondary">
                      {tag}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-3 w-3 p-0 ml-1"
                        onClick={() => handleRemoveTag(tag)}
                      >
                        ×
                      </Button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            <div className="text-sm text-muted-foreground">
              Adding tags to {selectedFiles.length} file{selectedFiles.length !== 1 ? 's' : ''}
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowTagDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleApplyTags}
              disabled={newTags.length === 0}
            >
              Apply Tags
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}