'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  Grid3X3, 
  List,
  FileImage,
  FileVideo,
  FileAudio,
  FileText,
  File as FileIcon
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { MediaCard } from './media-card'
import type { MediaGridProps, MediaFile } from './types'

interface ExtendedMediaGridProps extends MediaGridProps {
  onPreview?: (file: MediaFile) => void
  isLoading?: boolean
  enableDragDrop?: boolean
  onFilesUpload?: (files: File[]) => void
  emptyState?: React.ReactNode
}

export function MediaGrid({
  files,
  selectedFiles,
  onSelect,
  onDeselect,
  onToggleSelect,
  onPreview,
  view = 'grid',
  gridSize = 'medium',
  isSelectable = true,
  isMultiSelect = true,
  isLoading = false,
  enableDragDrop = false,
  onFilesUpload,
  emptyState
}: ExtendedMediaGridProps) {
  const [dragOver, setDragOver] = useState(false)

  // Drag and drop configuration
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (onFilesUpload) {
      onFilesUpload(acceptedFiles)
    }
    setDragOver(false)
  }, [onFilesUpload])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    onDragEnter: () => setDragOver(true),
    onDragLeave: () => setDragOver(false),
    multiple: true,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg'],
      'video/*': ['.mp4', '.webm', '.ogg', '.mov', '.avi'],
      'audio/*': ['.mp3', '.wav', '.ogg', '.m4a'],
      'application/pdf': ['.pdf'],
      'text/*': ['.txt', '.md', '.csv'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    },
    maxSize: 50 * 1024 * 1024, // 50MB
    disabled: !enableDragDrop || !onFilesUpload
  })

  // Grid size classes
  const getGridClasses = () => {
    if (view === 'list') return 'grid-cols-1'
    
    switch (gridSize) {
      case 'small':
        return 'grid-cols-6 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12'
      case 'large':
        return 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5'
      default: // medium
        return 'grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8'
    }
  }

  // Loading skeleton
  const renderLoadingSkeleton = () => {
    const skeletonCount = view === 'list' ? 5 : 12
    return (
      <div className={cn('grid gap-4 p-4', getGridClasses())}>
        {Array.from({ length: skeletonCount }).map((_, index) => (
          <div key={index} className="space-y-2">
            <Skeleton className={cn(
              'rounded-lg',
              view === 'list' ? 'h-16 w-full' : 'aspect-square w-full'
            )} />
            {view === 'list' && (
              <div className="space-y-1">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
            )}
          </div>
        ))}
      </div>
    )
  }

  // Empty state
  const renderEmptyState = () => {
    if (emptyState) {
      return <div className="p-8">{emptyState}</div>
    }

    return (
      <div className="flex flex-col items-center justify-center p-12 text-center">
        <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
          <FileImage className="w-8 h-8 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-medium mb-2">No files found</h3>
        <p className="text-muted-foreground mb-4">
          Upload some files to get started
        </p>
        {enableDragDrop && onFilesUpload && (
          <Button
            onClick={() => {
              const input = document.createElement('input')
              input.type = 'file'
              input.multiple = true
              input.accept = 'image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.md'
              input.onchange = (e) => {
                const files = Array.from((e.target as HTMLInputElement).files || [])
                if (files.length > 0) {
                  onFilesUpload(files)
                }
              }
              input.click()
            }}
          >
            <Upload className="w-4 h-4 mr-2" />
            Upload Files
          </Button>
        )}
      </div>
    )
  }

  // Drag overlay
  const renderDragOverlay = () => (
    <div className="absolute inset-0 bg-primary/10 border-2 border-dashed border-primary rounded-lg flex items-center justify-center z-10">
      <div className="text-center">
        <Upload className="w-12 h-12 text-primary mx-auto mb-4" />
        <p className="text-lg font-medium text-primary">Drop files here to upload</p>
        <p className="text-sm text-muted-foreground">
          Supports images, videos, audio, and documents
        </p>
      </div>
    </div>
  )

  // File type stats
  const getFileTypeStats = () => {
    const stats = files.reduce((acc, file) => {
      acc[file.type] = (acc[file.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return Object.entries(stats).map(([type, count]) => {
      const getIcon = () => {
        switch (type) {
          case 'IMAGE': return <FileImage className="w-4 h-4" />
          case 'VIDEO': return <FileVideo className="w-4 h-4" />
          case 'AUDIO': return <FileAudio className="w-4 h-4" />
          case 'DOCUMENT': return <FileText className="w-4 h-4" />
          default: return <FileIcon className="w-4 h-4" />
        }
      }

      return (
        <Badge key={type} variant="outline" className="flex items-center gap-1">
          {getIcon()}
          {type}: {count}
        </Badge>
      )
    })
  }

  if (isLoading) {
    return renderLoadingSkeleton()
  }

  const dropzoneProps = enableDragDrop && onFilesUpload ? getRootProps() : {}

  return (
    <div 
      {...dropzoneProps}
      className={cn(
        'relative h-full',
        enableDragDrop && 'cursor-pointer'
      )}
    >
      {enableDragDrop && onFilesUpload && <input {...getInputProps()} />}
      
      {/* Drag overlay */}
      {dragOver && isDragActive && renderDragOverlay()}

      {/* File type stats */}
      {files.length > 0 && (
        <div className="flex flex-wrap gap-2 p-4 border-b bg-muted/30">
          <span className="text-sm font-medium">Files:</span>
          {getFileTypeStats()}
          <Badge variant="secondary">
            Total: {files.length}
          </Badge>
        </div>
      )}

      {/* Files grid */}
      {files.length === 0 ? (
        renderEmptyState()
      ) : (
        <div className={cn('grid gap-4 p-4', getGridClasses())}>
          {files.map((file) => (
            <MediaCard
              key={file.$id}
              file={file}
              isSelected={selectedFiles.some(f => f.$id === file.$id)}
              onSelect={onSelect}
              onDeselect={onDeselect}
              onToggleSelect={onToggleSelect}
              onPreview={onPreview}
              view={view}
              size={gridSize}
              isSelectable={isSelectable}
              showDetails={view === 'list'}
            />
          ))}
        </div>
      )}

      {/* View toggle for mobile */}
      <div className="fixed bottom-4 right-4 md:hidden">
        <Card className="p-2">
          <div className="flex gap-1">
            <Button
              variant={view === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => {
                // This would need to be passed as a prop
                // onViewChange?.('grid')
              }}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={view === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => {
                // This would need to be passed as a prop
                // onViewChange?.('list')
              }}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </Card>
      </div>
    </div>
  )
}