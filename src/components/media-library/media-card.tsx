'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { 
  MoreHorizontal,
  Eye,
  Download,
  Edit,
  Trash2,
  Copy,
  Share,
  Tag,
  Move,
  FileImage,
  FileVideo,
  FileAudio,
  FileText,
  File as FileIcon,
  Calendar,
  User,
  HardDrive
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { formatDistanceToNow } from 'date-fns'

import type { MediaCardProps, MediaFile } from './types'

interface ExtendedMediaCardProps extends MediaCardProps {
  onPreview?: (file: MediaFile) => void
  onDownload?: (file: MediaFile) => void
  onEdit?: (file: MediaFile) => void
  onDelete?: (file: MediaFile) => void
  onCopy?: (file: MediaFile) => void
  onShare?: (file: MediaFile) => void
  onTag?: (file: MediaFile) => void
  onMove?: (file: MediaFile) => void
}

export function MediaCard({
  file,
  isSelected,
  onSelect,
  onDeselect,
  onToggleSelect,
  onPreview,
  onDownload,
  onEdit,
  onDelete,
  onCopy,
  onShare,
  onTag,
  onMove,
  view = 'grid',
  size = 'medium',
  isSelectable = true,
  showDetails = false
}: ExtendedMediaCardProps) {
  const [imageError, setImageError] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  // Get file type icon
  const getFileIcon = () => {
    switch (file.type) {
      case 'IMAGE':
        return <FileImage className="w-6 h-6" />
      case 'VIDEO':
        return <FileVideo className="w-6 h-6" />
      case 'AUDIO':
        return <FileAudio className="w-6 h-6" />
      case 'DOCUMENT':
        return <FileText className="w-6 h-6" />
      default:
        return <FileIcon className="w-6 h-6" />
    }
  }

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // Get card size classes
  const getCardClasses = () => {
    if (view === 'list') {
      return 'flex-row h-20'
    }
    
    return 'flex-col aspect-square'
  }

  // Get image size classes
  const getImageClasses = () => {
    if (view === 'list') {
      return 'w-16 h-16 flex-shrink-0'
    }
    
    return 'w-full h-full'
  }

  // Handle card click
  const handleCardClick = (e: React.MouseEvent) => {
    e.preventDefault()
    
    if (isSelectable) {
      onToggleSelect(file)
    } else if (onPreview) {
      onPreview(file)
    }
  }

  // Handle checkbox change
  const handleCheckboxChange = (checked: boolean) => {
    if (checked) {
      onSelect(file)
    } else {
      onDeselect(file.$id)
    }
  }

  // Render file preview
  const renderFilePreview = () => {
    if (file.type === 'IMAGE' && file.thumbnailUrl && !imageError) {
      return (
        <img
          src={file.thumbnailUrl}
          alt={file.alt || file.filename}
          className={cn(
            'object-cover rounded-md',
            getImageClasses()
          )}
          onError={() => setImageError(true)}
          loading="lazy"
        />
      )
    }

    // Fallback to icon
    return (
      <div className={cn(
        'bg-muted rounded-md flex items-center justify-center text-muted-foreground',
        getImageClasses()
      )}>
        {getFileIcon()}
      </div>
    )
  }

  // Render file info
  const renderFileInfo = () => (
    <div className={cn(
      'space-y-1',
      view === 'list' ? 'flex-1 min-w-0 px-3' : 'p-2'
    )}>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <h4 className={cn(
              'font-medium truncate',
              view === 'list' ? 'text-sm' : 'text-xs'
            )}>
              {file.originalName}
            </h4>
          </TooltipTrigger>
          <TooltipContent>
            <p>{file.originalName}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <div className={cn(
        'flex items-center gap-2 text-muted-foreground',
        view === 'list' ? 'text-xs' : 'text-xs'
      )}>
        <Badge variant="outline" className="text-xs px-1 py-0">
          {file.type}
        </Badge>
        <span>{formatFileSize(file.size)}</span>
      </div>

      {showDetails && (
        <div className="space-y-1 text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Calendar className="w-3 h-3" />
            <span>{formatDistanceToNow(new Date(file.$createdAt), { addSuffix: true })}</span>
          </div>
          {file.width && file.height && (
            <div className="flex items-center gap-1">
              <HardDrive className="w-3 h-3" />
              <span>{file.width} × {file.height}</span>
            </div>
          )}
        </div>
      )}
    </div>
  )

  // Render actions menu
  const renderActionsMenu = () => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            'h-8 w-8 p-0',
            view === 'list' ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
          )}
          onClick={(e) => e.stopPropagation()}
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {onPreview && (
          <DropdownMenuItem onClick={() => onPreview(file)}>
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </DropdownMenuItem>
        )}
        {onDownload && (
          <DropdownMenuItem onClick={() => onDownload(file)}>
            <Download className="h-4 w-4 mr-2" />
            Download
          </DropdownMenuItem>
        )}
        {onCopy && (
          <DropdownMenuItem onClick={() => onCopy(file)}>
            <Copy className="h-4 w-4 mr-2" />
            Copy URL
          </DropdownMenuItem>
        )}
        {onShare && (
          <DropdownMenuItem onClick={() => onShare(file)}>
            <Share className="h-4 w-4 mr-2" />
            Share
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator />
        {onEdit && (
          <DropdownMenuItem onClick={() => onEdit(file)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </DropdownMenuItem>
        )}
        {onTag && (
          <DropdownMenuItem onClick={() => onTag(file)}>
            <Tag className="h-4 w-4 mr-2" />
            Add Tags
          </DropdownMenuItem>
        )}
        {onMove && (
          <DropdownMenuItem onClick={() => onMove(file)}>
            <Move className="h-4 w-4 mr-2" />
            Move
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator />
        {onDelete && (
          <DropdownMenuItem 
            onClick={() => onDelete(file)}
            className="text-destructive focus:text-destructive"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )

  return (
    <Card
      className={cn(
        'group relative cursor-pointer transition-all duration-200 hover:shadow-md',
        isSelected && 'ring-2 ring-primary ring-offset-2',
        getCardClasses()
      )}
      onClick={handleCardClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardContent className={cn(
        'p-0 h-full flex',
        getCardClasses()
      )}>
        {/* Selection checkbox */}
        {isSelectable && (
          <div className={cn(
            'absolute z-10',
            view === 'list' ? 'left-2 top-1/2 -translate-y-1/2' : 'top-2 left-2'
          )}>
            <Checkbox
              checked={isSelected}
              onCheckedChange={handleCheckboxChange}
              onClick={(e) => e.stopPropagation()}
              className="bg-background/80 backdrop-blur-sm"
            />
          </div>
        )}

        {/* Actions menu */}
        <div className={cn(
          'absolute z-10',
          view === 'list' ? 'right-2 top-1/2 -translate-y-1/2' : 'top-2 right-2'
        )}>
          {renderActionsMenu()}
        </div>

        {/* File preview */}
        <div className={cn(
          'relative overflow-hidden',
          view === 'list' ? 'w-16 h-16 flex-shrink-0' : 'flex-1'
        )}>
          {renderFilePreview()}
          
          {/* Overlay for better text visibility */}
          {view === 'grid' && (
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
          )}
        </div>

        {/* File info */}
        {renderFileInfo()}

        {/* File tags */}
        {file.tags && file.tags.length > 0 && (
          <div className={cn(
            'flex flex-wrap gap-1',
            view === 'list' ? 'px-3 pb-2' : 'p-2 pt-0'
          )}>
            {file.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs px-1 py-0">
                {tag}
              </Badge>
            ))}
            {file.tags.length > 3 && (
              <Badge variant="secondary" className="text-xs px-1 py-0">
                +{file.tags.length - 3}
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}