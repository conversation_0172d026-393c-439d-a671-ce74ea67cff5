'use client'

import { useState, useCallback } from 'react'
import { useToast } from '@/hooks/use-toast'
import { storageService } from '@/lib/appwrite/services/storage'
import type { MediaUploadProgress } from '../types'

interface UseMediaUploadOptions {
  maxFileSize?: number
  allowedTypes?: string[]
  onSuccess?: (files: any[]) => void
  onError?: (error: string) => void
}

export function useMediaUpload(options: UseMediaUploadOptions = {}) {
  const { toast } = useToast()
  const [uploadProgress, setUploadProgress] = useState<MediaUploadProgress[]>([])
  const [isUploading, setIsUploading] = useState(false)

  const validateFile = useCallback((file: File): string | null => {
    // Check file size
    if (options.maxFileSize && file.size > options.maxFileSize) {
      return `File size exceeds ${Math.round(options.maxFileSize / 1024 / 1024)}MB limit`
    }

    // Check file type
    if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {
      return `File type ${file.type} is not allowed`
    }

    return null
  }, [options.maxFileSize, options.allowedTypes])

  const uploadFiles = useCallback(async (files: File[], folderId?: string) => {
    // Validate files
    const validationErrors: string[] = []
    files.forEach((file, index) => {
      const error = validateFile(file)
      if (error) {
        validationErrors.push(`File ${index + 1}: ${error}`)
      }
    })

    if (validationErrors.length > 0) {
      const errorMessage = validationErrors.join('\n')
      toast({
        title: 'Validation Error',
        description: errorMessage,
        variant: 'destructive'
      })
      options.onError?.(errorMessage)
      return
    }

    setIsUploading(true)
    
    // Initialize progress tracking
    const initialProgress: MediaUploadProgress[] = files.map((file, index) => ({
      fileId: `upload-${Date.now()}-${index}`,
      filename: file.name,
      progress: 0,
      status: 'pending'
    }))
    
    setUploadProgress(initialProgress)

    const uploadPromises = files.map(async (file, index) => {
      const progressItem = initialProgress[index]
      
      try {
        // Update status to uploading
        setUploadProgress(prev => 
          prev.map(p => 
            p.fileId === progressItem.fileId 
              ? { ...p, status: 'uploading' }
              : p
          )
        )

        // Upload to Appwrite Storage
        const uploadResponse = await storageService.uploadFile(file, {
          onProgress: (progress) => {
            setUploadProgress(prev => 
              prev.map(p => 
                p.fileId === progressItem.fileId 
                  ? { ...p, progress: progress.progress || 0 }
                  : p
              )
            )
          }
        })

        if (!uploadResponse.success || !uploadResponse.data) {
          throw new Error(uploadResponse.error || 'Upload failed')
        }

        // Update status to processing
        setUploadProgress(prev => 
          prev.map(p => 
            p.fileId === progressItem.fileId 
              ? { ...p, status: 'processing', progress: 100 }
              : p
          )
        )

        // Get file URLs
        const viewResponse = await storageService.getFileView(uploadResponse.data.$id)
        const previewResponse = await storageService.getFilePreview(uploadResponse.data.$id, {
          width: 300,
          height: 300
        })

        // Create media file object
        const mediaFile = {
          $id: uploadResponse.data.$id,
          filename: uploadResponse.data.name,
          originalName: file.name,
          mimeType: file.type,
          size: file.size,
          url: viewResponse.success ? viewResponse.data : '',
          thumbnailUrl: previewResponse.success ? previewResponse.data : undefined,
          type: getMediaType(file.type),
          uploadedBy: 'current-user', // Replace with actual user ID
          folder: folderId,
          $createdAt: new Date().toISOString(),
          $updatedAt: new Date().toISOString()
        }

        // Update status to success
        setUploadProgress(prev => 
          prev.map(p => 
            p.fileId === progressItem.fileId 
              ? { ...p, status: 'success' }
              : p
          )
        )

        return mediaFile

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Upload failed'
        
        setUploadProgress(prev => 
          prev.map(p => 
            p.fileId === progressItem.fileId 
              ? { ...p, status: 'error', error: errorMessage }
              : p
          )
        )

        throw error
      }
    })

    try {
      const uploadedFiles = await Promise.all(uploadPromises)
      
      toast({
        title: 'Success',
        description: `${uploadedFiles.length} file(s) uploaded successfully`
      })

      options.onSuccess?.(uploadedFiles)

      // Clear progress after delay
      setTimeout(() => {
        setUploadProgress([])
        setIsUploading(false)
      }, 3000)

      return uploadedFiles

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      
      toast({
        title: 'Upload Error',
        description: errorMessage,
        variant: 'destructive'
      })

      options.onError?.(errorMessage)
      setIsUploading(false)
    }
  }, [validateFile, toast, options])

  const cancelUpload = useCallback((fileId: string) => {
    setUploadProgress(prev => prev.filter(p => p.fileId !== fileId))
  }, [])

  const clearProgress = useCallback(() => {
    setUploadProgress([])
    setIsUploading(false)
  }, [])

  return {
    uploadFiles,
    uploadProgress,
    isUploading,
    cancelUpload,
    clearProgress
  }
}

function getMediaType(mimeType: string): 'IMAGE' | 'VIDEO' | 'AUDIO' | 'DOCUMENT' | 'OTHER' {
  if (mimeType.startsWith('image/')) return 'IMAGE'
  if (mimeType.startsWith('video/')) return 'VIDEO'
  if (mimeType.startsWith('audio/')) return 'AUDIO'
  if (mimeType.includes('pdf') || mimeType.includes('document') || mimeType.includes('text')) return 'DOCUMENT'
  return 'OTHER'
}