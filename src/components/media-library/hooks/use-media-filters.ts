'use client'

import { useState, useCallback, useMemo } from 'react'
import { useDebounce } from '@/hooks/use-debounce'
import type { MediaFile, MediaFilters } from '../types'

interface UseMediaFiltersOptions {
  onFiltersChange?: (filters: MediaFilters) => void
  debounceMs?: number
}

export function useMediaFilters(options: UseMediaFiltersOptions = {}) {
  const [filters, setFilters] = useState<MediaFilters>({})
  const [searchQuery, setSearchQuery] = useState('')
  
  // Debounce search query
  const debouncedSearch = useDebounce(searchQuery, options.debounceMs || 300)

  // Update filters when debounced search changes
  useMemo(() => {
    if (debouncedSearch !== filters.search) {
      const newFilters = { ...filters, search: debouncedSearch || undefined }
      setFilters(newFilters)
      options.onFiltersChange?.(newFilters)
    }
  }, [debouncedSearch, filters, options])

  const updateFilters = useCallback((newFilters: Partial<MediaFilters>) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)
    options.onFiltersChange?.(updatedFilters)
  }, [filters, options])

  const setType = useCallback((type?: MediaFilters['type']) => {
    updateFilters({ type })
  }, [updateFilters])

  const setFolder = useCallback((folder?: string) => {
    updateFilters({ folder })
  }, [updateFilters])

  const setSearch = useCallback((search: string) => {
    setSearchQuery(search)
  }, [])

  const setDateRange = useCallback((dateRange?: MediaFilters['dateRange']) => {
    updateFilters({ dateRange })
  }, [updateFilters])

  const setSizeRange = useCallback((sizeRange?: MediaFilters['sizeRange']) => {
    updateFilters({ sizeRange })
  }, [updateFilters])

  const setTags = useCallback((tags?: string[]) => {
    updateFilters({ tags })
  }, [updateFilters])

  const setUploadedBy = useCallback((uploadedBy?: string) => {
    updateFilters({ uploadedBy })
  }, [updateFilters])

  const clearFilters = useCallback(() => {
    setFilters({})
    setSearchQuery('')
    options.onFiltersChange?.({})
  }, [options])

  const clearFilter = useCallback((filterKey: keyof MediaFilters) => {
    const newFilters = { ...filters }
    delete newFilters[filterKey]
    setFilters(newFilters)
    
    if (filterKey === 'search') {
      setSearchQuery('')
    }
    
    options.onFiltersChange?.(newFilters)
  }, [filters, options])

  // Filter files based on current filters
  const filterFiles = useCallback((files: MediaFile[]): MediaFile[] => {
    return files.filter(file => {
      // Type filter
      if (filters.type && file.type !== filters.type) {
        return false
      }

      // Folder filter
      if (filters.folder !== undefined) {
        if (filters.folder === '' && file.folder) {
          return false
        }
        if (filters.folder !== '' && file.folder !== filters.folder) {
          return false
        }
      }

      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase()
        const matchesFilename = file.filename.toLowerCase().includes(searchLower)
        const matchesOriginalName = file.originalName.toLowerCase().includes(searchLower)
        const matchesAlt = file.alt?.toLowerCase().includes(searchLower)
        const matchesCaption = file.caption?.toLowerCase().includes(searchLower)
        
        if (!matchesFilename && !matchesOriginalName && !matchesAlt && !matchesCaption) {
          return false
        }
      }

      // Date range filter
      if (filters.dateRange) {
        const fileDate = new Date(file.$createdAt)
        
        if (filters.dateRange.from && fileDate < filters.dateRange.from) {
          return false
        }
        
        if (filters.dateRange.to && fileDate > filters.dateRange.to) {
          return false
        }
      }

      // Size range filter
      if (filters.sizeRange) {
        if (filters.sizeRange.min && file.size < filters.sizeRange.min) {
          return false
        }
        
        if (filters.sizeRange.max && file.size > filters.sizeRange.max) {
          return false
        }
      }

      // Tags filter
      if (filters.tags && filters.tags.length > 0) {
        if (!file.tags || !filters.tags.some(tag => file.tags!.includes(tag))) {
          return false
        }
      }

      // Uploaded by filter
      if (filters.uploadedBy && file.uploadedBy !== filters.uploadedBy) {
        return false
      }

      return true
    })
  }, [filters])

  // Get active filter count
  const getActiveFilterCount = useCallback(() => {
    let count = 0
    
    if (filters.type) count++
    if (filters.folder !== undefined) count++
    if (filters.search) count++
    if (filters.dateRange) count++
    if (filters.sizeRange) count++
    if (filters.tags && filters.tags.length > 0) count++
    if (filters.uploadedBy) count++
    
    return count
  }, [filters])

  // Check if filters are active
  const hasActiveFilters = useCallback(() => {
    return getActiveFilterCount() > 0
  }, [getActiveFilterCount])

  // Get filter summary
  const getFilterSummary = useCallback(() => {
    const summary: string[] = []
    
    if (filters.type) {
      summary.push(`Type: ${filters.type}`)
    }
    
    if (filters.folder !== undefined) {
      summary.push(filters.folder ? `Folder: ${filters.folder}` : 'Root folder')
    }
    
    if (filters.search) {
      summary.push(`Search: "${filters.search}"`)
    }
    
    if (filters.dateRange) {
      if (filters.dateRange.from && filters.dateRange.to) {
        summary.push(`Date: ${filters.dateRange.from.toLocaleDateString()} - ${filters.dateRange.to.toLocaleDateString()}`)
      } else if (filters.dateRange.from) {
        summary.push(`Date: After ${filters.dateRange.from.toLocaleDateString()}`)
      } else if (filters.dateRange.to) {
        summary.push(`Date: Before ${filters.dateRange.to.toLocaleDateString()}`)
      }
    }
    
    if (filters.sizeRange) {
      if (filters.sizeRange.min && filters.sizeRange.max) {
        summary.push(`Size: ${formatFileSize(filters.sizeRange.min)} - ${formatFileSize(filters.sizeRange.max)}`)
      } else if (filters.sizeRange.min) {
        summary.push(`Size: > ${formatFileSize(filters.sizeRange.min)}`)
      } else if (filters.sizeRange.max) {
        summary.push(`Size: < ${formatFileSize(filters.sizeRange.max)}`)
      }
    }
    
    if (filters.tags && filters.tags.length > 0) {
      summary.push(`Tags: ${filters.tags.join(', ')}`)
    }
    
    if (filters.uploadedBy) {
      summary.push(`Uploaded by: ${filters.uploadedBy}`)
    }
    
    return summary
  }, [filters])

  return {
    filters,
    searchQuery,
    updateFilters,
    setType,
    setFolder,
    setSearch,
    setDateRange,
    setSizeRange,
    setTags,
    setUploadedBy,
    clearFilters,
    clearFilter,
    filterFiles,
    getActiveFilterCount,
    hasActiveFilters,
    getFilterSummary
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}