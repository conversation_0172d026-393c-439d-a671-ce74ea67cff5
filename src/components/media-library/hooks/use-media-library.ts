'use client'

import { useState, useCallback, useEffect } from 'react'
import { useToast } from '@/hooks/use-toast'
import { storageService } from '@/lib/appwrite/services/storage'
import { databaseService } from '@/lib/appwrite/services/database'
import type {
  MediaFile,
  MediaFolder,
  MediaFilters,
  MediaLibraryState,
  MediaLibraryActions,
  MediaUploadProgress
} from '../types'

const MEDIA_COLLECTION_ID = 'media_files'
const FOLDERS_COLLECTION_ID = 'media_folders'

export function useMediaLibrary() {
  const { toast } = useToast()
  
  const [state, setState] = useState<MediaLibraryState>({
    files: [],
    folders: [],
    currentFolder: undefined,
    selectedFiles: [],
    selectedFolders: [],
    filters: {},
    view: 'grid',
    gridSize: 'medium',
    isLoading: false,
    uploadProgress: []
  })

  // Load files from Appwrite
  const loadFiles = useCallback(async (folderId?: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: undefined }))
    
    try {
      const queries = []
      
      // Filter by folder
      if (folderId) {
        queries.push(`equal("folder", "${folderId}")`)
      } else if (folderId === undefined && !state.filters.folder) {
        queries.push(`isNull("folder")`)
      }
      
      // Apply filters
      if (state.filters.type) {
        queries.push(`equal("type", "${state.filters.type}")`)
      }
      
      if (state.filters.search) {
        queries.push(`search("filename", "${state.filters.search}")`)
      }
      
      if (state.filters.uploadedBy) {
        queries.push(`equal("uploadedBy", "${state.filters.uploadedBy}")`)
      }
      
      if (state.filters.dateRange?.from) {
        queries.push(`greaterThanEqual("$createdAt", "${state.filters.dateRange.from.toISOString()}")`)
      }
      
      if (state.filters.dateRange?.to) {
        queries.push(`lessThanEqual("$createdAt", "${state.filters.dateRange.to.toISOString()}")`)
      }

      const response = await databaseService.listDocuments(MEDIA_COLLECTION_ID, queries)
      
      if (response.success && response.data) {
        setState(prev => ({
          ...prev,
          files: response.data.documents as MediaFile[],
          currentFolder: folderId,
          isLoading: false
        }))
      } else {
        throw new Error(response.error || 'Failed to load files')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load files'
      setState(prev => ({ ...prev, error: errorMessage, isLoading: false }))
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      })
    }
  }, [state.filters, toast])

  // Load folders from Appwrite
  const loadFolders = useCallback(async () => {
    try {
      const response = await databaseService.listDocuments(FOLDERS_COLLECTION_ID)
      
      if (response.success && response.data) {
        setState(prev => ({
          ...prev,
          folders: response.data.documents as MediaFolder[]
        }))
      }
    } catch (error) {
      console.error('Failed to load folders:', error)
    }
  }, [])

  // Upload files to Appwrite
  const uploadFiles = useCallback(async (files: File[], folderId?: string) => {
    const uploadPromises = files.map(async (file, index) => {
      const fileId = `upload-${Date.now()}-${index}`
      
      // Initialize progress
      setState(prev => ({
        ...prev,
        uploadProgress: [
          ...prev.uploadProgress,
          {
            fileId,
            filename: file.name,
            progress: 0,
            status: 'pending'
          }
        ]
      }))

      try {
        // Update status to uploading
        setState(prev => ({
          ...prev,
          uploadProgress: prev.uploadProgress.map(p =>
            p.fileId === fileId ? { ...p, status: 'uploading' } : p
          )
        }))

        // Upload to Appwrite Storage
        const uploadResponse = await storageService.uploadFile(file, {
          onProgress: (progress) => {
            setState(prev => ({
              ...prev,
              uploadProgress: prev.uploadProgress.map(p =>
                p.fileId === fileId ? { ...p, progress: progress.progress || 0 } : p
              )
            }))
          }
        })

        if (!uploadResponse.success || !uploadResponse.data) {
          throw new Error(uploadResponse.error || 'Upload failed')
        }

        const uploadedFile = uploadResponse.data

        // Update status to processing
        setState(prev => ({
          ...prev,
          uploadProgress: prev.uploadProgress.map(p =>
            p.fileId === fileId ? { ...p, status: 'processing', progress: 100 } : p
          )
        }))

        // Get file URL
        const urlResponse = await storageService.getFileView(uploadedFile.$id)
        const thumbnailResponse = await storageService.getFilePreview(uploadedFile.$id, {
          width: 300,
          height: 300
        })

        // Determine media type
        let mediaType: MediaFile['type'] = 'OTHER'
        if (file.type.startsWith('image/')) mediaType = 'IMAGE'
        else if (file.type.startsWith('video/')) mediaType = 'VIDEO'
        else if (file.type.startsWith('audio/')) mediaType = 'AUDIO'
        else if (file.type.includes('pdf') || file.type.includes('document')) mediaType = 'DOCUMENT'

        // Save metadata to database
        const mediaData = {
          filename: uploadedFile.name,
          originalName: file.name,
          mimeType: file.type,
          size: file.size,
          url: urlResponse.success ? urlResponse.data : '',
          thumbnailUrl: thumbnailResponse.success ? thumbnailResponse.data : undefined,
          type: mediaType,
          uploadedBy: 'current-user', // Replace with actual user ID
          folder: folderId,
          fileId: uploadedFile.$id
        }

        const dbResponse = await databaseService.createDocument(MEDIA_COLLECTION_ID, mediaData)
        
        if (dbResponse.success) {
          // Update status to success
          setState(prev => ({
            ...prev,
            uploadProgress: prev.uploadProgress.map(p =>
              p.fileId === fileId ? { ...p, status: 'success' } : p
            )
          }))

          return dbResponse.data as MediaFile
        } else {
          throw new Error(dbResponse.error || 'Failed to save file metadata')
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Upload failed'
        
        setState(prev => ({
          ...prev,
          uploadProgress: prev.uploadProgress.map(p =>
            p.fileId === fileId ? { ...p, status: 'error', error: errorMessage } : p
          )
        }))

        throw error
      }
    })

    try {
      const uploadedFiles = await Promise.all(uploadPromises)
      
      // Add uploaded files to state
      setState(prev => ({
        ...prev,
        files: [...prev.files, ...uploadedFiles]
      }))

      // Clear upload progress after delay
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          uploadProgress: []
        }))
      }, 3000)

      toast({
        title: 'Success',
        description: `${uploadedFiles.length} file(s) uploaded successfully`
      })

    } catch (error) {
      toast({
        title: 'Upload Error',
        description: 'Some files failed to upload',
        variant: 'destructive'
      })
    }
  }, [toast])

  // Delete files
  const deleteFiles = useCallback(async (fileIds: string[]) => {
    try {
      setState(prev => ({ ...prev, isLoading: true }))

      // Get file details first
      const filesToDelete = state.files.filter(f => fileIds.includes(f.$id))

      // Delete from storage and database
      await Promise.all(fileIds.map(async (fileId) => {
        const file = filesToDelete.find(f => f.$id === fileId)
        if (file?.fileId) {
          await storageService.deleteFile(file.fileId)
        }
        await databaseService.deleteDocument(MEDIA_COLLECTION_ID, fileId)
      }))

      // Remove from state
      setState(prev => ({
        ...prev,
        files: prev.files.filter(f => !fileIds.includes(f.$id)),
        selectedFiles: prev.selectedFiles.filter(f => !fileIds.includes(f.$id)),
        isLoading: false
      }))

      toast({
        title: 'Success',
        description: `${fileIds.length} file(s) deleted successfully`
      })

    } catch (error) {
      setState(prev => ({ ...prev, isLoading: false }))
      toast({
        title: 'Error',
        description: 'Failed to delete files',
        variant: 'destructive'
      })
    }
  }, [state.files, toast])

  // Update file
  const updateFile = useCallback(async (fileId: string, updates: Partial<MediaFile>) => {
    try {
      const response = await databaseService.updateDocument(MEDIA_COLLECTION_ID, fileId, updates)
      
      if (response.success) {
        setState(prev => ({
          ...prev,
          files: prev.files.map(f => f.$id === fileId ? { ...f, ...updates } : f)
        }))

        toast({
          title: 'Success',
          description: 'File updated successfully'
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update file',
        variant: 'destructive'
      })
    }
  }, [toast])

  // Move files
  const moveFiles = useCallback(async (fileIds: string[], targetFolderId: string) => {
    try {
      await Promise.all(fileIds.map(fileId =>
        databaseService.updateDocument(MEDIA_COLLECTION_ID, fileId, { folder: targetFolderId })
      ))

      setState(prev => ({
        ...prev,
        files: prev.files.map(f => 
          fileIds.includes(f.$id) ? { ...f, folder: targetFolderId } : f
        )
      }))

      toast({
        title: 'Success',
        description: `${fileIds.length} file(s) moved successfully`
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to move files',
        variant: 'destructive'
      })
    }
  }, [toast])

  // Create folder
  const createFolder = useCallback(async (name: string, parentId?: string) => {
    try {
      const folderData = {
        name,
        path: parentId ? `${parentId}/${name}` : name,
        parentId,
        fileCount: 0
      }

      const response = await databaseService.createDocument(FOLDERS_COLLECTION_ID, folderData)
      
      if (response.success) {
        setState(prev => ({
          ...prev,
          folders: [...prev.folders, response.data as MediaFolder]
        }))

        toast({
          title: 'Success',
          description: 'Folder created successfully'
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create folder',
        variant: 'destructive'
      })
    }
  }, [toast])

  // Selection methods
  const selectFile = useCallback((file: MediaFile) => {
    setState(prev => ({
      ...prev,
      selectedFiles: [...prev.selectedFiles, file]
    }))
  }, [])

  const selectFiles = useCallback((files: MediaFile[]) => {
    setState(prev => ({
      ...prev,
      selectedFiles: files
    }))
  }, [])

  const deselectFile = useCallback((fileId: string) => {
    setState(prev => ({
      ...prev,
      selectedFiles: prev.selectedFiles.filter(f => f.$id !== fileId)
    }))
  }, [])

  const deselectAll = useCallback(() => {
    setState(prev => ({
      ...prev,
      selectedFiles: [],
      selectedFolders: []
    }))
  }, [])

  const toggleFileSelection = useCallback((file: MediaFile) => {
    setState(prev => {
      const isSelected = prev.selectedFiles.some(f => f.$id === file.$id)
      return {
        ...prev,
        selectedFiles: isSelected
          ? prev.selectedFiles.filter(f => f.$id !== file.$id)
          : [...prev.selectedFiles, file]
      }
    })
  }, [])

  // Filter methods
  const setFilters = useCallback((filters: Partial<MediaFilters>) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...filters }
    }))
  }, [])

  const clearFilters = useCallback(() => {
    setState(prev => ({
      ...prev,
      filters: {}
    }))
  }, [])

  const setSearch = useCallback((query: string) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, search: query }
    }))
  }, [])

  // View methods
  const setView = useCallback((view: 'grid' | 'list') => {
    setState(prev => ({ ...prev, view }))
  }, [])

  const setGridSize = useCallback((gridSize: 'small' | 'medium' | 'large') => {
    setState(prev => ({ ...prev, gridSize }))
  }, [])

  const setCurrentFolder = useCallback((folderId?: string) => {
    setState(prev => ({ ...prev, currentFolder: folderId }))
    loadFiles(folderId)
  }, [loadFiles])

  // Load initial data
  useEffect(() => {
    loadFiles()
    loadFolders()
  }, [])

  // Reload files when filters change
  useEffect(() => {
    loadFiles(state.currentFolder)
  }, [state.filters])

  const actions: MediaLibraryActions = {
    loadFiles,
    uploadFiles,
    deleteFiles,
    updateFile,
    moveFiles,
    loadFolders,
    createFolder,
    deleteFolder: async () => {}, // TODO: Implement
    renameFolder: async () => {}, // TODO: Implement
    moveFolder: async () => {}, // TODO: Implement
    selectFile,
    selectFiles,
    deselectFile,
    deselectAll,
    toggleFileSelection,
    setFilters,
    clearFilters,
    setSearch,
    setView,
    setGridSize,
    setCurrentFolder
  }

  return {
    ...state,
    actions
  }
}