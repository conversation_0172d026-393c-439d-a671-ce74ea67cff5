'use client'

import { useState, useCallback } from 'react'
import type { MediaFile, MediaFolder } from '../types'

interface UseMediaSelectionOptions {
  allowMultiple?: boolean
  maxSelection?: number
  allowedTypes?: string[]
  onSelectionChange?: (files: MediaFile[], folders: MediaFolder[]) => void
}

export function useMediaSelection(options: UseMediaSelectionOptions = {}) {
  const [selectedFiles, setSelectedFiles] = useState<MediaFile[]>([])
  const [selectedFolders, setSelectedFolders] = useState<MediaFolder[]>([])

  const selectFile = useCallback((file: MediaFile) => {
    if (!options.allowMultiple) {
      setSelectedFiles([file])
      options.onSelectionChange?.([file], selectedFolders)
      return
    }

    if (options.maxSelection && selectedFiles.length >= options.maxSelection) {
      return
    }

    if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {
      return
    }

    const isAlreadySelected = selectedFiles.some(f => f.$id === file.$id)
    if (!isAlreadySelected) {
      const newSelection = [...selectedFiles, file]
      setSelectedFiles(newSelection)
      options.onSelectionChange?.(newSelection, selectedFolders)
    }
  }, [selectedFiles, selectedFolders, options])

  const deselectFile = useCallback((fileId: string) => {
    const newSelection = selectedFiles.filter(f => f.$id !== fileId)
    setSelectedFiles(newSelection)
    options.onSelectionChange?.(newSelection, selectedFolders)
  }, [selectedFiles, selectedFolders, options])

  const toggleFileSelection = useCallback((file: MediaFile) => {
    const isSelected = selectedFiles.some(f => f.$id === file.$id)
    if (isSelected) {
      deselectFile(file.$id)
    } else {
      selectFile(file)
    }
  }, [selectedFiles, selectFile, deselectFile])

  const selectFiles = useCallback((files: MediaFile[]) => {
    let newSelection = files

    if (options.allowedTypes) {
      newSelection = files.filter(f => options.allowedTypes!.includes(f.type))
    }

    if (options.maxSelection) {
      newSelection = newSelection.slice(0, options.maxSelection)
    }

    if (!options.allowMultiple) {
      newSelection = newSelection.slice(0, 1)
    }

    setSelectedFiles(newSelection)
    options.onSelectionChange?.(newSelection, selectedFolders)
  }, [selectedFolders, options])

  const selectFolder = useCallback((folder: MediaFolder) => {
    if (!options.allowMultiple) {
      setSelectedFolders([folder])
      options.onSelectionChange?.(selectedFiles, [folder])
      return
    }

    const isAlreadySelected = selectedFolders.some(f => f.id === folder.id)
    if (!isAlreadySelected) {
      const newSelection = [...selectedFolders, folder]
      setSelectedFolders(newSelection)
      options.onSelectionChange?.(selectedFiles, newSelection)
    }
  }, [selectedFiles, selectedFolders, options])

  const deselectFolder = useCallback((folderId: string) => {
    const newSelection = selectedFolders.filter(f => f.id !== folderId)
    setSelectedFolders(newSelection)
    options.onSelectionChange?.(selectedFiles, newSelection)
  }, [selectedFiles, selectedFolders, options])

  const toggleFolderSelection = useCallback((folder: MediaFolder) => {
    const isSelected = selectedFolders.some(f => f.id === folder.id)
    if (isSelected) {
      deselectFolder(folder.id)
    } else {
      selectFolder(folder)
    }
  }, [selectedFolders, selectFolder, deselectFolder])

  const selectAll = useCallback((files: MediaFile[], folders: MediaFolder[] = []) => {
    selectFiles(files)
    if (folders.length > 0) {
      setSelectedFolders(options.allowMultiple ? folders : folders.slice(0, 1))
    }
  }, [selectFiles, options.allowMultiple])

  const deselectAll = useCallback(() => {
    setSelectedFiles([])
    setSelectedFolders([])
    options.onSelectionChange?.([], [])
  }, [options])

  const isFileSelected = useCallback((fileId: string) => {
    return selectedFiles.some(f => f.$id === fileId)
  }, [selectedFiles])

  const isFolderSelected = useCallback((folderId: string) => {
    return selectedFolders.some(f => f.id === folderId)
  }, [selectedFolders])

  const canSelectMore = useCallback(() => {
    if (!options.maxSelection) return true
    return selectedFiles.length < options.maxSelection
  }, [selectedFiles.length, options.maxSelection])

  const getSelectionCount = useCallback(() => {
    return selectedFiles.length + selectedFolders.length
  }, [selectedFiles.length, selectedFolders.length])

  const hasSelection = useCallback(() => {
    return selectedFiles.length > 0 || selectedFolders.length > 0
  }, [selectedFiles.length, selectedFolders.length])

  return {
    selectedFiles,
    selectedFolders,
    selectFile,
    deselectFile,
    toggleFileSelection,
    selectFiles,
    selectFolder,
    deselectFolder,
    toggleFolderSelection,
    selectAll,
    deselectAll,
    isFileSelected,
    isFolderSelected,
    canSelectMore,
    getSelectionCount,
    hasSelection
  }
}