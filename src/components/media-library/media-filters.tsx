'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { 
  Filter, 
  X, 
  Calendar as CalendarIcon,
  HardDrive,
  User,
  Tag,
  Folder
} from 'lucide-react'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'

import type { MediaFiltersProps, MediaFilters } from './types'

export function MediaFilters({
  filters,
  onFiltersChange,
  onClearFilters,
  availableTypes = [],
  availableFolders = [],
  availableTags = []
}: MediaFiltersProps) {
  const [dateFromOpen, setDateFromOpen] = useState(false)
  const [dateToOpen, setDateToOpen] = useState(false)

  const handleTypeChange = (type: string) => {
    onFiltersChange({ 
      type: type === 'all' ? undefined : type as MediaFilters['type']
    })
  }

  const handleFolderChange = (folder: string) => {
    onFiltersChange({ 
      folder: folder === 'all' ? undefined : folder
    })
  }

  const handleDateFromChange = (date: Date | undefined) => {
    onFiltersChange({
      dateRange: {
        ...filters.dateRange,
        from: date
      }
    })
    setDateFromOpen(false)
  }

  const handleDateToChange = (date: Date | undefined) => {
    onFiltersChange({
      dateRange: {
        ...filters.dateRange,
        to: date
      }
    })
    setDateToOpen(false)
  }

  const handleSizeRangeChange = (values: number[]) => {
    onFiltersChange({
      sizeRange: {
        min: values[0] * 1024 * 1024, // Convert MB to bytes
        max: values[1] * 1024 * 1024
      }
    })
  }

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024)
    return `${mb.toFixed(0)}MB`
  }

  const getActiveFilterCount = () => {
    let count = 0
    if (filters.type) count++
    if (filters.folder !== undefined) count++
    if (filters.dateRange?.from || filters.dateRange?.to) count++
    if (filters.sizeRange?.min || filters.sizeRange?.max) count++
    if (filters.tags && filters.tags.length > 0) count++
    if (filters.uploadedBy) count++
    return count
  }

  const clearFilter = (filterKey: keyof MediaFilters) => {
    const newFilters = { ...filters }
    delete newFilters[filterKey]
    onFiltersChange(newFilters)
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filters
            {getActiveFilterCount() > 0 && (
              <Badge variant="secondary" className="text-xs">
                {getActiveFilterCount()}
              </Badge>
            )}
          </CardTitle>
          {getActiveFilterCount() > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="h-6 text-xs"
            >
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* File Type Filter */}
        <div className="space-y-2">
          <Label className="text-xs font-medium flex items-center gap-1">
            <Tag className="h-3 w-3" />
            File Type
          </Label>
          <Select
            value={filters.type || 'all'}
            onValueChange={handleTypeChange}
          >
            <SelectTrigger className="h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              {availableTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {filters.type && (
            <Badge variant="outline" className="text-xs">
              {filters.type}
              <Button
                variant="ghost"
                size="sm"
                className="h-3 w-3 p-0 ml-1"
                onClick={() => clearFilter('type')}
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          )}
        </div>

        {/* Folder Filter */}
        {availableFolders.length > 0 && (
          <div className="space-y-2">
            <Label className="text-xs font-medium flex items-center gap-1">
              <Folder className="h-3 w-3" />
              Folder
            </Label>
            <Select
              value={filters.folder === undefined ? 'all' : filters.folder || 'root'}
              onValueChange={handleFolderChange}
            >
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Folders</SelectItem>
                <SelectItem value="">Root Folder</SelectItem>
                {availableFolders.map((folder) => (
                  <SelectItem key={folder.id} value={folder.id}>
                    {folder.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {filters.folder !== undefined && (
              <Badge variant="outline" className="text-xs">
                {filters.folder || 'Root'}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-3 w-3 p-0 ml-1"
                  onClick={() => clearFilter('folder')}
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            )}
          </div>
        )}

        {/* Date Range Filter */}
        <div className="space-y-2">
          <Label className="text-xs font-medium flex items-center gap-1">
            <CalendarIcon className="h-3 w-3" />
            Upload Date
          </Label>
          <div className="flex gap-2">
            <Popover open={dateFromOpen} onOpenChange={setDateFromOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    'flex-1 justify-start text-left font-normal h-8',
                    !filters.dateRange?.from && 'text-muted-foreground'
                  )}
                >
                  <CalendarIcon className="mr-2 h-3 w-3" />
                  {filters.dateRange?.from ? (
                    format(filters.dateRange.from, 'MMM dd')
                  ) : (
                    'From'
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={filters.dateRange?.from}
                  onSelect={handleDateFromChange}
                  initialFocus
                />
              </PopoverContent>
            </Popover>

            <Popover open={dateToOpen} onOpenChange={setDateToOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    'flex-1 justify-start text-left font-normal h-8',
                    !filters.dateRange?.to && 'text-muted-foreground'
                  )}
                >
                  <CalendarIcon className="mr-2 h-3 w-3" />
                  {filters.dateRange?.to ? (
                    format(filters.dateRange.to, 'MMM dd')
                  ) : (
                    'To'
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={filters.dateRange?.to}
                  onSelect={handleDateToChange}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          {(filters.dateRange?.from || filters.dateRange?.to) && (
            <Badge variant="outline" className="text-xs">
              {filters.dateRange?.from && format(filters.dateRange.from, 'MMM dd')}
              {filters.dateRange?.from && filters.dateRange?.to && ' - '}
              {filters.dateRange?.to && format(filters.dateRange.to, 'MMM dd')}
              <Button
                variant="ghost"
                size="sm"
                className="h-3 w-3 p-0 ml-1"
                onClick={() => clearFilter('dateRange')}
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          )}
        </div>

        {/* File Size Filter */}
        <div className="space-y-2">
          <Label className="text-xs font-medium flex items-center gap-1">
            <HardDrive className="h-3 w-3" />
            File Size
          </Label>
          <div className="px-2">
            <Slider
              value={[
                (filters.sizeRange?.min || 0) / (1024 * 1024),
                (filters.sizeRange?.max || 100 * 1024 * 1024) / (1024 * 1024)
              ]}
              onValueChange={handleSizeRangeChange}
              max={100}
              min={0}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>0MB</span>
              <span>100MB</span>
            </div>
          </div>
          {(filters.sizeRange?.min || filters.sizeRange?.max) && (
            <Badge variant="outline" className="text-xs">
              {filters.sizeRange?.min && formatFileSize(filters.sizeRange.min)}
              {filters.sizeRange?.min && filters.sizeRange?.max && ' - '}
              {filters.sizeRange?.max && formatFileSize(filters.sizeRange.max)}
              <Button
                variant="ghost"
                size="sm"
                className="h-3 w-3 p-0 ml-1"
                onClick={() => clearFilter('sizeRange')}
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  )
}