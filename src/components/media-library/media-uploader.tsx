'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { 
  Upload, 
  X, 
  FileImage, 
  FileVideo, 
  FileAudio, 
  FileText, 
  File as FileIcon,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { useMediaUpload } from './hooks/use-media-upload'
import type { MediaUploaderProps } from './types'

interface MediaUploaderDialogProps extends MediaUploaderProps {
  isOpen: boolean
  onClose: () => void
}

export function MediaUploader({
  isOpen,
  onClose,
  onUpload,
  onProgress,
  config = {}
}: MediaUploaderDialogProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [defaultAlt, setDefaultAlt] = useState('')
  const [defaultCaption, setDefaultCaption] = useState('')

  const {
    allowedTypes = ['image/*', 'video/*', 'audio/*', 'application/pdf'],
    maxFileSize = 50 * 1024 * 1024, // 50MB
    maxFiles = 10,
    folder,
    enableCrop = false,
    enableResize = false
  } = config

  const { uploadFiles, uploadProgress, isUploading } = useMediaUpload({
    maxFileSize,
    allowedTypes,
    onSuccess: (files) => {
      onUpload?.(files)
      handleClose()
    },
    onError: (error) => {
      console.error('Upload error:', error)
    }
  })

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = [...selectedFiles, ...acceptedFiles].slice(0, maxFiles)
    setSelectedFiles(newFiles)
  }, [selectedFiles, maxFiles])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: true,
    accept: allowedTypes.reduce((acc, type) => {
      acc[type] = []
      return acc
    }, {} as Record<string, string[]>),
    maxSize: maxFileSize,
    disabled: isUploading
  })

  const removeFile = (index: number) => {
    setSelectedFiles(files => files.filter((_, i) => i !== index))
  }

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return
    
    await uploadFiles(selectedFiles, folder)
  }

  const handleClose = () => {
    if (!isUploading) {
      setSelectedFiles([])
      setDefaultAlt('')
      setDefaultCaption('')
      onClose()
    }
  }

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return <FileImage className="h-8 w-8" />
    if (type.startsWith('video/')) return <FileVideo className="h-8 w-8" />
    if (type.startsWith('audio/')) return <FileAudio className="h-8 w-8" />
    if (type.includes('pdf') || type.includes('document')) return <FileText className="h-8 w-8" />
    return <FileIcon className="h-8 w-8" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploading':
      case 'processing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Upload Media Files</DialogTitle>
          <DialogDescription>
            Upload images, videos, documents, and other media files
            {folder && ` to ${folder}`}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6">
          {/* Upload Area */}
          <div
            {...getRootProps()}
            className={cn(
              'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
              isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25',
              'hover:border-primary/50 hover:bg-primary/5',
              isUploading && 'pointer-events-none opacity-50'
            )}
          >
            <input {...getInputProps()} />
            <div className="flex flex-col items-center gap-4">
              <Upload className="h-12 w-12 text-muted-foreground" />
              <div>
                <p className="text-lg font-medium">
                  {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
                </p>
                <p className="text-muted-foreground">
                  or click to browse files
                </p>
              </div>
              <div className="text-sm text-muted-foreground space-y-1">
                <p>Supported formats: {allowedTypes.join(', ')}</p>
                <p>Maximum file size: {formatFileSize(maxFileSize)}</p>
                <p>Maximum files: {maxFiles}</p>
              </div>
            </div>
          </div>

          {/* Selected Files */}
          {selectedFiles.length > 0 && (
            <div className="space-y-4">
              <h3 className="font-medium">Selected Files ({selectedFiles.length})</h3>
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {selectedFiles.map((file, index) => {
                  const progress = uploadProgress.find(p => p.filename === file.name)
                  
                  return (
                    <Card key={index}>
                      <CardContent className="p-4">
                        <div className="flex items-start gap-4">
                          {file.type.startsWith('image/') ? (
                            <img 
                              src={URL.createObjectURL(file)} 
                              alt={file.name}
                              className="w-16 h-16 object-cover rounded"
                            />
                          ) : (
                            <div className="w-16 h-16 flex items-center justify-center bg-muted rounded">
                              {getFileIcon(file.type)}
                            </div>
                          )}
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium truncate">{file.name}</p>
                                <p className="text-sm text-muted-foreground">
                                  {formatFileSize(file.size)} • {file.type}
                                </p>
                              </div>
                              
                              <div className="flex items-center gap-2">
                                {progress && getStatusIcon(progress.status)}
                                {!isUploading && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeFile(index)}
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                )}
                              </div>
                            </div>
                            
                            {progress && (progress.status === 'uploading' || progress.status === 'processing') && (
                              <div className="mt-2">
                                <Progress value={progress.progress} className="h-2" />
                                <p className="text-xs text-muted-foreground mt-1">
                                  {progress.status === 'processing' ? 'Processing...' : `${progress.progress}% uploaded`}
                                </p>
                              </div>
                            )}
                            
                            {progress?.error && (
                              <p className="text-xs text-red-500 mt-1">{progress.error}</p>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>
          )}

          {/* Default Metadata */}
          {selectedFiles.length > 0 && (
            <div className="space-y-4">
              <h3 className="font-medium">Default Metadata</h3>
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="default-alt">Default Alt Text</Label>
                  <Input
                    id="default-alt"
                    placeholder="Applied to all images..."
                    value={defaultAlt}
                    onChange={(e) => setDefaultAlt(e.target.value)}
                    disabled={isUploading}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="default-caption">Default Caption</Label>
                  <Textarea
                    id="default-caption"
                    placeholder="Applied to all files..."
                    value={defaultCaption}
                    onChange={(e) => setDefaultCaption(e.target.value)}
                    disabled={isUploading}
                    rows={3}
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isUploading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleUpload}
            disabled={selectedFiles.length === 0 || isUploading}
          >
            {isUploading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Upload {selectedFiles.length} File{selectedFiles.length !== 1 ? 's' : ''}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}