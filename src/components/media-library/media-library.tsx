'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  Upload, 
  Grid3X3, 
  List, 
  Filter, 
  Search,
  Folder,
  Settings,
  RefreshCw,
  Download,
  Trash2
} from 'lucide-react'

import { MediaGrid } from './media-grid'
import { MediaUploader } from './media-uploader'
import { MediaFilters } from './media-filters'
import { MediaSearch } from './media-search'
import { MediaFolders } from './media-folders'
import { MediaBulkActions } from './media-bulk-actions'
import { MediaPreview } from './media-preview'
import { useMediaLibrary } from './hooks/use-media-library'
import type { MediaLibraryConfig, MediaFile } from './types'

interface MediaLibraryProps {
  config?: MediaLibraryConfig
  onFileSelect?: (files: MediaFile[]) => void
  className?: string
}

export function MediaLibrary({ 
  config = {}, 
  onFileSelect,
  className 
}: MediaLibraryProps) {
  const {
    files,
    folders,
    selectedFiles,
    selectedFolders,
    currentFolder,
    filters,
    view,
    gridSize,
    isLoading,
    error,
    uploadProgress,
    actions
  } = useMediaLibrary()

  const [showUploader, setShowUploader] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [previewFile, setPreviewFile] = useState<MediaFile | null>(null)
  const [activeTab, setActiveTab] = useState('files')

  const {
    allowedTypes = ['IMAGE', 'VIDEO', 'AUDIO', 'DOCUMENT', 'OTHER'],
    maxFileSize = 50 * 1024 * 1024, // 50MB
    maxFiles = 10,
    allowMultiple = true,
    showUploader: configShowUploader = true,
    showFolders: configShowFolders = true,
    showSearch: configShowSearch = true,
    showFilters: configShowFilters = true,
    showBulkActions: configShowBulkActions = true,
    defaultView = 'grid',
    gridSize: configGridSize = 'medium',
    enableDragDrop = true
  } = config

  const handleFileSelect = (file: MediaFile) => {
    if (allowMultiple) {
      actions.toggleFileSelection(file)
    } else {
      actions.selectFiles([file])
      onFileSelect?.([file])
    }
  }

  const handleFilesUpload = async (files: File[]) => {
    await actions.uploadFiles(files, currentFolder)
    setShowUploader(false)
  }

  const handleBulkDelete = async () => {
    if (selectedFiles.length > 0) {
      await actions.deleteFiles(selectedFiles.map(f => f.$id))
    }
  }

  const handleRefresh = () => {
    actions.loadFiles(currentFolder)
    actions.loadFolders()
  }

  const filteredFiles = files.filter(file => {
    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
      return false
    }
    return true
  })

  return (
    <div className={`flex flex-col h-full bg-background ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Media Library</h2>
          {selectedFiles.length > 0 && (
            <Badge variant="secondary">
              {selectedFiles.length} selected
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
          
          {configShowFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4" />
              Filters
            </Button>
          )}
          
          <div className="flex items-center border rounded-md">
            <Button
              variant={view === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => actions.setView('grid')}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={view === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => actions.setView('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
          
          {configShowUploader && (
            <Button
              onClick={() => setShowUploader(true)}
              size="sm"
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload
            </Button>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      {(configShowSearch || showFilters) && (
        <div className="p-4 border-b space-y-4">
          {configShowSearch && (
            <MediaSearch
              query={filters.search || ''}
              onSearch={actions.setSearch}
              placeholder="Search files..."
            />
          )}
          
          {showFilters && (
            <MediaFilters
              filters={filters}
              onFiltersChange={actions.setFilters}
              onClearFilters={actions.clearFilters}
              availableTypes={allowedTypes}
              availableFolders={folders}
            />
          )}
        </div>
      )}

      {/* Bulk Actions */}
      {configShowBulkActions && selectedFiles.length > 0 && (
        <div className="p-4 border-b">
          <MediaBulkActions
            selectedFiles={selectedFiles}
            selectedFolders={selectedFolders}
            onDelete={handleBulkDelete}
            onMove={actions.moveFiles}
            onTag={() => {}} // TODO: Implement tagging
            onExport={() => {}} // TODO: Implement export
            availableFolders={folders}
          />
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        {configShowFolders && (
          <div className="w-64 border-r bg-muted/30">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
              <TabsList className="grid w-full grid-cols-2 m-2">
                <TabsTrigger value="files">
                  <Folder className="h-4 w-4 mr-2" />
                  Folders
                </TabsTrigger>
                <TabsTrigger value="settings">
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="files" className="h-full p-2">
                <MediaFolders
                  folders={folders}
                  currentFolder={currentFolder}
                  onFolderSelect={actions.setCurrentFolder}
                  onFolderCreate={actions.createFolder}
                  onFolderDelete={actions.deleteFolder}
                  onFolderRename={actions.renameFolder}
                  isEditable={true}
                />
              </TabsContent>
              
              <TabsContent value="settings" className="h-full p-2">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Grid Size</label>
                    <div className="flex gap-1 mt-1">
                      {(['small', 'medium', 'large'] as const).map((size) => (
                        <Button
                          key={size}
                          variant={gridSize === size ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => actions.setGridSize(size)}
                        >
                          {size}
                        </Button>
                      ))}
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Statistics</h4>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <div>Total Files: {files.length}</div>
                      <div>Selected: {selectedFiles.length}</div>
                      <div>Folders: {folders.length}</div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}

        {/* File Grid */}
        <div className="flex-1 overflow-auto">
          {error && (
            <div className="p-4 text-center text-red-500">
              <p>Error: {error}</p>
              <Button onClick={handleRefresh} variant="outline" className="mt-2">
                Try Again
              </Button>
            </div>
          )}
          
          {!error && (
            <MediaGrid
              files={filteredFiles}
              selectedFiles={selectedFiles}
              onSelect={handleFileSelect}
              onDeselect={actions.deselectFile}
              onToggleSelect={actions.toggleFileSelection}
              onPreview={setPreviewFile}
              view={view}
              gridSize={gridSize}
              isSelectable={true}
              isMultiSelect={allowMultiple}
              isLoading={isLoading}
              enableDragDrop={enableDragDrop}
              onFilesUpload={enableDragDrop ? handleFilesUpload : undefined}
            />
          )}
        </div>
      </div>

      {/* Upload Progress */}
      {uploadProgress.length > 0 && (
        <div className="border-t p-4">
          <h4 className="text-sm font-medium mb-2">Upload Progress</h4>
          <div className="space-y-2">
            {uploadProgress.map((progress) => (
              <div key={progress.fileId} className="flex items-center gap-2 text-sm">
                <div className="flex-1">
                  <div className="flex justify-between">
                    <span>{progress.filename}</span>
                    <span>{progress.progress}%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-1 mt-1">
                    <div 
                      className="bg-primary h-1 rounded-full transition-all"
                      style={{ width: `${progress.progress}%` }}
                    />
                  </div>
                </div>
                <Badge variant={
                  progress.status === 'success' ? 'default' :
                  progress.status === 'error' ? 'destructive' :
                  'secondary'
                }>
                  {progress.status}
                </Badge>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Modals */}
      {showUploader && (
        <MediaUploader
          isOpen={showUploader}
          onClose={() => setShowUploader(false)}
          onUpload={handleFilesUpload}
          config={{
            allowedTypes: allowedTypes.map(type => {
              switch (type) {
                case 'IMAGE': return 'image/*'
                case 'VIDEO': return 'video/*'
                case 'AUDIO': return 'audio/*'
                case 'DOCUMENT': return 'application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                default: return '*/*'
              }
            }),
            maxFileSize,
            maxFiles,
            folder: currentFolder
          }}
        />
      )}

      {previewFile && (
        <MediaPreview
          file={previewFile}
          isOpen={!!previewFile}
          onClose={() => setPreviewFile(null)}
          onNext={() => {
            const currentIndex = filteredFiles.findIndex(f => f.$id === previewFile.$id)
            const nextFile = filteredFiles[currentIndex + 1]
            if (nextFile) setPreviewFile(nextFile)
          }}
          onPrevious={() => {
            const currentIndex = filteredFiles.findIndex(f => f.$id === previewFile.$id)
            const prevFile = filteredFiles[currentIndex - 1]
            if (prevFile) setPreviewFile(prevFile)
          }}
          onDelete={async (fileId) => {
            await actions.deleteFiles([fileId])
            setPreviewFile(null)
          }}
          onUpdate={actions.updateFile}
        />
      )}
    </div>
  )
}