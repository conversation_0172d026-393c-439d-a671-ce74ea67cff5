'use client'

import { useState, useRef, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Search, X, Clock, TrendingUp } from 'lucide-react'
import { cn } from '@/lib/utils'

import type { MediaSearchProps } from './types'

interface ExtendedMediaSearchProps extends MediaSearchProps {
  recentSearches?: string[]
  popularSearches?: string[]
  onRecentSearch?: (query: string) => void
  showSuggestions?: boolean
  className?: string
}

export function MediaSearch({
  query,
  onSearch,
  placeholder = 'Search files...',
  suggestions = [],
  recentSearches = [],
  popularSearches = [],
  onRecentSearch,
  showSuggestions = true,
  className
}: ExtendedMediaSearchProps) {
  const [inputValue, setInputValue] = useState(query)
  const [isOpen, setIsOpen] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  // Update input value when query prop changes
  useEffect(() => {
    setInputValue(query)
  }, [query])

  const handleInputChange = (value: string) => {
    setInputValue(value)
    onSearch(value)
  }

  const handleClear = () => {
    setInputValue('')
    onSearch('')
    inputRef.current?.focus()
  }

  const handleSuggestionSelect = (suggestion: string) => {
    setInputValue(suggestion)
    onSearch(suggestion)
    onRecentSearch?.(suggestion)
    setIsOpen(false)
    inputRef.current?.blur()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false)
      inputRef.current?.blur()
    } else if (e.key === 'Enter') {
      if (inputValue.trim()) {
        onRecentSearch?.(inputValue.trim())
      }
      setIsOpen(false)
    }
  }

  const allSuggestions = [
    ...suggestions,
    ...recentSearches.filter(search => 
      search.toLowerCase().includes(inputValue.toLowerCase()) && 
      !suggestions.includes(search)
    ),
    ...popularSearches.filter(search => 
      search.toLowerCase().includes(inputValue.toLowerCase()) && 
      !suggestions.includes(search) &&
      !recentSearches.includes(search)
    )
  ].slice(0, 8)

  const shouldShowSuggestions = showSuggestions && 
    (allSuggestions.length > 0 || recentSearches.length > 0 || popularSearches.length > 0)

  return (
    <div className={cn('relative', className)}>
      <Popover open={isOpen && shouldShowSuggestions} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              ref={inputRef}
              type="text"
              placeholder={placeholder}
              value={inputValue}
              onChange={(e) => handleInputChange(e.target.value)}
              onFocus={() => {
                setIsFocused(true)
                if (shouldShowSuggestions) {
                  setIsOpen(true)
                }
              }}
              onBlur={() => {
                setIsFocused(false)
                // Delay closing to allow suggestion clicks
                setTimeout(() => setIsOpen(false), 200)
              }}
              onKeyDown={handleKeyDown}
              className="pl-10 pr-10"
            />
            {inputValue && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                onClick={handleClear}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </PopoverTrigger>

        <PopoverContent 
          className="w-[--radix-popover-trigger-width] p-0" 
          align="start"
          side="bottom"
        >
          <Command>
            <CommandList>
              {inputValue && allSuggestions.length === 0 && (
                <CommandEmpty>No suggestions found</CommandEmpty>
              )}

              {/* Current search suggestions */}
              {inputValue && allSuggestions.length > 0 && (
                <CommandGroup heading="Suggestions">
                  {allSuggestions.map((suggestion, index) => (
                    <CommandItem
                      key={`suggestion-${index}`}
                      value={suggestion}
                      onSelect={() => handleSuggestionSelect(suggestion)}
                      className="cursor-pointer"
                    >
                      <Search className="h-4 w-4 mr-2" />
                      <span>{suggestion}</span>
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}

              {/* Recent searches */}
              {!inputValue && recentSearches.length > 0 && (
                <CommandGroup heading="Recent Searches">
                  {recentSearches.slice(0, 5).map((search, index) => (
                    <CommandItem
                      key={`recent-${index}`}
                      value={search}
                      onSelect={() => handleSuggestionSelect(search)}
                      className="cursor-pointer"
                    >
                      <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>{search}</span>
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}

              {/* Popular searches */}
              {!inputValue && popularSearches.length > 0 && (
                <CommandGroup heading="Popular Searches">
                  {popularSearches.slice(0, 5).map((search, index) => (
                    <CommandItem
                      key={`popular-${index}`}
                      value={search}
                      onSelect={() => handleSuggestionSelect(search)}
                      className="cursor-pointer"
                    >
                      <TrendingUp className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>{search}</span>
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Search filters/tags */}
      {query && (
        <div className="flex items-center gap-2 mt-2">
          <Badge variant="secondary" className="text-xs">
            <Search className="h-3 w-3 mr-1" />
            Searching: "{query}"
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 ml-1"
              onClick={handleClear}
            >
              <X className="h-2 w-2" />
            </Button>
          </Badge>
        </div>
      )}
    </div>
  )
}

// Simple search component without suggestions
export function SimpleMediaSearch({
  query,
  onSearch,
  placeholder = 'Search files...',
  className
}: Pick<MediaSearchProps, 'query' | 'onSearch' | 'placeholder'> & { className?: string }) {
  const [inputValue, setInputValue] = useState(query)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    setInputValue(query)
  }, [query])

  const handleInputChange = (value: string) => {
    setInputValue(value)
    onSearch(value)
  }

  const handleClear = () => {
    setInputValue('')
    onSearch('')
    inputRef.current?.focus()
  }

  return (
    <div className={cn('relative', className)}>
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      <Input
        ref={inputRef}
        type="text"
        placeholder={placeholder}
        value={inputValue}
        onChange={(e) => handleInputChange(e.target.value)}
        className="pl-10 pr-10"
      />
      {inputValue && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
          onClick={handleClear}
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  )
}