'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { 
  Folder, 
  FolderOpen, 
  Plus, 
  MoreHorizontal,
  Edit,
  Trash2,
  Move,
  ChevronRight,
  ChevronDown,
  Home
} from 'lucide-react'
import { cn } from '@/lib/utils'

import type { MediaFoldersProps, MediaFolder } from './types'

interface FolderTreeItemProps {
  folder: MediaFolder
  level: number
  isSelected: boolean
  isExpanded: boolean
  onSelect: (folderId: string) => void
  onToggleExpand: (folderId: string) => void
  onEdit?: (folder: MediaFolder) => void
  onDelete?: (folder: MediaFolder) => void
  onMove?: (folder: MediaFolder) => void
  isEditable: boolean
}

function FolderTreeItem({
  folder,
  level,
  isSelected,
  isExpanded,
  onSelect,
  onToggleExpand,
  onEdit,
  onDelete,
  onMove,
  isEditable
}: FolderTreeItemProps) {
  const hasChildren = folder.children && folder.children.length > 0

  return (
    <div>
      <div
        className={cn(
          'flex items-center gap-1 px-2 py-1 rounded-md cursor-pointer hover:bg-muted/50 group',
          isSelected && 'bg-primary/10 text-primary',
          'transition-colors'
        )}
        style={{ paddingLeft: `${level * 12 + 8}px` }}
        onClick={() => onSelect(folder.id)}
      >
        {hasChildren ? (
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0"
            onClick={(e) => {
              e.stopPropagation()
              onToggleExpand(folder.id)
            }}
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </Button>
        ) : (
          <div className="w-4" />
        )}

        <div className="flex items-center gap-2 flex-1 min-w-0">
          {isSelected ? (
            <FolderOpen className="h-4 w-4 flex-shrink-0" />
          ) : (
            <Folder className="h-4 w-4 flex-shrink-0" />
          )}
          <span className="text-sm truncate">{folder.name}</span>
          {folder.fileCount > 0 && (
            <Badge variant="secondary" className="text-xs ml-auto">
              {folder.fileCount}
            </Badge>
          )}
        </div>

        {isEditable && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(folder)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Rename
                </DropdownMenuItem>
              )}
              {onMove && (
                <DropdownMenuItem onClick={() => onMove(folder)}>
                  <Move className="h-4 w-4 mr-2" />
                  Move
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {onDelete && (
                <DropdownMenuItem 
                  onClick={() => onDelete(folder)}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {hasChildren && isExpanded && (
        <div>
          {folder.children!.map((child) => (
            <FolderTreeItem
              key={child.id}
              folder={child}
              level={level + 1}
              isSelected={isSelected}
              isExpanded={isExpanded}
              onSelect={onSelect}
              onToggleExpand={onToggleExpand}
              onEdit={onEdit}
              onDelete={onDelete}
              onMove={onMove}
              isEditable={isEditable}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export function MediaFolders({
  folders,
  currentFolder,
  onFolderSelect,
  onFolderCreate,
  onFolderDelete,
  onFolderRename,
  onFolderMove,
  isEditable = false
}: MediaFoldersProps) {
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set())
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showRenameDialog, setShowRenameDialog] = useState(false)
  const [newFolderName, setNewFolderName] = useState('')
  const [selectedParentFolder, setSelectedParentFolder] = useState<string | undefined>()
  const [folderToRename, setFolderToRename] = useState<MediaFolder | null>(null)

  // Build folder tree structure
  const buildFolderTree = (folders: MediaFolder[]): MediaFolder[] => {
    const folderMap = new Map<string, MediaFolder>()
    const rootFolders: MediaFolder[] = []

    // Create folder map
    folders.forEach(folder => {
      folderMap.set(folder.id, { ...folder, children: [] })
    })

    // Build tree structure
    folders.forEach(folder => {
      const folderWithChildren = folderMap.get(folder.id)!
      
      if (folder.parentId) {
        const parent = folderMap.get(folder.parentId)
        if (parent) {
          parent.children = parent.children || []
          parent.children.push(folderWithChildren)
        }
      } else {
        rootFolders.push(folderWithChildren)
      }
    })

    return rootFolders
  }

  const folderTree = buildFolderTree(folders)

  const handleToggleExpand = (folderId: string) => {
    const newExpanded = new Set(expandedFolders)
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId)
    } else {
      newExpanded.add(folderId)
    }
    setExpandedFolders(newExpanded)
  }

  const handleCreateFolder = () => {
    if (newFolderName.trim()) {
      onFolderCreate(newFolderName.trim(), selectedParentFolder)
      setNewFolderName('')
      setSelectedParentFolder(undefined)
      setShowCreateDialog(false)
    }
  }

  const handleRenameFolder = () => {
    if (folderToRename && newFolderName.trim()) {
      onFolderRename(folderToRename.id, newFolderName.trim())
      setFolderToRename(null)
      setNewFolderName('')
      setShowRenameDialog(false)
    }
  }

  const openRenameDialog = (folder: MediaFolder) => {
    setFolderToRename(folder)
    setNewFolderName(folder.name)
    setShowRenameDialog(true)
  }

  const renderFolderTree = (folders: MediaFolder[], level = 0) => {
    return folders.map((folder) => (
      <FolderTreeItem
        key={folder.id}
        folder={folder}
        level={level}
        isSelected={currentFolder === folder.id}
        isExpanded={expandedFolders.has(folder.id)}
        onSelect={onFolderSelect}
        onToggleExpand={handleToggleExpand}
        onEdit={isEditable ? openRenameDialog : undefined}
        onDelete={isEditable ? (folder) => onFolderDelete(folder.id) : undefined}
        onMove={isEditable ? onFolderMove : undefined}
        isEditable={isEditable}
      />
    ))
  }

  return (
    <div className="space-y-2">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Folders</h3>
        {isEditable && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowCreateDialog(true)}
            className="h-6 w-6 p-0"
          >
            <Plus className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Root folder */}
      <div
        className={cn(
          'flex items-center gap-2 px-2 py-1 rounded-md cursor-pointer hover:bg-muted/50',
          !currentFolder && 'bg-primary/10 text-primary'
        )}
        onClick={() => onFolderSelect(undefined)}
      >
        <Home className="h-4 w-4" />
        <span className="text-sm">All Files</span>
      </div>

      {/* Folder tree */}
      <div className="space-y-1">
        {renderFolderTree(folderTree)}
      </div>

      {folders.length === 0 && (
        <div className="text-center py-4 text-sm text-muted-foreground">
          No folders created yet
          {isEditable && (
            <div className="mt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCreateDialog(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Folder
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Create Folder Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Folder</DialogTitle>
            <DialogDescription>
              Create a new folder to organize your media files.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="folder-name">Folder Name</Label>
              <Input
                id="folder-name"
                placeholder="Enter folder name..."
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleCreateFolder()
                  }
                }}
              />
            </div>

            <div className="space-y-2">
              <Label>Parent Folder (Optional)</Label>
              <select
                className="w-full p-2 border rounded-md"
                value={selectedParentFolder || ''}
                onChange={(e) => setSelectedParentFolder(e.target.value || undefined)}
              >
                <option value="">Root Folder</option>
                {folders.map((folder) => (
                  <option key={folder.id} value={folder.id}>
                    {folder.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateFolder}
              disabled={!newFolderName.trim()}
            >
              Create Folder
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rename Folder Dialog */}
      <Dialog open={showRenameDialog} onOpenChange={setShowRenameDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rename Folder</DialogTitle>
            <DialogDescription>
              Enter a new name for the folder "{folderToRename?.name}".
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-2">
            <Label htmlFor="rename-folder">Folder Name</Label>
            <Input
              id="rename-folder"
              placeholder="Enter new folder name..."
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleRenameFolder()
                }
              }}
            />
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowRenameDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleRenameFolder}
              disabled={!newFolderName.trim()}
            >
              Rename
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}