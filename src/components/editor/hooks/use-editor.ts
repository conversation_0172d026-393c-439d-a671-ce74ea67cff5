import { useCallback } from 'react';
import { Editor } from '@tiptap/react';

export function useEditorActions(editor: Editor | null) {
  const setLink = useCallback(() => {
    if (!editor) return;
    
    const previousUrl = editor.getAttributes('link').href;
    const url = window.prompt('URL', previousUrl);

    if (url === null) return;
    if (url === '') {
      editor.chain().focus().extendMarkRange('link').unsetLink().run();
      return;
    }
    
    editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
  }, [editor]);

  const addImage = useCallback(() => {
    if (!editor) return;
    
    const url = window.prompt('Enter the URL of the image:');
    if (url) {
      editor.chain().focus().setImage({ src: url }).run();
    }
  }, [editor]);

  return {
    setLink,
    addImage,
    // Add more editor actions as needed
  };
}

export function useEditorState(editor: Editor | null) {
  if (!editor) {
    return {
      isActive: () => false,
      can: () => false,
    };
  }

  return {
    isActive: (type: string, attributes: Record<string, any> = {}) => 
      editor.isActive(type, attributes),
    can: (action: string) => {
      switch (action) {
        case 'undo':
          return editor.can().undo();
        case 'redo':
          return editor.can().redo();
        default:
          return false;
      }
    },
  };
}
