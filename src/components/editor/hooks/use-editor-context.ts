import { useEditorContext } from '../context/editor-context';

export function useEditorInstance() {
  const { editor } = useEditorContext();
  return editor;
}

export function useEditorState() {
  const editor = useEditorInstance();
  
  if (!editor) {
    return {
      isActive: () => false,
      can: () => false,
    };
  }

  return {
    isActive: (type: string, attributes: Record<string, any> = {}) => 
      editor.isActive(type, attributes),
    can: (action: string) => {
      switch (action) {
        case 'undo':
          return editor.can().undo();
        case 'redo':
          return editor.can().redo();
        default:
          return false;
      }
    },
  };
}
