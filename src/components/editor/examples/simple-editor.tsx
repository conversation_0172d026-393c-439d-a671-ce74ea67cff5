'use client';

import { useState } from 'react';
import { Editor } from '../editor';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

export function SimpleEditor() {
  const [content, setContent] = useState('<p>Start typing here...</p>');
  const [savedContent, setSavedContent] = useState('');

  const handleSave = () => {
    setSavedContent(content);
    alert('Content saved successfully!');
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Simple Rich Text Editor</CardTitle>
        <CardDescription>
          Create content with a powerful editor
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Editor
          content={content}
          onChange={setContent}
          enableLayout={false}
          placeholder="Start typing..."
        />
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">Cancel</Button>
        <Button onClick={handleSave}>Save</Button>
      </CardFooter>
    </Card>
  );
}