'use client';

import { useState } from 'react';
import { Draggable } from '@hello-pangea/dnd';
import { MoreHorizontal, Trash2, Copy, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { LayoutItem } from '../context/layout-context';

interface DraggableItemProps {
  item: LayoutItem;
  index: number;
  isEditing: boolean;
  onEdit: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
  className?: string;
}

export function DraggableItem({
  item,
  index,
  isEditing,
  onEdit,
  onDelete,
  onDuplicate,
  className
}: DraggableItemProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Draggable draggableId={item.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={cn(
            'relative rounded-md border p-3 mb-2 bg-background',
            snapshot.isDragging && 'shadow-lg',
            isEditing && 'ring-2 ring-primary',
            className
          )}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className="flex items-center justify-between mb-2">
            <div
              {...provided.dragHandleProps}
              className="cursor-grab active:cursor-grabbing p-1 hover:bg-muted rounded"
            >
              <MoreHorizontal className="h-4 w-4" />
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onDuplicate}>
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={onDelete}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          
          <div className="prose dark:prose-invert prose-sm max-w-none">
            {item.type === 'text' && (
              <div dangerouslySetInnerHTML={{ __html: item.content }} />
            )}
            {item.type === 'image' && (
              <img 
                src={item.content} 
                alt={item.meta?.alt || 'Image'} 
                className="max-w-full h-auto"
              />
            )}
            {item.type === 'table' && (
              <div dangerouslySetInnerHTML={{ __html: item.content }} />
            )}
            {item.type === 'code' && (
              <pre className="p-2 bg-muted rounded-md overflow-x-auto">
                <code>{item.content}</code>
              </pre>
            )}
            {item.type === 'custom' && (
              <div dangerouslySetInnerHTML={{ __html: item.content }} />
            )}
          </div>
        </div>
      )}
    </Draggable>
  );
}