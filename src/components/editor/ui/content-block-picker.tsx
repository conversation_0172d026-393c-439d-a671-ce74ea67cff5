'use client';

import React from 'react';
import { 
  Type, 
  Image as ImageIcon, 
  Table2, 
  Code2, 
  FileText, 
  Layout, 
  Columns, 
  Video,
  Plus
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';

interface ContentBlockPickerProps {
  onSelect: (type: string) => void;
  className?: string;
}

const CONTENT_BLOCKS = [
  {
    id: 'text',
    name: 'Text',
    description: 'Rich text content',
    icon: Type
  },
  {
    id: 'image',
    name: 'Image',
    description: 'Upload or embed image',
    icon: ImageIcon
  },
  {
    id: 'table',
    name: 'Table',
    description: 'Tabular data',
    icon: Table2
  },
  {
    id: 'code',
    name: 'Code',
    description: 'Code snippet',
    icon: Code2
  },
  {
    id: 'document',
    name: 'Document',
    description: 'Embed document',
    icon: FileText
  },
  {
    id: 'layout',
    name: 'Layout',
    description: 'Custom layout',
    icon: Layout
  },
  {
    id: 'columns',
    name: 'Columns',
    description: 'Multi-column layout',
    icon: Columns
  },
  {
    id: 'video',
    name: 'Video',
    description: 'Embed video',
    icon: Video
  }
];

export function ContentBlockPicker({ onSelect, className }: ContentBlockPickerProps) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" className={cn("w-full justify-start", className)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Content Block
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        <div className="grid grid-cols-2 gap-2 p-4">
          {CONTENT_BLOCKS.map((block) => {
            const Icon = block.icon;
            return (
              <Button
                key={block.id}
                variant="ghost"
                className="flex h-auto flex-col items-center justify-start gap-2 p-4 text-center"
                onClick={() => onSelect(block.id)}
              >
                <Icon className="h-8 w-8 text-primary" />
                <div>
                  <div className="text-sm font-medium">{block.name}</div>
                  <div className="text-xs text-muted-foreground">
                    {block.description}
                  </div>
                </div>
              </Button>
            );
          })}
        </div>
      </PopoverContent>
    </Popover>
  );
}