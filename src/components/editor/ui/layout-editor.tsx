'use client';

import React, { useState } from 'react';
import { DragDropContext, DropResult } from '@hello-pangea/dnd';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  ResizablePanelGroup, 
  ResizablePanel, 
  ResizableHandle 
} from '@/components/ui/resizable';
import { DroppableSection } from './droppable-section';
import { ContentBlockPicker } from './content-block-picker';
import { useLayoutContext } from '../context/layout-context';
import { useEditorContext } from '../context/editor-context';

interface LayoutEditorProps {
  className?: string;
}

export function LayoutEditor({ className }: LayoutEditorProps) {
  const { 
    sections, 
    activeSection, 
    setActiveSection, 
    addSection, 
    removeSection, 
    addItem, 
    moveItem 
  } = useLayoutContext();
  const { editor } = useEditorContext();
  const [showPreview, setShowPreview] = useState(false);

  const handleDragEnd = (result: DropResult) => {
    const { source, destination } = result;
    
    // Dropped outside a droppable area
    if (!destination) return;
    
    // Same position
    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) return;
    
    // Move the item
    moveItem(
      source.droppableId,
      source.index,
      destination.droppableId,
      destination.index
    );
  };

  const handleAddItem = (sectionId: string, type = 'text') => {
    if (!editor) return;
    
    const content = editor.getHTML();
    if (!content && type === 'text') return;
    
    // Different handling based on content type
    switch (type) {
      case 'text':
        addItem(sectionId, {
          type: 'text',
          content,
        });
        // Clear the editor
        editor.commands.clearContent();
        break;
        
      case 'image':
        const imageUrl = window.prompt('Enter image URL:');
        if (imageUrl) {
          addItem(sectionId, {
            type: 'image',
            content: imageUrl,
            meta: { alt: 'Image' }
          });
        }
        break;
        
      case 'table':
        // Add a simple table
        editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
        const tableContent = editor.getHTML();
        addItem(sectionId, {
          type: 'table',
          content: tableContent,
        });
        editor.commands.clearContent();
        break;
        
      case 'code':
        const code = window.prompt('Enter code:');
        if (code) {
          addItem(sectionId, {
            type: 'code',
            content: code,
            meta: { language: 'javascript' }
          });
        }
        break;
        
      default:
        // For other types, create a placeholder
        addItem(sectionId, {
          type: 'custom',
          content: `<div class="p-4 border rounded bg-muted/30 text-center">
            <p>${type.charAt(0).toUpperCase() + type.slice(1)} block</p>
            <p class="text-sm text-muted-foreground">This is a placeholder for a ${type} block</p>
          </div>`,
          meta: { blockType: type }
        });
    }
  };

  const handleAddSection = () => {
    const newSectionId = addSection();
    setActiveSection(newSectionId);
  };

  const handleRemoveSection = (sectionId: string) => {
    removeSection(sectionId);
  };

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Layout Editor</h2>
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setShowPreview(!showPreview)}
          >
            {showPreview ? 'Edit Mode' : 'Preview'}
          </Button>
          <Button 
            variant="default" 
            size="sm"
            onClick={handleAddSection}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Section
          </Button>
        </div>
      </div>
      
      <DragDropContext onDragEnd={handleDragEnd}>
        <ResizablePanelGroup direction="vertical" className="min-h-[500px]">
          {sections.map((section, index) => (
            <React.Fragment key={section.id}>
              {index > 0 && <ResizableHandle withHandle />}
              <ResizablePanel defaultSize={100 / sections.length}>
                <DroppableSection
                  section={section}
                  isActive={activeSection === section.id}
                  onActivate={() => setActiveSection(section.id)}
                  onAddItem={() => handleAddItem(section.id)}
                  onRemove={() => handleRemoveSection(section.id)}
                />
              </ResizablePanel>
            </React.Fragment>
          ))}
        </ResizablePanelGroup>
      </DragDropContext>
    </div>
  );
}