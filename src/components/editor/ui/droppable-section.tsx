'use client';

import { useState } from 'react';
import { Droppable } from '@hello-pangea/dnd';
import { Plus, Trash2, GripVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { DraggableItem } from './draggable-item';
import { ContentBlockPicker } from './content-block-picker';
import { LayoutSection, useLayoutContext } from '../context/layout-context';

interface DroppableSectionProps {
  section: LayoutSection;
  isActive: boolean;
  onActivate: () => void;
  onAddItem: (type?: string) => void;
  onRemove: () => void;
  className?: string;
}

export function DroppableSection({
  section,
  isActive,
  onActivate,
  onAddItem,
  onRemove,
  className
}: DroppableSectionProps) {
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const { updateItem, removeItem } = useLayoutContext();

  const handleEditItem = (itemId: string) => {
    setEditingItemId(itemId);
  };

  const handleDeleteItem = (itemId: string) => {
    removeItem(section.id, itemId);
  };

  const handleDuplicateItem = (itemId: string) => {
    const item = section.items.find(item => item.id === itemId);
    if (item) {
      const { id, ...rest } = item;
      const newItem = { ...rest };
      // Add the duplicated item right after the original
      const index = section.items.findIndex(i => i.id === itemId);
      section.items.splice(index + 1, 0, { ...newItem, id: Math.random().toString(36).substring(2, 9) });
    }
  };

  return (
    <div 
      className={cn(
        'border rounded-md p-4 h-full',
        isActive && 'ring-2 ring-primary',
        className
      )}
      onClick={onActivate}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <GripVertical className="h-5 w-5 text-muted-foreground" />
          <h3 className="text-sm font-medium">Section {section.id.slice(0, 4)}</h3>
        </div>
        <div className="flex items-center gap-2">
          <div onClick={(e) => e.stopPropagation()}>
            <ContentBlockPicker 
              onSelect={(type) => {
                onAddItem(type);
              }}
            />
          </div>
          {section.items.length === 0 && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={(e) => {
                e.stopPropagation();
                onRemove();
              }}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
      
      <Droppable droppableId={section.id}>
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={cn(
              'min-h-[100px] p-2 rounded-md transition-colors',
              snapshot.isDraggingOver ? 'bg-accent/50' : 'bg-muted/30'
            )}
          >
            {section.items.map((item, index) => (
              <DraggableItem
                key={item.id}
                item={item}
                index={index}
                isEditing={editingItemId === item.id}
                onEdit={() => handleEditItem(item.id)}
                onDelete={() => handleDeleteItem(item.id)}
                onDuplicate={() => handleDuplicateItem(item.id)}
              />
            ))}
            {provided.placeholder}
            {section.items.length === 0 && (
              <div className="flex items-center justify-center h-24 text-muted-foreground text-sm">
                Drag items here or click Add Item
              </div>
            )}
          </div>
        )}
      </Droppable>
    </div>
  );
}