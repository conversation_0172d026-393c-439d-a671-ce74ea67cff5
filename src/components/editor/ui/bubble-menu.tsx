'use client';

import { useState, useCallback } from 'react';
import { Editor } from '@tiptap/react';
import { BubbleMenu as BaseBubbleMenu } from '@tiptap/react';
import { 
  Bold, 
  Italic, 
  Strikethrough, 
  Underline,
  Link as LinkIcon,
  Code,
  Code2,
  Quote,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Image as ImageIcon,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';

function cn(...classes: (string | undefined)[]) {
  return classes.filter(Boolean).join(' ');
}

interface BubbleMenuProps {
  editor: Editor;
  className?: string;
}

// Extend Tiptap Commands for custom extensions
declare global {
  namespace TiptapCore {
    interface Commands<ReturnType> {
      textAlign: {
        setTextAlign: (alignment: 'left' | 'center' | 'right' | 'justify') => ReturnType;
      };
      underline: {
        toggleUnderline: () => ReturnType;
      };
    }
  }
}

const LinkButton = ({ editor }: { editor: Editor }) => {
  const [url, setUrl] = useState('');
  const [open, setOpen] = useState(false);
  const [previousUrl, setPreviousUrl] = useState('');

  const handleSetLink = useCallback(() => {
    if (url) {
      editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
      setOpen(false);
      setUrl('');
    }
  }, [editor, url]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            setPreviousUrl(editor.getAttributes('link').href || '');
            setUrl(editor.getAttributes('link').href || '');
          }}
          className={editor.isActive('link') ? 'bg-accent' : ''}
        >
          <LinkIcon className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-4" align="start">
        <div className="space-y-4">
          <h4 className="font-medium leading-none">
            {editor.isActive('link') ? 'Edit Link' : 'Add Link'}
          </h4>
          <div className="flex gap-2">
            <Input
              placeholder="Paste URL"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleSetLink();
                }
              }}
              autoFocus
            />
            <Button size="sm" onClick={handleSetLink}>
              Apply
            </Button>
          </div>
          {editor.isActive('link') && (
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => {
                editor.chain().focus().extendMarkRange('link').unsetLink().run();
                setOpen(false);
              }}
            >
              Remove Link
            </Button>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
};

export function EditorBubbleMenu({ editor, className }: BubbleMenuProps) {
  // Define the callback before any conditional returns
  const addImage = useCallback(() => {
    if (!editor) return;
    
    const url = window.prompt('Enter the URL of the image:');
    if (url) {
      editor.chain().focus().setImage({ src: url }).run();
    }
  }, [editor]);
  
  if (!editor) return null;

  return (
    <BaseBubbleMenu
      editor={editor}
      tippyOptions={{
        duration: 100,
        placement: 'top',
        animation: 'fade',
        interactive: true,
      }}
      className={cn(
        'flex items-center gap-1 rounded-md border bg-background p-1 shadow-lg',
        className
      )}
      shouldShow={({ editor, view, state, oldState, from, to }) => {
        // Don't show if selection is empty
        const { empty } = state.selection;
        if (empty) return false;

        // Don't show if selection is a code block
        if (editor.isActive('codeBlock')) return false;

        return true;
      }}
    >
      {/* Text Formatting */}
      <div className="flex items-center gap-1 border-r px-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={editor.isActive('bold') ? 'bg-accent' : ''}
          title="Bold (Ctrl+B)"
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={editor.isActive('italic') ? 'bg-accent' : ''}
          title="Italic (Ctrl+I)"
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          className={editor.isActive('underline') ? 'bg-accent' : ''}
          title="Underline (Ctrl+U)"
        >
          <Underline className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={editor.isActive('strike') ? 'bg-accent' : ''}
          title="Strikethrough"
        >
          <Strikethrough className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleCode().run()}
          className={editor.isActive('code') ? 'bg-accent' : ''}
          title="Inline Code"
        >
          <Code2 className="h-4 w-4" />
        </Button>
      </div>

      {/* Links & Images */}
      <div className="flex items-center gap-1 border-r px-1">
        <LinkButton editor={editor} />
        <Button
          variant="ghost"
          size="sm"
          onClick={addImage}
          title="Insert Image"
          className={editor.isActive('image') ? 'bg-accent' : ''}
        >
          <ImageIcon className="h-4 w-4" />
        </Button>
      </div>

      {/* Block Types */}
      <div className="flex items-center gap-1 border-r px-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={editor.isActive('bulletList') ? 'bg-accent' : ''}
          title="Bullet List"
        >
          <List className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={editor.isActive('orderedList') ? 'bg-accent' : ''}
          title="Numbered List"
        >
          <ListOrdered className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={editor.isActive('blockquote') ? 'bg-accent' : ''}
          title="Blockquote"
        >
          <Quote className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleCodeBlock().run()}
          className={editor.isActive('codeBlock') ? 'bg-accent' : ''}
          title="Code Block"
        >
          <Code className="h-4 w-4" />
        </Button>
      </div>

      {/* Text Alignment */}
      <div className="flex items-center gap-1 px-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign('left').run()}
          className={editor.isActive({ textAlign: 'left' }) ? 'bg-accent' : ''}
          title="Align Left"
        >
          <AlignLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign('center').run()}
          className={editor.isActive({ textAlign: 'center' }) ? 'bg-accent' : ''}
          title="Align Center"
        >
          <AlignCenter className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign('right').run()}
          className={editor.isActive({ textAlign: 'right' }) ? 'bg-accent' : ''}
          title="Align Right"
        >
          <AlignRight className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign?.('justify').run()}
          className={editor.isActive({ textAlign: 'justify' }) ? 'bg-accent' : ''}
          title="Justify"
        >
          <AlignJustify className="h-4 w-4" />
        </Button>
      </div>
    </BaseBubbleMenu>
  );
}