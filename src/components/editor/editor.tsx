'use client';

import './editor.css';
import { use<PERSON><PERSON><PERSON>, EditorContent, Editor as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EditorEvents } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import { Image } from '@tiptap/extension-image';
import { Link } from '@tiptap/extension-link';
import { Table } from '@tiptap/extension-table';
import { TableRow } from '@tiptap/extension-table-row';
import { TableCell } from '@tiptap/extension-table-cell';
import { TableHeader } from '@tiptap/extension-table-header';
import { Color } from '@tiptap/extension-color';
import Text from '@tiptap/extension-text-style';
import { Placeholder } from '@tiptap/extension-placeholder';
import { CharacterCount } from '@tiptap/extension-character-count';
import Focus from '@tiptap/extension-focus';
import { Dropcursor } from '@tiptap/extension-dropcursor';
import { Gapcursor } from '@tiptap/extension-gapcursor';
import { EditorBubbleMenu } from './ui/bubble-menu';
import { Toolbar } from './ui/toolbar';
import { EditorProvider, useEditorContext } from './context/editor-context';
import { LayoutProvider } from './context/layout-context';
import { LayoutEditor } from './ui/layout-editor';
import { useDebounce } from '../../hooks/use-debounce';
import { useEffect, useMemo, useState } from 'react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ResizablePanelGroup, 
  ResizablePanel, 
  ResizableHandle 
} from '@/components/ui/resizable';

interface EditorProps {
  content?: string;
  placeholder?: string;
  onChange?: (content: string) => void;
  onUpdate?: (props: { editor: TiptapEditor; event?: EditorEvents['update'] }) => void;
  editable?: boolean;
  className?: string;
  debounceWait?: number;
  enableLayout?: boolean;
}

function EditorComponent({
  content = '',
  onChange,
  onUpdate,
  editable = true,
  className = '',
  placeholder = 'Write something...',
  debounceWait = 300,
  enableLayout = true,
}: EditorProps) {
  const { setEditor } = useEditorContext();
  const [activeTab, setActiveTab] = useState<string>("editor");
  const debouncedOnChange = useDebounce((value: string) => {
    onChange?.(value);
  }, debounceWait);

  const charLimit = 10000; // Default character limit
  
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        codeBlock: false, // Disable default code block to use our custom one
      }),
      // Text formatting
      Underline,
      Text,
      Color,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      
      // UI Enhancements
      Placeholder.configure({
        placeholder: placeholder,
        emptyEditorClass: 'is-editor-empty',
        emptyNodeClass: 'is-empty',
      }),
      Focus.configure({
        className: 'has-focus',
        mode: 'shallowest',
      }),
      Dropcursor,
      Gapcursor,
      CharacterCount.configure({
        limit: charLimit,
      }),
      // Text alignment is already configured above
      Image,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-500 underline hover:text-blue-700',
        },
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
    ],
    content,
    editable,
    editorProps: {
      attributes: {
        class: `prose dark:prose-invert prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none ${className}`,
        'data-placeholder': placeholder,
      },
    },
    onUpdate: (props) => {
      const html = props.editor.getHTML();
      debouncedOnChange(html);
      onUpdate?.(props);
    },
    autofocus: 'end',
  });
  
  // All useEffect hooks must be declared before any conditional returns
  
  // Set editor instance in context
  useEffect(() => {
    if (editor) {
      setEditor(editor);
    }
    return () => {
      setEditor(null);
    };
  }, [editor, setEditor]);

  // Update editor content when content prop changes
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [content, editor]);
  
  // Add CSS classes for the placeholder extension and update editor options
  useEffect(() => {
    if (editor) {
      editor.setOptions({
        editorProps: {
          attributes: {
            class: `prose dark:prose-invert prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none ${className} ${
              editor.isEmpty ? 'is-editor-empty' : ''
            }`,
            'data-placeholder': placeholder,
          },
        },
      });
    }
  }, [editor, className, placeholder]);

  // Get character count and calculate progress
  const characterCount = editor?.storage?.characterCount || { characters: () => 0 };
  const charCount = editor ? characterCount.characters() : 0;
  
  // Calculate derived values using useMemo - must be before conditional returns
  const progressPercentage = useMemo(() => {
    return Math.min(100, Math.round((charCount / charLimit) * 100));
  }, [charCount, charLimit]);

  const isNearLimit = useMemo(() => {
    return charCount > charLimit * 0.9;
  }, [charCount, charLimit]);

  // Early return if editor is not initialized
  if (!editor) {
    return null;
  }

  if (!enableLayout) {
    // Return the simple editor without layout features
    return (
      <div className="relative w-full">
        <Toolbar editor={editor} />
        <div className="relative">
          <div className={`editor ${isNearLimit ? 'border-amber-200 dark:border-amber-800' : ''}`}>
            <EditorContent 
              editor={editor}
              className="min-h-[300px]"
            />
            <EditorBubbleMenu editor={editor} />
          </div>
          
          <div className="character-count">
            <div className="h-1 w-full bg-muted overflow-hidden rounded-full mb-1">
              <div 
                className={`h-full ${
                  isNearLimit ? 'bg-amber-500' : 'bg-primary'
                }`}
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
            <div className="flex justify-between">
              <span>
                {charCount} / {charLimit} characters
              </span>
              <span className={isNearLimit ? 'near-limit' : ''}>
                {progressPercentage}% used
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Return the enhanced editor with layout features
  return (
    <div className="relative w-full">
      <Tabs defaultValue="editor" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="editor">Editor</TabsTrigger>
          <TabsTrigger value="layout">Layout</TabsTrigger>
        </TabsList>
        
        <TabsContent value="editor" className="mt-0">
          <Toolbar editor={editor} />
          <div className="relative">
            <div className={`editor ${isNearLimit ? 'border-amber-200 dark:border-amber-800' : ''}`}>
              <EditorContent 
                editor={editor}
                className="min-h-[300px]"
              />
              <EditorBubbleMenu editor={editor} />
            </div>
            
            <div className="character-count mt-2">
              <div className="h-1 w-full bg-muted overflow-hidden rounded-full mb-1">
                <div 
                  className={`h-full ${
                    isNearLimit ? 'bg-amber-500' : 'bg-primary'
                  }`}
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
              <div className="flex justify-between">
                <span>
                  {charCount} / {charLimit} characters
                </span>
                <span className={isNearLimit ? 'near-limit' : ''}>
                  {progressPercentage}% used
                </span>
              </div>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="layout" className="mt-0">
          <ResizablePanelGroup direction="horizontal" className="min-h-[600px] border rounded-md">
            <ResizablePanel defaultSize={30} minSize={20}>
              <div className="h-full p-4 border-r">
                <h3 className="text-lg font-medium mb-4">Content Editor</h3>
                <Toolbar editor={editor} />
                <EditorContent 
                  editor={editor}
                  className="min-h-[200px] mt-2 border rounded-md p-2"
                />
                <div className="character-count mt-2">
                  <div className="text-xs text-muted-foreground">
                    {charCount} / {charLimit} characters
                  </div>
                </div>
              </div>
            </ResizablePanel>
            
            <ResizableHandle withHandle />
            
            <ResizablePanel defaultSize={70}>
              <div className="h-full p-4">
                <LayoutEditor className="h-full" />
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export function Editor(props: EditorProps) {
  return (
    <EditorProvider>
      <LayoutProvider>
        <EditorComponent {...props} />
      </LayoutProvider>
    </EditorProvider>
  );
}
