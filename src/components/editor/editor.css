/* Editor container styles */
.editor {
  min-height: 300px;
  border: 1px solid hsl(var(--border));
  border-radius: 0.375rem;
  padding: 1rem;
  transition: all 0.2s ease-in-out;
}

.editor:focus-within {
  box-shadow: 0 0 0 2px rgba(24, 24, 27, 0.1);
  outline: none;
}

/* Layout editor styles */
.layout-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.layout-section {
  border: 1px solid hsl(var(--border));
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.2s ease-in-out;
}

.layout-section.active {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 1px hsl(var(--primary));
}

.layout-item {
  border: 1px solid hsl(var(--border));
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background-color: hsl(var(--background));
  transition: all 0.2s ease-in-out;
  position: relative;
}

.layout-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.layout-item.dragging {
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  opacity: 0.8;
}

.layout-item-handle {
  cursor: grab;
  padding: 0.25rem;
  border-radius: 0.25rem;
}

.layout-item-handle:active {
  cursor: grabbing;
}

.layout-item-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.layout-item:hover .layout-item-actions {
  opacity: 1;
}

/* Placeholder styling */
.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
  color: hsl(var(--muted-foreground));
}

/* Focus styles */
.has-focus {
  box-shadow: 0 0 0 2px rgba(24, 24, 27, 0.1);
  outline: none;
}

/* Table styles */
.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 1rem 0;
}

.ProseMirror th,
.ProseMirror td {
  border: 1px solid hsl(var(--border));
  padding: 0.5rem 1rem;
}

.ProseMirror th {
  background-color: hsl(var(--muted));
}

/* Image styles */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 1rem 0;
}

/* Code block styles */
.ProseMirror pre {
  background-color: hsl(var(--muted));
  padding: 1rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.ProseMirror pre code {
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Character count styles */
.character-count {
  font-size: 0.75rem;
  line-height: 1rem;
  color: hsl(var(--muted-foreground));
  margin-top: 0.5rem;
  padding: 0 0.25rem;
}

.character-count.near-limit {
  color: #f59e0b;
  font-weight: 500;
}

/* Drop cursor */
.ProseMirror .ProseMirror-dropcursor {
  height: 0.25rem;
  background-color: hsl(var(--primary));
  border-radius: 9999px;
}

/* Gap cursor */
.ProseMirror-gapcursor {
  position: relative;
  height: 1.25rem;
  width: 0.125rem;
  background-color: hsl(var(--primary));
  margin: 0.5rem auto;
}

.ProseMirror-gapcursor:after {
  content: '';
  position: absolute;
  left: -0.375rem;
  top: -0.375rem;
  width: 1rem;
  height: 1rem;
  border: 2px solid hsl(var(--primary));
  border-radius: 9999px;
  background-color: white;
}

.dark .ProseMirror-gapcursor:after {
  background-color: black;
}

/* Selection styles */
.ProseMirror-selectednode {
  outline: 2px solid rgba(24, 24, 27, 0.3);
  border-radius: 0.125rem;
}

/* Drag and drop styles */
.droppable-section {
  transition: background-color 0.2s ease-in-out;
}

.droppable-section.is-dragging-over {
  background-color: rgba(var(--primary-rgb), 0.1);
}

.draggable-item {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.draggable-item.is-dragging {
  transform: scale(1.02);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* Resizable panels */
.resizable-panel-container {
  display: flex;
  height: 100%;
  border: 1px solid hsl(var(--border));
  border-radius: 0.375rem;
  overflow: hidden;
}

.resizable-panel {
  overflow: auto;
}

.resizable-handle {
  width: 4px;
  background-color: hsl(var(--border));
  cursor: col-resize;
  transition: background-color 0.2s ease-in-out;
}

.resizable-handle:hover,
.resizable-handle:active {
  background-color: hsl(var(--primary));
}
