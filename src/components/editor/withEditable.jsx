import React, { useState, useRef, useEffect } from 'react';

const withEditable = (WrappedComponent, defaultProps = {}) => {
  return function EditableComponent({
    id,
    isSelected = false,
    onSelect,
    onUpdate,
    children,
    ...props
  }) {
    const [isHovered, setIsHovered] = useState(false);
    const [localProps, setLocalProps] = useState({ ...defaultProps, ...props });
    const componentRef = useRef(null);

    useEffect(() => {
      setLocalProps(prev => ({ ...prev, ...props }));
    }, [props]);

    const handlePropChange = (propName, value) => {
      const updatedProps = { ...localProps, [propName]: value };
      setLocalProps(updatedProps);
      onUpdate?.(id, updatedProps);
    };

    return (
      <div
        ref={componentRef}
        className={`relative ${isSelected ? 'ring-2 ring-blue-500' : ''} ${
          isHovered ? 'ring-1 ring-gray-300' : ''
        }`}
        onClick={(e) => {
          e.stopPropagation();
          onSelect?.(id);
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <WrappedComponent {...localProps}>
          {children}
        </WrappedComponent>
        {isSelected && (
          <div className="absolute -top-2 -right-2 bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
            {id}
          </div>
        )}
      </div>
    );
  };
};

export default withEditable;
