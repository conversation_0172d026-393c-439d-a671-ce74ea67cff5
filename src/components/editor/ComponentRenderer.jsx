import React from 'react';
import { v4 as uuidv4 } from 'uuid';

const ComponentRenderer = ({
  componentTree,
  componentsMap,
  selectedId,
  onSelect,
  onUpdate,
  onMove,
  parentId = null,
  index = 0,
}) => {
  if (!componentTree) return null;

  const renderComponent = (node) => {
    const { id, type, props = {}, children = [] } = node;
    const Component = componentsMap[type];

    if (!Component) {
      console.warn(`Component ${type} not found in componentsMap`);
      return null;
    }

    const handleUpdate = (updatedId, updatedProps) => {
      onUpdate?.(updatedId, updatedProps);
    };

    const handleSelect = (e, componentId) => {
      e?.stopPropagation();
      onSelect?.(componentId);
    };

    const renderedChildren = children.length ? (
      <ComponentRenderer
        componentTree={children}
        componentsMap={componentsMap}
        selectedId={selectedId}
        onSelect={onSelect}
        onUpdate={onUpdate}
        onMove={onMove}
        parentId={id}
      />
    ) : null;

    return (
      <div
        key={id}
        onClick={(e) => handleSelect(e, id)}
        className="relative"
        data-component-id={id}
        data-parent-id={parentId}
        data-index={index}
      >
        <Component
          {...props}
          id={id}
          isSelected={selectedId === id}
          onSelect={handleSelect}
          onUpdate={handleUpdate}
        >
          {renderedChildren}
        </Component>
      </div>
    );
  };

  if (Array.isArray(componentTree)) {
    return componentTree.map((node, index) => (
      <React.Fragment key={node.id || uuidv4()}>
        {renderComponent({ ...node, index })}
      </React.Fragment>
    ));
  }

  return renderComponent(componentTree);
};

export default ComponentRenderer;
