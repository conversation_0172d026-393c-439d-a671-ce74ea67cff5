'use client';

import { createContext, useContext, useState, ReactNode, useCallback } from 'react';

export type LayoutItem = {
  id: string;
  type: 'text' | 'image' | 'table' | 'code' | 'custom';
  content: string;
  meta?: Record<string, any>;
};

export type LayoutSection = {
  id: string;
  items: LayoutItem[];
};

type LayoutContextType = {
  sections: LayoutSection[];
  activeSection: string | null;
  setActiveSection: (id: string | null) => void;
  addSection: () => void;
  removeSection: (id: string) => void;
  addItem: (sectionId: string, item: Omit<LayoutItem, 'id'>) => string;
  updateItem: (sectionId: string, itemId: string, updates: Partial<Omit<LayoutItem, 'id'>>) => void;
  removeItem: (sectionId: string, itemId: string) => void;
  moveItem: (sourceSectionId: string, sourceIndex: number, destinationSectionId: string, destinationIndex: number) => void;
  getItemById: (itemId: string) => { item: LayoutItem | null, sectionId: string | null };
};

const LayoutContext = createContext<LayoutContextType | undefined>(undefined);

function generateId() {
  return Math.random().toString(36).substring(2, 9);
}

export function LayoutProvider({ children }: { children: ReactNode }) {
  const [sections, setSections] = useState<LayoutSection[]>([
    { id: generateId(), items: [] }
  ]);
  const [activeSection, setActiveSection] = useState<string | null>(sections[0].id);

  const addSection = useCallback(() => {
    const newSection = { id: generateId(), items: [] };
    setSections(prev => [...prev, newSection]);
    return newSection.id;
  }, []);

  const removeSection = useCallback((id: string) => {
    setSections(prev => prev.filter(section => section.id !== id));
    if (activeSection === id) {
      setActiveSection(sections.length > 1 ? sections[0].id : null);
    }
  }, [activeSection, sections]);

  const addItem = useCallback((sectionId: string, item: Omit<LayoutItem, 'id'>) => {
    const newItemId = generateId();
    setSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { ...section, items: [...section.items, { ...item, id: newItemId }] }
          : section
      )
    );
    return newItemId;
  }, []);

  const updateItem = useCallback((sectionId: string, itemId: string, updates: Partial<Omit<LayoutItem, 'id'>>) => {
    setSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { 
              ...section, 
              items: section.items.map(item => 
                item.id === itemId 
                  ? { ...item, ...updates }
                  : item
              )
            }
          : section
      )
    );
  }, []);

  const removeItem = useCallback((sectionId: string, itemId: string) => {
    setSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { ...section, items: section.items.filter(item => item.id !== itemId) }
          : section
      )
    );
  }, []);

  const moveItem = useCallback((
    sourceSectionId: string, 
    sourceIndex: number, 
    destinationSectionId: string, 
    destinationIndex: number
  ) => {
    setSections(prev => {
      const newSections = [...prev];
      
      // Find the source and destination sections
      const sourceSection = newSections.find(s => s.id === sourceSectionId);
      const destinationSection = newSections.find(s => s.id === destinationSectionId);
      
      if (!sourceSection || !destinationSection) return prev;
      
      // Get the item being moved
      const [movedItem] = sourceSection.items.splice(sourceIndex, 1);
      
      // Insert the item at the destination
      destinationSection.items.splice(destinationIndex, 0, movedItem);
      
      return newSections;
    });
  }, []);

  const getItemById = useCallback((itemId: string) => {
    for (const section of sections) {
      const item = section.items.find(item => item.id === itemId);
      if (item) {
        return { item, sectionId: section.id };
      }
    }
    return { item: null, sectionId: null };
  }, [sections]);

  return (
    <LayoutContext.Provider 
      value={{ 
        sections, 
        activeSection, 
        setActiveSection, 
        addSection, 
        removeSection, 
        addItem, 
        updateItem, 
        removeItem, 
        moveItem,
        getItemById
      }}
    >
      {children}
    </LayoutContext.Provider>
  );
}

export function useLayoutContext() {
  const context = useContext(LayoutContext);
  if (context === undefined) {
    throw new Error('useLayoutContext must be used within a LayoutProvider');
  }
  return context;
}