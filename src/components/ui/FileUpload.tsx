import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { storageService } from '@/lib/appwrite/services/storage';
import type { File as AppFile, UploadOptions, UploadProgress } from '@/lib/appwrite/types/storage';

interface FileUploadProps {
  bucketId: string;
  onUploadComplete?: (file: AppFile) => void;
  onError?: (error: string) => void;
  maxSize?: number;
  accept?: string | string[];
  multiple?: boolean;
  readPermissions?: string[];
  writePermissions?: string[];
  className?: string;
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export function FileUpload({
  bucketId,
  onUploadComplete,
  onError,
  maxSize = 5 * 1024 * 1024, // 5MB
  accept = ['image/*'],
  multiple = false,
  readPermissions = [],
  writePermissions = [],
  className = '',
}: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (!acceptedFiles.length) return;
      setSelectedFile(acceptedFiles[0]);
    },
    []
  );

  const handleUpload = useCallback(async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setError(null);

    try {
      if (selectedFile.size > maxSize) {
        throw new Error(`File size exceeds the maximum limit of ${maxSize / 1024 / 1024}MB`);
      }

      const options: UploadOptions = {
        read: readPermissions.length > 0 ? readPermissions.join(',') : undefined,
        write: writePermissions.length > 0 ? writePermissions.join(',') : undefined,
        onProgress: (progress: UploadProgress) => {
          setProgress(progress.progress);
        },
      };

      const result = await storageService.uploadFile(selectedFile, options);

      if (result.success && result.data) {
        onUploadComplete?.(result.data);
        setSelectedFile(null);
      } else {
        throw new Error(result.error || 'Failed to upload file');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsUploading(false);
      setProgress(0);
    }
  }, [selectedFile, maxSize, onUploadComplete, onError, readPermissions, writePermissions]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: Array.isArray(accept) ? accept.reduce((acc, a) => ({
      ...acc,
      [a]: []
    }), {}) : { [accept]: [] },
    multiple: false,
    maxSize,
    disabled: isUploading,
  });

  const removeFile = useCallback(() => {
    setSelectedFile(null);
    setError(null);
  }, []);

  return (
    <div className={`w-full space-y-4 ${className}`}>
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
          isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
        } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        <input {...getInputProps()} />
        <div className="space-y-2">
          <p className="text-sm text-gray-600">
            {isDragActive
              ? 'Drop the file here'
              : 'Drag and drop a file here, or click to select'}
          </p>
          <p className="text-xs text-gray-500">
            {Array.isArray(accept) ? accept.join(', ') : accept} • Max {formatFileSize(maxSize)}
          </p>
        </div>
      </div>

      {selectedFile && (
        <div className="space-y-4">
          <div className="flex items-center justify-between rounded-md border p-3">
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-md bg-gray-100">
                <span className="text-xs font-medium text-gray-500">
                  {selectedFile.name.split('.').pop()?.toUpperCase()}
                </span>
              </div>
              <div className="min-w-0">
                <p className="truncate text-sm font-medium">{selectedFile.name}</p>
                <p className="text-xs text-gray-500">
                  {formatFileSize(selectedFile.size)}
                </p>
              </div>
            </div>
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                removeFile();
              }}
              disabled={isUploading}
              className="rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            >
              <span className="sr-only">Remove file</span>
              <svg
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <div className="flex justify-end">
            <button
              type="button"
              onClick={handleUpload}
              disabled={isUploading}
              className="inline-flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
            >
              {isUploading ? (
                <>
                  <svg
                    className="-ml-1 mr-2 h-4 w-4 animate-spin text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  Uploading... {Math.round(progress)}%
                </>
              ) : (
                'Upload File'
              )}
            </button>
          </div>

          {isUploading && (
            <div className="h-1.5 w-full overflow-hidden rounded-full bg-gray-200">
              <div
                className="h-full bg-blue-600 transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          )}
        </div>
      )}

      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                {error}
              </h3>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default FileUpload;
