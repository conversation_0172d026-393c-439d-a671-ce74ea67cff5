import { Clock, FileText, PenSquare, User, Settings } from "lucide-react"

const activities = [
  {
    id: 1,
    type: 'page',
    title: 'About Us',
    action: 'updated',
    time: '2 minutes ago',
    icon: FileText,
  },
  {
    id: 2,
    type: 'post',
    title: 'Getting Started with Next.js',
    action: 'published',
    time: '1 hour ago',
    icon: PenSquare,
  },
  {
    id: 3,
    type: 'user',
    title: '<PERSON>',
    action: 'signed in',
    time: '3 hours ago',
    icon: User,
  },
  {
    id: 4,
    type: 'settings',
    title: 'Site Settings',
    action: 'updated',
    time: 'Yesterday',
    icon: Settings,
  },
]

export function RecentActivity() {
  return (
    <div className="space-y-4">
      {activities.map((activity) => {
        const Icon = activity.icon
        return (
          <div key={activity.id} className="flex items-start gap-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
              <Icon className="h-4 w-4" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium leading-none">
                {activity.title} <span className="text-muted-foreground">was {activity.action}</span>
              </p>
              <p className="text-xs text-muted-foreground flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {activity.time}
              </p>
            </div>
          </div>
        )
      })}
    </div>
  )
}
