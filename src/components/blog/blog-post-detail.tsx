import { format } from "date-fns"
import Link from "next/link"
import { Calendar, Clock, User, ArrowLeft, Share2, Tag, Folder } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { BlogPostDetail } from "@/types/blog"
import { BlogCard } from "./blog-card"
import { SocialShare } from "./social-share"
import { cn } from "@/lib/utils"

interface BlogPostDetailProps {
  post: BlogPostDetail
  className?: string
}

export function BlogPostDetailComponent({ post, className }: BlogPostDetailProps) {
  return (
    <article className={cn("max-w-4xl mx-auto", className)}>
      {/* Back to Blog */}
      <div className="mb-8">
        <Button variant="ghost" asChild className="text-muted-foreground hover:text-foreground">
          <Link href="/blog">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Blog
          </Link>
        </Button>
      </div>

      {/* Article Header */}
      <header className="mb-8 space-y-6">
        {/* Category */}
        {post.category && (
          <div>
            <Badge 
              variant="outline"
              className="text-sm"
              style={{
                backgroundColor: post.category.color ? `${post.category.color}20` : undefined,
                borderColor: post.category.color || undefined,
                color: post.category.color || undefined
              }}
            >
              <Folder className="mr-1 h-3 w-3" />
              {post.category.name}
            </Badge>
          </div>
        )}

        {/* Title */}
        <h1 className="text-4xl font-bold tracking-tight lg:text-5xl">
          {post.title}
        </h1>

        {/* Excerpt */}
        {post.excerpt && (
          <p className="text-xl text-muted-foreground leading-relaxed">
            {post.excerpt}
          </p>
        )}

        {/* Meta Information */}
        <div className="flex flex-wrap items-center gap-6 text-sm text-muted-foreground">
          {/* Author */}
          <div className="flex items-center space-x-2">
            {post.author.image && (
              <img
                src={post.author.image}
                alt={post.author.name || 'Author'}
                className="h-8 w-8 rounded-full"
              />
            )}
            <div className="flex items-center">
              {!post.author.image && <User className="mr-1 h-4 w-4" />}
              <span>By {post.author.name}</span>
            </div>
          </div>

          {/* Published Date */}
          {post.publishedAt && (
            <div className="flex items-center">
              <Calendar className="mr-1 h-4 w-4" />
              {format(new Date(post.publishedAt), 'MMMM d, yyyy')}
            </div>
          )}

          {/* Read Time */}
          {post.readTime && (
            <div className="flex items-center">
              <Clock className="mr-1 h-4 w-4" />
              {post.readTime} min read
            </div>
          )}

          {/* View Count */}
          {post.viewCount && (
            <div className="flex items-center">
              <span>{post.viewCount.toLocaleString()} views</span>
            </div>
          )}
        </div>

        {/* Tags */}
        {post.tags && post.tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <Badge 
                key={tag.slug} 
                variant="outline" 
                className="text-xs"
                style={{
                  backgroundColor: tag.color ? `${tag.color}15` : undefined,
                  borderColor: tag.color || undefined,
                  color: tag.color || undefined
                }}
              >
                <Tag className="mr-1 h-3 w-3" />
                {tag.name}
              </Badge>
            ))}
          </div>
        )}

        {/* Social Share */}
        <div className="flex items-center justify-between">
          <SocialShare
            url={`${process.env.NEXT_PUBLIC_SITE_URL || ''}/blog/${post.slug}`}
            title={post.title}
            description={post.excerpt}
            image={post.featuredImage}
          />
        </div>
      </header>

      {/* Featured Image */}
      {post.featuredImage && (
        <div className="mb-8">
          <div className="relative aspect-[16/9] overflow-hidden rounded-lg border">
            <img
              src={post.featuredImage}
              alt={post.title}
              className="h-full w-full object-cover"
            />
          </div>
        </div>
      )}

      {/* Article Content */}
      <div className="prose prose-lg max-w-none mb-12">
        {post.content ? (
          <div dangerouslySetInnerHTML={{ __html: post.content }} />
        ) : (
          <p className="text-muted-foreground">No content available.</p>
        )}
      </div>

      <Separator className="my-12" />

      {/* Article Footer */}
      <footer className="space-y-8">
        {/* Author Bio */}
        <div className="rounded-lg border bg-muted/50 p-6">
          <div className="flex items-start space-x-4">
            {post.author.image && (
              <img
                src={post.author.image}
                alt={post.author.name || 'Author'}
                className="h-16 w-16 rounded-full"
              />
            )}
            <div className="flex-1">
              <h3 className="font-semibold text-lg">
                About {post.author.name}
              </h3>
              <p className="text-muted-foreground mt-2">
                {/* Add author bio here when available in the data model */}
                Content creator and technology enthusiast sharing insights and experiences.
              </p>
            </div>
          </div>
        </div>

        {/* Share Again */}
        <div className="text-center">
          <h3 className="font-semibold mb-4">Share this article</h3>
          <SocialShare
            url={`${process.env.NEXT_PUBLIC_SITE_URL || ''}/blog/${post.slug}`}
            title={post.title}
            description={post.excerpt}
            image={post.featuredImage}
            variant="large"
          />
        </div>
      </footer>

      {/* Related Posts */}
      {post.relatedPosts && post.relatedPosts.length > 0 && (
        <section className="mt-16">
          <h2 className="text-2xl font-bold mb-8">Related Articles</h2>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {post.relatedPosts.map((relatedPost) => (
              <BlogCard
                key={relatedPost.id}
                post={relatedPost}
                variant="compact"
                showExcerpt={false}
              />
            ))}
          </div>
        </section>
      )}

      {/* Navigation to Previous/Next Posts */}
      {(post.previousPost || post.nextPost) && (
        <nav className="mt-16 grid gap-4 md:grid-cols-2">
          {post.previousPost && (
            <Link
              href={`/blog/${post.previousPost.slug}`}
              className="group rounded-lg border p-6 transition-colors hover:bg-muted/50"
            >
              <div className="text-sm text-muted-foreground mb-2">Previous Article</div>
              <div className="font-semibold group-hover:text-primary transition-colors">
                {post.previousPost.title}
              </div>
            </Link>
          )}
          {post.nextPost && (
            <Link
              href={`/blog/${post.nextPost.slug}`}
              className="group rounded-lg border p-6 transition-colors hover:bg-muted/50 md:text-right"
            >
              <div className="text-sm text-muted-foreground mb-2">Next Article</div>
              <div className="font-semibold group-hover:text-primary transition-colors">
                {post.nextPost.title}
              </div>
            </Link>
          )}
        </nav>
      )}
    </article>
  )
}

// Skeleton component for loading state
export function BlogPostDetailSkeleton() {
  return (
    <div className="max-w-4xl mx-auto animate-pulse">
      {/* Back button */}
      <div className="mb-8">
        <div className="h-10 w-32 bg-muted rounded" />
      </div>

      {/* Header */}
      <div className="mb-8 space-y-6">
        <div className="h-6 w-24 bg-muted rounded" />
        <div className="space-y-3">
          <div className="h-12 bg-muted rounded" />
          <div className="h-12 w-3/4 bg-muted rounded" />
        </div>
        <div className="space-y-2">
          <div className="h-6 bg-muted rounded" />
          <div className="h-6 w-5/6 bg-muted rounded" />
        </div>
        <div className="flex space-x-4">
          <div className="h-5 w-32 bg-muted rounded" />
          <div className="h-5 w-24 bg-muted rounded" />
          <div className="h-5 w-20 bg-muted rounded" />
        </div>
      </div>

      {/* Featured image */}
      <div className="mb-8">
        <div className="aspect-[16/9] bg-muted rounded-lg" />
      </div>

      {/* Content */}
      <div className="space-y-4 mb-12">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="h-4 bg-muted rounded" />
        ))}
      </div>
    </div>
  )
}
