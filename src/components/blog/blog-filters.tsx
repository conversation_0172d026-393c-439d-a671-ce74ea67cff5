'use client'

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { BlogCategory, BlogTag, BlogPostFilters } from "@/types/blog"
import { ChevronDown, ChevronUp, X, Filter } from "lucide-react"
import { cn } from "@/lib/utils"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"

interface BlogFiltersProps {
  categories: BlogCategory[]
  tags: BlogTag[]
  selectedFilters: BlogPostFilters
  onFiltersChange: (filters: BlogPostFilters) => void
  className?: string
}

export function BlogFilters({
  categories,
  tags,
  selectedFilters,
  onFiltersChange,
  className
}: BlogFiltersProps) {
  const [categoriesOpen, setCategoriesOpen] = useState(true)
  const [tagsOpen, setTagsOpen] = useState(true)

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    const newFilters = {
      ...selectedFilters,
      categoryId: checked ? categoryId : ''
    }
    onFiltersChange(newFilters)
  }

  const handleTagChange = (tagId: string, checked: boolean) => {
    const currentTags = selectedFilters.tagIds || []
    const newTags = checked
      ? [...currentTags, tagId]
      : currentTags.filter(id => id !== tagId)
    
    const newFilters = {
      ...selectedFilters,
      tagIds: newTags
    }
    onFiltersChange(newFilters)
  }

  const clearAllFilters = () => {
    onFiltersChange({
      search: selectedFilters.search || '',
      categoryId: '',
      tagIds: [],
      status: 'published'
    })
  }

  const hasActiveFilters = selectedFilters.categoryId || (selectedFilters.tagIds && selectedFilters.tagIds.length > 0)

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="flex items-center text-lg font-semibold">
          <Filter className="mr-2 h-5 w-5" />
          Filters
        </h3>
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-muted-foreground hover:text-foreground"
          >
            Clear all
          </Button>
        )}
      </div>

      {/* Active Filters */}
      {hasActiveFilters && (
        <div className="space-y-2">
          <Label className="text-sm font-medium">Active filters:</Label>
          <div className="flex flex-wrap gap-2">
            {selectedFilters.categoryId && (
              <Badge variant="secondary" className="gap-1">
                {categories.find(c => c.id === selectedFilters.categoryId)?.name}
                <button
                  onClick={() => handleCategoryChange(selectedFilters.categoryId!, false)}
                  className="ml-1 rounded-full hover:bg-muted"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
            {selectedFilters.tagIds?.map(tagId => {
              const tag = tags.find(t => t.id === tagId)
              return tag ? (
                <Badge key={tagId} variant="secondary" className="gap-1">
                  #{tag.name}
                  <button
                    onClick={() => handleTagChange(tagId, false)}
                    className="ml-1 rounded-full hover:bg-muted"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ) : null
            })}
          </div>
        </div>
      )}

      <Separator />

      {/* Categories Filter */}
      {categories.length > 0 && (
        <Collapsible open={categoriesOpen} onOpenChange={setCategoriesOpen}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Categories</span>
              {categoriesOpen ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 pt-3">
            {categories.map((category) => (
              <div key={category.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`category-${category.id}`}
                  checked={selectedFilters.categoryId === category.id}
                  onCheckedChange={(checked) => 
                    handleCategoryChange(category.id, checked as boolean)
                  }
                />
                <Label
                  htmlFor={`category-${category.id}`}
                  className="flex-1 cursor-pointer text-sm font-normal"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {category.color && (
                        <div
                          className="h-3 w-3 rounded-full border"
                          style={{ backgroundColor: category.color }}
                        />
                      )}
                      <span>{category.name}</span>
                    </div>
                    {category._count?.posts !== undefined && (
                      <span className="text-xs text-muted-foreground">
                        {category._count.posts}
                      </span>
                    )}
                  </div>
                </Label>
              </div>
            ))}
          </CollapsibleContent>
        </Collapsible>
      )}

      {categories.length > 0 && tags.length > 0 && <Separator />}

      {/* Tags Filter */}
      {tags.length > 0 && (
        <Collapsible open={tagsOpen} onOpenChange={setTagsOpen}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Tags</span>
              {tagsOpen ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 pt-3">
            <div className="max-h-64 overflow-y-auto space-y-3">
              {tags.map((tag) => (
                <div key={tag.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`tag-${tag.id}`}
                    checked={selectedFilters.tagIds?.includes(tag.id) || false}
                    onCheckedChange={(checked) => 
                      handleTagChange(tag.id, checked as boolean)
                    }
                  />
                  <Label
                    htmlFor={`tag-${tag.id}`}
                    className="flex-1 cursor-pointer text-sm font-normal"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {tag.color && (
                          <div
                            className="h-3 w-3 rounded-full border"
                            style={{ backgroundColor: tag.color }}
                          />
                        )}
                        <span>#{tag.name}</span>
                      </div>
                      {tag._count?.posts !== undefined && (
                        <span className="text-xs text-muted-foreground">
                          {tag._count.posts}
                        </span>
                      )}
                    </div>
                  </Label>
                </div>
              ))}
            </div>
          </CollapsibleContent>
        </Collapsible>
      )}

      {/* No filters available */}
      {categories.length === 0 && tags.length === 0 && (
        <div className="text-center py-8">
          <p className="text-sm text-muted-foreground">
            No categories or tags available yet.
          </p>
        </div>
      )}
    </div>
  )
}
