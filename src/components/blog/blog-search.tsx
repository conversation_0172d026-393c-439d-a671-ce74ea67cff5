'use client'

import { useState, useEffect } from "react"
import { Search, X } from "lucide-react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface BlogSearchProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  debounceMs?: number
}

export function BlogSearch({
  value,
  onChange,
  placeholder = "Search posts...",
  className,
  debounceMs = 300
}: BlogSearchProps) {
  const [searchTerm, setSearchTerm] = useState(value)

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      onChange(searchTerm)
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [searchTerm, onChange, debounceMs])

  // Update local state when external value changes
  useEffect(() => {
    setSearchTerm(value)
  }, [value])

  const handleClear = () => {
    setSearchTerm('')
    onChange('')
  }

  return (
    <div className={cn("relative", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          type="text"
          placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-9 pr-9"
        />
        {searchTerm && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2"
            onClick={handleClear}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Clear search</span>
          </Button>
        )}
      </div>
    </div>
  )
}
