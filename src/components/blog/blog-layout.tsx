import { ReactNode } from 'react'
import { cn } from '@/lib/utils'

interface BlogLayoutProps {
  children: ReactNode
  className?: string
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

export function BlogLayout({ 
  children, 
  className,
  maxWidth = 'xl',
  padding = 'md'
}: BlogLayoutProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-7xl',
    '2xl': 'max-w-7xl',
    full: 'max-w-full'
  }

  const paddingClasses = {
    none: '',
    sm: 'px-4 py-6',
    md: 'px-6 py-8',
    lg: 'px-8 py-12'
  }

  return (
    <div className={cn(
      'mx-auto w-full',
      maxWidthClasses[maxWidth],
      paddingClasses[padding],
      className
    )}>
      {children}
    </div>
  )
}

interface BlogContainerProps {
  children: ReactNode
  className?: string
  variant?: 'default' | 'narrow' | 'wide'
}

export function BlogContainer({ 
  children, 
  className,
  variant = 'default'
}: BlogContainerProps) {
  const variantClasses = {
    default: 'max-w-4xl',
    narrow: 'max-w-2xl',
    wide: 'max-w-6xl'
  }

  return (
    <div className={cn(
      'mx-auto w-full px-4 sm:px-6 lg:px-8',
      variantClasses[variant],
      className
    )}>
      {children}
    </div>
  )
}

interface BlogGridProps {
  children: ReactNode
  className?: string
  columns?: {
    default?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  gap?: 'sm' | 'md' | 'lg'
}

export function BlogGrid({ 
  children, 
  className,
  columns = { default: 1, md: 2, lg: 3 },
  gap = 'md'
}: BlogGridProps) {
  const gapClasses = {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8'
  }

  const getColumnClasses = () => {
    const classes = ['grid']
    
    if (columns.default) classes.push(`grid-cols-${columns.default}`)
    if (columns.sm) classes.push(`sm:grid-cols-${columns.sm}`)
    if (columns.md) classes.push(`md:grid-cols-${columns.md}`)
    if (columns.lg) classes.push(`lg:grid-cols-${columns.lg}`)
    if (columns.xl) classes.push(`xl:grid-cols-${columns.xl}`)
    
    return classes.join(' ')
  }

  return (
    <div className={cn(
      getColumnClasses(),
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  )
}

interface BlogSectionProps {
  children: ReactNode
  className?: string
  title?: string
  description?: string
  spacing?: 'sm' | 'md' | 'lg'
}

export function BlogSection({ 
  children, 
  className,
  title,
  description,
  spacing = 'md'
}: BlogSectionProps) {
  const spacingClasses = {
    sm: 'space-y-4',
    md: 'space-y-6',
    lg: 'space-y-8'
  }

  return (
    <section className={cn(spacingClasses[spacing], className)}>
      {(title || description) && (
        <div className="space-y-2">
          {title && (
            <h2 className="text-2xl font-bold tracking-tight lg:text-3xl">
              {title}
            </h2>
          )}
          {description && (
            <p className="text-muted-foreground lg:text-lg">
              {description}
            </p>
          )}
        </div>
      )}
      {children}
    </section>
  )
}

interface ResponsiveImageProps {
  src: string
  alt: string
  className?: string
  aspectRatio?: 'square' | 'video' | 'wide' | 'tall'
  sizes?: string
  priority?: boolean
}

export function ResponsiveImage({ 
  src, 
  alt, 
  className,
  aspectRatio = 'video',
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
}: ResponsiveImageProps) {
  const aspectRatioClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    wide: 'aspect-[21/9]',
    tall: 'aspect-[3/4]'
  }

  return (
    <div className={cn(
      'relative overflow-hidden rounded-lg',
      aspectRatioClasses[aspectRatio],
      className
    )}>
      <img
        src={src}
        alt={alt}
        className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
        sizes={sizes}
      />
    </div>
  )
}

// Responsive text component
interface ResponsiveTextProps {
  children: ReactNode
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'body' | 'caption'
  className?: string
}

export function ResponsiveText({ 
  children, 
  variant = 'body',
  className 
}: ResponsiveTextProps) {
  const variantClasses = {
    h1: 'text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl',
    h2: 'text-2xl font-bold tracking-tight sm:text-3xl lg:text-4xl',
    h3: 'text-xl font-semibold sm:text-2xl lg:text-3xl',
    h4: 'text-lg font-semibold sm:text-xl lg:text-2xl',
    body: 'text-base sm:text-lg',
    caption: 'text-sm text-muted-foreground'
  }

  const Component = variant.startsWith('h') ? variant as keyof JSX.IntrinsicElements : 'p'

  return (
    <Component className={cn(variantClasses[variant], className)}>
      {children}
    </Component>
  )
}

// Mobile-first responsive utilities
export function MobileOnly({ children }: { children: ReactNode }) {
  return <div className="block md:hidden">{children}</div>
}

export function DesktopOnly({ children }: { children: ReactNode }) {
  return <div className="hidden md:block">{children}</div>
}

export function TabletUp({ children }: { children: ReactNode }) {
  return <div className="hidden sm:block">{children}</div>
}

export function TabletOnly({ children }: { children: ReactNode }) {
  return <div className="hidden sm:block md:hidden">{children}</div>
}
