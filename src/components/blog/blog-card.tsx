import Link from "next/link"
import { format } from "date-fns"
import { Clock, User, Calendar, ArrowRight } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { BlogPostCard } from "@/types/blog"
import { cn } from "@/lib/utils"

interface BlogCardProps {
  post: BlogPostCard
  variant?: 'default' | 'featured' | 'compact'
  showExcerpt?: boolean
  showCategory?: boolean
  showTags?: boolean
  showAuthor?: boolean
  showDate?: boolean
  showReadTime?: boolean
  className?: string
}

export function BlogCard({
  post,
  variant = 'default',
  showExcerpt = true,
  showCategory = true,
  showTags = false,
  showAuthor = true,
  showDate = true,
  showReadTime = true,
  className
}: BlogCardProps) {
  const isCompact = variant === 'compact'
  const isFeatured = variant === 'featured'

  return (
    <article className={cn(
      "group relative overflow-hidden rounded-lg border bg-card transition-all duration-300 hover:shadow-lg",
      isFeatured && "md:col-span-2 lg:col-span-2",
      className
    )}>
      <Link href={`/blog/${post.slug}`} className="block">
        {/* Featured Image */}
        {post.featuredImage && (
          <div className={cn(
            "relative overflow-hidden",
            isCompact ? "aspect-[4/3]" : isFeatured ? "aspect-[2/1]" : "aspect-[16/10]"
          )}>
            <img
              src={post.featuredImage}
              alt={post.title}
              className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
            
            {/* Category Badge on Image */}
            {showCategory && post.category && (
              <div className="absolute top-4 left-4">
                <Badge 
                  variant="secondary"
                  className="bg-white/90 text-gray-900 hover:bg-white"
                  style={{
                    backgroundColor: post.category.color ? `${post.category.color}20` : undefined,
                    borderColor: post.category.color || undefined,
                    color: post.category.color || undefined
                  }}
                >
                  {post.category.name}
                </Badge>
              </div>
            )}

            {/* Read Time Badge */}
            {showReadTime && post.readTime && (
              <div className="absolute top-4 right-4">
                <Badge variant="secondary" className="bg-black/70 text-white hover:bg-black/80">
                  <Clock className="mr-1 h-3 w-3" />
                  {post.readTime} min
                </Badge>
              </div>
            )}
          </div>
        )}

        {/* Content */}
        <div className={cn(
          "p-6",
          isCompact && "p-4",
          isFeatured && "p-8"
        )}>
          {/* Category (if no image) */}
          {showCategory && post.category && !post.featuredImage && (
            <div className="mb-3">
              <Badge 
                variant="outline"
                style={{
                  backgroundColor: post.category.color ? `${post.category.color}20` : undefined,
                  borderColor: post.category.color || undefined,
                  color: post.category.color || undefined
                }}
              >
                {post.category.name}
              </Badge>
            </div>
          )}

          {/* Title */}
          <h2 className={cn(
            "font-bold leading-tight text-foreground transition-colors group-hover:text-primary",
            isCompact ? "text-lg mb-2" : isFeatured ? "text-3xl mb-4" : "text-xl mb-3"
          )}>
            {post.title}
          </h2>

          {/* Excerpt */}
          {showExcerpt && post.excerpt && !isCompact && (
            <p className={cn(
              "text-muted-foreground leading-relaxed",
              isFeatured ? "text-lg mb-6" : "text-base mb-4"
            )}>
              {post.excerpt}
            </p>
          )}

          {/* Tags */}
          {showTags && post.tags && post.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {post.tags.slice(0, 3).map((tag) => (
                <Badge 
                  key={tag.slug} 
                  variant="outline" 
                  className="text-xs"
                  style={{
                    backgroundColor: tag.color ? `${tag.color}15` : undefined,
                    borderColor: tag.color || undefined,
                    color: tag.color || undefined
                  }}
                >
                  #{tag.name}
                </Badge>
              ))}
              {post.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{post.tags.length - 3} more
                </Badge>
              )}
            </div>
          )}

          {/* Meta Information */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center space-x-4">
              {/* Author */}
              {showAuthor && post.author && (
                <div className="flex items-center space-x-2">
                  {post.author.image && (
                    <img
                      src={post.author.image}
                      alt={post.author.name}
                      className="h-6 w-6 rounded-full"
                    />
                  )}
                  <span className="flex items-center">
                    {!post.author.image && <User className="mr-1 h-4 w-4" />}
                    {post.author.name}
                  </span>
                </div>
              )}

              {/* Date */}
              {showDate && post.publishedAt && (
                <div className="flex items-center">
                  <Calendar className="mr-1 h-4 w-4" />
                  {format(new Date(post.publishedAt), 'MMM d, yyyy')}
                </div>
              )}
            </div>

            {/* Read More Arrow */}
            <div className="flex items-center text-primary opacity-0 transition-opacity group-hover:opacity-100">
              <span className="mr-1 text-sm font-medium">Read more</span>
              <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
            </div>
          </div>
        </div>
      </Link>
    </article>
  )
}

// Skeleton component for loading states
export function BlogCardSkeleton({ variant = 'default' }: { variant?: 'default' | 'featured' | 'compact' }) {
  const isCompact = variant === 'compact'
  const isFeatured = variant === 'featured'

  return (
    <div className={cn(
      "rounded-lg border bg-card overflow-hidden",
      isFeatured && "md:col-span-2 lg:col-span-2"
    )}>
      {/* Image Skeleton */}
      <div className={cn(
        "bg-muted animate-pulse",
        isCompact ? "aspect-[4/3]" : isFeatured ? "aspect-[2/1]" : "aspect-[16/10]"
      )} />
      
      {/* Content Skeleton */}
      <div className={cn(
        "p-6 space-y-3",
        isCompact && "p-4",
        isFeatured && "p-8"
      )}>
        {/* Category */}
        <div className="h-5 w-20 bg-muted animate-pulse rounded" />
        
        {/* Title */}
        <div className="space-y-2">
          <div className="h-6 bg-muted animate-pulse rounded" />
          {!isCompact && <div className="h-6 w-3/4 bg-muted animate-pulse rounded" />}
        </div>
        
        {/* Excerpt */}
        {!isCompact && (
          <div className="space-y-2">
            <div className="h-4 bg-muted animate-pulse rounded" />
            <div className="h-4 w-5/6 bg-muted animate-pulse rounded" />
          </div>
        )}
        
        {/* Meta */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="h-4 w-24 bg-muted animate-pulse rounded" />
            <div className="h-4 w-20 bg-muted animate-pulse rounded" />
          </div>
          <div className="h-4 w-16 bg-muted animate-pulse rounded" />
        </div>
      </div>
    </div>
  )
}
