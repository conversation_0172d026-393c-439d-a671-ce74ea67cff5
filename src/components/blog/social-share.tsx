'use client'

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { Share2, Twitter, Facebook, Linkedin, Link2, Mail } from "lucide-react"
import { SocialShareData } from "@/types/blog"
import { cn } from "@/lib/utils"

interface SocialShareProps extends Partial<SocialShareData> {
  variant?: 'default' | 'large' | 'compact'
  className?: string
}

export function SocialShare({
  url = '',
  title = '',
  description = '',
  image = '',
  hashtags = [],
  variant = 'default',
  className
}: SocialShareProps) {
  const { toast } = useToast()
  const [isSharing, setIsSharing] = useState(false)

  const shareData = {
    title,
    text: description,
    url,
  }

  const encodedUrl = encodeURIComponent(url)
  const encodedTitle = encodeURIComponent(title)
  const encodedDescription = encodeURIComponent(description)
  const encodedHashtags = hashtags.join(',')

  const shareUrls = {
    twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}&hashtags=${encodedHashtags}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
    email: `mailto:?subject=${encodedTitle}&body=${encodedDescription}%0A%0A${encodedUrl}`,
  }

  const handleNativeShare = async () => {
    if (navigator.share) {
      setIsSharing(true)
      try {
        await navigator.share(shareData)
      } catch (error) {
        if ((error as Error).name !== 'AbortError') {
          console.error('Error sharing:', error)
          toast({
            title: "Error",
            description: "Failed to share. Please try again.",
            variant: "destructive",
          })
        }
      } finally {
        setIsSharing(false)
      }
    }
  }

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(url)
      toast({
        title: "Link copied!",
        description: "The link has been copied to your clipboard.",
      })
    } catch (error) {
      console.error('Error copying to clipboard:', error)
      toast({
        title: "Error",
        description: "Failed to copy link. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleSocialShare = (platform: keyof typeof shareUrls) => {
    window.open(shareUrls[platform], '_blank', 'width=600,height=400')
  }

  const isCompact = variant === 'compact'
  const isLarge = variant === 'large'

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {/* Native Share (if supported) */}
      {navigator?.share && (
        <Button
          variant="outline"
          size={isCompact ? "sm" : isLarge ? "lg" : "default"}
          onClick={handleNativeShare}
          disabled={isSharing}
          className="flex items-center gap-2"
        >
          <Share2 className={cn("h-4 w-4", isLarge && "h-5 w-5")} />
          {!isCompact && <span>Share</span>}
        </Button>
      )}

      {/* Social Media Buttons */}
      <div className="flex items-center gap-1">
        <Button
          variant="outline"
          size={isCompact ? "sm" : isLarge ? "lg" : "default"}
          onClick={() => handleSocialShare('twitter')}
          className="flex items-center gap-2 text-blue-500 hover:text-blue-600"
        >
          <Twitter className={cn("h-4 w-4", isLarge && "h-5 w-5")} />
          {isLarge && <span>Twitter</span>}
        </Button>

        <Button
          variant="outline"
          size={isCompact ? "sm" : isLarge ? "lg" : "default"}
          onClick={() => handleSocialShare('facebook')}
          className="flex items-center gap-2 text-blue-600 hover:text-blue-700"
        >
          <Facebook className={cn("h-4 w-4", isLarge && "h-5 w-5")} />
          {isLarge && <span>Facebook</span>}
        </Button>

        <Button
          variant="outline"
          size={isCompact ? "sm" : isLarge ? "lg" : "default"}
          onClick={() => handleSocialShare('linkedin')}
          className="flex items-center gap-2 text-blue-700 hover:text-blue-800"
        >
          <Linkedin className={cn("h-4 w-4", isLarge && "h-5 w-5")} />
          {isLarge && <span>LinkedIn</span>}
        </Button>

        <Button
          variant="outline"
          size={isCompact ? "sm" : isLarge ? "lg" : "default"}
          onClick={() => handleSocialShare('email')}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-700"
        >
          <Mail className={cn("h-4 w-4", isLarge && "h-5 w-5")} />
          {isLarge && <span>Email</span>}
        </Button>

        <Button
          variant="outline"
          size={isCompact ? "sm" : isLarge ? "lg" : "default"}
          onClick={handleCopyLink}
          className="flex items-center gap-2"
        >
          <Link2 className={cn("h-4 w-4", isLarge && "h-5 w-5")} />
          {isLarge && <span>Copy Link</span>}
        </Button>
      </div>
    </div>
  )
}

// Floating share buttons for sticky positioning
export function FloatingSocialShare(props: SocialShareProps) {
  return (
    <div className="fixed left-4 top-1/2 -translate-y-1/2 z-50 hidden lg:block">
      <div className="flex flex-col gap-2 rounded-lg border bg-background p-2 shadow-lg">
        <SocialShare {...props} variant="compact" className="flex-col" />
      </div>
    </div>
  )
}
