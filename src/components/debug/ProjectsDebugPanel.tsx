"use client";
import { useState } from 'react';

interface DebugResult {
  success: boolean;
  message: string;
  data?: {
    totalProjects: number;
    publishedProjects: number;
    projects: Array<{
      id: string;
      title: string;
      slug: string;
      status: string;
      category?: string;
      featuredImage?: string;
      excerpt?: string;
      clientName?: string;
      location?: string;
    }>;
  };
  error?: string;
}

export default function ProjectsDebugPanel() {
  const [result, setResult] = useState<DebugResult | null>(null);
  const [loading, setLoading] = useState(false);

  const testDatabase = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/debug/projects');
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        message: 'Failed to connect to debug API',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const testMainAPI = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/projects?limit=6');
      const data = await response.json();
      
      setResult({
        success: true,
        message: `Main API returned ${data.projects?.length || 0} projects`,
        data: {
          totalProjects: data.projects?.length || 0,
          publishedProjects: data.projects?.length || 0,
          projects: data.projects || []
        }
      });
    } catch (error) {
      setResult({
        success: false,
        message: 'Failed to connect to main projects API',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container my-5">
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">
            <i className="fas fa-database me-2"></i>
            Projects Database Debug Panel
          </h5>
        </div>
        <div className="card-body">
          <div className="mb-3">
            <button 
              className="btn btn-primary me-2" 
              onClick={testDatabase}
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2"></span>
                  Testing...
                </>
              ) : (
                <>
                  <i className="fas fa-flask me-2"></i>
                  Test Database Loading
                </>
              )}
            </button>
            
            <button 
              className="btn btn-secondary" 
              onClick={testMainAPI}
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2"></span>
                  Testing...
                </>
              ) : (
                <>
                  <i className="fas fa-api me-2"></i>
                  Test Main API
                </>
              )}
            </button>
          </div>

          {result && (
            <div className={`alert ${result.success ? 'alert-success' : 'alert-danger'}`}>
              <h6>
                {result.success ? (
                  <i className="fas fa-check-circle me-2"></i>
                ) : (
                  <i className="fas fa-exclamation-triangle me-2"></i>
                )}
                {result.message}
              </h6>
              
              {result.error && (
                <div className="mt-2">
                  <strong>Error:</strong> {result.error}
                </div>
              )}
              
              {result.data && (
                <div className="mt-3">
                  <h6>Database Summary:</h6>
                  <ul className="mb-0">
                    <li>Total Projects: {result.data.totalProjects}</li>
                    <li>Published Projects: {result.data.publishedProjects}</li>
                  </ul>
                  
                  {result.data.projects.length > 0 && (
                    <div className="mt-3">
                      <h6>Projects Preview:</h6>
                      <div className="table-responsive">
                        <table className="table table-sm">
                          <thead>
                            <tr>
                              <th>Title</th>
                              <th>Status</th>
                              <th>Category</th>
                              <th>Client</th>
                              <th>Location</th>
                            </tr>
                          </thead>
                          <tbody>
                            {result.data.projects.map((project) => (
                              <tr key={project.id}>
                                <td>
                                  <strong>{project.title}</strong>
                                  <br />
                                  <small className="text-muted">{project.slug}</small>
                                </td>
                                <td>
                                  <span className={`badge ${
                                    project.status === 'PUBLISHED' ? 'bg-success' : 'bg-warning'
                                  }`}>
                                    {project.status}
                                  </span>
                                </td>
                                <td>{project.category || '-'}</td>
                                <td>{project.clientName || '-'}</td>
                                <td>{project.location?.split(',')[0] || '-'}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}