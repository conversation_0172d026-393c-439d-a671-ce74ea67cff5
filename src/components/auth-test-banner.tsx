"use client"

import { useSession } from "next-auth/react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Shield, User, LogIn, Settings } from "lucide-react"

export function AuthTestBanner() {
  const { data: session, status } = useSession()

  if (status === "loading") {
    return (
      <div className="bg-blue-50 border-b border-blue-200 p-4">
        <div className="container mx-auto text-center">
          <p className="text-blue-700">Loading authentication status...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return (
      <div className="bg-yellow-50 border-b border-yellow-200 p-4">
        <div className="container mx-auto flex items-center justify-center gap-4">
          <p className="text-yellow-800">
            <LogIn className="inline w-4 h-4 mr-2" />
            Not signed in
          </p>
          <div className="flex gap-2">
            <Button asChild size="sm" variant="outline">
              <Link href="/auth/signin">Sign In</Link>
            </Button>
            <Button asChild size="sm">
              <Link href="/auth/signup">Sign Up</Link>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-green-50 border-b border-green-200 p-4">
      <div className="container mx-auto flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <User className="w-4 h-4 text-green-700" />
            <span className="text-green-800">
              Welcome, {session.user.name || session.user.email}!
            </span>
          </div>
          <Badge variant="outline" className="flex items-center gap-1">
            <Shield className="w-3 h-3" />
            {session.user.role}
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          {session.user.role === "ADMIN" && (
            <Button asChild size="sm" variant="default">
              <Link href="/admin">
                <Settings className="w-4 h-4 mr-2" />
                Admin Panel
              </Link>
            </Button>
          )}
          <Button asChild size="sm" variant="outline">
            <Link href="/auth/signin">Sign Out</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}