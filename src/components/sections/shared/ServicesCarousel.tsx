"use client";
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { getAllServiceImages } from '@/utils/serviceImages';

interface ServicesCarouselProps {
  title?: string;
  subtitle?: string;
  showControls?: boolean;
  autoPlay?: boolean;
  interval?: number;
}

export default function ServicesCarousel({ 
  title = "Our Services",
  subtitle = "Comprehensive IT Solutions",
  showControls = true,
  autoPlay = true,
  interval = 4000
}: ServicesCarouselProps) {
  const [currentSlide, setCurrentSlide] = useState(0);
  const serviceImages = getAllServiceImages();
  
  // Filter out misc images and group services into slides of 4
  const mainServices = serviceImages.filter(service => 
    !service.id.includes('misc') && !service.id.includes('image')
  );
  
  const slidesData = [];
  for (let i = 0; i < mainServices.length; i += 4) {
    slidesData.push(mainServices.slice(i, i + 4));
  }

  useEffect(() => {
    if (autoPlay && slidesData.length > 1) {
      const timer = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % slidesData.length);
      }, interval);
      return () => clearInterval(timer);
    }
  }, [autoPlay, interval, slidesData.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slidesData.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slidesData.length) % slidesData.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  return (
    <section className="services-carousel py-5">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 mx-auto text-center mb-5">
            <span className="subtitle text-primary fw-semibold mb-3 d-block">{subtitle}</span>
            <h2 className="display-6 fw-bold mb-3">{title}</h2>
            <div className="decorative-line mx-auto mb-3" />
          </div>
        </div>
        
        <div className="carousel-container position-relative">
          <div className="carousel-wrapper">
            <div 
              className="carousel-track"
              style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
              {slidesData.map((slide, slideIndex) => (
                <div key={slideIndex} className="carousel-slide">
                  <div className="row g-4">
                    {slide.map((service) => (
                      <div key={service.id} className="col-lg-3 col-md-6">
                        <div className="service-carousel-card">
                          <div className="service-image-wrapper">
                            <img 
                              src={service.image} 
                              alt={service.id.replace('-', ' ')}
                              className="service-carousel-image"
                            />
                            <div className="service-carousel-overlay">
                              <div className="service-carousel-icon">
                                <span className="icon-emoji">{service.icon}</span>
                              </div>
                            </div>
                          </div>
                          <div className="service-carousel-content">
                            <div className="service-carousel-category">
                              {service.category}
                            </div>
                            <h5 className="service-carousel-title">
                              {service.id.split('-').map(word => 
                                word.charAt(0).toUpperCase() + word.slice(1)
                              ).join(' ')}
                            </h5>
                            <Link 
                              href={`/service/${service.id}`}
                              className="service-carousel-link"
                            >
                              Learn More
                            </Link>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {showControls && slidesData.length > 1 && (
            <>
              <button 
                className="carousel-control carousel-control-prev"
                onClick={prevSlide}
              >
                <i className="fas fa-chevron-left"></i>
              </button>
              <button 
                className="carousel-control carousel-control-next"
                onClick={nextSlide}
              >
                <i className="fas fa-chevron-right"></i>
              </button>
            </>
          )}
        </div>
        
        {slidesData.length > 1 && (
          <div className="carousel-indicators mt-4 d-flex justify-content-center gap-2">
            {slidesData.map((_, index) => (
              <button
                key={index}
                className={`indicator ${index === currentSlide ? 'active' : ''}`}
                onClick={() => goToSlide(index)}
              />
            ))}
          </div>
        )}
      </div>

      <style jsx>{`
        .services-carousel {
          background: white;
        }
        
        .subtitle {
          letter-spacing: 2px;
          text-transform: uppercase;
          font-size: 0.9rem;
        }
        
        .decorative-line {
          width: 60px;
          height: 4px;
          background: linear-gradient(90deg, #007bff, #00c6ff);
          border-radius: 2px;
        }
        
        .carousel-container {
          overflow: hidden;
          border-radius: 20px;
        }
        
        .carousel-wrapper {
          position: relative;
          width: 100%;
        }
        
        .carousel-track {
          display: flex;
          transition: transform 0.5s ease-in-out;
          width: ${slidesData.length * 100}%;
        }
        
        .carousel-slide {
          width: 100%;
          flex-shrink: 0;
          padding: 1rem;
        }
        
        .service-carousel-card {
          background: #f8f9fa;
          border-radius: 15px;
          overflow: hidden;
          transition: all 0.3s ease;
          border: 1px solid #e9ecef;
        }
        
        .service-carousel-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
          border-color: #007bff;
        }
        
        .service-image-wrapper {
          position: relative;
          height: 150px;
          overflow: hidden;
        }
        
        .service-carousel-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }
        
        .service-carousel-card:hover .service-carousel-image {
          transform: scale(1.1);
        }
        
        .service-carousel-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 123, 255, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        .service-carousel-card:hover .service-carousel-overlay {
          opacity: 1;
        }
        
        .service-carousel-icon {
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          width: 3rem;
          height: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(10px);
        }
        
        .icon-emoji {
          font-size: 1.5rem;
        }
        
        .service-carousel-content {
          padding: 1rem;
        }
        
        .service-carousel-category {
          color: #007bff;
          font-size: 0.75rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 1px;
          margin-bottom: 0.5rem;
        }
        
        .service-carousel-title {
          color: #2c3e50;
          font-weight: bold;
          font-size: 1rem;
          margin-bottom: 0.75rem;
          line-height: 1.3;
        }
        
        .service-carousel-link {
          color: #007bff;
          text-decoration: none;
          font-weight: 600;
          font-size: 0.85rem;
          transition: color 0.3s ease;
        }
        
        .service-carousel-link:hover {
          color: #0056b3;
          text-decoration: none;
        }
        
        .carousel-control {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          background: rgba(255, 255, 255, 0.9);
          border: none;
          border-radius: 50%;
          width: 3rem;
          height: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #007bff;
          font-size: 1rem;
          transition: all 0.3s ease;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          z-index: 10;
        }
        
        .carousel-control:hover {
          background: #007bff;
          color: white;
          transform: translateY(-50%) scale(1.1);
        }
        
        .carousel-control-prev {
          left: -1.5rem;
        }
        
        .carousel-control-next {
          right: -1.5rem;
        }
        
        .indicator {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: none;
          background: #dee2e6;
          transition: all 0.3s ease;
          cursor: pointer;
        }
        
        .indicator.active {
          background: #007bff;
          transform: scale(1.2);
        }
        
        .indicator:hover {
          background: #007bff;
        }
        
        @media (max-width: 768px) {
          .carousel-control-prev {
            left: -0.5rem;
          }
          
          .carousel-control-next {
            right: -0.5rem;
          }
          
          .service-image-wrapper {
            height: 120px;
          }
          
          .service-carousel-content {
            padding: 0.75rem;
          }
        }
      `}</style>
    </section>
  );
}
