"use client";
import React from 'react';

type BannerProps = {
  banner: {
    pageTitle: string;
    breadTitle: string;
    description: string;
    type?: string;
  };
};

const ServiceBanner: React.FC<BannerProps> = ({ banner }) => (
  <section className="service-banner creative-banner py-10 text-center position-relative">
    <div className="banner-bg position-absolute top-0 start-0 w-100 h-100" />
    <div className="container position-relative z-1">
      <div className="banner-content">
        <span className="badge bg-primary text-white px-3 py-2 rounded-pill mb-3 fade-in-up">
          Motshwanelo IT Consulting
        </span>
        <h1 className="display-4 fw-bold mb-3 fade-in-up">{banner.pageTitle}</h1>
        <p className="lead mb-4 fade-in-up" style={{animationDelay:'0.15s'}}>{banner.description}</p>
        <div className="breadcrumb mb-2 d-flex justify-content-center align-items-center gap-2 fade-in-up" style={{animationDelay:'0.3s'}}>
          <span className="breadcrumb-icon">🏠</span>
          <span>Home</span>
          <span className="mx-1">/</span>
          <span className="breadcrumb-icon">🛠️</span>
          <span>Services</span>
          <span className="mx-1">/</span>
          <span className="fw-bold text-primary">{banner.breadTitle}</span>
        </div>
        <div className="service-highlights fade-in-up" style={{animationDelay:'0.45s'}}>
          <div className="row justify-content-center">
            <div className="col-md-3 col-6 mb-2">
              <div className="highlight-item">
                <i className="fas fa-check-circle text-primary"></i>
                <span>17+ Years Experience</span>
              </div>
            </div>
            <div className="col-md-3 col-6 mb-2">
              <div className="highlight-item">
                <i className="fas fa-award text-primary"></i>
                <span>Enterprise Grade</span>
              </div>
            </div>
            <div className="col-md-3 col-6 mb-2">
              <div className="highlight-item">
                <i className="fas fa-clock text-primary"></i>
                <span>24/7 Support</span>
              </div>
            </div>
            <div className="col-md-3 col-6 mb-2">
              <div className="highlight-item">
                <i className="fas fa-users text-primary"></i>
                <span>500+ Projects</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <style jsx>{`
      .creative-banner {
        background: linear-gradient(120deg, #e0eaff 0%, #f8fcff 100%);
        overflow: hidden;
        min-height: 350px;
        display: flex;
        align-items: center;
      }
      .banner-bg {
        background: radial-gradient(circle at 60% 40%, #b6e0ff 0%, #f8fcff 100%);
        opacity: 0.35;
        z-index: 0;
      }
      .fade-in-up {
        opacity: 0;
        transform: translateY(24px);
        animation: fadeInUp 0.7s forwards;
      }
      .fade-in-up[style*='0.15s'] { animation-delay: 0.15s; }
      .fade-in-up[style*='0.3s'] { animation-delay: 0.3s; }
      .fade-in-up[style*='0.45s'] { animation-delay: 0.45s; }
      @keyframes fadeInUp {
        to {
          opacity: 1;
          transform: none;
        }
      }
      .breadcrumb-icon {
        font-size: 1.1em;
      }
      .service-highlights {
        margin-top: 2rem;
        padding: 1.5rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }
      .highlight-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        font-weight: 500;
        color: #03276e;
        justify-content: center;
      }
      .highlight-item i {
        font-size: 1rem;
      }
      .badge {
        font-size: 0.9rem;
        font-weight: 500;
        letter-spacing: 0.5px;
      }
      .banner-content {
        width: 100%;
      }
      @media (max-width: 768px) {
        .service-highlights {
          padding: 1rem;
        }
        .highlight-item {
          font-size: 0.8rem;
          gap: 0.3rem;
        }
        .highlight-item i {
          font-size: 0.9rem;
        }
      }
    `}</style>
  </section>
);

export default ServiceBanner; 