"use client";
import React, { useState } from 'react';

type Feature = {
  num: string;
  title: string;
  text: string;
};

type FeaturesProps = {
  features: {
    title: string;
    description: string;
    features: Feature[];
  };
};

const ServiceFeatures: React.FC<FeaturesProps> = ({ features }) => {
  const [open, setOpen] = useState<number | null>(0);
  return (
    <section className="service-features py-5 bg-light">
      <div className="container">
        <h2 className="fw-bold mb-3 text-center">{features.title}</h2>
        <p className="mb-4 text-center">{features.description}</p>
        <div className="accordion" id="featuresAccordion">
          {features.features.map((f, idx) => (
            <div className={`accordion-item mb-2 feature-card${open === idx ? ' open' : ''}`} key={f.num}>
              <h2 className="accordion-header d-flex align-items-center" id={`heading${f.num}`}> 
                <span className="feature-badge me-2">{f.num}</span>
                <button
                  className={`accordion-button flex-grow-1${open === idx ? '' : ' collapsed'}`}
                  type="button"
                  onClick={() => setOpen(open === idx ? null : idx)}
                  aria-expanded={open === idx}
                  aria-controls={`collapse${f.num}`}
                >
                  {f.title}
                  <span className={`chevron ms-2${open === idx ? ' rotate' : ''}`}>▼</span>
                </button>
              </h2>
              <div
                id={`collapse${f.num}`}
                className={`accordion-collapse collapse${open === idx ? ' show' : ''}`}
                aria-labelledby={`heading${f.num}`}
                data-bs-parent="#featuresAccordion"
              >
                <div className="accordion-body">{f.text}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
      <style jsx>{`
        .feature-card {
          border-radius: 0.5rem;
          box-shadow: 0 1px 4px rgba(0,123,255,0.06);
          transition: box-shadow 0.2s;
        }
        .feature-card.open, .feature-card:hover {
          box-shadow: 0 4px 16px rgba(0,123,255,0.13), 0 1.5px 6px rgba(0,0,0,0.08);
        }
        .feature-badge {
          display: inline-block;
          min-width: 2em;
          height: 2em;
          background: linear-gradient(135deg,#007bff,#00c6ff);
          color: #fff;
          border-radius: 50%;
          text-align: center;
          line-height: 2em;
          font-weight: bold;
          font-size: 1.1em;
          box-shadow: 0 2px 8px rgba(0,123,255,0.10);
        }
        .chevron {
          display: inline-block;
          transition: transform 0.3s;
        }
        .chevron.rotate {
          transform: rotate(180deg);
        }
      `}</style>
    </section>
  );
};

export default ServiceFeatures; 