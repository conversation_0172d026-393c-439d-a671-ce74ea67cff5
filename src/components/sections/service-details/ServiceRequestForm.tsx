"use client";
import React, { useState } from 'react';

type FormField = {
  type: string;
  name: string;
  label: string;
  placeholder?: string;
};

type RequestFormProps = {
  form: {
    title: string;
    description: string;
    'form-fields': FormField[];
  };
};

const ServiceRequestForm: React.FC<RequestFormProps> = ({ form }) => {
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [submitted, setSubmitted] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);
    setTimeout(() => setSubmitted(false), 2000);
  };

  return (
    <section className="service-request-form py-5 bg-white position-relative">
      <div className="container">
        <h2 className="fw-bold mb-3 text-center">{form.title}</h2>
        <p className="mb-4 text-center">{form.description}</p>
        <form className="mx-auto" style={{ maxWidth: 500 }} onSubmit={handleSubmit}>
          {form['form-fields'].map((field) => {
            if (field.type === 'submit') {
              return (
                <button key={field.name} type="submit" className="btn btn-primary w-100 mt-3 animated-submit-btn">
                  {field.label}
                </button>
              );
            }
            if (field.type === 'textarea') {
              return (
                <div className="mb-3" key={field.name}>
                  <label className="form-label">{field.label}</label>
                  <textarea
                    className="form-control animated-field"
                    name={field.name}
                    placeholder={field.placeholder}
                    value={formData[field.name] || ''}
                    onChange={handleChange}
                  />
                </div>
              );
            }
            return (
              <div className="mb-3" key={field.name}>
                <label className="form-label">{field.label}</label>
                <input
                  className="form-control animated-field"
                  type={field.type}
                  name={field.name}
                  placeholder={field.placeholder}
                  value={formData[field.name] || ''}
                  onChange={handleChange}
                />
              </div>
            );
          })}
        </form>
        {submitted && (
          <div className="form-success-animation text-center mt-4">
            <span className="checkmark">✔️</span>
            <div className="confetti">🎉🎉🎉</div>
            <div className="fw-bold text-success mt-2">Thank you! We'll be in touch soon.</div>
          </div>
        )}
      </div>
      <style jsx>{`
        .animated-field:focus {
          border-color: #00c6ff;
          box-shadow: 0 0 0 2px rgba(0,198,255,0.15);
          transition: border 0.2s, box-shadow 0.2s;
        }
        .animated-submit-btn {
          transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
          box-shadow: 0 2px 8px rgba(0,123,255,0.08);
        }
        .animated-submit-btn:hover {
          background: linear-gradient(90deg,#007bff,#00c6ff);
          color: #fff;
          transform: scale(1.04);
          box-shadow: 0 4px 16px rgba(0,123,255,0.18);
        }
        .form-success-animation {
          animation: popIn 0.5s;
        }
        @keyframes popIn {
          0% { opacity: 0; transform: scale(0.7); }
          80% { opacity: 1; transform: scale(1.1); }
          100% { opacity: 1; transform: scale(1); }
        }
        .checkmark {
          font-size: 2.5em;
          color: #00c6ff;
        }
        .confetti {
          font-size: 1.5em;
          margin-top: 0.5em;
        }
      `}</style>
    </section>
  );
};

export default ServiceRequestForm; 