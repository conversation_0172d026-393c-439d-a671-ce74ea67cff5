"use client";
import React from 'react';

type AboutProps = {
  about: {
    title: string;
    description: string;
    image?: { src: string; alt?: string };
  };
};

const ServiceAbout: React.FC<AboutProps> = ({ about }) => (
  <section className="service-about py-5 bg-white">
    <div className="container">
      <div className="row align-items-center">
        {about.image && (
          <div className="col-md-5 mb-4 mb-md-0 slide-in-left">
            <img src={about.image.src} alt={about.image.alt || about.title} className="img-fluid rounded shadow" />
          </div>
        )}
        <div className="col-md-7 fade-in">
          <h2 className="fw-bold mb-3 position-relative">
            <span className="accent-bar me-2" />{about.title}
          </h2>
          <p style={{ whiteSpace: 'pre-line' }}>{about.description}</p>
        </div>
      </div>
    </div>
    <style jsx>{`
      .slide-in-left {
        opacity: 0;
        transform: translateX(-40px);
        animation: slideInLeft 0.7s 0.1s forwards;
      }
      @keyframes slideInLeft {
        to {
          opacity: 1;
          transform: none;
        }
      }
      .fade-in {
        opacity: 0;
        animation: fadeIn 0.7s 0.25s forwards;
      }
      @keyframes fadeIn {
        to { opacity: 1; }
      }
      .accent-bar {
        display: inline-block;
        width: 6px;
        height: 1.5em;
        background: linear-gradient(180deg,#007bff,#00c6ff);
        border-radius: 2px;
        vertical-align: middle;
        margin-right: 0.5em;
      }
    `}</style>
  </section>
);

export default ServiceAbout; 