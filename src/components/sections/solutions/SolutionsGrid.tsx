"use client";
import Link from 'next/link';
import { useState } from 'react';
import { getServiceImage, getServiceIcon } from '@/utils/serviceImages';

type Solution = {
  id: string;
  pageBanner: {
    pageTitle: string;
    description: string;
  };
  about?: {
    image?: {
      src: string;
      alt?: string;
    };
  };
  features?: {
    features: Array<{
      title: string;
      text: string;
    }>;
  };
};

type SolutionsGridProps = {
  solutions: Solution[];
};

export default function SolutionsGrid({ solutions }: SolutionsGridProps) {
  const [hoveredSolution, setHoveredSolution] = useState<string | null>(null);

  return (
    <section className="solutions-listing py-5">
      <div className="container">
        <div className="text-center mb-5">
          <span className="subtitle text-info fw-semibold" style={{letterSpacing:2}}>Our Products</span>
          <h1 className="display-5 fw-bold mb-2">Technology Solutions</h1>
          <div className="decorative-line mx-auto mb-2" style={{width:60, height:4, background:'linear-gradient(90deg,#17a2b8,#6f42c1)', borderRadius:2}} />
          <p className="lead text-muted">Innovative technology products designed to transform your business operations.</p>
        </div>
        
        <div className="row g-4">
          {solutions.map(solution => (
            <div className="col-lg-4 col-md-6" key={solution.id}>
              <div 
                className={`solution-card h-100 position-relative overflow-hidden ${
                  hoveredSolution === solution.id ? 'hovered' : ''
                }`}
                onMouseEnter={() => setHoveredSolution(solution.id)}
                onMouseLeave={() => setHoveredSolution(null)}
              >
                {/* Background Image */}
                <div className="card-background">
                  <img
                    src={solution.about?.image?.src || getServiceImage(solution.id)}
                    alt={solution.about?.image?.alt || solution.pageBanner.pageTitle}
                    className="background-image"
                  />
                  <div className="background-overlay" />
                </div>

                {/* Content */}
                <div className="card-content position-absolute w-100 h-100 d-flex flex-column justify-content-between p-4">
                  <div className="solution-header">
                    <div className="solution-icon mb-3">
                      <span className="icon-emoji">{getServiceIcon(solution.id)}</span>
                    </div>
                    <h3 className="solution-title text-white fw-bold mb-3">
                      {solution.pageBanner.pageTitle}
                    </h3>
                    <p className="solution-description text-white-75 mb-3">
                      {solution.pageBanner.description}
                    </p>
                  </div>

                  {/* Features Preview */}
                  <div className="solution-details">
                    {solution.features?.features && (
                      <div className="features-preview mb-3">
                        <div className="feature-tags d-flex flex-wrap gap-2">
                          {solution.features.features.slice(0, 3).map((feature, idx) => (
                            <span key={idx} className="feature-tag">
                              {feature.title}
                            </span>
                          ))}
                          {solution.features.features.length > 3 && (
                            <span className="feature-tag more">
                              +{solution.features.features.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                    
                    <Link 
                      href={`/solution/${solution.id}`} 
                      className="solution-cta-btn d-inline-flex align-items-center"
                    >
                      <span>Explore Solution</span>
                      <i className="fas fa-arrow-right ms-2"></i>
                    </Link>
                  </div>
                </div>

                {/* Hover Effect Overlay */}
                <div className="hover-overlay position-absolute w-100 h-100 top-0 start-0" />
              </div>
            </div>
          ))}
        </div>
      </div>

      <style jsx>{`
        .solutions-listing {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .decorative-line {
          margin-bottom: 1rem;
        }
        
        .solution-card {
          border-radius: 20px;
          cursor: pointer;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          min-height: 400px;
        }
        
        .solution-card:hover {
          transform: translateY(-10px) scale(1.02);
          box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }
        
        .card-background {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 1;
        }
        
        .background-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.4s ease;
        }
        
        .solution-card:hover .background-image {
          transform: scale(1.1);
        }
        
        .background-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            135deg,
            rgba(23, 162, 184, 0.8) 0%,
            rgba(111, 66, 193, 0.6) 50%,
            rgba(0, 0, 0, 0.7) 100%
          );
          transition: opacity 0.3s ease;
        }
        
        .solution-card:hover .background-overlay {
          opacity: 0.9;
        }
        
        .card-content {
          z-index: 2;
          transition: transform 0.3s ease;
        }
        
        .solution-icon {
          display: inline-block;
        }
        
        .icon-emoji {
          font-size: 3rem;
          display: inline-block;
          transition: transform 0.3s ease;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }
        
        .solution-card:hover .icon-emoji {
          transform: scale(1.2) rotate(5deg);
        }
        
        .solution-title {
          font-size: 1.4rem;
          line-height: 1.3;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .solution-description {
          font-size: 0.95rem;
          line-height: 1.5;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .text-white-75 {
          color: rgba(255, 255, 255, 0.75);
        }
        
        .features-preview {
          opacity: 0;
          transform: translateY(20px);
          transition: all 0.3s ease 0.1s;
        }
        
        .solution-card:hover .features-preview {
          opacity: 1;
          transform: translateY(0);
        }
        
        .feature-tag {
          background: rgba(255, 255, 255, 0.2);
          color: white;
          padding: 0.25rem 0.75rem;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: 500;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          transition: all 0.2s ease;
        }
        
        .feature-tag:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(1.05);
        }
        
        .feature-tag.more {
          background: rgba(23, 162, 184, 0.3);
          border-color: rgba(23, 162, 184, 0.5);
        }
        
        .solution-cta-btn {
          background: linear-gradient(45deg, #17a2b8, #6f42c1);
          color: white;
          padding: 0.75rem 1.5rem;
          border-radius: 25px;
          text-decoration: none;
          font-weight: 600;
          font-size: 0.9rem;
          transition: all 0.3s ease;
          box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .solution-cta-btn:hover {
          background: linear-gradient(45deg, #138496, #5a32a3);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
          color: white;
          text-decoration: none;
        }
        
        .solution-cta-btn i {
          transition: transform 0.3s ease;
        }
        
        .solution-cta-btn:hover i {
          transform: translateX(5px);
        }
        
        .hover-overlay {
          background: linear-gradient(
            45deg,
            rgba(23, 162, 184, 0.1) 0%,
            rgba(111, 66, 193, 0.1) 100%
          );
          opacity: 0;
          transition: opacity 0.3s ease;
          pointer-events: none;
        }
        
        .solution-card:hover .hover-overlay {
          opacity: 1;
        }
        
        @media (max-width: 768px) {
          .solution-card {
            min-height: 350px;
          }
          
          .solution-title {
            font-size: 1.2rem;
          }
          
          .solution-description {
            font-size: 0.9rem;
          }
          
          .icon-emoji {
            font-size: 2.5rem;
          }
        }
        
        @media (max-width: 576px) {
          .col-lg-4 {
            flex: 0 0 100%;
            max-width: 100%;
          }
        }
      `}</style>
    </section>
  );
}
