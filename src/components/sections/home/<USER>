"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import Link from "next/link";
import "swiper/css";
import "swiper/css/autoplay";

const swiperOptions = {
    modules: [Autoplay],
    slidesPerView: 1,
    spaceBetween: 30,
    autoplay: {
        delay: 2500,
        disableOnInteraction: false,
    },
    loop: true,
    breakpoints: {
        320: {
            slidesPerView: 2,
            spaceBetween: 20,
        },
        576: {
            slidesPerView: 3,
            spaceBetween: 20,
        },
        768: {
            slidesPerView: 4,
            spaceBetween: 25,
        },
        992: {
            slidesPerView: 4,
            spaceBetween: 25,
        },
        1200: {
            slidesPerView: 5,
            spaceBetween: 30,
        },
        1400: {
            slidesPerView: 6,
            spaceBetween: 30,
        },
    },
};

export default function Section2({ backgroundColor }: { backgroundColor: string }) {
    return (
        <>
            {/*=====HERO SLIDER AREA END=======*/}
            <div className={`hero1-slider ${!backgroundColor ? "" : backgroundColor}`}>
                <div className="container">
                    <div className="row justify-content-center">
                        <div className="col-lg-12 d-flex justify-content-center">
                            <Swiper {...swiperOptions} className="client-logo-slider">
                                <SwiperSlide>
                                    <img className="client-logo" src="assets/img/logo/partners/2560px-Hikvision_logo.svg.png" alt="Hikvision Partner" />
                                </SwiperSlide>
                                <SwiperSlide>
                                    <img className="client-logo" src="assets/img/logo/partners/acers.png" alt="Acer Partner" />
                                </SwiperSlide>
                                <SwiperSlide>
                                    <img className="client-logo dark-logo" src="assets/img/logo/partners/Dahua-LOGO_All-White.png" alt="Dahua Partner" />
                                </SwiperSlide>
                                <SwiperSlide>
                                    <img className="client-logo dark-logo" src="assets/img/logo/partners/dell.png" alt="Dell Partner" />
                                </SwiperSlide>
                                <SwiperSlide>
                                    <img className="client-logo dark-logo" src="assets/img/logo/partners/hp.png" alt="HP Partner" />
                                </SwiperSlide>
                                <SwiperSlide>
                                    <img className="client-logo dark-logo" src="assets/img/logo/partners/intel.png" alt="Intel Partner" />
                                </SwiperSlide>
                                <SwiperSlide>
                                    <img className="client-logo dark-logo" src="assets/img/logo/partners/lenovo.png" alt="Lenovo Partner" />
                                </SwiperSlide>
                                <SwiperSlide>
                                    <img className="client-logo dark-logo" src="assets/img/logo/partners/lexmark.png" alt="Lexmark Partner" />
                                </SwiperSlide>
                                <SwiperSlide>
                                    <img className="client-logo dark-logo" src="assets/img/logo/partners/toshiba.png" alt="Toshiba Partner" />
                                </SwiperSlide>
                                <SwiperSlide>
                                    <img className="client-logo" src="assets/img/logo/partners/2560px-Hikvision_logo.svg.png" alt="Hikvision Partner" />
                                </SwiperSlide>
                                <SwiperSlide>
                                    <img className="client-logo" src="assets/img/logo/partners/acers.png" alt="Acer Partner" />
                                </SwiperSlide>
                                <SwiperSlide>
                                    <img className="client-logo dark-logo" src="assets/img/logo/partners/Dahua-LOGO_All-White.png" alt="Dahua Partner" />
                                </SwiperSlide>
                                <SwiperSlide>
                                    <img className="client-logo dark-logo" src="assets/img/logo/partners/dell.png" alt="Dell Partner" />
                                </SwiperSlide>
                                <SwiperSlide>
                                    <img className="client-logo dark-logo" src="assets/img/logo/partners/hp.png" alt="HP Partner" />
                                </SwiperSlide>
                            </Swiper>
                        </div>
                    </div>
                </div>
            </div>
            {/*=====HERO SLIDER AREA END=======*/}
            <style jsx>{`
                .hero1-slider {
                    padding: 60px 0;
                    background-color: #1a1a1a;
                }
                .client-logo-slider {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                }
                .client-logo-slider .swiper-slide {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 80px;
                }
                .client-logo {
                    max-height: 50px;
                    max-width: 120px;
                    object-fit: contain;
                    filter: grayscale(100%) brightness(0.8);
                    transition: all 0.3s ease;
                    opacity: 0.7;
                }
                .client-logo:hover {
                    filter: grayscale(0%) brightness(1);
                    opacity: 1;
                    transform: scale(1.05);
                }
                .dark-logo {
                    filter: grayscale(100%) brightness(0) invert(1) brightness(0.8);
                }
                .dark-logo:hover {
                    filter: grayscale(0%) brightness(0) invert(1) brightness(1);
                }
                .hero1-slider .container {
                    max-width: 1200px;
                }
                .hero1-slider .row {
                    margin-left: 0;
                    margin-right: 0;
                }
            `}</style>
        </>
    );
}
