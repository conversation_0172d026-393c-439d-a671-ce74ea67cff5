"use client";

import Link from "next/link";
import heroData from "@/data/sections/hero-1.json";

export default function Section1() {
    return (
        <>
            {/*=====HERO AREA START =======*/}
            <div className="hero-area1">
                <div className="container">
                    <div className="row align-items-center">
                        <div className="col-lg-6">
                            <div className="main-heading">
                                <span className="span" data-aos="fade-right" data-aos-duration={800}>
                                    {heroData.subtitle}
                                </span>
                                <h1 className="text-anime-style-3" dangerouslySetInnerHTML={{ __html: heroData.title }}></h1>
                                <div className="space16" />
                                <p data-aos="fade-right" data-aos-duration={1000}>
                                    {heroData.description}
                                </p>
                                {heroData.highlights && (
                                    <div className="hero-highlights" data-aos="fade-right" data-aos-duration={900}>
                                        <div className="row">
                                            {heroData.highlights.map((highlight, index) => (
                                                <div key={index} className="col-6 mb-2">
                                                    <div className="highlight-item">
                                                        <i className="fa-solid fa-check-circle text-primary me-2"></i>
                                                        <span className="highlight-text">{highlight}</span>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                                <div className="space30" />
                                <div className="hero1-buttons" data-aos="fade-right" data-aos-duration={1200}>
                                    <Link className="theme-btn1" href={heroData.button1.link}>
                                        {heroData.button1.label}
                                        <span>
                                            <i className="fa-solid fa-arrow-right" />
                                        </span>
                                    </Link>
                                    <Link className="theme-btn2" href={heroData.button2.link}>
                                        {heroData.button2.label}
                                        <span>
                                            <i className="fa-solid fa-arrow-right" />
                                        </span>
                                    </Link>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-6">
                            <div className="hero1-images">
                                <div className="image1">
                                    <img src="assets/img/bg/hero1-main-bg.png" alt="" />
                                </div>
                                <div className="image2 overlay-anim" data-aos="zoom-in-up" data-aos-duration={700}>
                                    <img src="assets/img/hero/hero1-main-img.png" alt={heroData.image.alt} />
                                </div>
                                <div className="image3 shape-animaiton2" data-aos="zoom-in-up" data-aos-duration={700}>
                                    <img src="assets/img/shapes/review-img.png" alt="" />
                                </div>
                                <div className="image4 shape-animaiton3">
                                    <img src="assets/img/shapes/hero1-shape.png" alt="" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/*=====HERO AREA END=======*/}
            
            <style jsx>{`
                .hero-highlights {
                    margin: 20px 0;
                    padding: 20px;
                    background: rgba(3, 39, 110, 0.05);
                    border-radius: 10px;
                    border-left: 4px solid #03276e;
                }
                
                .highlight-item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 8px;
                }
                
                .highlight-text {
                    font-size: 14px;
                    font-weight: 500;
                    color: #03276e;
                }
                
                .text-primary {
                    color: #03276e !important;
                }
                
                @media (max-width: 768px) {
                    .hero-highlights {
                        padding: 15px;
                    }
                    
                    .highlight-text {
                        font-size: 12px;
                    }
                }
            `}</style>
        </>
    );
}
