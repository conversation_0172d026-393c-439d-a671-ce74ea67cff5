"use client";
import { useEffect, useState, useRef } from "react";
import Link from "next/link";
import { getServiceImage, getServiceIcon } from '@/utils/serviceImages';

export default function AnimatedHero() {
  const [scrollY, setScrollY] = useState(0);
  const heroRef = useRef<HTMLDivElement>(null);
  
  // Handle scroll for parallax effect
  useEffect(() => {
    const handleScroll = () => {
      if (heroRef.current) {
        const { top } = heroRef.current.getBoundingClientRect();
        setScrollY(-top * 0.5);
      }
    };
    
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  
  return (
    <section className="animated-hero" ref={heroRef}>
      <div className="hero-background">
        <div 
          className="parallax-layer layer-1" 
          style={{ transform: `translateY(${scrollY * 0.1}px)` }}
        ></div>
        <div 
          className="parallax-layer layer-2" 
          style={{ transform: `translateY(${scrollY * 0.2}px)` }}
        ></div>
        <div 
          className="parallax-layer layer-3" 
          style={{ transform: `translateY(${scrollY * 0.3}px)` }}
        ></div>
      </div>
      
      <div className="hero-particles">
        {Array.from({ length: 50 }).map((_, i) => (
          <div key={i} className={`particle particle-${i + 1}`}></div>
        ))}
      </div>
      
      <div className="hero-content">
        <div className="animated-logo">
          <div className="logo-m">
            <div className="logo-part m-left"></div>
            <div className="logo-part m-right"></div>
            <div className="logo-part m-middle-1"></div>
            <div className="logo-part m-middle-2"></div>
          </div>
          <div className="logo-text">
            <div className="logo-part i-letter"></div>
            <div className="logo-part t-letter-1"></div>
            <div className="logo-part t-letter-2"></div>
          </div>
        </div>
        
        <h1 className="hero-title">
          <span className="title-word word-1">Motshwanelo</span>
          <span className="title-word word-2">IT</span>
          <span className="title-word word-3">Consulting</span>
        </h1>
        
        <p className="hero-subtitle">
          Transforming African Infrastructure with Innovative Technology
        </p>
        
        <div className="hero-cta">
          <Link href="/services" className="thm-btn">
            <span>Explore Our Services</span>
          </Link>
          <Link href="/contact" className="thm-btn outline">
            <span>Contact Us</span>
          </Link>
        </div>
      </div>
      
      <div className="hero-cards">
        <div className="service-card" data-aos="fade-up" data-aos-delay="100">
          <div className="card-icon">
            <div className="service-emoji">{getServiceIcon('data-center')}</div>
          </div>
          <h3>Data Centre</h3>
          <p>Secure, efficient data environments</p>
        </div>

        <div className="service-card" data-aos="fade-up" data-aos-delay="200">
          <div className="card-icon">
            <div className="service-emoji">{getServiceIcon('smart-city')}</div>
          </div>
          <h3>Smart City</h3>
          <p>Comprehensive urban security</p>
        </div>

        <div className="service-card" data-aos="fade-up" data-aos-delay="300">
          <div className="card-icon">
            <div className="service-emoji">{getServiceIcon('it-consulting')}</div>
          </div>
          <h3>IT Consulting</h3>
          <p>Strategic technology guidance</p>
        </div>
      </div>
      
      <div className="scroll-indicator">
        <span>Scroll to explore</span>
        <div className="scroll-arrow">
          <i className="fa-solid fa-chevron-down"></i>
        </div>
      </div>

      <style jsx>{`
        .animated-hero {
          padding-top: 0;
          padding-bottom: 0;
        }
        .hero-content {
          padding-top: 0;
          padding-bottom: 0;
          height: 80vh;
          min-height: 600px;
        }
        .service-emoji {
          font-size: 2rem;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          transition: all 0.3s ease;
        }

        .service-card:hover .service-emoji {
          transform: scale(1.2) rotate(5deg);
        }

        .card-icon {
          width: 60px;
          height: 60px;
          background: linear-gradient(135deg, #007bff, #00c6ff);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 1rem;
          transition: all 0.3s ease;
          box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .service-card:hover .card-icon {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
        }
      `}</style>
    </section>
  );
}
