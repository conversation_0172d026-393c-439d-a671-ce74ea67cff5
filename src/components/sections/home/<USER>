"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination } from "swiper/modules";
import Link from "next/link";
import "swiper/css";
import "swiper/css/autoplay";
import { useState, useEffect, useMemo } from "react";

// Custom styles for enhanced presentation
const customStyles = `
  .project-item {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }
  
  .project-item:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
  }
  
  .project-item.active {
    box-shadow: 0 12px 40px rgba(0, 123, 255, 0.3);
  }
  
  .loading-container .spinner-border {
    border-width: 3px;
  }
  
  .error-container, .empty-container, .loading-container {
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  
  .project-category {
    background: rgba(0, 123, 255, 0.8);
    padding: 4px 12px;
    border-radius: 20px;
    display: inline-block;
    margin-bottom: 12px !important;
  }
  
  .project-two__carousel .swiper-pagination-bullet {
    background: rgba(255, 255, 255, 0.5);
    opacity: 1;
    width: 12px;
    height: 12px;
    margin: 0 6px;
    transition: all 0.3s ease;
  }
  
  .project-two__carousel .swiper-pagination-bullet-active {
    background: #007bff;
    transform: scale(1.2);
  }
  
  .alert-warning {
    border-left: 4px solid #ffc107;
    background: rgba(255, 193, 7, 0.1);
    border-radius: 8px;
  }
  
  @media (max-width: 768px) {
    .project-item {
      margin-bottom: 20px;
    }
  }
`;

interface Project {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  description?: string;
  featuredImage?: string;
  category?: string;
  clientName?: string;
  clientSector?: string;
  location?: string;
  projectValue?: string;
  status: string;
  tags: string[];
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

const swiperOptions = {
    modules: [Autoplay, Pagination],
    slidesPerView: 1,
    spaceBetween: 0,
    autoplay: {
        delay: 5000,
        disableOnInteraction: false,
    },
    loop: true,
    pagination: {
        el: ".swiper-pagination",
        clickable: true,
    },
};

export default function Section5() {
    const [activeIndex, setActiveIndex] = useState<number | null>(null);
    const [projects, setProjects] = useState<Project[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const handleMouseEnter = (index: number) => {
        setActiveIndex(index);
    };

    const handleMouseLeave = () => {
        setActiveIndex(null);
    };

    const handleKeyDown = (event: React.KeyboardEvent, globalIndex: number) => {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            setActiveIndex(globalIndex);
        }
    };

    const retryFetch = () => {
        setError(null);
        setLoading(true);
        fetchProjects();
    };

    const fetchProjects = async () => {
        try {
            setError(null);
            console.log('Fetching projects from API...');
            const response = await fetch('/api/projects?limit=6');
            
            if (response.ok) {
                const data = await response.json();
                console.log('Projects fetched successfully:', data);
                setProjects(data.projects || []);
                
                if (!data.projects || data.projects.length === 0) {
                    console.warn('No projects returned from API');
                }
            } else {
                const errorText = await response.text();
                console.error('API Error Response:', errorText);
                throw new Error(`Failed to fetch projects: ${response.status} - ${response.statusText}`);
            }
        } catch (error) {
            console.error('Error fetching projects:', error);
            setError(error instanceof Error ? error.message : 'Failed to load projects. Please try again.');
            setProjects([]);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchProjects();
    }, []);

    // Memoize grouped projects for performance
    const groupedProjects = useMemo(() => {
        const groups: Project[][] = [];
        for (let i = 0; i < projects.length; i += 3) {
            groups.push(projects.slice(i, i + 3));
        }
        return groups;
    }, [projects]);

    // Loading State Component
    const LoadingState = () => (
        <section className="project-two d-none d-lg-block" id="project">
            <div className="project-two__bottom">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-8 m-auto text-center">
                            <div className="heading1">
                                <span className="span" data-aos="zoom-in-left" data-aos-duration={700}>
                                    Digital Victories
                                </span>
                                <h2 className="text-anime-style-3">17 Years of Transforming African Infrastructure</h2>
                                <div className="space16" />
                                <p data-aos="fade-up" data-aos-duration={800}>
                                    From taxi ranks to corporate boardrooms, from crime hotspots to data sanctuaries - <br />
                                    witness how we've revolutionized Africa's digital landscape, one intelligent solution at a time.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div className="space60" />
                    <div className="project-two__carousel-container">
                        <div className="row">
                            {[1, 2, 3].map((index) => (
                                <div key={index} className="col-lg-4 col-md-6 mb-4">
                                    <div className="project-skeleton" style={{
                                        height: '300px',
                                        background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
                                        backgroundSize: '200% 100%',
                                        animation: 'loading 1.5s infinite',
                                        borderRadius: '12px',
                                        position: 'relative',
                                        overflow: 'hidden'
                                    }}>
                                        <div style={{
                                            position: 'absolute',
                                            bottom: '20px',
                                            left: '20px',
                                            right: '20px'
                                        }}>
                                            <div style={{
                                                height: '12px',
                                                background: 'rgba(255,255,255,0.3)',
                                                borderRadius: '6px',
                                                marginBottom: '8px',
                                                width: '60%'
                                            }}></div>
                                            <div style={{
                                                height: '20px',
                                                background: 'rgba(255,255,255,0.4)',
                                                borderRadius: '6px',
                                                marginBottom: '8px',
                                                width: '90%'
                                            }}></div>
                                            <div style={{
                                                height: '12px',
                                                background: 'rgba(255,255,255,0.3)',
                                                borderRadius: '6px',
                                                width: '40%'
                                            }}></div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                        <div className="text-center mt-4">
                            <div className="spinner-border text-primary mb-3" role="status" style={{ width: '2rem', height: '2rem' }}>
                                <span className="visually-hidden">Loading...</span>
                            </div>
                            <h5 className="text-muted">Loading Our Latest Projects...</h5>
                            <p className="text-muted">Discovering innovative solutions across Africa</p>
                        </div>
                    </div>
                    <style jsx>{`
                        @keyframes loading {
                            0% { background-position: 200% 0; }
                            100% { background-position: -200% 0; }
                        }
                    `}</style>
                </div>
            </div>
        </section>
    );

    // Error State Component
    const ErrorState = () => (
        <section className="project-two d-none d-lg-block" id="project">
            <div className="project-two__bottom">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-8 m-auto text-center">
                            <div className="heading1">
                                <span className="span" data-aos="zoom-in-left" data-aos-duration={700}>
                                    Digital Victories
                                </span>
                                <h2 className="text-anime-style-3">17 Years of Transforming African Infrastructure</h2>
                                <div className="space16" />
                                <p data-aos="fade-up" data-aos-duration={800}>
                                    From taxi ranks to corporate boardrooms, from crime hotspots to data sanctuaries - <br />
                                    witness how we've revolutionized Africa's digital landscape, one intelligent solution at a time.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div className="space60" />
                    <div className="row">
                        <div className="col-12 text-center">
                            <div className="error-container" style={{ padding: '60px 0' }}>
                                <div className="mb-4">
                                    <i className="fas fa-exclamation-triangle text-warning" style={{ fontSize: '3rem' }}></i>
                                </div>
                                <h4 className="text-danger mb-3">Unable to Load Projects</h4>
                                <p className="text-muted mb-4">{error}</p>
                                <button 
                                    className="btn btn-primary px-4 py-2"
                                    onClick={retryFetch}
                                    disabled={loading}
                                >
                                    {loading ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                                            Retrying...
                                        </>
                                    ) : (
                                        <>
                                            <i className="fas fa-redo me-2"></i>
                                            Try Again
                                        </>
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );

    // Empty State Component
    const EmptyState = () => (
        <section className="project-two d-none d-lg-block" id="project">
            <div className="project-two__bottom">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-8 m-auto text-center">
                            <div className="heading1">
                                <span className="span" data-aos="zoom-in-left" data-aos-duration={700}>
                                    Digital Victories
                                </span>
                                <h2 className="text-anime-style-3">17 Years of Transforming African Infrastructure</h2>
                                <div className="space16" />
                                <p data-aos="fade-up" data-aos-duration={800}>
                                    From taxi ranks to corporate boardrooms, from crime hotspots to data sanctuaries - <br />
                                    witness how we've revolutionized Africa's digital landscape, one intelligent solution at a time.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div className="space60" />
                    <div className="row">
                        <div className="col-12 text-center">
                            <div className="empty-container" style={{ padding: '60px 0' }}>
                                <div className="mb-4">
                                    <i className="fas fa-folder-open text-muted" style={{ fontSize: '3rem' }}></i>
                                </div>
                                <h4 className="text-muted mb-3">No Projects Available</h4>
                                <p className="text-muted">We're currently updating our project showcase. Check back soon!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );

    // Render appropriate state
    if (loading) return <LoadingState />;
    if (error && projects.length === 0) return <ErrorState />;
    if (!loading && projects.length === 0) return <EmptyState />;

    return (
        <>
            {/* Inject custom styles */}
            <style jsx>{customStyles}</style>
            
            {/*=====PROJECT AREA START=======*/}
            <section className="project-two d-none d-lg-block" id="project">
                <div className="project-two__bottom">
                    <div className="container">
                        <div className="row">
                            <div className="col-lg-8 m-auto text-center">
                                <div className="heading1">
                                    <span className="span" data-aos="zoom-in-left" data-aos-duration={700}>
                                        Digital Victories
                                    </span>
                                    <h2 className="text-anime-style-3">17 Years of Transforming African Infrastructure</h2>
                                    <div className="space16" />
                                    <p data-aos="fade-up" data-aos-duration={800}>
                                        From taxi ranks to corporate boardrooms, from crime hotspots to data sanctuaries - <br />
                                        witness how we've revolutionized Africa's digital landscape, one intelligent solution at a time.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="space60" />
                        {error && projects.length > 0 && (
                            <div className="row mb-4">
                                <div className="col-12">
                                    <div className="alert alert-warning d-flex align-items-center" role="alert">
                                        <i className="fas fa-exclamation-triangle me-2"></i>
                                        <div className="flex-grow-1">
                                            <strong>Notice:</strong> {error} Showing cached projects below.
                                        </div>
                                        <button 
                                            className="btn btn-sm btn-outline-warning ms-2"
                                            onClick={retryFetch}
                                            disabled={loading}
                                        >
                                            {loading ? (
                                                <span className="spinner-border spinner-border-sm" role="status"></span>
                                            ) : (
                                                <i className="fas fa-redo"></i>
                                            )}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        )}
                        
                        {/* Development info - remove in production */}
                        {process.env.NODE_ENV === 'development' && projects.length > 0 && (
                            <div className="row mb-3">
                                <div className="col-12">
                                    <div className="alert alert-info py-2" style={{ fontSize: '0.85rem' }}>
                                        <i className="fas fa-database me-2"></i>
                                        <strong>Database Status:</strong> Successfully loaded {projects.length} projects from file-based database
                                    </div>
                                </div>
                            </div>
                        )}
                        <div className="project-two__carousel-container" data-aos="fade-up" data-aos-duration={900}>
                            <Swiper {...swiperOptions} className="project-two__carousel owl-carousel owl-theme thm-owl__carousel project-style1-carousel owl-dot-style1 pb-5">
                                {groupedProjects.map((projectGroup, slideIndex) => (
                                    <SwiperSlide key={slideIndex} className="project-two__single-box">
                                        <ul className="project-two__box list-unstyled" onMouseLeave={handleMouseLeave}>
                                            {projectGroup.map((project, projectIndex) => {
                                                const globalIndex = slideIndex * 3 + projectIndex;
                                                const isActive = activeIndex === globalIndex;
                                                return (
                                                    <li 
                                                        key={project.id}
                                                        className={`project-item ${isActive ? "active" : ""}`} 
                                                        onMouseEnter={() => handleMouseEnter(globalIndex)}
                                                        onKeyDown={(e) => handleKeyDown(e, globalIndex)}
                                                        tabIndex={0}
                                                        role="button"
                                                        aria-label={`View project: ${project.title}`}
                                                        style={{
                                                            transition: 'all 0.3s ease',
                                                            transform: isActive ? 'scale(1.02)' : 'scale(1)',
                                                            zIndex: isActive ? 10 : 1,
                                                        }}
                                                    >
                                                        <div className="project-two__box-content">
                                                            <div
                                                                className="single-project-two__bg"
                                                                style={{
                                                                    backgroundImage: `url(${project.featuredImage || 'assets/img/project/project1-img1.png'})`,
                                                                    backgroundSize: 'cover',
                                                                    backgroundPosition: 'center',
                                                                    transition: 'all 0.3s ease',
                                                                    filter: isActive ? 'brightness(1.1)' : 'brightness(1)',
                                                                }}
                                                            />
                                                            <div 
                                                                className="img-holder-img-bg"
                                                                style={{
                                                                    background: isActive 
                                                                        ? 'linear-gradient(45deg, rgba(0,0,0,0.3), rgba(0,0,0,0.6))' 
                                                                        : 'linear-gradient(45deg, rgba(0,0,0,0.4), rgba(0,0,0,0.7))',
                                                                    transition: 'all 0.3s ease',
                                                                }}
                                                            />
                                                            <div 
                                                                className="project-two__box-content-inner-icon"
                                                                style={{
                                                                    opacity: isActive ? 1 : 0.8,
                                                                    transform: isActive ? 'scale(1.1)' : 'scale(1)',
                                                                    transition: 'all 0.3s ease',
                                                                }}
                                                            >
                                                                <Link 
                                                                    href={`/projects/${project.slug}`} 
                                                                    className="img-popup"
                                                                    title={`View ${project.title}`}
                                                                >
                                                                    <i className="icon-next" />
                                                                </Link>
                                                            </div>
                                                            <div className="project-two__box-content-inner">
                                                                <div className="project-two__box-content-inner-wrapper">
                                                                    <p 
                                                                        className="project-category"
                                                                        style={{
                                                                            fontSize: '0.9rem',
                                                                            fontWeight: '500',
                                                                            textTransform: 'uppercase',
                                                                            letterSpacing: '0.5px',
                                                                            marginBottom: '8px',
                                                                            color: isActive ? '#fff' : 'rgba(255,255,255,0.9)',
                                                                            transition: 'all 0.3s ease',
                                                                        }}
                                                                    >
                                                                        {project.category?.replace('-', ' ') || 'Project'}
                                                                    </p>
                                                                    <h4 
                                                                        style={{
                                                                            fontSize: isActive ? '1.3rem' : '1.2rem',
                                                                            transition: 'all 0.3s ease',
                                                                            lineHeight: '1.3',
                                                                        }}
                                                                    >
                                                                        <Link 
                                                                            href={`/projects/${project.slug}`}
                                                                            style={{
                                                                                color: '#fff',
                                                                                textDecoration: 'none',
                                                                                transition: 'all 0.3s ease',
                                                                            }}
                                                                            onMouseEnter={(e) => {
                                                                                e.currentTarget.style.color = '#007bff';
                                                                            }}
                                                                            onMouseLeave={(e) => {
                                                                                e.currentTarget.style.color = '#fff';
                                                                            }}
                                                                        >
                                                                            {project.title}
                                                                        </Link>
                                                                    </h4>
                                                                    {project.excerpt && (
                                                                        <p 
                                                                            className="project-excerpt"
                                                                            style={{
                                                                                fontSize: '0.9rem',
                                                                                color: 'rgba(255,255,255,0.85)',
                                                                                marginTop: '6px',
                                                                                lineHeight: '1.4',
                                                                                display: '-webkit-box',
                                                                                WebkitLineClamp: 2,
                                                                                WebkitBoxOrient: 'vertical',
                                                                                overflow: 'hidden',
                                                                            }}
                                                                        >
                                                                            {project.excerpt}
                                                                        </p>
                                                                    )}
                                                                    {(project.location || project.clientName) && (
                                                                        <div 
                                                                            className="project-meta"
                                                                            style={{
                                                                                fontSize: '0.8rem',
                                                                                color: 'rgba(255,255,255,0.7)',
                                                                                marginTop: '8px',
                                                                                display: 'flex',
                                                                                flexWrap: 'wrap',
                                                                                gap: '12px',
                                                                            }}
                                                                        >
                                                                            {project.location && (
                                                                                <span>
                                                                                    <i className="fas fa-map-marker-alt me-1"></i>
                                                                                    {project.location.split(',')[0]}
                                                                                </span>
                                                                            )}
                                                                            {project.clientName && (
                                                                                <span>
                                                                                    <i className="fas fa-building me-1"></i>
                                                                                    {project.clientName}
                                                                                </span>
                                                                            )}
                                                                        </div>
                                                                    )}
                                                                </div>
                                                                <div 
                                                                    className="icon"
                                                                    style={{
                                                                        transform: isActive ? 'translateX(5px)' : 'translateX(0)',
                                                                        transition: 'all 0.3s ease',
                                                                    }}
                                                                >
                                                                    <Link 
                                                                        href={`/projects/${project.slug}`}
                                                                        title={`Learn more about ${project.title}`}
                                                                    >
                                                                        <i className="fa-solid fa-arrow-right" />
                                                                    </Link>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                );
                                            })}
                                        </ul>
                                    </SwiperSlide>
                                ))}
                                <div className="swiper-pagination"></div>
                            </Swiper>
                        </div>
                    </div>
                </div>
            </section>
            {/*=====PROJECT AREA END=======*/}
        </>
    );
}
