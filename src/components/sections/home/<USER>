"use client";
import { useState, useEffect } from "react";
import techStackData from "@/data/tech-stack.json";

export default function TechStackSection() {
  const [activeCategory, setActiveCategory] = useState(0);
  const [isVisible, setIsVisible] = useState(Array(techStackData.categories[0].technologies.length).fill(false));

  useEffect(() => {
    // Reset visibility when category changes
    setIsVisible(Array(techStackData.categories[activeCategory].technologies.length).fill(false));
    
    // Animate technologies appearing one by one
    const timeouts = techStackData.categories[activeCategory].technologies.map((_, index) => {
      return setTimeout(() => {
        setIsVisible(prev => {
          const newState = [...prev];
          newState[index] = true;
          return newState;
        });
      }, 200 * (index + 1));
    });
    
    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, [activeCategory]);

  return (
    <section className="tech-stack-section sp bg-light" id="tech-stack">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 m-auto text-center">
            <div className="heading1">
              <span className="span" data-aos="zoom-in-left" data-aos-duration={700}>
                {techStackData.subtitle}
              </span>
              <h2 className="text-anime-style-3">{techStackData.title}</h2>
              <div className="space16" />
              <p data-aos="fade-up" data-aos-duration={800}>
                {techStackData.description}
              </p>
            </div>
          </div>
        </div>

        <div className="space60" />

        <div className="tech-stack-container">
          <div className="tech-categories" data-aos="fade-up" data-aos-duration={700}>
            {techStackData.categories.map((category, index) => (
              <div
                key={index}
                className={`tech-category ${activeCategory === index ? "active" : ""}`}
                onClick={() => setActiveCategory(index)}
              >
                <div className="category-icon">
                  <i className={category.icon}></i>
                </div>
                <h4>{category.name}</h4>
              </div>
            ))}
          </div>

          <div className="space40" />

          <div className="tech-grid" data-aos="fade-up" data-aos-duration={900}>
            {techStackData.categories[activeCategory].technologies.map((tech, index) => (
              <div 
                key={index} 
                className={`tech-card ${isVisible[index] ? "visible" : ""}`}
              >
                <div className="tech-logo">
                  <img src={tech.logo} alt={tech.name} />
                </div>
                <div className="tech-info">
                  <h4>{tech.name}</h4>
                  <p>{tech.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}