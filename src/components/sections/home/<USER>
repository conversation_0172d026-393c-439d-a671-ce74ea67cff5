'use client'
import Link from "next/link";
import { getServiceImage, getServiceIcon } from '@/utils/serviceImages';

export default function Section4() {
    return (
        <>
            {/*=====SERVICE AREA START=======*/}
            <div className="service1 sp overflow-hidden" id="service">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-8 m-auto text-center">
                            <div className="heading1-w">
                                <span className="span" data-aos="zoom-in-left" data-aos-duration={700}>
                                    Our Signature Solutions
                                </span>
                                <h2 className="text-anime-style-3">Pioneering Africa's Digital Renaissance</h2>
                                <div className="space16" />
                                <p data-aos="fade-left" data-aos-duration={800}>
                                    From crime-fighting surveillance networks to enterprise-grade data fortresses, <br />
                                    we architect the digital backbone that powers modern African businesses and communities.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div className="space30" />
                    <div className="row">
                        <div className="col-lg-4 col-md-6">
                            <div className="service1-box" data-aos="zoom-in-up" data-aos-duration={700}>
                                <div className="image overlay-anim">
                                    <img src={getServiceImage('it-consulting')} alt="IT Consulting Services" />
                                </div>
                                <div className="hover-area">
                                    <div className="icon">
                                        <div className="service-emoji-icon">{getServiceIcon('it-consulting')}</div>
                                    </div>
                                    <div className="space16" />
                                    <div className="heading1-w">
                                        <h4>
                                            <Link href="/service/it-consulting">Digital Transformation Architects</Link>
                                        </h4>
                                        <div className="space16" />
                                        <p>We don't just consult - we revolutionize. Transforming business DNA with cutting-edge technology strategies.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-4 col-md-6">
                            <div className="service1-box active" data-aos="zoom-in-up" data-aos-duration={900}>
                                <div className="image overlay-anim">
                                    <img src={getServiceImage('data-center')} alt="Data Center Solutions" />
                                </div>
                                <div className="hover-area">
                                    <div className="icon">
                                        <div className="service-emoji-icon">{getServiceIcon('data-center')}</div>
                                    </div>
                                    <div className="space16" />
                                    <div className="heading1-w">
                                        <h4>
                                            <Link href="/service/data-center">Digital Fortress Engineering</Link>
                                        </h4>
                                        <div className="space16" />
                                        <p>Fusion Module 2000 powered sanctuaries where your data lives, breathes, and thrives with military-grade security.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-4 col-md-6">
                            <div className="service1-box" data-aos="zoom-in-up" data-aos-duration={1100}>
                                <div className="image overlay-anim">
                                    <img src={getServiceImage('smart-city')} alt="Smart City Solutions" />
                                </div>
                                <div className="hover-area">
                                    <div className="icon">
                                        <div className="service-emoji-icon">{getServiceIcon('smart-city')}</div>
                                    </div>
                                    <div className="space16" />
                                    <div className="heading1-w">
                                        <h4>
                                            <Link href="/service/smart-city">Urban Intelligence Networks</Link>
                                        </h4>
                                        <div className="space16" />
                                        <p>IVS surveillance meets ITS traffic intelligence - creating cities that see, think, and protect 24/7.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="space50" />
                        <div className="col-lg-12">
                            <div className="text-center" data-aos="zoom-in-up" data-aos-duration={700}>
                                <Link className="theme-btn3" href="/service">
                                    Let’s get started
                                    <span>
                                        <i className="fa-solid fa-arrow-right" />
                                    </span>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/*=====SERVICE AREA END=======*/}

            <style jsx>{`
                .service-emoji-icon {
                    font-size: 2.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 60px;
                    height: 60px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 50%;
                    backdrop-filter: blur(10px);
                    border: 2px solid rgba(255, 255, 255, 0.2);
                    transition: all 0.3s ease;
                }

                .service1-box:hover .service-emoji-icon {
                    transform: scale(1.1) rotate(5deg);
                    background: rgba(255, 255, 255, 0.2);
                }
            `}</style>
        </>
    );
}
