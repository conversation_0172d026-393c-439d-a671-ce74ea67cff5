"use client";
import Link from 'next/link';
import { useState } from 'react';
import { getAllServiceImages, getServiceCategory } from '@/utils/serviceImages';

export default function ServicesShowcase() {
  const [hoveredService, setHoveredService] = useState<string | null>(null);
  const serviceImages = getAllServiceImages();
  
  // Filter to show main services (excluding misc images)
  const mainServices = serviceImages.filter(service => 
    !service.id.includes('misc') && !service.id.includes('image')
  ).slice(0, 8); // Show first 8 services

  return (
    <section className="services-showcase py-5">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 mx-auto text-center mb-5">
            <span className="subtitle text-primary fw-semibold mb-3 d-block">Our Expertise</span>
            <h2 className="display-5 fw-bold mb-3">Comprehensive IT Solutions</h2>
            <div className="decorative-line mx-auto mb-3" />
            <p className="lead text-muted">
              From smart city infrastructure to cutting-edge software development, 
              we deliver innovative solutions that transform businesses across Africa.
            </p>
          </div>
        </div>
        
        <div className="services-grid">
          {mainServices.map((service, index) => (
            <div 
              key={service.id} 
              className={`service-showcase-card ${hoveredService === service.id ? 'hovered' : ''}`}
              onMouseEnter={() => setHoveredService(service.id)}
              onMouseLeave={() => setHoveredService(null)}
            >
              <div className="service-image-container">
                <img 
                  src={service.image} 
                  alt={service.id.replace('-', ' ')}
                  className="service-image"
                />
                <div className="service-overlay">
                  <div className="service-icon">
                    <span className="icon-emoji">{service.icon}</span>
                  </div>
                </div>
              </div>
              
              <div className="service-content">
                <div className="service-category">
                  {getServiceCategory(service.id)}
                </div>
                <h4 className="service-title">
                  {service.id.split('-').map(word => 
                    word.charAt(0).toUpperCase() + word.slice(1)
                  ).join(' ')}
                </h4>
                <Link 
                  href={`/service/${service.id}`}
                  className="service-link"
                >
                  Learn More
                  <i className="fas fa-arrow-right ms-2"></i>
                </Link>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center mt-5">
          <Link href="/service" className="btn btn-primary btn-lg px-5 py-3 rounded-pill">
            View All Services
            <i className="fas fa-arrow-right ms-2"></i>
          </Link>
        </div>
      </div>

      <style jsx>{`
        .services-showcase {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .subtitle {
          letter-spacing: 2px;
          text-transform: uppercase;
          font-size: 0.9rem;
        }
        
        .decorative-line {
          width: 80px;
          height: 4px;
          background: linear-gradient(90deg, #007bff, #00c6ff);
          border-radius: 2px;
          margin-bottom: 1rem;
        }
        
        .services-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 2rem;
          margin-top: 3rem;
        }
        
        .service-showcase-card {
          background: white;
          border-radius: 20px;
          overflow: hidden;
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        
        .service-showcase-card:hover {
          transform: translateY(-10px);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .service-image-container {
          position: relative;
          height: 200px;
          overflow: hidden;
        }
        
        .service-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.4s ease;
        }
        
        .service-showcase-card:hover .service-image {
          transform: scale(1.1);
        }
        
        .service-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            135deg,
            rgba(0, 123, 255, 0.8) 0%,
            rgba(0, 198, 255, 0.6) 100%
          );
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        .service-showcase-card:hover .service-overlay {
          opacity: 1;
        }
        
        .service-icon {
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          width: 4rem;
          height: 4rem;
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(10px);
          border: 2px solid rgba(255, 255, 255, 0.3);
          transform: scale(0.8);
          transition: transform 0.3s ease;
        }
        
        .service-showcase-card:hover .service-icon {
          transform: scale(1);
        }
        
        .icon-emoji {
          font-size: 2rem;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }
        
        .service-content {
          padding: 1.5rem;
        }
        
        .service-category {
          color: #007bff;
          font-size: 0.8rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 1px;
          margin-bottom: 0.5rem;
        }
        
        .service-title {
          color: #2c3e50;
          font-weight: bold;
          font-size: 1.2rem;
          margin-bottom: 1rem;
          line-height: 1.3;
        }
        
        .service-link {
          color: #007bff;
          text-decoration: none;
          font-weight: 600;
          font-size: 0.9rem;
          transition: all 0.3s ease;
          display: inline-flex;
          align-items: center;
        }
        
        .service-link:hover {
          color: #0056b3;
          text-decoration: none;
        }
        
        .service-link i {
          transition: transform 0.3s ease;
        }
        
        .service-link:hover i {
          transform: translateX(5px);
        }
        
        .btn-primary {
          background: linear-gradient(45deg, #007bff, #00c6ff);
          border: none;
          font-weight: 600;
          letter-spacing: 1px;
          transition: all 0.3s ease;
          box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
        }
        
        .btn-primary:hover {
          background: linear-gradient(45deg, #0056b3, #0099cc);
          transform: translateY(-3px);
          box-shadow: 0 12px 35px rgba(0, 123, 255, 0.4);
        }
        
        .btn-primary i {
          transition: transform 0.3s ease;
        }
        
        .btn-primary:hover i {
          transform: translateX(5px);
        }
        
        @media (max-width: 768px) {
          .services-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
          }
          
          .service-image-container {
            height: 160px;
          }
          
          .service-content {
            padding: 1rem;
          }
          
          .service-title {
            font-size: 1.1rem;
          }
        }
        
        @media (max-width: 576px) {
          .services-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </section>
  );
}
