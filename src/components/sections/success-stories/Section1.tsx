"use client";
import { useState } from "react";
import Link from "next/link";
import successStoriesData from "@/data/success-stories.json";

export default function SuccessStoriesSection() {
  const [activeStory, setActiveStory] = useState(successStoriesData.stories[0]);

  return (
    <>
      {/*=====SUCCESS STORIES AREA START=======*/}
      <section className="success-stories-section sp" id="success-stories">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 m-auto text-center">
              <div className="heading1">
                <span className="span" data-aos="zoom-in-left" data-aos-duration={700}>
                  {successStoriesData.subtitle}
                </span>
                <h2 className="text-anime-style-3">{successStoriesData.title}</h2>
                <div className="space16" />
                <p data-aos="fade-up" data-aos-duration={800}>
                  {successStoriesData.description}
                </p>
              </div>
            </div>
          </div>
          
          <div className="space60" />
          
          <div className="row">
            <div className="col-lg-4">
              <div className="success-stories-nav">
                <h3>Our Success Stories</h3>
                <div className="space20" />
                <ul className="nav-tabs custom-tabs">
                  {successStoriesData.stories.map((story) => (
                    <li 
                      key={story.id} 
                      className={`nav-item ${activeStory.id === story.id ? 'active' : ''}`}
                      onClick={() => setActiveStory(story)}
                    >
                      <div className="nav-link">
                        <div className="story-tab-item">
                          <div className="category-badge">{story.category}</div>
                          <h4>{story.title}</h4>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            
            <div className="col-lg-8">
              <div className="success-story-content" data-aos="fade-up" data-aos-duration={900}>
                <div className="story-image">
                  <img 
                    src={activeStory.image} 
                    alt={activeStory.title} 
                    className="img-fluid rounded"
                  />
                  <div className="category-overlay">{activeStory.category}</div>
                </div>
                
                <div className="story-details">
                  <h3>{activeStory.title}</h3>
                  
                  <div className="story-section">
                    <h4>The Challenge</h4>
                    <p>{activeStory.challenge}</p>
                  </div>
                  
                  <div className="story-section">
                    <h4>Our Solution</h4>
                    <p>{activeStory.solution}</p>
                  </div>
                  
                  <div className="story-section">
                    <h4>The Results</h4>
                    <ul className="results-list">
                      {activeStory.results.map((result, index) => (
                        <li key={index}>
                          <i className="fa-solid fa-check-circle"></i>
                          <span>{result}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="testimonial-box">
                    <div className="quote-icon">
                      <i className="fa-solid fa-quote-left"></i>
                    </div>
                    <p className="quote-text">{activeStory.testimonial.quote}</p>
                    <div className="testimonial-author">
                      <p className="author-name">{activeStory.testimonial.author}</p>
                      <p className="author-position">{activeStory.testimonial.position}</p>
                    </div>
                  </div>
                  
                  <div className="story-cta">
                    <Link href="/contact" className="thm-btn">
                      <span>Discuss Your Project</span>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/*=====SUCCESS STORIES AREA END=======*/}
    </>
  );
}