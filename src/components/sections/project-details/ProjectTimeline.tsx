"use client";
import React from 'react';

interface TimelineProps {
  timeline: {
    title: string;
    phases: Array<{
      phase: string;
      duration: string;
      title: string;
      description: string;
    }>;
  };
}

const ProjectTimeline: React.FC<TimelineProps> = ({ timeline }) => (
  <section className="project-timeline py-5">
    <div className="container">
      <div className="row">
        <div className="col-lg-8 mx-auto text-center mb-5">
          <span className="subtitle text-primary fw-semibold mb-3 d-block">Project Execution</span>
          <h2 className="display-6 fw-bold mb-3">{timeline.title}</h2>
          <p className="lead text-muted">Our structured approach to project delivery ensures successful outcomes</p>
        </div>
      </div>
      
      <div className="timeline-container">
        <div className="timeline-line"></div>
        {timeline.phases.map((phase, index) => (
          <div key={index} className={`timeline-item ${index % 2 === 0 ? 'left' : 'right'}`}>
            <div className="timeline-content">
              <div className="timeline-marker">
                <span className="phase-number">{index + 1}</span>
              </div>
              <div className="timeline-card">
                <div className="timeline-header">
                  <h4 className="phase-title">{phase.phase}</h4>
                  <span className="duration-badge">{phase.duration}</span>
                </div>
                <h5 className="timeline-title">{phase.title}</h5>
                <p className="timeline-description">{phase.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>

    <style jsx>{`
      .project-timeline {
        background: #f8f9fa;
        position: relative;
      }
      
      .subtitle {
        letter-spacing: 2px;
        text-transform: uppercase;
        font-size: 0.9rem;
      }
      
      .timeline-container {
        position: relative;
        max-width: 1000px;
        margin: 0 auto;
        padding: 2rem 0;
      }
      
      .timeline-line {
        position: absolute;
        left: 50%;
        top: 0;
        bottom: 0;
        width: 4px;
        background: linear-gradient(180deg, #007bff, #00c6ff);
        transform: translateX(-50%);
        border-radius: 2px;
      }
      
      .timeline-item {
        position: relative;
        margin-bottom: 3rem;
        width: 50%;
      }
      
      .timeline-item.left {
        left: 0;
        padding-right: 3rem;
      }
      
      .timeline-item.right {
        left: 50%;
        padding-left: 3rem;
      }
      
      .timeline-content {
        position: relative;
      }
      
      .timeline-marker {
        position: absolute;
        width: 3rem;
        height: 3rem;
        background: white;
        border: 4px solid #007bff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
      }
      
      .timeline-item.left .timeline-marker {
        right: -1.5rem;
      }
      
      .timeline-item.right .timeline-marker {
        left: -1.5rem;
      }
      
      .phase-number {
        font-weight: bold;
        color: #007bff;
        font-size: 1rem;
      }
      
      .timeline-card {
        background: white;
        padding: 2rem;
        border-radius: 1rem;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }
      
      .timeline-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #007bff, #00c6ff);
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.3s ease;
      }
      
      .timeline-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      }
      
      .timeline-card:hover::before {
        transform: scaleX(1);
      }
      
      .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
      }
      
      .phase-title {
        color: #007bff;
        font-weight: bold;
        font-size: 1.1rem;
        margin: 0;
      }
      
      .duration-badge {
        background: linear-gradient(45deg, #007bff, #00c6ff);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.8rem;
        font-weight: 600;
      }
      
      .timeline-title {
        color: #2c3e50;
        font-weight: bold;
        margin-bottom: 0.75rem;
        font-size: 1.2rem;
      }
      
      .timeline-description {
        color: #6c757d;
        line-height: 1.6;
        margin: 0;
      }
      
      /* Arrow indicators */
      .timeline-item.left .timeline-card::after {
        content: '';
        position: absolute;
        right: -10px;
        top: 2rem;
        width: 0;
        height: 0;
        border-left: 10px solid white;
        border-top: 10px solid transparent;
        border-bottom: 10px solid transparent;
      }
      
      .timeline-item.right .timeline-card::after {
        content: '';
        position: absolute;
        left: -10px;
        top: 2rem;
        width: 0;
        height: 0;
        border-right: 10px solid white;
        border-top: 10px solid transparent;
        border-bottom: 10px solid transparent;
      }
      
      @media (max-width: 768px) {
        .timeline-line {
          left: 2rem;
        }
        
        .timeline-item {
          width: 100%;
          left: 0 !important;
          padding-left: 4rem !important;
          padding-right: 0 !important;
        }
        
        .timeline-marker {
          left: 0.5rem !important;
          right: auto !important;
          width: 2.5rem;
          height: 2.5rem;
        }
        
        .timeline-card::after {
          display: none;
        }
        
        .timeline-card {
          padding: 1.5rem;
        }
        
        .timeline-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.5rem;
        }
      }
    `}</style>
  </section>
);

export default ProjectTimeline;
