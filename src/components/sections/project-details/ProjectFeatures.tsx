"use client";
import React from 'react';

interface FeaturesProps {
  features: {
    title: string;
    description: string;
    features: Array<{
      num: string;
      title: string;
      text: string;
    }>;
  };
}

const ProjectFeatures: React.FC<FeaturesProps> = ({ features }) => (
  <section className="project-features py-5">
    <div className="container">
      <div className="row">
        <div className="col-lg-8 mx-auto text-center mb-5">
          <span className="subtitle text-primary fw-semibold mb-3 d-block">Key Components</span>
          <h2 className="display-6 fw-bold mb-3">{features.title}</h2>
          <p className="lead text-muted">{features.description}</p>
        </div>
      </div>
      
      <div className="row g-4">
        {features.features.map((feature, index) => (
          <div key={index} className="col-lg-6 col-md-6">
            <div className="feature-card h-100 p-4 rounded-4 position-relative">
              <div className="feature-number position-absolute">
                {feature.num}
              </div>
              <div className="feature-content">
                <h4 className="feature-title fw-bold mb-3">{feature.title}</h4>
                <p className="feature-text text-muted mb-0">{feature.text}</p>
              </div>
              <div className="feature-icon position-absolute">
                <i className="fas fa-check-circle"></i>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>

    <style jsx>{`
      .project-features {
        background: white;
      }
      
      .subtitle {
        letter-spacing: 2px;
        text-transform: uppercase;
        font-size: 0.9rem;
      }
      
      .feature-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        transition: all 0.4s ease;
        overflow: hidden;
        padding-left: 5rem !important;
        padding-top: 2rem !important;
        position: relative;
      }
      
      .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(180deg, #007bff, #00c6ff);
        transform: scaleY(0);
        transform-origin: bottom;
        transition: transform 0.3s ease;
      }
      
      .feature-card:hover {
        background: white;
        transform: translateY(-8px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
        border-color: #007bff;
      }
      
      .feature-card:hover::before {
        transform: scaleY(1);
      }
      
      .feature-number {
        top: 1.5rem;
        left: 1.5rem;
        width: 2.5rem;
        height: 2.5rem;
        background: linear-gradient(45deg, #007bff, #00c6ff);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.9rem;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        transition: all 0.3s ease;
      }
      
      .feature-card:hover .feature-number {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
      }
      
      .feature-icon {
        top: 1.5rem;
        right: 1.5rem;
        color: #28a745;
        font-size: 1.5rem;
        opacity: 0;
        transform: scale(0);
        transition: all 0.3s ease 0.1s;
      }
      
      .feature-card:hover .feature-icon {
        opacity: 1;
        transform: scale(1);
      }
      
      .feature-title {
        color: #2c3e50;
        font-size: 1.25rem;
        line-height: 1.3;
        margin-bottom: 1rem;
        transition: color 0.3s ease;
      }
      
      .feature-card:hover .feature-title {
        color: #007bff;
      }
      
      .feature-text {
        line-height: 1.6;
        font-size: 0.95rem;
      }
      
      .feature-content {
        position: relative;
        z-index: 2;
      }
      
      @media (max-width: 768px) {
        .feature-card {
          padding-left: 4rem !important;
          padding-top: 1.5rem !important;
        }
        
        .feature-number {
          width: 2rem;
          height: 2rem;
          font-size: 0.8rem;
          top: 1rem;
          left: 1rem;
        }
        
        .feature-icon {
          top: 1rem;
          right: 1rem;
          font-size: 1.25rem;
        }
        
        .feature-title {
          font-size: 1.1rem;
        }
        
        .feature-text {
          font-size: 0.9rem;
        }
      }
      
      @media (max-width: 576px) {
        .col-lg-6 {
          flex: 0 0 100%;
          max-width: 100%;
        }
      }
    `}</style>
  </section>
);

export default ProjectFeatures;
