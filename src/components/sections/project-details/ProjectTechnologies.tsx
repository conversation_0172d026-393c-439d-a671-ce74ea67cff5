"use client";
import React from 'react';

interface TechnologiesProps {
  technologies: string[];
}

const ProjectTechnologies: React.FC<TechnologiesProps> = ({ technologies }) => (
  <section className="project-technologies py-5">
    <div className="container">
      <div className="row">
        <div className="col-lg-8 mx-auto text-center mb-5">
          <span className="subtitle text-primary fw-semibold mb-3 d-block">Technology Stack</span>
          <h2 className="display-6 fw-bold mb-3">Technologies Used</h2>
          <p className="lead text-muted">Cutting-edge technologies and solutions powering this project</p>
        </div>
      </div>
      
      <div className="row justify-content-center">
        <div className="col-lg-10">
          <div className="technologies-grid">
            {technologies.map((tech, index) => (
              <div key={index} className="tech-item">
                <div className="tech-card">
                  <div className="tech-icon">
                    <i className="fas fa-microchip"></i>
                  </div>
                  <span className="tech-name">{tech}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>

    <style jsx>{`
      .project-technologies {
        background: white;
      }
      
      .subtitle {
        letter-spacing: 2px;
        text-transform: uppercase;
        font-size: 0.9rem;
      }
      
      .technologies-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
      }
      
      .tech-item {
        display: flex;
        justify-content: center;
      }
      
      .tech-card {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1.5rem 2rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
        width: 100%;
        max-width: 280px;
        position: relative;
        overflow: hidden;
      }
      
      .tech-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.1), transparent);
        transition: left 0.5s ease;
      }
      
      .tech-card:hover {
        background: white;
        border-color: #007bff;
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 123, 255, 0.15);
      }
      
      .tech-card:hover::before {
        left: 100%;
      }
      
      .tech-icon {
        width: 2.5rem;
        height: 2.5rem;
        background: linear-gradient(45deg, #007bff, #00c6ff);
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        transition: all 0.3s ease;
        flex-shrink: 0;
      }
      
      .tech-card:hover .tech-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
      }
      
      .tech-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.95rem;
        transition: color 0.3s ease;
        text-align: left;
        line-height: 1.3;
      }
      
      .tech-card:hover .tech-name {
        color: #007bff;
      }
      
      @media (max-width: 768px) {
        .technologies-grid {
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 1rem;
        }
        
        .tech-card {
          padding: 1rem 1.5rem;
          max-width: none;
        }
        
        .tech-icon {
          width: 2rem;
          height: 2rem;
          font-size: 1rem;
        }
        
        .tech-name {
          font-size: 0.9rem;
        }
      }
      
      @media (max-width: 576px) {
        .technologies-grid {
          grid-template-columns: 1fr;
        }
        
        .tech-card {
          justify-content: center;
          text-align: center;
        }
      }
    `}</style>
  </section>
);

export default ProjectTechnologies;
