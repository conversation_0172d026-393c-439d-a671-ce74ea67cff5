"use client";
import React from 'react';

interface AboutProps {
  about: {
    title: string;
    description: string;
    image?: {
      src: string;
      alt: string;
    };
    stats?: Array<{
      number: string;
      label: string;
    }>;
  };
}

const ProjectAbout: React.FC<AboutProps> = ({ about }) => (
  <section className="project-about py-5">
    <div className="container">
      <div className="row align-items-center">
        <div className="col-lg-6 mb-4 mb-lg-0">
          <div className="about-content">
            <span className="subtitle text-primary fw-semibold mb-3 d-block">Project Overview</span>
            <h2 className="display-6 fw-bold mb-4">{about.title}</h2>
            <div className="description">
              {about.description.split('\n\n').map((paragraph, index) => (
                <p key={index} className="mb-3 text-muted" style={{lineHeight: '1.7'}}>
                  {paragraph}
                </p>
              ))}
            </div>
          </div>
        </div>
        
        <div className="col-lg-6">
          <div className="about-visual">
            {about.image && (
              <div className="project-image mb-4">
                <img 
                  src={about.image.src} 
                  alt={about.image.alt}
                  className="img-fluid rounded-4 shadow-lg"
                  style={{width: '100%', height: 'auto'}}
                />
              </div>
            )}
            
            {about.stats && (
              <div className="project-stats">
                <div className="row g-3">
                  {about.stats.map((stat, index) => (
                    <div key={index} className="col-6">
                      <div className="stat-card text-center p-4 rounded-3 h-100">
                        <div className="stat-number display-6 fw-bold text-primary mb-2">
                          {stat.number}
                        </div>
                        <div className="stat-label text-muted small fw-medium">
                          {stat.label}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>

    <style jsx>{`
      .project-about {
        background: #f8f9fa;
      }
      
      .subtitle {
        letter-spacing: 2px;
        text-transform: uppercase;
        font-size: 0.9rem;
      }
      
      .description p {
        font-size: 1.1rem;
      }
      
      .project-image {
        position: relative;
        overflow: hidden;
      }
      
      .project-image::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(0, 123, 255, 0.1), rgba(0, 198, 255, 0.1));
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1;
        border-radius: 1rem;
      }
      
      .project-image:hover::before {
        opacity: 1;
      }
      
      .project-image img {
        transition: transform 0.3s ease;
      }
      
      .project-image:hover img {
        transform: scale(1.05);
      }
      
      .stat-card {
        background: white;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }
      
      .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.1), transparent);
        transition: left 0.5s ease;
      }
      
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border-color: #007bff;
      }
      
      .stat-card:hover::before {
        left: 100%;
      }
      
      .stat-number {
        background: linear-gradient(45deg, #007bff, #00c6ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      .about-content {
        padding-right: 2rem;
      }
      
      @media (max-width: 991px) {
        .about-content {
          padding-right: 0;
          text-align: center;
        }
        
        .project-stats {
          margin-top: 2rem;
        }
      }
      
      @media (max-width: 576px) {
        .stat-card {
          padding: 1.5rem !important;
        }
        
        .stat-number {
          font-size: 2rem !important;
        }
      }
    `}</style>
  </section>
);

export default ProjectAbout;
