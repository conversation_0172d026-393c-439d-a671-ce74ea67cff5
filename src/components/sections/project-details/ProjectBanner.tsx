"use client";
import React from 'react';

interface BannerProps {
  banner: {
    pageTitle: string;
    breadTitle: string;
    description: string;
  };
  client?: {
    name: string;
    sector: string;
    location: string;
    projectValue: string;
  };
}

const ProjectBanner: React.FC<BannerProps> = ({ banner, client }) => (
  <section className="project-banner creative-banner py-10 text-center position-relative">
    <div className="banner-bg position-absolute top-0 start-0 w-100 h-100" />
    <div className="container position-relative z-1">
      <div className="banner-content">
        <span className="badge bg-success text-white px-3 py-2 rounded-pill mb-3 fade-in-up">
          Project Success Story
        </span>
        <h1 className="display-4 fw-bold mb-3 fade-in-up text-white">{banner.pageTitle}</h1>
        <p className="lead mb-4 fade-in-up text-white-75" style={{animationDelay:'0.15s'}}>{banner.description}</p>
        
        {/* Breadcrumb */}
        <div className="breadcrumb mb-4 d-flex justify-content-center align-items-center gap-2 fade-in-up" style={{animationDelay:'0.3s'}}>
          <span className="breadcrumb-icon">🏠</span>
          <span className="text-white-75">Home</span>
          <span className="mx-1 text-white-50">/</span>
          <span className="breadcrumb-icon">📁</span>
          <span className="text-white-75">Projects</span>
          <span className="mx-1 text-white-50">/</span>
          <span className="fw-bold text-white">{banner.breadTitle}</span>
        </div>

        {/* Client Information */}
        {client && (
          <div className="client-info fade-in-up" style={{animationDelay:'0.45s'}}>
            <div className="row justify-content-center">
              <div className="col-lg-8">
                <div className="client-card bg-white bg-opacity-10 backdrop-blur rounded-4 p-4">
                  <div className="row text-center">
                    <div className="col-md-3 col-6 mb-3 mb-md-0">
                      <div className="client-detail">
                        <i className="fas fa-building text-white mb-2 d-block" style={{fontSize: '1.5rem'}}></i>
                        <h6 className="text-white mb-1">Client</h6>
                        <p className="text-white-75 mb-0 small">{client.name}</p>
                      </div>
                    </div>
                    <div className="col-md-3 col-6 mb-3 mb-md-0">
                      <div className="client-detail">
                        <i className="fas fa-industry text-white mb-2 d-block" style={{fontSize: '1.5rem'}}></i>
                        <h6 className="text-white mb-1">Sector</h6>
                        <p className="text-white-75 mb-0 small">{client.sector}</p>
                      </div>
                    </div>
                    <div className="col-md-3 col-6 mb-3 mb-md-0">
                      <div className="client-detail">
                        <i className="fas fa-map-marker-alt text-white mb-2 d-block" style={{fontSize: '1.5rem'}}></i>
                        <h6 className="text-white mb-1">Location</h6>
                        <p className="text-white-75 mb-0 small">{client.location}</p>
                      </div>
                    </div>
                    <div className="col-md-3 col-6">
                      <div className="client-detail">
                        <i className="fas fa-dollar-sign text-white mb-2 d-block" style={{fontSize: '1.5rem'}}></i>
                        <h6 className="text-white mb-1">Value</h6>
                        <p className="text-white-75 mb-0 small">{client.projectValue}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>

    <style jsx>{`
      .project-banner {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        min-height: 60vh;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
      }
      
      .banner-bg::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('/img/patterns/circuit-pattern.svg') repeat;
        opacity: 0.1;
        animation: float 20s ease-in-out infinite;
      }
      
      .fade-in-up {
        animation: fadeInUp 0.8s ease forwards;
        opacity: 0;
        transform: translateY(30px);
      }
      
      @keyframes fadeInUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(1deg); }
      }
      
      .text-white-75 {
        color: rgba(255, 255, 255, 0.75);
      }
      
      .text-white-50 {
        color: rgba(255, 255, 255, 0.5);
      }
      
      .backdrop-blur {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
      }
      
      .client-card {
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
      }
      
      .client-card:hover {
        background: rgba(255, 255, 255, 0.15) !important;
        transform: translateY(-5px);
      }
      
      .client-detail {
        transition: transform 0.3s ease;
      }
      
      .client-detail:hover {
        transform: scale(1.05);
      }
      
      .breadcrumb-icon {
        font-size: 1.1rem;
      }
      
      @media (max-width: 768px) {
        .project-banner {
          min-height: 50vh;
          padding: 2rem 0;
        }
        
        .display-4 {
          font-size: 2rem;
        }
        
        .client-card {
          margin-top: 2rem;
        }
      }
    `}</style>
  </section>
);

export default ProjectBanner;
