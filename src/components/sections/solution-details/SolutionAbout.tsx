"use client";
import React from 'react';

interface AboutProps {
  about: {
    title: string;
    description: string;
    image?: {
      src: string;
      alt: string;
    };
  };
}

const SolutionAbout: React.FC<AboutProps> = ({ about }) => (
  <section className="solution-about py-5">
    <div className="container">
      <div className="row align-items-center">
        <div className="col-lg-6 mb-4 mb-lg-0">
          <div className="about-content">
            <span className="subtitle text-info fw-semibold mb-3 d-block">Solution Overview</span>
            <h2 className="display-6 fw-bold mb-4">{about.title}</h2>
            <div className="description">
              {about.description.split('\n\n').map((paragraph, index) => (
                <p key={index} className="mb-3 text-muted" style={{lineHeight: '1.7'}}>
                  {paragraph}
                </p>
              ))}
            </div>
          </div>
        </div>
        
        <div className="col-lg-6">
          <div className="about-visual">
            {about.image && (
              <div className="solution-image">
                <img 
                  src={about.image.src} 
                  alt={about.image.alt}
                  className="img-fluid rounded-4 shadow-lg"
                  style={{width: '100%', height: 'auto'}}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>

    <style jsx>{`
      .solution-about {
        background: #f8f9fa;
      }
      
      .subtitle {
        letter-spacing: 2px;
        text-transform: uppercase;
        font-size: 0.9rem;
      }
      
      .description p {
        font-size: 1.1rem;
      }
      
      .solution-image {
        position: relative;
        overflow: hidden;
      }
      
      .solution-image::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1;
        border-radius: 1rem;
      }
      
      .solution-image:hover::before {
        opacity: 1;
      }
      
      .solution-image img {
        transition: transform 0.3s ease;
      }
      
      .solution-image:hover img {
        transform: scale(1.05);
      }
      
      .about-content {
        padding-right: 2rem;
      }
      
      @media (max-width: 991px) {
        .about-content {
          padding-right: 0;
          text-align: center;
        }
      }
    `}</style>
  </section>
);

export default SolutionAbout;
