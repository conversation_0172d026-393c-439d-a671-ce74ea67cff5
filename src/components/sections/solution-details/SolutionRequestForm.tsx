"use client";
import React from 'react';

interface RequestFormProps {
  requestForm: {
    title: string;
    description: string;
    "form-fields": Array<{
      type: string;
      name: string;
      label: string;
      placeholder?: string;
      options?: string[];
    }>;
  };
}

const SolutionRequestForm: React.FC<RequestFormProps> = ({ requestForm }) => (
  <section className="solution-request-form py-5">
    <div className="container">
      <div className="row">
        <div className="col-lg-8 mx-auto">
          <div className="form-container p-5 rounded-4">
            <div className="text-center mb-4">
              <span className="subtitle text-info fw-semibold mb-3 d-block">Get Started</span>
              <h2 className="display-6 fw-bold mb-3">{requestForm.title}</h2>
              <p className="lead text-muted">{requestForm.description}</p>
            </div>
            
            <form className="solution-form">
              <div className="row g-3">
                {requestForm["form-fields"].map((field, index) => {
                  if (field.type === 'submit') {
                    return (
                      <div key={index} className="col-12 text-center">
                        <button type="submit" className="btn btn-primary btn-lg px-5 py-3 rounded-pill">
                          {field.label}
                          <i className="fas fa-arrow-right ms-2"></i>
                        </button>
                      </div>
                    );
                  }
                  
                  if (field.type === 'textarea') {
                    return (
                      <div key={index} className="col-12">
                        <label className="form-label fw-semibold">{field.label}</label>
                        <textarea 
                          className="form-control form-control-lg" 
                          name={field.name}
                          placeholder={field.placeholder}
                          rows={4}
                        ></textarea>
                      </div>
                    );
                  }
                  
                  if (field.type === 'select') {
                    return (
                      <div key={index} className="col-md-6">
                        <label className="form-label fw-semibold">{field.label}</label>
                        <select className="form-select form-select-lg" name={field.name}>
                          <option value="">Select {field.label}</option>
                          {field.options?.map((option, optIndex) => (
                            <option key={optIndex} value={option}>{option}</option>
                          ))}
                        </select>
                      </div>
                    );
                  }
                  
                  return (
                    <div key={index} className="col-md-6">
                      <label className="form-label fw-semibold">{field.label}</label>
                      <input 
                        type={field.type} 
                        className="form-control form-control-lg" 
                        name={field.name}
                        placeholder={field.placeholder}
                      />
                    </div>
                  );
                })}
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <style jsx>{`
      .solution-request-form {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      }
      
      .subtitle {
        letter-spacing: 2px;
        text-transform: uppercase;
        font-size: 0.9rem;
      }
      
      .form-container {
        background: white;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
        position: relative;
        overflow: hidden;
      }
      
      .form-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
      }
      
      .form-label {
        color: #2c3e50;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
      }
      
      .form-control,
      .form-select {
        border: 2px solid #e9ecef;
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        font-size: 0.95rem;
      }
      
      .form-control:focus,
      .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
      }
      
      .btn-primary {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        font-weight: 600;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
      }
      
      .btn-primary:hover {
        background: linear-gradient(45deg, #5a67d8, #6b46c1);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
      }
      
      .btn-primary i {
        transition: transform 0.3s ease;
      }
      
      .btn-primary:hover i {
        transform: translateX(5px);
      }
      
      @media (max-width: 768px) {
        .form-container {
          padding: 2rem !important;
        }
        
        .col-md-6 {
          flex: 0 0 100%;
          max-width: 100%;
        }
        
        .display-6 {
          font-size: 1.75rem;
        }
      }
    `}</style>
  </section>
);

export default SolutionRequestForm;
