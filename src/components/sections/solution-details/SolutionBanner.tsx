"use client";
import React from 'react';

interface BannerProps {
  banner: {
    pageTitle: string;
    breadTitle: string;
    description: string;
  };
}

const SolutionBanner: React.FC<BannerProps> = ({ banner }) => (
  <section className="solution-banner creative-banner py-10 text-center position-relative">
    <div className="banner-bg position-absolute top-0 start-0 w-100 h-100" />
    <div className="container position-relative z-1">
      <div className="banner-content">
        <span className="badge bg-info text-white px-3 py-2 rounded-pill mb-3 fade-in-up">
          Innovative Solution
        </span>
        <h1 className="display-4 fw-bold mb-3 fade-in-up text-white">{banner.pageTitle}</h1>
        <p className="lead mb-4 fade-in-up text-white-75" style={{animationDelay:'0.15s'}}>{banner.description}</p>
        
        {/* Breadcrumb */}
        <div className="breadcrumb mb-4 d-flex justify-content-center align-items-center gap-2 fade-in-up" style={{animationDelay:'0.3s'}}>
          <span className="breadcrumb-icon">🏠</span>
          <span className="text-white-75">Home</span>
          <span className="mx-1 text-white-50">/</span>
          <span className="breadcrumb-icon">💡</span>
          <span className="text-white-75">Solutions</span>
          <span className="mx-1 text-white-50">/</span>
          <span className="fw-bold text-white">{banner.breadTitle}</span>
        </div>

        {/* Solution highlights */}
        <div className="solution-highlights fade-in-up" style={{animationDelay:'0.45s'}}>
          <div className="row justify-content-center">
            <div className="col-md-3 col-6 mb-2">
              <div className="highlight-item">
                <i className="fas fa-rocket text-info"></i>
                <span>Cutting-Edge Technology</span>
              </div>
            </div>
            <div className="col-md-3 col-6 mb-2">
              <div className="highlight-item">
                <i className="fas fa-shield-alt text-info"></i>
                <span>Enterprise Security</span>
              </div>
            </div>
            <div className="col-md-3 col-6 mb-2">
              <div className="highlight-item">
                <i className="fas fa-cogs text-info"></i>
                <span>Scalable Architecture</span>
              </div>
            </div>
            <div className="col-md-3 col-6 mb-2">
              <div className="highlight-item">
                <i className="fas fa-headset text-info"></i>
                <span>24/7 Support</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <style jsx>{`
      .solution-banner {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 60vh;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
      }
      
      .banner-bg::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('/img/patterns/tech-pattern.svg') repeat;
        opacity: 0.1;
        animation: float 25s ease-in-out infinite;
      }
      
      .fade-in-up {
        animation: fadeInUp 0.8s ease forwards;
        opacity: 0;
        transform: translateY(30px);
      }
      
      @keyframes fadeInUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-15px) rotate(2deg); }
      }
      
      .text-white-75 {
        color: rgba(255, 255, 255, 0.75);
      }
      
      .text-white-50 {
        color: rgba(255, 255, 255, 0.5);
      }
      
      .solution-highlights {
        margin-top: 2rem;
      }
      
      .highlight-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 1rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
      }
      
      .highlight-item:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-5px);
      }
      
      .highlight-item i {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
      }
      
      .highlight-item span {
        color: white;
        font-size: 0.9rem;
        font-weight: 600;
        text-align: center;
      }
      
      .breadcrumb-icon {
        font-size: 1.1rem;
      }
      
      @media (max-width: 768px) {
        .solution-banner {
          min-height: 50vh;
          padding: 2rem 0;
        }
        
        .display-4 {
          font-size: 2rem;
        }
        
        .solution-highlights {
          margin-top: 1.5rem;
        }
        
        .highlight-item {
          padding: 0.75rem;
          margin-bottom: 1rem;
        }
        
        .highlight-item i {
          font-size: 1.25rem;
        }
        
        .highlight-item span {
          font-size: 0.8rem;
        }
      }
    `}</style>
  </section>
);

export default SolutionBanner;
