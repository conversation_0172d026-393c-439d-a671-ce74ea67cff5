"use client";
import React from 'react';

interface SpecificationsProps {
  specifications: {
    title: string;
    categories: Array<{
      name: string;
      specs: string[];
    }>;
  };
}

const SolutionSpecifications: React.FC<SpecificationsProps> = ({ specifications }) => (
  <section className="solution-specifications py-5">
    <div className="container">
      <div className="row">
        <div className="col-lg-8 mx-auto text-center mb-5">
          <span className="subtitle text-info fw-semibold mb-3 d-block">Technical Details</span>
          <h2 className="display-6 fw-bold mb-3">{specifications.title}</h2>
          <p className="lead text-muted">Comprehensive technical specifications and requirements</p>
        </div>
      </div>
      
      <div className="row g-4">
        {specifications.categories.map((category, index) => (
          <div key={index} className="col-lg-4 col-md-6">
            <div className="spec-card h-100 p-4 rounded-4">
              <h4 className="spec-title fw-bold mb-3">{category.name}</h4>
              <ul className="spec-list">
                {category.specs.map((spec, specIndex) => (
                  <li key={specIndex} className="spec-item">{spec}</li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </div>

    <style jsx>{`
      .solution-specifications {
        background: #f8f9fa;
      }
      
      .subtitle {
        letter-spacing: 2px;
        text-transform: uppercase;
        font-size: 0.9rem;
      }
      
      .spec-card {
        background: white;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }
      
      .spec-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.3s ease;
      }
      
      .spec-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
        border-color: #667eea;
      }
      
      .spec-card:hover::before {
        transform: scaleX(1);
      }
      
      .spec-title {
        color: #2c3e50;
        font-size: 1.2rem;
        margin-bottom: 1.5rem;
        transition: color 0.3s ease;
      }
      
      .spec-card:hover .spec-title {
        color: #667eea;
      }
      
      .spec-list {
        list-style: none;
        padding: 0;
        margin: 0;
      }
      
      .spec-item {
        padding: 0.5rem 0;
        border-bottom: 1px solid #f0f0f0;
        color: #6c757d;
        font-size: 0.9rem;
        position: relative;
        padding-left: 1.5rem;
        transition: all 0.3s ease;
      }
      
      .spec-item::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: #28a745;
        font-weight: bold;
      }
      
      .spec-item:last-child {
        border-bottom: none;
      }
      
      .spec-card:hover .spec-item {
        color: #495057;
      }
      
      @media (max-width: 768px) {
        .spec-card {
          padding: 1.5rem;
        }
        
        .spec-title {
          font-size: 1.1rem;
        }
        
        .spec-item {
          font-size: 0.85rem;
        }
      }
    `}</style>
  </section>
);

export default SolutionSpecifications;
