"use client";
import React, { useState } from 'react';

interface GalleryProps {
  gallery: {
    items: Array<{
      url: string;
      orientation: string;
    }>;
  };
}

const SolutionGallery: React.FC<GalleryProps> = ({ gallery }) => {
  const [selectedImage, setSelectedImage] = useState<number | null>(null);

  const openLightbox = (index: number) => {
    setSelectedImage(index);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  const nextImage = () => {
    if (selectedImage !== null) {
      setSelectedImage((selectedImage + 1) % gallery.items.length);
    }
  };

  const prevImage = () => {
    if (selectedImage !== null) {
      setSelectedImage(selectedImage === 0 ? gallery.items.length - 1 : selectedImage - 1);
    }
  };

  return (
    <section className="solution-gallery py-5">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 mx-auto text-center mb-5">
            <span className="subtitle text-info fw-semibold mb-3 d-block">Visual Showcase</span>
            <h2 className="display-6 fw-bold mb-3">Solution Gallery</h2>
            <p className="lead text-muted">Explore our solution through detailed screenshots and visuals</p>
          </div>
        </div>
        
        <div className="gallery-grid">
          {gallery.items.map((item, index) => (
            <div 
              key={index} 
              className={`gallery-item ${item.orientation === 'v' ? 'vertical' : 'horizontal'}`}
              onClick={() => openLightbox(index)}
            >
              <div className="gallery-card">
                <img src={item.url} alt={`Solution screenshot ${index + 1}`} className="gallery-image" />
                <div className="gallery-overlay">
                  <div className="gallery-icon">
                    <i className="fas fa-search-plus"></i>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Lightbox */}
      {selectedImage !== null && (
        <div className="lightbox-overlay" onClick={closeLightbox}>
          <div className="lightbox-container">
            <button className="lightbox-close" onClick={closeLightbox}>
              <i className="fas fa-times"></i>
            </button>
            <button className="lightbox-prev" onClick={prevImage}>
              <i className="fas fa-chevron-left"></i>
            </button>
            <button className="lightbox-next" onClick={nextImage}>
              <i className="fas fa-chevron-right"></i>
            </button>
            <img 
              src={gallery.items[selectedImage].url} 
              alt={`Solution screenshot ${selectedImage + 1}`}
              className="lightbox-image"
            />
          </div>
        </div>
      )}

      <style jsx>{`
        .solution-gallery {
          background: white;
        }
        
        .subtitle {
          letter-spacing: 2px;
          text-transform: uppercase;
          font-size: 0.9rem;
        }
        
        .gallery-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 2rem;
          margin-top: 2rem;
        }
        
        .gallery-item {
          cursor: pointer;
        }
        
        .gallery-item.vertical {
          grid-row: span 2;
        }
        
        .gallery-card {
          position: relative;
          border-radius: 1rem;
          overflow: hidden;
          background: #f8f9fa;
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          height: 100%;
        }
        
        .gallery-card:hover {
          transform: translateY(-10px);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .gallery-image {
          width: 100%;
          height: 100%;
          min-height: 250px;
          object-fit: cover;
          transition: transform 0.3s ease;
        }
        
        .gallery-item.vertical .gallery-image {
          min-height: 400px;
        }
        
        .gallery-card:hover .gallery-image {
          transform: scale(1.1);
        }
        
        .gallery-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(102, 126, 234, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        .gallery-card:hover .gallery-overlay {
          opacity: 1;
        }
        
        .gallery-icon {
          color: white;
          font-size: 2rem;
          transform: scale(0.8);
          transition: transform 0.3s ease;
        }
        
        .gallery-card:hover .gallery-icon {
          transform: scale(1);
        }
        
        .lightbox-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.9);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 9999;
          padding: 2rem;
        }
        
        .lightbox-container {
          position: relative;
          max-width: 90vw;
          max-height: 90vh;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .lightbox-image {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
          border-radius: 0.5rem;
        }
        
        .lightbox-close,
        .lightbox-prev,
        .lightbox-next {
          position: absolute;
          background: rgba(255, 255, 255, 0.2);
          border: none;
          color: white;
          width: 3rem;
          height: 3rem;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.2rem;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
        }
        
        .lightbox-close:hover,
        .lightbox-prev:hover,
        .lightbox-next:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(1.1);
        }
        
        .lightbox-close {
          top: -1rem;
          right: -1rem;
        }
        
        .lightbox-prev {
          left: -4rem;
          top: 50%;
          transform: translateY(-50%);
        }
        
        .lightbox-next {
          right: -4rem;
          top: 50%;
          transform: translateY(-50%);
        }
        
        @media (max-width: 768px) {
          .gallery-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
          }
          
          .gallery-item.vertical {
            grid-row: span 1;
          }
          
          .gallery-image,
          .gallery-item.vertical .gallery-image {
            min-height: 200px;
          }
          
          .lightbox-prev,
          .lightbox-next {
            left: 1rem;
            right: 1rem;
            top: auto;
            bottom: 1rem;
            position: fixed;
          }
          
          .lightbox-next {
            right: 1rem;
            left: auto;
          }
          
          .lightbox-close {
            top: 1rem;
            right: 1rem;
            position: fixed;
          }
        }
      `}</style>
    </section>
  );
};

export default SolutionGallery;
