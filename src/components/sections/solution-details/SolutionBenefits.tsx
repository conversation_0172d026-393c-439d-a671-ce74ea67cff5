"use client";
import React from 'react';

interface BenefitsProps {
  benefits: {
    title: string;
    items: Array<{
      icon: string;
      title: string;
      description: string;
    }>;
  };
}

const SolutionBenefits: React.FC<BenefitsProps> = ({ benefits }) => (
  <section className="solution-benefits py-5">
    <div className="container">
      <div className="row">
        <div className="col-lg-8 mx-auto text-center mb-5">
          <span className="subtitle text-info fw-semibold mb-3 d-block">Value Proposition</span>
          <h2 className="display-6 fw-bold mb-3">{benefits.title}</h2>
          <p className="lead text-muted">Discover the transformative benefits of our solution</p>
        </div>
      </div>
      
      <div className="row g-4">
        {benefits.items.map((benefit, index) => (
          <div key={index} className="col-lg-6 col-md-6">
            <div className="benefit-card h-100 p-4 rounded-4 d-flex align-items-start">
              <div className="benefit-icon me-3">
                <span className="icon-emoji">{benefit.icon}</span>
              </div>
              <div className="benefit-content">
                <h4 className="benefit-title fw-bold mb-2">{benefit.title}</h4>
                <p className="benefit-description text-muted mb-0">{benefit.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>

    <style jsx>{`
      .solution-benefits {
        background: white;
      }
      
      .subtitle {
        letter-spacing: 2px;
        text-transform: uppercase;
        font-size: 0.9rem;
      }
      
      .benefit-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
      }
      
      .benefit-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.5s ease;
      }
      
      .benefit-card:hover {
        background: white;
        transform: translateY(-8px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
        border-color: #667eea;
      }
      
      .benefit-card:hover::before {
        left: 100%;
      }
      
      .benefit-icon {
        width: 4rem;
        height: 4rem;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      }
      
      .benefit-card:hover .benefit-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }
      
      .icon-emoji {
        font-size: 1.8rem;
        display: inline-block;
        transition: transform 0.3s ease;
      }
      
      .benefit-card:hover .icon-emoji {
        transform: scale(1.1);
      }
      
      .benefit-title {
        color: #2c3e50;
        font-size: 1.2rem;
        line-height: 1.3;
        transition: color 0.3s ease;
      }
      
      .benefit-card:hover .benefit-title {
        color: #667eea;
      }
      
      .benefit-description {
        line-height: 1.6;
        font-size: 0.95rem;
      }
      
      .benefit-content {
        flex: 1;
      }
      
      @media (max-width: 768px) {
        .benefit-card {
          padding: 1.5rem;
        }
        
        .benefit-icon {
          width: 3rem;
          height: 3rem;
          margin-right: 1rem;
        }
        
        .icon-emoji {
          font-size: 1.5rem;
        }
        
        .benefit-title {
          font-size: 1.1rem;
        }
        
        .benefit-description {
          font-size: 0.9rem;
        }
      }
      
      @media (max-width: 576px) {
        .col-lg-6 {
          flex: 0 0 100%;
          max-width: 100%;
        }
      }
    `}</style>
  </section>
);

export default SolutionBenefits;
