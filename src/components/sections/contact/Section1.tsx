'use client';

import Link from 'next/link';
import Image from 'next/image';
import { FormEvent, useState } from 'react';

export default function Section1() {
    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
    });

    const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        // Handle form submission here
        console.log('Form submitted:', formData);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    return (
        // Contact Area Start
        <div className="contact-page sp">
            <div className="container">
                <div className="row align-items-center">
                    <div className="col-lg-6">
                        <div className="heading1">
                            <span className="span">Connect With Digital Architects</span>
                            <h2>Ready to Transform Your Infrastructure? Let's Build Tomorrow Together</h2>
                            <div className="space16"></div>
                            <p>From smart city surveillance to enterprise data centres, we're here to architect your digital future. Whether you need IVS security solutions or Fusion Module 2000 data sanctuaries, let's start the conversation.</p>
                        </div>

                        <div className="contact-page-box">
                            <div className="row">
                                <div className="col-lg-5">
                                    <div className="contact-box">
                                        <div className="icon">
                                            <Image
                                                src="/assets/img/icons/contact-icon1.png"
                                                alt="contact icon"
                                                width={40}
                                                height={40}
                                            />
                                        </div>
                                        <div className="heading1">
                                            <p>Call Our Digital Architects</p>
                                            <h4><Link href="tel:0114920992">************/05</Link></h4>
                                        </div>
                                    </div>
                                </div>

                                <div className="col-lg-7">
                                    <div className="contact-box contact-box2">
                                        <div className="icon">
                                            <Image
                                                src="/assets/img/icons/contact-icon2.png"
                                                alt="contact icon"
                                                width={40}
                                                height={40}
                                            />
                                        </div>
                                        <div className="heading1">
                                            <p>Email Our Innovation Team</p>
                                            <h5><Link href="mailto:<EMAIL>"><EMAIL></Link></h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="col-lg-6">
                        <div className="contact1-form">
                            <div className="heading1">
                                <h3>Start Your Digital Transformation</h3>
                                <div className="space16"></div>
                                <p>Ready to revolutionize your infrastructure? Whether you need smart city surveillance, data centre solutions, or DiGiM broadcasting systems, our digital architects are here to help.</p>
                            </div>
                            <div className="space10"></div>

                            <form onSubmit={handleSubmit}>
                                <div className="row">
                                    <div className="col-md-6">
                                        <div className="single-input">
                                            <input
                                                type="text"
                                                name="firstName"
                                                placeholder="First Name"
                                                value={formData.firstName}
                                                onChange={handleChange}
                                            />
                                        </div>
                                    </div>

                                    <div className="col-md-6">
                                        <div className="single-input">
                                            <input
                                                type="text"
                                                name="lastName"
                                                placeholder="Last Name"
                                                value={formData.lastName}
                                                onChange={handleChange}
                                            />
                                        </div>
                                    </div>

                                    <div className="col-md-6">
                                        <div className="single-input">
                                            <input
                                                type="email"
                                                name="email"
                                                placeholder="Email"
                                                value={formData.email}
                                                onChange={handleChange}
                                            />
                                        </div>
                                    </div>

                                    <div className="col-md-6">
                                        <div className="single-input">
                                            <input
                                                type="tel"
                                                name="phone"
                                                placeholder="Phone"
                                                value={formData.phone}
                                                onChange={handleChange}
                                            />
                                        </div>
                                    </div>

                                    <div className="col-md-12">
                                        <div className="single-input">
                                            <input
                                                type="text"
                                                name="subject"
                                                placeholder="Subject"
                                                value={formData.subject}
                                                onChange={handleChange}
                                            />
                                        </div>
                                    </div>

                                    <div className="col-md-12">
                                        <div className="single-input">
                                            <textarea
                                                rows={4}
                                                name="message"
                                                placeholder="Message"
                                                value={formData.message}
                                                onChange={handleChange}
                                            ></textarea>
                                        </div>
                                    </div>

                                    <div className="col-md-12">
                                        <div className="button">
                                            <button type="submit" className="theme-btn1">
                                                Submit Now <span><i className="fa-solid fa-arrow-right"></i></span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        // Contact Area End
    );
}
