import Link from "next/link";

export default function Section3() {
    return (
        <>
            {/*===== CTA AREA START =======*/}
            <div className="cta">
                <div className="container">
                    <div className="row cta-border align-items-center">
                        <div className="col-lg-6">
                            <div className="heading1-w">
                                <h2>Ready to Transform Your Digital Infrastructure?</h2>
                                <div className="space16" />
                                <p>
                                    Join the digital revolution! From smart city surveillance to enterprise data centres, <br /> let's architect your organization's intelligent future together.
                                </p>
                            </div>
                        </div>
                        <div className="col-lg-6">
                            <div className="subscribe-area">
                                <form action="#">
                                    <input type="email" placeholder="Email Address" />
                                    <div className="button">
                                        <button type="submit" className="theme-btn1">
                                            Get Digital Updates
                                            <span>
                                                <i className="fa-solid fa-arrow-right" />
                                            </span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/*===== CTA AREA START =======*/}
        </>
    );
}
