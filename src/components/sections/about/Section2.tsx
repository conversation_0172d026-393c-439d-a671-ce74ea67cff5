import Link from "next/link";

export default function Section2() {
    return (
        <>
            {/*=====OUR MISSION AREA START=======*/}
            <div className="mission sp">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-7 m-auto text-center">
                            <div className="heading1">
                                <span className="span">Our Digital Manifesto</span>
                                <h2>Architecting Africa's Intelligent Tomorrow</h2>
                                <div className="space16" />
                                <p>We don't just build technology - we craft digital ecosystems that make cities safer, businesses smarter, and communities stronger. Every solution we create becomes part of Africa's digital DNA.</p>
                            </div>
                        </div>
                    </div>
                    <div className="space30" />
                    <div className="row">
                        <div className="col-lg-4 col-md-6">
                            <div className="mission-box">
                                <div className="icon">
                                    <img src="assets/img/icons/mission-icon1.png" alt="" />
                                </div>
                                <div className="heading1">
                                    <h5>
                                        <Link href="#">Urban Intelligence Revolution</Link>
                                    </h5>
                                    <div className="space16" />
                                    <p>IVS surveillance meets ITS traffic intelligence - creating cities that see, think, and protect communities 24/7.</p>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-4 col-md-6">
                            <div className="mission-box">
                                <div className="icon">
                                    <img src="assets/img/icons/mission-icon2.png" alt="" />
                                </div>
                                <div className="heading1">
                                    <h5>
                                        <Link href="#">Digital Fortress Engineering</Link>
                                    </h5>
                                    <div className="space16" />
                                    <p>Fusion Module 2000 powered data sanctuaries where enterprise information lives, breathes, and thrives with military-grade security.</p>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-4 col-md-6">
                            <div className="mission-box">
                                <div className="icon">
                                    <img src="assets/img/icons/mission-icon1.png" alt="" />
                                </div>
                                <div className="heading1">
                                    <h5>
                                        <Link href="#">Skills Renaissance Movement</Link>
                                    </h5>
                                    <div className="space16" />
                                    <p>Investing in tertiary graduates and disadvantaged communities, we're building Africa's tech talent pipeline one skilled professional at a time.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/*=====OUR MISSION AREA END=======*/}
        </>
    );
}
