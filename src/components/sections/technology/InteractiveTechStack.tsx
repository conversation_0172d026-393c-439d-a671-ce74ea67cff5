"use client";
import { useState, useEffect } from "react";
import techStackData from "@/data/tech-stack.json";

export default function InteractiveTechStack() {
  const [activeCategory, setActiveCategory] = useState(techStackData.categories[0].name);
  const [visibleTechs, setVisibleTechs] = useState<number[]>([]);
  const [hoveredTech, setHoveredTech] = useState<number | null>(null);

  // Animate technologies appearing one by one when category changes
  useEffect(() => {
    setVisibleTechs([]);
    
    const category = techStackData.categories.find(c => c.name === activeCategory);
    if (!category) return;
    
    const techCount = category.technologies.length;
    
    // Clear any existing timeouts
    const timeouts: NodeJS.Timeout[] = [];
    
    // Animate each technology appearing with a delay
    for (let i = 0; i < techCount; i++) {
      const timeout = setTimeout(() => {
        setVisibleTechs(prev => [...prev, i]);
      }, 100 * (i + 1));
      
      timeouts.push(timeout);
    }
    
    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, [activeCategory]);

  // Get color for category
  const getCategoryColor = (categoryName: string) => {
    const category = techStackData.categories.find(c => c.name === categoryName);
    return category?.icon.includes("server") ? "#03276e" : 
           category?.icon.includes("shield") ? "#e89d1a" : 
           category?.icon.includes("code") ? "#030376" : 
           category?.icon.includes("photo") ? "#6F6F87" : "#03276e";
  };

  return (
    <section className="interactive-tech-stack sp" id="tech-stack">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 m-auto text-center">
            <div className="heading1">
              <span className="span" data-aos="zoom-in-left" data-aos-duration={700}>
                {techStackData.subtitle}
              </span>
              <h2 className="text-anime-style-3">{techStackData.title}</h2>
              <div className="space16" />
              <p data-aos="fade-up" data-aos-duration={800}>
                {techStackData.description}
              </p>
            </div>
          </div>
        </div>

        <div className="space60" />

        <div className="tech-categories" data-aos="fade-up" data-aos-duration={700}>
          {techStackData.categories.map((category) => (
            <div
              key={category.name}
              className={`tech-category ${activeCategory === category.name ? "active" : ""}`}
              onClick={() => setActiveCategory(category.name)}
              style={{ 
                borderColor: getCategoryColor(category.name),
                backgroundColor: activeCategory === category.name ? getCategoryColor(category.name) : 'transparent'
              }}
            >
              <div className="category-icon">
                <i className={category.icon}></i>
              </div>
              <h4>{category.name}</h4>
            </div>
          ))}
        </div>

        <div className="space40" />

        <div className="tech-grid" data-aos="fade-up" data-aos-duration={900}>
          {techStackData.categories
            .find(c => c.name === activeCategory)?.technologies
            .map((tech, index) => (
              <div 
                key={index} 
                className={`tech-card ${visibleTechs.includes(index) ? "visible" : ""} ${hoveredTech === index ? "hovered" : ""}`}
                onMouseEnter={() => setHoveredTech(index)}
                onMouseLeave={() => setHoveredTech(null)}
                style={{ 
                  transitionDelay: `${index * 0.05}s`,
                  borderColor: getCategoryColor(activeCategory)
                }}
              >
                <div className="tech-logo">
                  <img src={tech.logo} alt={tech.name} />
                </div>
                <div className="tech-info">
                  <h4 style={{ color: getCategoryColor(activeCategory) }}>{tech.name}</h4>
                  <p>{tech.description}</p>
                </div>
                
                {hoveredTech === index && (
                  <div className="tech-details">
                    <h5>Key Benefits</h5>
                    <ul>
                      <li>Enhanced Performance</li>
                      <li>Scalable Architecture</li>
                      <li>Enterprise-grade Security</li>
                      <li>Seamless Integration</li>
                    </ul>
                  </div>
                )}
                
                <div className="tech-shine"></div>
              </div>
            ))}
        </div>
      </div>
    </section>
  );
}