"use client";
import { useState, useEffect } from "react";
import Link from "next/link";
import servicesData from "@/data/services-explorer.json";

export default function AnimatedServicesShowcase() {
  const [activeCategory, setActiveCategory] = useState(servicesData.categories[0]);
  const [activeService, setActiveService] = useState(servicesData.categories[0].services[0]);
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationDirection, setAnimationDirection] = useState("right");

  const handleCategoryChange = (category: any) => {
    if (category.id !== activeCategory.id) {
      setIsAnimating(true);
      setAnimationDirection(
        servicesData.categories.findIndex(c => c.id === category.id) > 
        servicesData.categories.findIndex(c => c.id === activeCategory.id) 
          ? "right" 
          : "left"
      );
      
      setTimeout(() => {
        setActiveCategory(category);
        setActiveService(category.services[0]);
        setIsAnimating(false);
      }, 300);
    }
  };

  const handleServiceChange = (service: any) => {
    if (service.id !== activeService.id) {
      setIsAnimating(true);
      setAnimationDirection(
        activeCategory.services.findIndex(s => s.id === service.id) > 
        activeCategory.services.findIndex(s => s.id === activeService.id) 
          ? "right" 
          : "left"
      );
      
      setTimeout(() => {
        setActiveService(service);
        setIsAnimating(false);
      }, 300);
    }
  };

  // Auto-rotate services every 5 seconds if no interaction
  useEffect(() => {
    const interval = setInterval(() => {
      const currentIndex = activeCategory.services.findIndex(s => s.id === activeService.id);
      const nextIndex = (currentIndex + 1) % activeCategory.services.length;
      handleServiceChange(activeCategory.services[nextIndex]);
    }, 5000);
    
    return () => clearInterval(interval);
  }, [activeCategory, activeService]);

  return (
    <section className="animated-services-showcase sp" id="services-showcase">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 m-auto text-center">
            <div className="heading1">
              <span className="span" data-aos="zoom-in-left" data-aos-duration={700}>
                {servicesData.subtitle}
              </span>
              <h2 className="text-anime-style-3">{servicesData.title}</h2>
              <div className="space16" />
              <p data-aos="fade-up" data-aos-duration={800}>
                {servicesData.description}
              </p>
            </div>
          </div>
        </div>

        <div className="space60" />

        <div className="services-categories" data-aos="fade-up" data-aos-duration={700}>
          {servicesData.categories.map((category) => (
            <div
              key={category.id}
              className={`category-item ${activeCategory.id === category.id ? "active" : ""}`}
              onClick={() => handleCategoryChange(category)}
            >
              <div className="category-icon" style={{ backgroundColor: category.color }}>
                <i className={category.icon}></i>
              </div>
              <h4>{category.name}</h4>
            </div>
          ))}
        </div>

        <div className="space40" />

        <div className="services-showcase-content">
          <div className="services-list">
            <h3>{activeCategory.name} Services</h3>
            <div className="space20" />
            <ul>
              {activeCategory.services.map((service) => (
                <li
                  key={service.id}
                  className={`service-list-item ${activeService.id === service.id ? "active" : ""}`}
                  onClick={() => handleServiceChange(service)}
                >
                  <h4>{service.name}</h4>
                  <i className="fa-solid fa-chevron-right"></i>
                </li>
              ))}
            </ul>
          </div>

          <div className={`service-details-wrapper ${isAnimating ? "animating" : ""} slide-${animationDirection}`}>
            <div className="service-image">
              <img src={activeService.image} alt={activeService.name} className="img-fluid rounded" />
              <div className="service-overlay">
                <h3>{activeService.name}</h3>
              </div>
            </div>
            
            <div className="service-info">
              <p>{activeService.description}</p>
              
              <div className="service-features">
                <h4>Key Features</h4>
                <ul>
                  {activeService.features.map((feature, index) => (
                    <li key={index}>
                      <i className="fa-solid fa-check"></i>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
              
              <Link href={activeService.link} className="thm-btn">
                <span>Learn More</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}