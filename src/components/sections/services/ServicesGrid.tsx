"use client";
import Link from 'next/link';
import { useState } from 'react';
import { getServiceImage, getServiceIcon } from '@/utils/serviceImages';

type Service = {
  id: string;
  pageBanner: {
    pageTitle: string;
    description: string;
  };
  about?: {
    image?: {
      src: string;
      alt?: string;
    };
  };
  features?: {
    features: Array<{
      title: string;
      text: string;
    }>;
  };
};

type ServicesGridProps = {
  services: Service[];
};

export default function ServicesGrid({ services }: ServicesGridProps) {
  const [hoveredService, setHoveredService] = useState<string | null>(null);

  // Create bento grid layout - different sizes for visual interest
  const getBentoClass = (index: number) => {
    const patterns = [
      'col-md-8 col-lg-8', // Large
      'col-md-4 col-lg-4', // Small
      'col-md-6 col-lg-6', // Medium
      'col-md-6 col-lg-6', // Medium
      'col-md-4 col-lg-4', // Small
      'col-md-8 col-lg-8', // Large
      'col-md-12 col-lg-12', // Full width
      'col-md-6 col-lg-6', // Medium
      'col-md-6 col-lg-6', // Medium
    ];
    return patterns[index % patterns.length];
  };

  const getCardHeight = (index: number) => {
    const heights = [320, 280, 300, 300, 280, 320, 250, 300, 300];
    return heights[index % heights.length];
  };

  return (
    <section className="services-bento-grid py-5">
      <div className="container-fluid px-4">
        <div className="text-center mb-5">
          <span className="subtitle text-primary fw-semibold" style={{letterSpacing:2}}>What We Do</span>
          <h1 className="display-4 fw-bold mb-3">Our Services</h1>
          <div className="decorative-line mx-auto mb-3" />
          <p className="lead text-muted max-w-3xl mx-auto">
            Explore our comprehensive range of innovative IT solutions designed to transform your business
            and drive digital excellence across all sectors.
          </p>
        </div>

        <div className="bento-grid row g-4">
          {services.map((service, index) => (
            <div className={getBentoClass(index)} key={service.id}>
              <div
                className={`service-bento-card h-100 position-relative overflow-hidden ${
                  hoveredService === service.id ? 'hovered' : ''
                }`}
                style={{ minHeight: getCardHeight(index) }}
                onMouseEnter={() => setHoveredService(service.id)}
                onMouseLeave={() => setHoveredService(null)}
              >
                {/* Background Image */}
                <div className="card-background">
                  <img
                    src={service.about?.image?.src || getServiceImage(service.id)}
                    alt={service.about?.image?.alt || service.pageBanner.pageTitle}
                    className="background-image"
                  />
                  <div className="background-overlay" />
                </div>

                {/* Content */}
                <div className="card-content position-absolute w-100 h-100 d-flex flex-column justify-content-between p-4">
                  <div className="service-header">
                    <div className="service-icon mb-3">
                      <span className="icon-emoji">{getServiceIcon(service.id)}</span>
                    </div>
                    <h3 className="service-title text-white fw-bold mb-2">
                      {service.pageBanner.pageTitle}
                    </h3>
                    <p className="service-description text-white-50 mb-3">
                      {service.pageBanner.description}
                    </p>
                  </div>

                  {/* Features Preview */}
                  <div className="service-features">
                    {service.features?.features && (
                      <div className="features-preview mb-3">
                        <div className="feature-tags d-flex flex-wrap gap-2">
                          {service.features.features.slice(0, 3).map((feature, idx) => (
                            <span key={idx} className="feature-tag">
                              {feature.title}
                            </span>
                          ))}
                          {service.features.features.length > 3 && (
                            <span className="feature-tag more">
                              +{service.features.features.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    <Link
                      href={`/service/${service.id}`}
                      className="service-cta-btn d-inline-flex align-items-center"
                    >
                      <span>Explore Service</span>
                      <i className="fas fa-arrow-right ms-2"></i>
                    </Link>
                  </div>
                </div>

                {/* Hover Effect Overlay */}
                <div className="hover-overlay position-absolute w-100 h-100 top-0 start-0" />
              </div>
            </div>
          ))}
        </div>
      </div>
      <style jsx>{`
        .services-bento-grid {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .decorative-line {
          width: 80px;
          height: 4px;
          background: linear-gradient(90deg, #007bff, #00c6ff);
          border-radius: 2px;
          margin-bottom: 1rem;
        }

        .max-w-3xl {
          max-width: 48rem;
        }

        .service-bento-card {
          border-radius: 20px;
          cursor: pointer;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }

        .service-bento-card:hover {
          transform: translateY(-8px) scale(1.02);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .card-background {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 1;
        }

        .background-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.4s ease;
        }

        .service-bento-card:hover .background-image {
          transform: scale(1.1);
        }

        .background-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            135deg,
            rgba(0, 123, 255, 0.8) 0%,
            rgba(0, 198, 255, 0.6) 50%,
            rgba(0, 0, 0, 0.7) 100%
          );
          transition: opacity 0.3s ease;
        }

        .service-bento-card:hover .background-overlay {
          opacity: 0.9;
        }

        .card-content {
          z-index: 2;
          transition: transform 0.3s ease;
        }

        .service-icon {
          display: inline-block;
        }

        .icon-emoji {
          font-size: 3rem;
          display: inline-block;
          transition: transform 0.3s ease;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        .service-bento-card:hover .icon-emoji {
          transform: scale(1.2) rotate(5deg);
        }

        .service-title {
          font-size: 1.5rem;
          line-height: 1.3;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .service-description {
          font-size: 0.95rem;
          line-height: 1.5;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .features-preview {
          opacity: 0;
          transform: translateY(20px);
          transition: all 0.3s ease 0.1s;
        }

        .service-bento-card:hover .features-preview {
          opacity: 1;
          transform: translateY(0);
        }

        .feature-tag {
          background: rgba(255, 255, 255, 0.2);
          color: white;
          padding: 0.25rem 0.75rem;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: 500;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          transition: all 0.2s ease;
        }

        .feature-tag:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(1.05);
        }

        .feature-tag.more {
          background: rgba(0, 123, 255, 0.3);
          border-color: rgba(0, 123, 255, 0.5);
        }

        .service-cta-btn {
          background: linear-gradient(45deg, #007bff, #00c6ff);
          color: white;
          padding: 0.75rem 1.5rem;
          border-radius: 25px;
          text-decoration: none;
          font-weight: 600;
          font-size: 0.9rem;
          transition: all 0.3s ease;
          box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .service-cta-btn:hover {
          background: linear-gradient(45deg, #0056b3, #0099cc);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
          color: white;
          text-decoration: none;
        }

        .service-cta-btn i {
          transition: transform 0.3s ease;
        }

        .service-cta-btn:hover i {
          transform: translateX(5px);
        }

        .hover-overlay {
          background: linear-gradient(
            45deg,
            rgba(0, 123, 255, 0.1) 0%,
            rgba(0, 198, 255, 0.1) 100%
          );
          opacity: 0;
          transition: opacity 0.3s ease;
          pointer-events: none;
        }

        .service-bento-card:hover .hover-overlay {
          opacity: 1;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .service-bento-card {
            min-height: 250px !important;
          }

          .service-title {
            font-size: 1.25rem;
          }

          .icon-emoji {
            font-size: 2.5rem;
          }
        }

        @media (max-width: 576px) {
          .bento-grid .col-md-8,
          .bento-grid .col-md-6,
          .bento-grid .col-md-4,
          .bento-grid .col-md-12 {
            flex: 0 0 100%;
            max-width: 100%;
          }
        }
      `}</style>
    </section>
  );
}