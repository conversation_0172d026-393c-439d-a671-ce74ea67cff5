import Link from "next/link";
import successStoriesData from "@/data/success-stories.json";

export default function Section3() {
    return (
        <>
            {/*=====PROJECT SUCCESS STORIES AREA START=======*/}
            <div className="service4 sp overflow-hidden" id="projects">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-8 m-auto text-center">
                            <div className="heading4-w">
                                <span className="span" data-aos="fade-up" data-aos-duration={700}>
                                    {successStoriesData.subtitle}
                                </span>
                                <h2 className="text-anime-style-3">{successStoriesData.title}</h2>
                                <div className="space16" />
                                <p data-aos="fade-up" data-aos-duration={1100}>
                                    {successStoriesData.description}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div className="space30" />
                    <div className="row">
                        {successStoriesData.stories.map((story, index) => (
                            <div 
                                key={story.id} 
                                className="col-lg-3 col-md-6" 
                                data-aos="zoom-in-up" 
                                data-aos-duration={700 + (index * 100)}
                            >
                                {index % 2 === 1 && <div className="space30" />}
                                <div className="service4-box">
                                    <div className="image overlay-anim">
                                        <img 
                                            src={`assets/mitc_images/${getProjectImage(story.category)}`} 
                                            alt={story.title} 
                                        />
                                    </div>
                                    <Link href="/success-stories" className="icon">
                                        <i className="fa-regular fa-arrow-right" />
                                    </Link>
                                    <div className="heading4-w">
                                        <h5>
                                            <Link href="/success-stories">{story.title}</Link>
                                        </h5>
                                        <div className="space8" />
                                        <span className="category-badge" style={{
                                            background: getCategoryColor(story.category),
                                            color: 'white',
                                            padding: '4px 8px',
                                            borderRadius: '12px',
                                            fontSize: '11px',
                                            fontWeight: '600'
                                        }}>
                                            {story.category}
                                        </span>
                                        <div className="space12" />
                                        <p style={{ 
                                            fontSize: '13px', 
                                            color: '#666', 
                                            lineHeight: '1.4',
                                            marginBottom: '10px'
                                        }}>
                                            {story.challenge.substring(0, 80)}...
                                        </p>
                                        <div className="results-preview">
                                            <small style={{ color: '#e89d1a', fontWeight: '600' }}>
                                                Key Result: {story.results[0]}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                    
                    <div className="space60" />
                    <div className="row">
                        <div className="col-lg-12 text-center">
                            <Link 
                                href="/success-stories" 
                                className="theme-btn1"
                                data-aos="fade-up" 
                                data-aos-duration={800}
                            >
                                View All Success Stories
                                <span>
                                    <i className="fa-solid fa-arrow-right" />
                                </span>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
            {/*=====PROJECT SUCCESS STORIES AREA END=======*/}
        </>
    );
}

// Helper function to get appropriate image based on category
function getProjectImage(category: string): string {
    switch (category) {
        case 'Smart City':
            return 'smartcity/2.jpg';
        case 'Data Centre Facility':
            return 'BoardroomSolution/1.jpg';
        case 'Broadcasting Innovation':
            return 'LiveStreaming/1.jpeg';
        case 'Training & Development':
            return 'Training/1.jpg';
        default:
            return 'Building.jpeg';
    }
}

// Helper function to get category colors
function getCategoryColor(category: string): string {
    switch (category) {
        case 'Smart City':
            return '#e89d1a';
        case 'Data Centre Facility':
            return '#03276e';
        case 'Broadcasting Innovation':
            return '#030376';
        case 'Training & Development':
            return '#6F6F87';
        default:
            return '#666';
    }
}