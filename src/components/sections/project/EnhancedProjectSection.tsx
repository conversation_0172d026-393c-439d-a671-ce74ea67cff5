"use client";
import { useState } from "react";
import Link from "next/link";
import successStoriesData from "@/data/success-stories.json";

export default function EnhancedProjectSection() {
    const [hoveredProject, setHoveredProject] = useState<number | null>(null);
    const [selectedCategory, setSelectedCategory] = useState<string>('All');

    const categories = ['All', ...Array.from(new Set(successStoriesData.stories.map(story => story.category)))];
    
    const filteredStories = selectedCategory === 'All' 
        ? successStoriesData.stories 
        : successStoriesData.stories.filter(story => story.category === selectedCategory);

    return (
        <>
            {/*=====ENHANCED PROJECT SUCCESS STORIES AREA START=======*/}
            <div className="enhanced-projects sp overflow-hidden" id="enhanced-projects">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-10 m-auto text-center">
                            <div className="heading1">
                                <span className="span" data-aos="fade-up" data-aos-duration={700}>
                                    {successStoriesData.subtitle}
                                </span>
                                <h2 className="text-anime-style-3">{successStoriesData.title}</h2>
                                <div className="space16" />
                                <p data-aos="fade-up" data-aos-duration={1100}>
                                    {successStoriesData.description}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Category Filter */}
                    <div className="space40" />
                    <div className="row">
                        <div className="col-lg-8 m-auto">
                            <div className="category-filter" data-aos="fade-up" data-aos-duration={800}>
                                <div className="filter-buttons" style={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    flexWrap: 'wrap',
                                    gap: '10px',
                                    marginBottom: '40px'
                                }}>
                                    {categories.map((category) => (
                                        <button
                                            key={category}
                                            onClick={() => setSelectedCategory(category)}
                                            className={`filter-btn ${selectedCategory === category ? 'active' : ''}`}
                                            style={{
                                                padding: '10px 20px',
                                                border: 'none',
                                                borderRadius: '25px',
                                                background: selectedCategory === category ? '#03276e' : 'transparent',
                                                color: selectedCategory === category ? 'white' : '#666',
                                                fontWeight: '600',
                                                fontSize: '14px',
                                                cursor: 'pointer',
                                                transition: 'all 0.3s ease',
                                                border: selectedCategory === category ? 'none' : '2px solid #e0e0e0'
                                            }}
                                        >
                                            {category}
                                        </button>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Projects Grid */}
                    <div className="row">
                        {filteredStories.map((story, index) => (
                            <div 
                                key={story.id} 
                                className="col-lg-6 col-md-6" 
                                data-aos="fade-up" 
                                data-aos-duration={700 + (index * 100)}
                            >
                                <div 
                                    className="enhanced-project-card"
                                    onMouseEnter={() => setHoveredProject(story.id)}
                                    onMouseLeave={() => setHoveredProject(null)}
                                    style={{
                                        background: 'white',
                                        borderRadius: '20px',
                                        overflow: 'hidden',
                                        boxShadow: hoveredProject === story.id 
                                            ? '0 20px 40px rgba(0,0,0,0.15)' 
                                            : '0 10px 30px rgba(0,0,0,0.1)',
                                        transform: hoveredProject === story.id ? 'translateY(-10px)' : 'translateY(0)',
                                        transition: 'all 0.4s ease',
                                        marginBottom: '30px',
                                        border: '1px solid #f0f0f0'
                                    }}
                                >
                                    {/* Project Image */}
                                    <div className="project-image" style={{ 
                                        position: 'relative', 
                                        height: '250px',
                                        overflow: 'hidden'
                                    }}>
                                        <img 
                                            src={`assets/mitc_images/${getProjectImage(story.category)}`}
                                            alt={story.title}
                                            style={{
                                                width: '100%',
                                                height: '100%',
                                                objectFit: 'cover',
                                                transform: hoveredProject === story.id ? 'scale(1.05)' : 'scale(1)',
                                                transition: 'transform 0.4s ease'
                                            }}
                                        />
                                        <div style={{
                                            position: 'absolute',
                                            top: '20px',
                                            right: '20px',
                                            background: getCategoryColor(story.category),
                                            color: 'white',
                                            padding: '6px 12px',
                                            borderRadius: '15px',
                                            fontSize: '12px',
                                            fontWeight: '600'
                                        }}>
                                            {story.category}
                                        </div>
                                        <div style={{
                                            position: 'absolute',
                                            bottom: 0,
                                            left: 0,
                                            right: 0,
                                            height: '50%',
                                            background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
                                            pointerEvents: 'none'
                                        }} />
                                    </div>

                                    {/* Project Content */}
                                    <div className="project-content" style={{ padding: '30px' }}>
                                        <h4 style={{ 
                                            color: '#03276e', 
                                            marginBottom: '15px',
                                            fontSize: '20px',
                                            fontWeight: '700'
                                        }}>
                                            {story.title}
                                        </h4>
                                        
                                        <p style={{ 
                                            color: '#666', 
                                            lineHeight: '1.6',
                                            marginBottom: '20px',
                                            fontSize: '14px'
                                        }}>
                                            <strong>Challenge:</strong> {story.challenge}
                                        </p>

                                        {/* Key Results */}
                                        <div className="key-results" style={{ marginBottom: '25px' }}>
                                            <h6 style={{ 
                                                color: '#03276e', 
                                                marginBottom: '10px',
                                                fontSize: '14px',
                                                fontWeight: '600'
                                            }}>
                                                Key Results:
                                            </h6>
                                            <ul style={{ 
                                                listStyle: 'none', 
                                                padding: 0,
                                                margin: 0
                                            }}>
                                                {story.results.slice(0, 2).map((result, idx) => (
                                                    <li key={idx} style={{
                                                        display: 'flex',
                                                        alignItems: 'flex-start',
                                                        marginBottom: '8px',
                                                        fontSize: '13px',
                                                        color: '#555'
                                                    }}>
                                                        <span style={{ 
                                                            color: '#e89d1a', 
                                                            marginRight: '8px',
                                                            fontSize: '12px'
                                                        }}>
                                                            ✓
                                                        </span>
                                                        {result}
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>

                                        {/* Testimonial Preview */}
                                        <div className="testimonial-preview" style={{
                                            background: '#f8f9fa',
                                            padding: '15px',
                                            borderRadius: '10px',
                                            borderLeft: `4px solid ${getCategoryColor(story.category)}`,
                                            marginBottom: '20px'
                                        }}>
                                            <p style={{ 
                                                fontSize: '12px', 
                                                color: '#666',
                                                fontStyle: 'italic',
                                                margin: 0,
                                                lineHeight: '1.4'
                                            }}>
                                                "{story.testimonial.quote.substring(0, 100)}..."
                                            </p>
                                            <small style={{ 
                                                color: '#999',
                                                fontSize: '11px',
                                                marginTop: '5px',
                                                display: 'block'
                                            }}>
                                                - {story.testimonial.author}
                                            </small>
                                        </div>

                                        {/* Action Button */}
                                        <Link 
                                            href="/success-stories"
                                            className="project-btn"
                                            style={{
                                                display: 'inline-flex',
                                                alignItems: 'center',
                                                padding: '10px 20px',
                                                background: hoveredProject === story.id ? '#03276e' : 'transparent',
                                                color: hoveredProject === story.id ? 'white' : '#03276e',
                                                border: `2px solid #03276e`,
                                                borderRadius: '25px',
                                                textDecoration: 'none',
                                                fontSize: '14px',
                                                fontWeight: '600',
                                                transition: 'all 0.3s ease'
                                            }}
                                        >
                                            View Case Study
                                            <i className="fa-solid fa-arrow-right" style={{ marginLeft: '8px' }} />
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                    
                    <div className="space60" />
                    <div className="row">
                        <div className="col-lg-12 text-center">
                            <Link 
                                href="/success-stories" 
                                className="theme-btn1"
                                data-aos="fade-up" 
                                data-aos-duration={800}
                            >
                                View All Success Stories
                                <span>
                                    <i className="fa-solid fa-arrow-right" />
                                </span>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
            {/*=====ENHANCED PROJECT SUCCESS STORIES AREA END=======*/}
        </>
    );
}

// Helper function to get appropriate image based on category
function getProjectImage(category: string): string {
    switch (category) {
        case 'Smart City':
            return 'smartcity/2.jpg';
        case 'Data Centre Facility':
            return 'BoardroomSolution/1.jpg';
        case 'Broadcasting Innovation':
            return 'LiveStreaming/1.jpeg';
        case 'Training & Development':
            return 'Training/1.jpg';
        default:
            return 'Building.jpeg';
    }
}

// Helper function to get category colors
function getCategoryColor(category: string): string {
    switch (category) {
        case 'Smart City':
            return '#e89d1a';
        case 'Data Centre Facility':
            return '#03276e';
        case 'Broadcasting Innovation':
            return '#030376';
        case 'Training & Development':
            return '#6F6F87';
        default:
            return '#666';
    }
}