"use client";
import { useState } from "react";

// Server rack data
const serverRacks = [
  { 
    id: 1, 
    name: "Fusion Module 2000", 
    description: "Next-generation modular data center solution with integrated cooling and power management.",
    specs: [
      { label: "Processing Power", value: "128 CPU cores" },
      { label: "Memory", value: "512GB RAM" },
      { label: "Storage", value: "24TB SSD" },
      { label: "Network", value: "100Gbps" }
    ]
  },
  { 
    id: 2, 
    name: "NetCol5000", 
    description: "Advanced cooling system designed for high-density server environments with minimal energy consumption.",
    specs: [
      { label: "Cooling Capacity", value: "30kW per rack" },
      { label: "Energy Efficiency", value: "PUE < 1.2" },
      { label: "Temperature Control", value: "±0.5°C" },
      { label: "Noise Level", value: "65dB" }
    ]
  },
  { 
    id: 3, 
    name: "UPS5000", 
    description: "Uninterruptible power supply system providing clean, reliable power with N+1 redundancy.",
    specs: [
      { label: "Capacity", value: "500kVA" },
      { label: "Efficiency", value: "96.5%" },
      { label: "Backup Time", value: "30 minutes" },
      { label: "Response Time", value: "<2ms" }
    ]
  },
  { 
    id: 4, 
    name: "Storage Array", 
    description: "High-performance storage solution with multi-tier architecture for optimal data access and protection.",
    specs: [
      { label: "Raw Capacity", value: "1.2PB" },
      { label: "IOPS", value: "1.5M" },
      { label: "Latency", value: "<1ms" },
      { label: "Data Protection", value: "RAID 6" }
    ]
  },
  { 
    id: 5, 
    name: "Network Switch", 
    description: "Enterprise-grade network infrastructure with redundant paths and advanced traffic management.",
    specs: [
      { label: "Throughput", value: "12.8Tbps" },
      { label: "Ports", value: "48x 100GbE" },
      { label: "Latency", value: "300ns" },
      { label: "Redundancy", value: "2N" }
    ]
  },
  { 
    id: 6, 
    name: "Backup System", 
    description: "Comprehensive backup solution with rapid recovery capabilities and off-site replication.",
    specs: [
      { label: "Backup Speed", value: "12TB/hour" },
      { label: "Recovery Time", value: "<15 minutes" },
      { label: "Retention Policy", value: "90 days" },
      { label: "Encryption", value: "AES-256" }
    ]
  }
];

export default function AnimatedDataCenter() {
  const [activeRack, setActiveRack] = useState<number | null>(null);
  
  const handleRackClick = (rackId: number) => {
    setActiveRack(activeRack === rackId ? null : rackId);
  };
  
  return (
    <section className="animated-data-center sp" id="data-center">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 m-auto text-center">
            <div className="heading1">
              <span className="span" data-aos="zoom-in-left" data-aos-duration={700}>
                State-of-the-Art Infrastructure
              </span>
              <h2 className="text-anime-style-3">Data Centre Facility</h2>
              <div className="space16" />
              <p data-aos="fade-up" data-aos-duration={800}>
                Our cutting-edge data center provides secure, efficient, and cost-effective environments
                for your mission-critical systems and data with Fusion Module 2000 implementation.
              </p>
            </div>
          </div>
        </div>

        <div className="space60" />

        <div className="data-center-visualization">
          <div className="data-center-floor">
            <div className="grid-lines"></div>
            
            {/* Server Racks */}
            <div className="server-racks-row front-row">
              {serverRacks.slice(0, 3).map((rack) => (
                <div 
                  key={rack.id}
                  className={`server-rack ${activeRack === rack.id ? 'active' : ''}`}
                  onClick={() => handleRackClick(rack.id)}
                >
                  <div className="rack-body">
                    <div className="rack-front">
                      {Array.from({ length: 8 }).map((_, i) => (
                        <div 
                          key={i} 
                          className={`server-unit ${activeRack === rack.id && i % 3 === 0 ? 'active' : ''}`}
                        >
                          {activeRack === rack.id && i % 3 === 0 && (
                            <div className="server-led"></div>
                          )}
                        </div>
                      ))}
                    </div>
                    <div className="rack-top"></div>
                    <div className="rack-side"></div>
                  </div>
                  <div className="rack-label">{rack.name}</div>
                  
                  {activeRack === rack.id && (
                    <div className="rack-details">
                      <h3>{rack.name}</h3>
                      <p>{rack.description}</p>
                      <div className="specs-grid">
                        {rack.specs.map((spec, index) => (
                          <div key={index} className="spec-item">
                            <div className="spec-label">{spec.label}</div>
                            <div className="spec-value">{spec.value}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
            
            <div className="server-racks-row back-row">
              {serverRacks.slice(3).map((rack) => (
                <div 
                  key={rack.id}
                  className={`server-rack ${activeRack === rack.id ? 'active' : ''}`}
                  onClick={() => handleRackClick(rack.id)}
                >
                  <div className="rack-body">
                    <div className="rack-front">
                      {Array.from({ length: 8 }).map((_, i) => (
                        <div 
                          key={i} 
                          className={`server-unit ${activeRack === rack.id && i % 3 === 0 ? 'active' : ''}`}
                        >
                          {activeRack === rack.id && i % 3 === 0 && (
                            <div className="server-led"></div>
                          )}
                        </div>
                      ))}
                    </div>
                    <div className="rack-top"></div>
                    <div className="rack-side"></div>
                  </div>
                  <div className="rack-label">{rack.name}</div>
                  
                  {activeRack === rack.id && (
                    <div className="rack-details">
                      <h3>{rack.name}</h3>
                      <p>{rack.description}</p>
                      <div className="specs-grid">
                        {rack.specs.map((spec, index) => (
                          <div key={index} className="spec-item">
                            <div className="spec-label">{spec.label}</div>
                            <div className="spec-value">{spec.value}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
            
            {/* Animated elements */}
            <div className="data-packets">
              {Array.from({ length: 10 }).map((_, i) => (
                <div key={i} className={`data-packet packet-${i + 1}`}></div>
              ))}
            </div>
            
            <div className="cooling-indicators">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className={`cooling-indicator indicator-${i + 1}`}>
                  <div className="cooling-wave"></div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="data-center-instructions">
            <p>Click on a server rack to view details</p>
          </div>
        </div>
        
        <div className="space60" />
        
        <div className="row">
          <div className="col-lg-8 m-auto text-center">
            <div className="heading1">
              <h3>Key Benefits</h3>
              <div className="space16" />
              <div className="benefits-grid">
                <div className="benefit-item" data-aos="fade-up" data-aos-delay="100">
                  <div className="benefit-icon">
                    <i className="fa-solid fa-bolt"></i>
                  </div>
                  <h4>Energy Efficient</h4>
                  <p>42% reduction in energy consumption with our advanced cooling systems</p>
                </div>
                
                <div className="benefit-item" data-aos="fade-up" data-aos-delay="200">
                  <div className="benefit-icon">
                    <i className="fa-solid fa-shield-alt"></i>
                  </div>
                  <h4>Highly Secure</h4>
                  <p>Multi-layered physical and digital security protocols</p>
                </div>
                
                <div className="benefit-item" data-aos="fade-up" data-aos-delay="300">
                  <div className="benefit-icon">
                    <i className="fa-solid fa-chart-line"></i>
                  </div>
                  <h4>Scalable</h4>
                  <p>Easily expand capacity as your business grows</p>
                </div>
                
                <div className="benefit-item" data-aos="fade-up" data-aos-delay="400">
                  <div className="benefit-icon">
                    <i className="fa-solid fa-clock"></i>
                  </div>
                  <h4>99.999% Uptime</h4>
                  <p>Redundant systems ensure continuous operation</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}