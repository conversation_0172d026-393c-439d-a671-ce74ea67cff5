"use client";

import React from 'react';
import appData from '@/data/app.json';

const StructuredData: React.FC = () => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Motshwanelo IT Consulting",
    "alternateName": "MITC",
    "url": "https://motshwaneloitconsulting.co.za",
    "logo": "https://motshwaneloitconsulting.co.za/img/ui/logo_full.svg",
    "description": "Leading IT Solutions Provider in South Africa - Specializing in Smart City Solutions, Data Centre Infrastructure, Software Development & Digital Transformation",
    "foundingDate": "2007",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Unit 16 Glen Eagle Office Park, Glen Marias, Monument Road",
      "addressLocality": "Kempton Park",
      "addressRegion": "Gauteng",
      "postalCode": "1619",
      "addressCountry": "ZA"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+27-11-492-0992",
      "contactType": "customer service",
      "areaServed": "ZA",
      "availableLanguage": ["English", "Afrikaans"]
    },
    "sameAs": [
      "https://linkedin.com/in/motshwanelo-it-consulting-0b33331b3/",
      "https://www.facebook.com/p/Motshwanelo_IT_Consulting-100071950546728/",
      "https://twitter.com/motshwaneloit"
    ],
    "founder": {
      "@type": "Person",
      "name": "Motshwanelo Taunyane"
    },
    "numberOfEmployees": {
      "@type": "QuantitativeValue",
      "value": 25
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "IT Services and Solutions",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Smart City Solutions",
            "description": "Advanced IVS networks and intelligent traffic systems for safer communities"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Data Centre Infrastructure",
            "description": "Enterprise-grade data infrastructure with 99.999% uptime guarantee"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Software Development",
            "description": "Custom software solutions and mobile applications"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "IT Consulting",
            "description": "Strategic technology consulting and digital transformation services"
          }
        }
      ]
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "127",
      "bestRating": "5"
    },
    "priceRange": "$$"
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
};

export default StructuredData;