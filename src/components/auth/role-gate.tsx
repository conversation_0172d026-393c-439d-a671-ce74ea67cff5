"use client"

import { useSession } from "next-auth/react"
import { UserRole } from "../../generated/prisma"

interface RoleGateProps {
  children: React.ReactNode
  allowedRoles: UserRole[]
  fallback?: React.ReactNode
}

export function RoleGate({ children, allowedRoles, fallback }: RoleGateProps) {
  const { data: session } = useSession()
  const userRole = session?.user?.role

  if (!userRole || !allowedRoles.includes(userRole)) {
    return fallback || null
  }

  return <>{children}</>
}

interface AdminGateProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function AdminGate({ children, fallback }: AdminGateProps) {
  return (
    <RoleGate allowedRoles={[UserRole.ADMIN]} fallback={fallback}>
      {children}
    </RoleGate>
  )
}

interface UserGateProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function UserGate({ children, fallback }: UserGateProps) {
  return (
    <RoleGate allowedRoles={[UserRole.USER, UserRole.ADMIN]} fallback={fallback}>
      {children}
    </RoleGate>
  )
}