"use client"

import { useSession } from "next-auth/react"
import { useRout<PERSON> } from "next/navigation"
import { useEffect } from "react"
import { Loader2, <PERSON>, AlertTriangle } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface AdminProtectionClientProps {
  children: React.ReactNode
}

export function AdminProtectionClient({ children }: AdminProtectionClientProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    console.log("AdminProtectionClient - status:", status)
    console.log("AdminProtectionClient - session:", session)
    
    if (status === "loading") return // Still loading

    // Add a small delay to ensure session is fully established
    const timer = setTimeout(() => {
      if (!session) {
        console.log("AdminProtectionClient - No session after delay, redirecting to signin")
        router.push("/auth/signin?callbackUrl=/admin")
        return
      }

      if (session.user.role !== "ADMIN") {
        console.log("AdminProtectionClient - Not admin, redirecting to home")
        router.push("/")
        return
      }
      
      console.log("AdminProtectionClient - User is admin, allowing access")
    }, 200)

    return () => clearTimeout(timer)
  }, [session, status, router])

  // Loading state
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
            <CardTitle>Loading...</CardTitle>
            <CardDescription>
              Verifying your access permissions
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  // Not authenticated
  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Shield className="h-8 w-8 text-muted-foreground" />
            </div>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              You need to sign in to access the admin panel
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => router.push("/auth/signin?callbackUrl=/admin")}
              className="w-full"
            >
              Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Not admin
  if (session.user.role !== "ADMIN") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <AlertTriangle className="h-8 w-8 text-destructive" />
            </div>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don't have permission to access the admin panel
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Admin privileges are required to access this area.
              </AlertDescription>
            </Alert>
            <div className="flex flex-col gap-2">
              <Button 
                onClick={() => router.push("/")}
                className="w-full"
              >
                Go to Homepage
              </Button>
              <Button 
                variant="outline"
                onClick={() => router.back()}
                className="w-full"
              >
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // User is authenticated and is admin
  return <>{children}</>
}