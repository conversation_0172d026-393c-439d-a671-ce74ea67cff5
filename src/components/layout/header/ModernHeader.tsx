"use client";

import Link from "next/link";
import MobileMenu from "../MobileMenu";
import MainMenu from "../MainMenu";
import MainMenuOnePage1 from "../MainMenuOnePage1";

export default function Header({
  scroll,
  isMobileMenu,
  handleMobileMenu,
  mainMenuStyle,
}: any) {
  return (
    <>
      <header>
        <div
          className={`header-area header-area1 header-area-all d-none d-lg-block ${
            scroll ? "sticky" : ""
          } `}
          id="header"
        >
          <div className="container">
            <div className="row">
              <div className="col-12">
                <div className="header-elements">
                  <div className="site-logo">
                    <Link href="/" className="flex items-center">
                      <img
                        src="assets/img/logo/logo_full.svg"
                        alt="Motshwanelo IT Consulting"
                        className="h-12 w-auto transition-all duration-300"
                      />
                    </Link>
                  </div>
                  <div className="main-menu-ex main-menu-ex1">
                    {!mainMenuStyle && <MainMenu />}
                    {mainMenuStyle == "one-page" ? <MainMenuOnePage1 /> : null}
                  </div>
                  <div className="header2-buttons">
                    <div className="flex items-center gap-3">
                      <Link className="theme-btn1" href="/contact">
                        Start Digital Transformation
                        <span>
                          <i className="fa-solid fa-arrow-right" />
                        </span>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <MobileMenu
          isMobileMenu={isMobileMenu}
          handleMobileMenu={handleMobileMenu}
        />
      </header>
      <div className="mobile-header mobile-header-main d-block d-lg-none">
        <div className="container-fluid">
          <div className="col-12">
            <div className="mobile-header-elements">
              <div className="mobile-logo">
                <Link href="/" className="flex items-center">
                  <img
                    src="assets/img/logo/logo_full.svg"
                    alt="Motshwanelo IT Consulting"
                    className="h-10 w-auto transition-all duration-300"
                  />
                </Link>
              </div>
              <div className="flex items-center gap-3">
                <button
                  className="mobile-nav-icon flex items-center justify-center"
                  onClick={handleMobileMenu}
                  aria-label="Toggle mobile menu"
                >
                  <i className={`fa-duotone ${isMobileMenu ? 'fa-xmark' : 'fa-bars-staggered'} transition-all duration-300`} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
