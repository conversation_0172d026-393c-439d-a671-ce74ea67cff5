"use client"

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Home, FileText, PenSquare, Image, Settings, PlusCircle, Edit, Eye } from "lucide-react"
import { Breadcrumbs } from "./site-header/breadcrumbs"
import { CommandPalette } from "./site-header/command-palette"
import { Notifications } from "./site-header/notifications"
import { UserNav } from "./site-header/user-nav"
import { useAdminHeader } from '@/hooks/use-admin-header'
import { useSiteHeaderEditor, ComponentPosition } from '@/hooks/use-site-header-editor'
import { Toggle } from "@/components/ui/toggle"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { HeaderEditor } from "./site-header/header-editor"

const navItems = [
  { name: 'Dashboard', href: '/admin', icon: Home },
  { name: 'Pages', href: '/admin/pages', icon: FileText },
  { name: 'Blog', href: '/admin/blog', icon: PenSquare },
  { name: 'Media', href: '/admin/media', icon: Image },
  { name: 'Settings', href: '/admin/settings', icon: Settings },
]

export function SiteHeader() {
  const pathname = usePathname()
  const adminHeader = useAdminHeader()
  const {
    isVisible,
    title,
    actions,
    components,
    containerStyle,
    isEditMode,
    toggleEditMode
  } = useSiteHeaderEditor()

  const isAdminPage = pathname.startsWith('/admin')

  // For backward compatibility with existing code
  // This ensures the component works with both the old and new store
  const effectiveIsVisible = adminHeader.isVisible && isVisible
  const effectiveTitle = title || adminHeader.title
  const effectiveActions = actions.length ? actions : adminHeader.actions

  if (!effectiveIsVisible || !isAdminPage) {
    return null
  }

  // Apply container styles
  const headerStyle = {
    backgroundColor: containerStyle.backgroundColor,
    borderColor: containerStyle.borderColor,
    borderWidth: containerStyle.borderWidth,
    boxShadow: containerStyle.boxShadow,
    height: containerStyle.height,
    padding: containerStyle.padding,
    margin: containerStyle.margin,
  }

  // Group components by position
  const leftComponents = components.filter(comp => comp.position === 'left')
  const centerComponents = components.filter(comp => comp.position === 'center')
  const rightComponents = components.filter(comp => comp.position === 'right')

  return (
    <>
      <header 
        className={cn(
          "flex flex-col border-b relative", 
          containerStyle.customClasses
        )}
        style={headerStyle}
      >
        {/* Edit Mode Toggle */}
        {isAdminPage && (
          <div className="absolute top-1 right-1 z-50">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Toggle 
                    variant="outline" 
                    size="sm" 
                    pressed={isEditMode} 
                    onClick={toggleEditMode}
                  >
                    {isEditMode ? <Eye size={14} /> : <Edit size={14} />}
                  </Toggle>
                </TooltipTrigger>
                <TooltipContent>
                  {isEditMode ? "Exit Edit Mode" : "Edit Header"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}

        {/* Top Bar */}
        <div className="flex h-14 items-center gap-2 px-4">
          {/* Left Section */}
          <div className="flex items-center gap-2">
            <SidebarTrigger className="-ml-1" />
            <div className="flex items-center">
              {effectiveTitle ? (
                <h1 className="text-xl font-semibold tracking-tight">{effectiveTitle}</h1>
              ) : (
                <Breadcrumbs />
              )}
            </div>
            {leftComponents.map(comp => (
              <div key={comp.id}>{comp.component}</div>
            ))}
          </div>

          {/* Center Section */}
          {centerComponents.length > 0 && (
            <div className="flex-1 flex justify-center items-center gap-2">
              {centerComponents.map(comp => (
                <div key={comp.id}>{comp.component}</div>
              ))}
            </div>
          )}

          {/* Right Section */}
          <div className="ml-auto flex items-center gap-2">
            {rightComponents.map(comp => (
              <div key={comp.id}>{comp.component}</div>
            ))}
            {effectiveActions}
            <CommandPalette />
            <Notifications />
            <UserNav />
          </div>
        </div>
      </header>

      {/* Header Editor 
      {isEditMode && <HeaderEditor />}*/}
    </>
  )
}
