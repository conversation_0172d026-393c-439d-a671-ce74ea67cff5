import * as React from "react";

interface LogoIconProps extends React.SVGProps<SVGSVGElement> {
  primaryColor?: string;
  secondaryColor?: string;
  size?: number | string;
  className?: string;
}

const defaultLogoIcon: React.FC<LogoIconProps> = ({
  primaryColor = "#f19d09",
  secondaryColor = "#010e5d",
  size = 24,
  className = "",
  ...props
}) => {
  return (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 728.926 590.707" 
      className={`logo-icon ${className}`}
      {...props}
    >
      <path
        d="M328.11 564.796c-40.174-1.39-79.569-20.622-116.179-56.719-15.915-15.693-30.111-33.034-42.545-51.972-4.11-6.26-9.31-14.812-8.89-14.62.088.04 1.938.973 4.11 2.072C186.72 454.75 216.73 461.564 254.623 464c10.016.643 13.563.758 27.13.874 27.358.235 51.85-1.088 81.6-4.406 10.607-1.183 27.368-3.44 29.567-3.981.258-.063.468-.071.468-.017 0 .053-13.597 24.457-30.216 54.229l-30.217 54.131-1.538.014a93.794 93.794 0 01-3.306-.048z"
        fill={primaryColor}
      />
      <path
        d="M364.887 590.233c-1.063-2.334 5.389-19.471 18.212-48.374 12.917-29.114 34.745-75.594 57.065-121.51 7.988-16.434 8.727-17.983 8.58-17.983-.073 0-4.128.794-9.011 1.765-47.462 9.434-92.177 15.303-133.035 17.463-11.945.631-16.186.754-30.04.871-20.731.175-35.368-.35-53.53-1.923-69.505-6.018-126.18-26.264-165.791-59.225-6.672-5.551-15.498-14.079-20.56-19.862-16.015-18.301-26.416-36.916-31.592-56.545-2.734-10.368-3.97-19.613-4.943-37.004-.322-5.754-.323-22.825 0-28.065 1.033-16.852 3.158-30.534 6.8-43.795C21.919 121.875 60.04 86.58 119.497 71.929c15.196-3.744 30.252-5.884 49.27-7.001 5.736-.337 25.266-.336 31.183.001 12.195.696 27.3 2.242 31.836 3.259.346.077.996-.416 2.91-2.212C275.23 27.972 317.489 6.151 360.44 1.046 368.14.133 370.64 0 380.292 0c9.7 0 12.517.147 20.114 1.051 40.176 4.782 80.737 24.267 115.94 55.695 3.967 3.542 11.446 10.864 14.177 13.88l1.644 1.815 1.76-.196c2.959-.329 12.032-1.018 18.392-1.397 8.706-.519 31.404-.515 39.187.006 12.249.821 21.851 1.989 31.974 3.888 39.902 7.486 70.025 25.062 87.283 50.926 3.485 5.223 6.813 11.568 9.225 17.588.729 1.818 1.864 4.24 2.523 5.384 1.33 2.307 1.507 2.892 2.542 8.42a240.238 240.238 0 013.66 30.132c.283 4.917.284 18.035.003 22.82-1.985 33.667-11.632 64.477-29.366 93.792-21.812 36.057-57.023 69.452-98.173 93.112-4.308 2.478-12.581 6.825-12.581 6.612 0-.067 1.373-2.25 3.05-4.851 29.076-45.085 47.068-84.723 53.574-118.029 5.716-29.262 2.57-53.9-9.35-73.22-15.409-24.976-45.699-41.353-88.128-47.652-12.695-1.885-22.558-2.577-37.037-2.598l-10.432-.016-5.15-5.166c-29.036-29.132-58.6-46.325-89.758-52.2-8.056-1.519-14.196-2.071-23.098-2.077-7.133-.005-9.916.156-16.11.933-27.006 3.388-54.31 15.579-79.798 35.628-8.701 6.845-17.863 15.386-24.414 22.76l-.994 1.12-3.787-.447c-27.113-3.195-49.961-1.66-69.064 4.64-9.24 3.047-18.132 7.584-25.362 12.94-17.215 12.752-29.223 32.239-32.83 53.28-.944 5.502-1.056 7.04-1.054 14.552 0 5.74.077 7.753.38 10.082 1.374 10.524 4.117 19.737 8.578 28.81 4.578 9.31 9.773 16.67 16.917 23.963 10.25 10.466 22.063 17.903 35.866 22.58 4.32 1.463 7.94 2.405 13.929 3.621 34.845 7.077 80.119 8.919 135.75 5.522 55.03-3.36 121.63-12.312 190.305-25.579 17.021-3.288 38.347-7.741 51.258-10.703 3.2-.734 5.935-1.335 6.078-1.335.305 0 .228 1.097-.386 5.509-3.369 24.223-20.74 71.973-51.012 140.22-16.217 36.559-36.716 80.196-56.02 119.25l-5.972 12.083-6.838 1.68c-32.918 8.092-58.092 11.894-78.663 11.88-2.687-.002-7.411-.141-10.498-.308-8.1-.438-10.415-.4-12.832.212-.474.12-.606.058-.805-.379z"
        fill={secondaryColor}
      />
    </svg>
  );
};

const LogoIcon = React.memo(defaultLogoIcon);
export default LogoIcon;
