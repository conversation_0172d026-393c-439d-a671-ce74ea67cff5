'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { useToast } from '@/hooks/use-toast'
import { 
  Share2, 
  Facebook, 
  Twitter, 
  Linkedin, 
  Mail, 
  MessageCircle,
  Copy,
  Link,
  Send,
  Bookmark,
  Heart,
  MoreHorizontal
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ShareButtonsProps {
  url: string
  title: string
  description?: string
  image?: string
  hashtags?: string[]
  via?: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'minimal' | 'floating'
  showLabels?: boolean
  showCounts?: boolean
  platforms?: SharePlatform[]
}

type SharePlatform = 
  | 'facebook' 
  | 'twitter' 
  | 'linkedin' 
  | 'whatsapp' 
  | 'telegram'
  | 'email' 
  | 'copy'
  | 'native'

const DEFAULT_PLATFORMS: SharePlatform[] = [
  'facebook',
  'twitter',
  'linkedin',
  'whatsapp',
  'email',
  'copy'
]

export function ShareButtons({
  url,
  title,
  description,
  image,
  hashtags = [],
  via,
  className,
  size = 'md',
  variant = 'default',
  showLabels = true,
  showCounts = false,
  platforms = DEFAULT_PLATFORMS,
}: ShareButtonsProps) {
  const { toast } = useToast()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [shareCounts, setShareCounts] = useState<Record<string, number>>({})

  const absoluteUrl = url.startsWith('http') ? url : `${window.location.origin}${url}`

  const shareData = {
    title,
    text: description || title,
    url: absoluteUrl,
  }

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share(shareData)
        toast({
          title: 'Shared successfully!',
          description: 'Content has been shared.',
        })
      } catch (error) {
        if ((error as Error).name !== 'AbortError') {
          console.error('Error sharing:', error)
        }
      }
    } else {
      setIsDialogOpen(true)
    }
  }

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(absoluteUrl)
      toast({
        title: 'Link copied!',
        description: 'The link has been copied to your clipboard.',
      })
    } catch (error) {
      console.error('Error copying link:', error)
      toast({
        title: 'Copy failed',
        description: 'Unable to copy link to clipboard.',
        variant: 'destructive',
      })
    }
  }

  const getShareUrl = (platform: SharePlatform): string => {
    const encodedUrl = encodeURIComponent(absoluteUrl)
    const encodedTitle = encodeURIComponent(title)
    const encodedDescription = encodeURIComponent(description || title)
    const encodedHashtags = hashtags.map(tag => encodeURIComponent(tag)).join(',')

    switch (platform) {
      case 'facebook':
        return `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`
      
      case 'twitter':
        const twitterParams = new URLSearchParams({
          url: absoluteUrl,
          text: title,
          ...(hashtags.length > 0 && { hashtags: hashtags.join(',') }),
          ...(via && { via }),
        })
        return `https://twitter.com/intent/tweet?${twitterParams.toString()}`
      
      case 'linkedin':
        return `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`
      
      case 'whatsapp':
        return `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`
      
      case 'telegram':
        return `https://t.me/share/url?url=${encodedUrl}&text=${encodedTitle}`
      
      case 'email':
        const emailParams = new URLSearchParams({
          subject: title,
          body: `${description || title}\n\n${absoluteUrl}`,
        })
        return `mailto:?${emailParams.toString()}`
      
      default:
        return absoluteUrl
    }
  }

  const handleShare = (platform: SharePlatform) => {
    if (platform === 'copy') {
      handleCopyLink()
      return
    }

    if (platform === 'native') {
      handleNativeShare()
      return
    }

    const shareUrl = getShareUrl(platform)
    
    // Open in popup for social platforms
    if (['facebook', 'twitter', 'linkedin'].includes(platform)) {
      const popup = window.open(
        shareUrl,
        'share',
        'width=600,height=400,scrollbars=yes,resizable=yes'
      )
      
      // Focus the popup
      popup?.focus()
    } else {
      // Open in same tab for email, WhatsApp, etc.
      window.open(shareUrl, '_blank')
    }
  }

  const getPlatformIcon = (platform: SharePlatform) => {
    switch (platform) {
      case 'facebook':
        return <Facebook className="h-4 w-4" />
      case 'twitter':
        return <Twitter className="h-4 w-4" />
      case 'linkedin':
        return <Linkedin className="h-4 w-4" />
      case 'whatsapp':
        return <MessageCircle className="h-4 w-4" />
      case 'telegram':
        return <Send className="h-4 w-4" />
      case 'email':
        return <Mail className="h-4 w-4" />
      case 'copy':
        return <Copy className="h-4 w-4" />
      case 'native':
        return <Share2 className="h-4 w-4" />
      default:
        return <Share2 className="h-4 w-4" />
    }
  }

  const getPlatformLabel = (platform: SharePlatform) => {
    switch (platform) {
      case 'facebook':
        return 'Facebook'
      case 'twitter':
        return 'Twitter'
      case 'linkedin':
        return 'LinkedIn'
      case 'whatsapp':
        return 'WhatsApp'
      case 'telegram':
        return 'Telegram'
      case 'email':
        return 'Email'
      case 'copy':
        return 'Copy Link'
      case 'native':
        return 'Share'
      default:
        return 'Share'
    }
  }

  const getPlatformColor = (platform: SharePlatform) => {
    switch (platform) {
      case 'facebook':
        return 'hover:bg-blue-600 hover:text-white'
      case 'twitter':
        return 'hover:bg-sky-500 hover:text-white'
      case 'linkedin':
        return 'hover:bg-blue-700 hover:text-white'
      case 'whatsapp':
        return 'hover:bg-green-600 hover:text-white'
      case 'telegram':
        return 'hover:bg-blue-500 hover:text-white'
      case 'email':
        return 'hover:bg-gray-600 hover:text-white'
      case 'copy':
        return 'hover:bg-gray-600 hover:text-white'
      default:
        return 'hover:bg-primary hover:text-primary-foreground'
    }
  }

  if (variant === 'floating') {
    return (
      <div className={cn(
        "fixed right-4 top-1/2 transform -translate-y-1/2 z-50",
        className
      )}>
        <Card className="w-12">
          <CardContent className="p-2 space-y-2">
            {platforms.slice(0, 4).map((platform) => (
              <Button
                key={platform}
                variant="ghost"
                size="sm"
                className={cn(
                  "w-8 h-8 p-0",
                  getPlatformColor(platform)
                )}
                onClick={() => handleShare(platform)}
              >
                {getPlatformIcon(platform)}
              </Button>
            ))}
            {platforms.length > 4 && (
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Share this content</DialogTitle>
                  </DialogHeader>
                  <ShareDialog
                    platforms={platforms}
                    onShare={handleShare}
                    url={absoluteUrl}
                    title={title}
                  />
                </DialogContent>
              </Dialog>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  if (variant === 'minimal') {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <span className="text-sm text-muted-foreground">Share:</span>
        {platforms.map((platform) => (
          <Button
            key={platform}
            variant="ghost"
            size="sm"
            className={cn(
              "h-8 w-8 p-0",
              getPlatformColor(platform)
            )}
            onClick={() => handleShare(platform)}
          >
            {getPlatformIcon(platform)}
          </Button>
        ))}
      </div>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Share2 className="h-5 w-5" />
          <span>Share this content</span>
        </CardTitle>
        <CardDescription>
          Help others discover this content by sharing it on social media
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          {platforms.map((platform) => (
            <Button
              key={platform}
              variant="outline"
              className={cn(
                "flex items-center justify-start space-x-2 h-auto py-3",
                getPlatformColor(platform)
              )}
              onClick={() => handleShare(platform)}
            >
              {getPlatformIcon(platform)}
              {showLabels && (
                <span className="text-sm">{getPlatformLabel(platform)}</span>
              )}
              {showCounts && shareCounts[platform] && (
                <span className="text-xs text-muted-foreground ml-auto">
                  {shareCounts[platform]}
                </span>
              )}
            </Button>
          ))}
        </div>

        {/* Native share button for mobile */}
        {typeof navigator !== 'undefined' && navigator.share && (
          <div className="mt-4 pt-4 border-t">
            <Button
              onClick={handleNativeShare}
              className="w-full"
              variant="default"
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share via device
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

function ShareDialog({
  platforms,
  onShare,
  url,
  title,
}: {
  platforms: SharePlatform[]
  onShare: (platform: SharePlatform) => void
  url: string
  title: string
}) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-3">
        {platforms.map((platform) => (
          <Button
            key={platform}
            variant="outline"
            className="flex items-center justify-start space-x-2 h-12"
            onClick={() => onShare(platform)}
          >
            {getPlatformIcon(platform)}
            <span>{getPlatformLabel(platform)}</span>
          </Button>
        ))}
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">Or copy link:</label>
        <div className="flex space-x-2">
          <Input value={url} readOnly className="flex-1" />
          <Button
            variant="outline"
            onClick={() => onShare('copy')}
          >
            <Copy className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

// Utility functions (duplicated to avoid import issues)
function getPlatformIcon(platform: SharePlatform) {
  switch (platform) {
    case 'facebook':
      return <Facebook className="h-4 w-4" />
    case 'twitter':
      return <Twitter className="h-4 w-4" />
    case 'linkedin':
      return <Linkedin className="h-4 w-4" />
    case 'whatsapp':
      return <MessageCircle className="h-4 w-4" />
    case 'telegram':
      return <Send className="h-4 w-4" />
    case 'email':
      return <Mail className="h-4 w-4" />
    case 'copy':
      return <Copy className="h-4 w-4" />
    case 'native':
      return <Share2 className="h-4 w-4" />
    default:
      return <Share2 className="h-4 w-4" />
  }
}

function getPlatformLabel(platform: SharePlatform) {
  switch (platform) {
    case 'facebook':
      return 'Facebook'
    case 'twitter':
      return 'Twitter'
    case 'linkedin':
      return 'LinkedIn'
    case 'whatsapp':
      return 'WhatsApp'
    case 'telegram':
      return 'Telegram'
    case 'email':
      return 'Email'
    case 'copy':
      return 'Copy Link'
    case 'native':
      return 'Share'
    default:
      return 'Share'
  }
}

// Hook for tracking share analytics
export function useShareTracking() {
  const trackShare = (platform: SharePlatform, url: string, title: string) => {
    // Track share event (integrate with your analytics)
    if (typeof gtag !== 'undefined') {
      gtag('event', 'share', {
        method: platform,
        content_type: 'article',
        item_id: url,
        content_title: title,
      })
    }

    // Track with other analytics services
    if (typeof fbq !== 'undefined') {
      fbq('track', 'Share', {
        content_name: title,
        content_category: 'article',
      })
    }
  }

  return { trackShare }
}

// Quick share component for inline use
export function QuickShare({
  url,
  title,
  className,
}: {
  url: string
  title: string
  className?: string
}) {
  const { toast } = useToast()

  const handleQuickShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({ title, url })
      } catch (error) {
        if ((error as Error).name !== 'AbortError') {
          console.error('Error sharing:', error)
        }
      }
    } else {
      try {
        await navigator.clipboard.writeText(url)
        toast({
          title: 'Link copied!',
          description: 'The link has been copied to your clipboard.',
        })
      } catch (error) {
        console.error('Error copying link:', error)
      }
    }
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleQuickShare}
      className={className}
    >
      <Share2 className="h-4 w-4 mr-2" />
      Share
    </Button>
  )
}