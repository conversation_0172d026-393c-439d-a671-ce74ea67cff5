'use client'

import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { format } from 'date-fns'
import { 
  Clock, 
  User, 
  Calendar, 
  ArrowRight, 
  BookOpen, 
  TrendingUp,
  Star,
  Eye
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface RelatedPage {
  id: string
  title: string
  slug: string
  excerpt?: string
  featuredImage?: string
  publishedAt?: Date
  createdAt: Date
  author: {
    name: string
    image?: string
  }
  category?: {
    name: string
    slug: string
    color?: string
  }
  tags?: Array<{
    name: string
    slug: string
    color?: string
  }>
  _count?: {
    views: number
    comments: number
    likes: number
  }
}

interface RelatedContentProps {
  pages: RelatedPage[]
  currentPage: RelatedPage
  title?: string
  maxItems?: number
  layout?: 'grid' | 'list' | 'carousel'
  showAuthor?: boolean
  showDate?: boolean
  showStats?: boolean
  showExcerpt?: boolean
  className?: string
}

export function RelatedContent({
  pages,
  currentPage,
  title = 'Related Content',
  maxItems = 6,
  layout = 'grid',
  showAuthor = true,
  showDate = true,
  showStats = true,
  showExcerpt = true,
  className,
}: RelatedContentProps) {
  // Filter out current page and limit results
  const relatedPages = pages
    .filter(page => page.id !== currentPage.id)
    .slice(0, maxItems)

  if (relatedPages.length === 0) {
    return null
  }

  return (
    <section className={cn("space-y-6", className)}>
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight flex items-center space-x-2">
          <BookOpen className="h-6 w-6" />
          <span>{title}</span>
        </h2>
        {pages.length > maxItems && (
          <Button variant="outline" asChild>
            <Link href="/blog">
              View All
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        )}
      </div>

      {layout === 'grid' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {relatedPages.map((page) => (
            <RelatedPageCard
              key={page.id}
              page={page}
              showAuthor={showAuthor}
              showDate={showDate}
              showStats={showStats}
              showExcerpt={showExcerpt}
              variant="card"
            />
          ))}
        </div>
      )}

      {layout === 'list' && (
        <div className="space-y-4">
          {relatedPages.map((page) => (
            <RelatedPageCard
              key={page.id}
              page={page}
              showAuthor={showAuthor}
              showDate={showDate}
              showStats={showStats}
              showExcerpt={showExcerpt}
              variant="list"
            />
          ))}
        </div>
      )}

      {layout === 'carousel' && (
        <div className="flex space-x-4 overflow-x-auto pb-4">
          {relatedPages.map((page) => (
            <div key={page.id} className="flex-shrink-0 w-80">
              <RelatedPageCard
                page={page}
                showAuthor={showAuthor}
                showDate={showDate}
                showStats={showStats}
                showExcerpt={showExcerpt}
                variant="card"
              />
            </div>
          ))}
        </div>
      )}
    </section>
  )
}

interface RelatedPageCardProps {
  page: RelatedPage
  variant: 'card' | 'list'
  showAuthor: boolean
  showDate: boolean
  showStats: boolean
  showExcerpt: boolean
}

function RelatedPageCard({
  page,
  variant,
  showAuthor,
  showDate,
  showStats,
  showExcerpt,
}: RelatedPageCardProps) {
  const publishedDate = page.publishedAt || page.createdAt
  const readingTime = calculateReadingTime(page.excerpt || '')

  if (variant === 'list') {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            {page.featuredImage && (
              <div className="flex-shrink-0">
                <img
                  src={page.featuredImage}
                  alt={page.title}
                  className="w-24 h-24 object-cover rounded-lg"
                />
              </div>
            )}
            
            <div className="flex-1 space-y-2">
              <div className="space-y-1">
                <Link
                  href={`/${page.slug}`}
                  className="text-lg font-semibold hover:text-primary transition-colors line-clamp-2"
                >
                  {page.title}
                </Link>
                
                {showExcerpt && page.excerpt && (
                  <p className="text-muted-foreground text-sm line-clamp-2">
                    {page.excerpt}
                  </p>
                )}
              </div>

              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                {showAuthor && (
                  <div className="flex items-center space-x-1">
                    <User className="h-3 w-3" />
                    <span>{page.author.name}</span>
                  </div>
                )}
                
                {showDate && (
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-3 w-3" />
                    <span>{format(publishedDate, 'MMM dd, yyyy')}</span>
                  </div>
                )}
                
                {showStats && page._count && (
                  <div className="flex items-center space-x-1">
                    <Eye className="h-3 w-3" />
                    <span>{page._count.views} views</span>
                  </div>
                )}
              </div>

              {page.category && (
                <Badge
                  variant="secondary"
                  className="w-fit"
                  style={page.category.color ? { 
                    backgroundColor: page.category.color + '20', 
                    color: page.category.color 
                  } : {}}
                >
                  {page.category.name}
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 overflow-hidden">
      {page.featuredImage && (
        <div className="aspect-video overflow-hidden">
          <img
            src={page.featuredImage}
            alt={page.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </div>
      )}
      
      <CardHeader className="space-y-2">
        {page.category && (
          <Badge
            variant="secondary"
            className="w-fit"
            style={page.category.color ? { 
              backgroundColor: page.category.color + '20', 
              color: page.category.color 
            } : {}}
          >
            {page.category.name}
          </Badge>
        )}
        
        <CardTitle className="line-clamp-2 group-hover:text-primary transition-colors">
          <Link href={`/${page.slug}`}>
            {page.title}
          </Link>
        </CardTitle>
        
        {showExcerpt && page.excerpt && (
          <CardDescription className="line-clamp-3">
            {page.excerpt}
          </CardDescription>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Author and Date */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          {showAuthor && (
            <div className="flex items-center space-x-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src={page.author.image} alt={page.author.name} />
                <AvatarFallback className="text-xs">
                  {page.author.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <span>{page.author.name}</span>
            </div>
          )}
          
          {showDate && (
            <div className="flex items-center space-x-1">
              <Calendar className="h-3 w-3" />
              <span>{format(publishedDate, 'MMM dd')}</span>
            </div>
          )}
        </div>

        {/* Stats */}
        {showStats && page._count && (
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Eye className="h-3 w-3" />
                <span>{page._count.views}</span>
              </div>
              
              {readingTime > 0 && (
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3" />
                  <span>{readingTime} min</span>
                </div>
              )}
            </div>
            
            {page._count.likes > 0 && (
              <div className="flex items-center space-x-1">
                <Star className="h-3 w-3" />
                <span>{page._count.likes}</span>
              </div>
            )}
          </div>
        )}

        {/* Tags */}
        {page.tags && page.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {page.tags.slice(0, 3).map((tag) => (
              <Badge
                key={tag.slug}
                variant="outline"
                className="text-xs"
                style={tag.color ? { 
                  borderColor: tag.color, 
                  color: tag.color 
                } : {}}
              >
                {tag.name}
              </Badge>
            ))}
            {page.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{page.tags.length - 3}
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Smart related content component that fetches related pages
export function SmartRelatedContent({
  currentPage,
  ...props
}: Omit<RelatedContentProps, 'pages'> & {
  currentPage: RelatedPage
}) {
  // This would typically fetch related content based on:
  // - Same category
  // - Similar tags
  // - Same author
  // - Popular content
  // - Recent content
  
  const mockRelatedPages: RelatedPage[] = [
    // Mock data - replace with actual API call
  ]

  return (
    <RelatedContent
      pages={mockRelatedPages}
      currentPage={currentPage}
      {...props}
    />
  )
}

// Trending content component
export function TrendingContent({
  pages,
  title = 'Trending Now',
  maxItems = 5,
  className,
}: {
  pages: RelatedPage[]
  title?: string
  maxItems?: number
  className?: string
}) {
  const trendingPages = pages
    .sort((a, b) => (b._count?.views || 0) - (a._count?.views || 0))
    .slice(0, maxItems)

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <TrendingUp className="h-5 w-5" />
          <span>{title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {trendingPages.map((page, index) => (
          <div key={page.id} className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
              <span className="text-sm font-bold text-primary">
                {index + 1}
              </span>
            </div>
            
            <div className="flex-1 space-y-1">
              <Link
                href={`/${page.slug}`}
                className="font-medium text-sm hover:text-primary transition-colors line-clamp-2"
              >
                {page.title}
              </Link>
              
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <span>{page._count?.views || 0} views</span>
                <span>•</span>
                <span>{format(page.publishedAt || page.createdAt, 'MMM dd')}</span>
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

// Popular tags component
export function PopularTags({
  pages,
  title = 'Popular Tags',
  maxTags = 10,
  className,
}: {
  pages: RelatedPage[]
  title?: string
  maxTags?: number
  className?: string
}) {
  // Count tag occurrences
  const tagCounts = new Map<string, { tag: RelatedPage['tags'][0], count: number }>()
  
  pages.forEach(page => {
    page.tags?.forEach(tag => {
      const existing = tagCounts.get(tag.slug)
      if (existing) {
        existing.count++
      } else {
        tagCounts.set(tag.slug, { tag, count: 1 })
      }
    })
  })

  const popularTags = Array.from(tagCounts.values())
    .sort((a, b) => b.count - a.count)
    .slice(0, maxTags)

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-2">
          {popularTags.map(({ tag, count }) => (
            <Link key={tag.slug} href={`/tag/${tag.slug}`}>
              <Badge
                variant="secondary"
                className="hover:bg-primary hover:text-primary-foreground transition-colors"
                style={tag.color ? { 
                  backgroundColor: tag.color + '20', 
                  color: tag.color 
                } : {}}
              >
                {tag.name} ({count})
              </Badge>
            </Link>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

// Utility function
function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200
  const words = content.replace(/<[^>]*>/g, '').split(/\s+/).length
  return Math.ceil(words / wordsPerMinute)
}