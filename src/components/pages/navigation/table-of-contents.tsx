'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { List, ChevronRight, ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'

interface TOCItem {
  id: string
  text: string
  level: number
  children?: TOCItem[]
}

interface TableOfContentsProps {
  items: TOCItem[]
  activeId?: string
  className?: string
  title?: string
  collapsible?: boolean
  maxDepth?: number
  showNumbers?: boolean
  sticky?: boolean
  onItemClick?: (id: string) => void
}

export function TableOfContents({
  items,
  activeId,
  className,
  title = 'Table of Contents',
  collapsible = true,
  maxDepth = 6,
  showNumbers = false,
  sticky = true,
  onItemClick,
}: TableOfContentsProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [currentActiveId, setCurrentActiveId] = useState(activeId)

  // Auto-detect active heading based on scroll position
  useEffect(() => {
    if (activeId) return // Don't auto-detect if activeId is provided

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setCurrentActiveId(entry.target.id)
          }
        })
      },
      {
        rootMargin: '-20% 0% -35% 0%',
        threshold: 0,
      }
    )

    // Observe all headings
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
    headings.forEach((heading) => {
      if (heading.id) {
        observer.observe(heading)
      }
    })

    return () => observer.disconnect()
  }, [activeId])

  const handleItemClick = (id: string) => {
    if (onItemClick) {
      onItemClick(id)
    } else {
      // Default scroll behavior
      const element = document.getElementById(id)
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        })
      }
    }
  }

  const filteredItems = filterItemsByDepth(items, maxDepth)
  const hierarchicalItems = buildHierarchy(filteredItems)

  if (items.length === 0) return null

  return (
    <Card className={cn(
      "w-full",
      sticky && "sticky top-8",
      className
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center space-x-2">
            <List className="h-4 w-4" />
            <span>{title}</span>
          </CardTitle>
          {collapsible && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              {isCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>
      </CardHeader>
      
      {!isCollapsed && (
        <CardContent className="pt-0">
          <ScrollArea className="h-full max-h-96">
            <nav aria-label="Table of contents">
              <TOCList
                items={hierarchicalItems}
                activeId={currentActiveId}
                onItemClick={handleItemClick}
                showNumbers={showNumbers}
                level={0}
              />
            </nav>
          </ScrollArea>
        </CardContent>
      )}
    </Card>
  )
}

interface TOCListProps {
  items: TOCItem[]
  activeId?: string
  onItemClick: (id: string) => void
  showNumbers: boolean
  level: number
}

function TOCList({ 
  items, 
  activeId, 
  onItemClick, 
  showNumbers, 
  level 
}: TOCListProps) {
  return (
    <ul className={cn(
      "space-y-1",
      level > 0 && "ml-4 mt-1 border-l border-muted pl-4"
    )}>
      {items.map((item, index) => (
        <li key={item.id}>
          <button
            onClick={() => onItemClick(item.id)}
            className={cn(
              "w-full text-left text-sm py-1 px-2 rounded transition-colors hover:bg-muted",
              activeId === item.id && "bg-primary/10 text-primary font-medium",
              level === 0 && "font-medium",
              level > 0 && "text-muted-foreground"
            )}
          >
            <span className="flex items-start space-x-2">
              {showNumbers && (
                <span className="text-xs text-muted-foreground mt-0.5 min-w-[1rem]">
                  {index + 1}.
                </span>
              )}
              <span className="flex-1 leading-relaxed">
                {item.text}
              </span>
            </span>
          </button>
          
          {item.children && item.children.length > 0 && (
            <TOCList
              items={item.children}
              activeId={activeId}
              onItemClick={onItemClick}
              showNumbers={showNumbers}
              level={level + 1}
            />
          )}
        </li>
      ))}
    </ul>
  )
}

// Utility functions
function filterItemsByDepth(items: TOCItem[], maxDepth: number): TOCItem[] {
  return items.filter(item => item.level <= maxDepth)
}

function buildHierarchy(items: TOCItem[]): TOCItem[] {
  const result: TOCItem[] = []
  const stack: TOCItem[] = []

  items.forEach(item => {
    const newItem = { ...item, children: [] }

    // Find the appropriate parent
    while (stack.length > 0 && stack[stack.length - 1].level >= newItem.level) {
      stack.pop()
    }

    if (stack.length === 0) {
      result.push(newItem)
    } else {
      const parent = stack[stack.length - 1]
      if (!parent.children) parent.children = []
      parent.children.push(newItem)
    }

    stack.push(newItem)
  })

  return result
}

// Hook for generating TOC from content
export function useTableOfContents(
  content: string | HTMLElement,
  options: {
    selectors?: string
    maxDepth?: number
    includeLevel1?: boolean
  } = {}
) {
  const [items, setItems] = useState<TOCItem[]>([])

  const {
    selectors = 'h1, h2, h3, h4, h5, h6',
    maxDepth = 6,
    includeLevel1 = true,
  } = options

  useEffect(() => {
    let headings: NodeListOf<HTMLElement>

    if (typeof content === 'string') {
      // Parse HTML string
      const parser = new DOMParser()
      const doc = parser.parseFromString(content, 'text/html')
      headings = doc.querySelectorAll(selectors)
    } else if (content instanceof HTMLElement) {
      // Use provided element
      headings = content.querySelectorAll(selectors)
    } else {
      // Use document
      headings = document.querySelectorAll(selectors)
    }

    const tocItems: TOCItem[] = []

    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1))
      
      if (level > maxDepth || (!includeLevel1 && level === 1)) {
        return
      }

      let id = heading.id
      if (!id) {
        // Generate ID from text content
        id = heading.textContent
          ?.toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-|-$/g, '') || `heading-${index}`
        heading.id = id
      }

      tocItems.push({
        id,
        text: heading.textContent || '',
        level,
      })
    })

    setItems(tocItems)
  }, [content, selectors, maxDepth, includeLevel1])

  return items
}

// Floating TOC component
export function FloatingTOC({
  items,
  position = 'right',
  ...props
}: TableOfContentsProps & {
  position?: 'left' | 'right'
}) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const toggleVisibility = () => {
      setIsVisible(window.pageYOffset > 300)
    }

    window.addEventListener('scroll', toggleVisibility)
    return () => window.removeEventListener('scroll', toggleVisibility)
  }, [])

  if (!isVisible || items.length === 0) return null

  return (
    <div className={cn(
      "fixed top-1/2 transform -translate-y-1/2 z-50 max-w-xs",
      position === 'right' ? "right-4" : "left-4"
    )}>
      <TableOfContents
        items={items}
        sticky={false}
        collapsible={true}
        {...props}
      />
    </div>
  )
}

// Mini TOC for inline use
export function MiniTOC({
  items,
  maxItems = 5,
  ...props
}: TableOfContentsProps & {
  maxItems?: number
}) {
  const limitedItems = items.slice(0, maxItems)

  return (
    <div className="border rounded-lg p-4 bg-muted/30">
      <h4 className="font-medium mb-3 flex items-center space-x-2">
        <List className="h-4 w-4" />
        <span>In this article</span>
      </h4>
      <nav>
        <ul className="space-y-2">
          {limitedItems.map((item) => (
            <li key={item.id}>
              <button
                onClick={() => {
                  const element = document.getElementById(item.id)
                  element?.scrollIntoView({ behavior: 'smooth' })
                }}
                className="text-sm text-muted-foreground hover:text-foreground transition-colors text-left"
              >
                {item.text}
              </button>
            </li>
          ))}
        </ul>
      </nav>
      {items.length > maxItems && (
        <p className="text-xs text-muted-foreground mt-2">
          +{items.length - maxItems} more sections
        </p>
      )}
    </div>
  )
}