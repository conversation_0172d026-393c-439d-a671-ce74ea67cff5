'use client'

import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'
import { cn } from '@/lib/utils'
import { BreadcrumbSchema } from '../seo/seo-head'

interface BreadcrumbItem {
  label: string
  href: string
  current?: boolean
}

interface BreadcrumbNavProps {
  items: BreadcrumbItem[]
  separator?: React.ReactNode
  showHome?: boolean
  homeLabel?: string
  homeHref?: string
  className?: string
  maxItems?: number
  showSchema?: boolean
}

export function BreadcrumbNav({
  items,
  separator = <ChevronRight className="h-4 w-4" />,
  showHome = true,
  homeLabel = 'Home',
  homeHref = '/',
  className,
  maxItems,
  showSchema = true,
}: BreadcrumbNavProps) {
  // Add home item if requested
  const allItems = showHome 
    ? [{ label: homeLabel, href: homeHref }, ...items.filter(item => item.href !== homeHref)]
    : items

  // Truncate items if maxItems is specified
  const displayItems = maxItems && allItems.length > maxItems
    ? [
        allItems[0],
        { label: '...', href: '#', disabled: true },
        ...allItems.slice(-maxItems + 1)
      ]
    : allItems

  // Prepare schema data
  const schemaItems = allItems.map(item => ({
    name: item.label,
    url: item.href,
  }))

  return (
    <>
      {showSchema && <BreadcrumbSchema items={schemaItems} />}
      
      <nav 
        aria-label="Breadcrumb" 
        className={cn("flex items-center space-x-1 text-sm", className)}
      >
        <ol className="flex items-center space-x-1">
          {displayItems.map((item, index) => {
            const isLast = index === displayItems.length - 1
            const isCurrent = item.current || isLast
            const isDisabled = 'disabled' in item && item.disabled

            return (
              <li key={index} className="flex items-center space-x-1">
                {index > 0 && (
                  <span className="text-muted-foreground" aria-hidden="true">
                    {separator}
                  </span>
                )}
                
                {isDisabled ? (
                  <span className="text-muted-foreground">
                    {item.label}
                  </span>
                ) : isCurrent ? (
                  <span 
                    className="font-medium text-foreground"
                    aria-current="page"
                  >
                    {item.label}
                  </span>
                ) : (
                  <Link
                    href={item.href}
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {index === 0 && showHome ? (
                      <span className="flex items-center space-x-1">
                        <Home className="h-4 w-4" />
                        <span>{item.label}</span>
                      </span>
                    ) : (
                      item.label
                    )}
                  </Link>
                )}
              </li>
            )
          })}
        </ol>
      </nav>
    </>
  )
}

// Utility function to generate breadcrumbs from pathname
export function generateBreadcrumbs(
  pathname: string,
  options: {
    labels?: Record<string, string>
    excludePaths?: string[]
    includeHome?: boolean
  } = {}
): BreadcrumbItem[] {
  const { labels = {}, excludePaths = [], includeHome = true } = options
  
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs: BreadcrumbItem[] = []

  if (includeHome && pathname !== '/') {
    breadcrumbs.push({
      label: 'Home',
      href: '/',
    })
  }

  let currentPath = ''
  
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`
    
    if (!excludePaths.includes(currentPath)) {
      const isLast = index === segments.length - 1
      const label = labels[currentPath] || labels[segment] || formatSegment(segment)
      
      breadcrumbs.push({
        label,
        href: currentPath,
        current: isLast,
      })
    }
  })

  return breadcrumbs
}

// Format URL segment for display
function formatSegment(segment: string): string {
  return segment
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

// Hook for automatic breadcrumb generation
export function useBreadcrumbs(
  pathname: string,
  customItems?: BreadcrumbItem[],
  options?: Parameters<typeof generateBreadcrumbs>[1]
) {
  if (customItems) {
    return customItems
  }

  return generateBreadcrumbs(pathname, options)
}

// Breadcrumb component with automatic generation
export function AutoBreadcrumbs({
  pathname,
  labels,
  excludePaths,
  ...props
}: Omit<BreadcrumbNavProps, 'items'> & {
  pathname: string
  labels?: Record<string, string>
  excludePaths?: string[]
}) {
  const items = generateBreadcrumbs(pathname, { labels, excludePaths })
  
  return <BreadcrumbNav items={items} {...props} />
}

// Structured breadcrumb component for rich snippets
export function StructuredBreadcrumbs({
  items,
  ...props
}: BreadcrumbNavProps) {
  return (
    <div itemScope itemType="https://schema.org/BreadcrumbList">
      <BreadcrumbNav items={items} showSchema={false} {...props} />
      {items.map((item, index) => (
        <div
          key={index}
          itemProp="itemListElement"
          itemScope
          itemType="https://schema.org/ListItem"
          className="hidden"
        >
          <Link href={item.href} itemProp="item">
            <span itemProp="name">{item.label}</span>
          </Link>
          <meta itemProp="position" content={String(index + 1)} />
        </div>
      ))}
    </div>
  )
}