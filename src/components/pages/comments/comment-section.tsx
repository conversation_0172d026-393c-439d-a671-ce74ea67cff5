'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { useToast } from '@/hooks/use-toast'
import { format, formatDistanceToNow } from 'date-fns'
import { 
  MessageSquare, 
  Reply, 
  Heart, 
  Flag, 
  MoreHorizontal,
  Send,
  User,
  Calendar,
  ThumbsUp,
  Thum<PERSON>Down,
  Edit,
  Trash2
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface Comment {
  id: string
  content: string
  approved: boolean
  createdAt: Date
  updatedAt?: Date
  author: {
    name: string
    email: string
    image?: string
    website?: string
  }
  parentId?: string
  replies?: Comment[]
  likes?: number
  dislikes?: number
  isLiked?: boolean
  isDisliked?: boolean
  canEdit?: boolean
  canDelete?: boolean
}

interface CommentSectionProps {
  pageId: string
  comments: Comment[]
  allowComments?: boolean
  requireApproval?: boolean
  allowReplies?: boolean
  allowVoting?: boolean
  allowGuests?: boolean
  maxDepth?: number
  className?: string
  onCommentSubmit?: (comment: Partial<Comment>) => Promise<void>
  onCommentUpdate?: (commentId: string, content: string) => Promise<void>
  onCommentDelete?: (commentId: string) => Promise<void>
  onCommentVote?: (commentId: string, type: 'like' | 'dislike') => Promise<void>
}

const commentSchema = z.object({
  content: z.string().min(10, 'Comment must be at least 10 characters').max(1000, 'Comment too long'),
  author: z.object({
    name: z.string().min(2, 'Name is required'),
    email: z.string().email('Valid email is required'),
    website: z.string().url('Must be a valid URL').optional().or(z.literal('')),
  }),
})

type CommentFormData = z.infer<typeof commentSchema>

export function CommentSection({
  pageId,
  comments,
  allowComments = true,
  requireApproval = true,
  allowReplies = true,
  allowVoting = true,
  allowGuests = true,
  maxDepth = 3,
  className,
  onCommentSubmit,
  onCommentUpdate,
  onCommentDelete,
  onCommentVote,
}: CommentSectionProps) {
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [editingComment, setEditingComment] = useState<string | null>(null)
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'popular'>('newest')

  const form = useForm<CommentFormData>({
    resolver: zodResolver(commentSchema),
    defaultValues: {
      content: '',
      author: {
        name: '',
        email: '',
        website: '',
      },
    },
  })

  // Build hierarchical comment structure
  const buildCommentTree = (comments: Comment[], parentId?: string, depth = 0): Comment[] => {
    if (depth >= maxDepth) return []
    
    return comments
      .filter(comment => comment.parentId === parentId)
      .map(comment => ({
        ...comment,
        replies: buildCommentTree(comments, comment.id, depth + 1),
      }))
  }

  // Sort comments
  const sortComments = (comments: Comment[]): Comment[] => {
    const sorted = [...comments].sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        case 'popular':
          return (b.likes || 0) - (a.likes || 0)
        default:
          return 0
      }
    })

    return sorted.map(comment => ({
      ...comment,
      replies: comment.replies ? sortComments(comment.replies) : [],
    }))
  }

  const approvedComments = comments.filter(comment => comment.approved)
  const commentTree = buildCommentTree(approvedComments)
  const sortedComments = sortComments(commentTree)

  const handleSubmit = async (data: CommentFormData) => {
    if (!onCommentSubmit) return

    try {
      setIsSubmitting(true)
      
      await onCommentSubmit({
        content: data.content,
        author: data.author,
        parentId: replyingTo || undefined,
      })

      form.reset()
      setReplyingTo(null)
      
      toast({
        title: 'Comment submitted!',
        description: requireApproval 
          ? 'Your comment is awaiting approval.' 
          : 'Your comment has been posted.',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to submit comment. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleVote = async (commentId: string, type: 'like' | 'dislike') => {
    if (!onCommentVote) return

    try {
      await onCommentVote(commentId, type)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to vote on comment.',
        variant: 'destructive',
      })
    }
  }

  const handleEdit = async (commentId: string, content: string) => {
    if (!onCommentUpdate) return

    try {
      await onCommentUpdate(commentId, content)
      setEditingComment(null)
      toast({
        title: 'Comment updated!',
        description: 'Your comment has been updated.',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update comment.',
        variant: 'destructive',
      })
    }
  }

  const handleDelete = async (commentId: string) => {
    if (!onCommentDelete) return

    try {
      await onCommentDelete(commentId)
      toast({
        title: 'Comment deleted!',
        description: 'Your comment has been deleted.',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete comment.',
        variant: 'destructive',
      })
    }
  }

  if (!allowComments) {
    return (
      <Card className={className}>
        <CardContent className="pt-6 text-center text-muted-foreground">
          <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p>Comments are disabled for this content.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <section className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight flex items-center space-x-2">
          <MessageSquare className="h-6 w-6" />
          <span>Comments ({approvedComments.length})</span>
        </h2>
        
        {approvedComments.length > 0 && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as typeof sortBy)}
              className="text-sm border rounded px-2 py-1"
            >
              <option value="newest">Newest</option>
              <option value="oldest">Oldest</option>
              <option value="popular">Most Liked</option>
            </select>
          </div>
        )}
      </div>

      {/* Comment Form */}
      <Card>
        <CardHeader>
          <CardTitle>Leave a Comment</CardTitle>
          <CardDescription>
            Share your thoughts and join the conversation.
            {requireApproval && ' Comments are moderated and may take some time to appear.'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Comment</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Write your comment here..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="author.name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Your name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="author.email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email *</FormLabel>
                      <FormControl>
                        <Input 
                          type="email" 
                          placeholder="<EMAIL>" 
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="author.website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website (optional)</FormLabel>
                    <FormControl>
                      <Input 
                        type="url" 
                        placeholder="https://yourwebsite.com" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end">
                <Button type="submit" disabled={isSubmitting}>
                  <Send className="h-4 w-4 mr-2" />
                  {isSubmitting ? 'Submitting...' : 'Post Comment'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Comments List */}
      {sortedComments.length > 0 ? (
        <div className="space-y-4">
          {sortedComments.map((comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              depth={0}
              maxDepth={maxDepth}
              allowReplies={allowReplies}
              allowVoting={allowVoting}
              onReply={setReplyingTo}
              onEdit={setEditingComment}
              onDelete={handleDelete}
              onVote={handleVote}
              replyingTo={replyingTo}
              editingComment={editingComment}
              onSubmitReply={handleSubmit}
              onSubmitEdit={handleEdit}
            />
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="pt-6 text-center text-muted-foreground">
            <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <h3 className="font-medium mb-2">No comments yet</h3>
            <p>Be the first to share your thoughts!</p>
          </CardContent>
        </Card>
      )}
    </section>
  )
}

interface CommentItemProps {
  comment: Comment
  depth: number
  maxDepth: number
  allowReplies: boolean
  allowVoting: boolean
  onReply: (commentId: string) => void
  onEdit: (commentId: string) => void
  onDelete: (commentId: string) => void
  onVote: (commentId: string, type: 'like' | 'dislike') => void
  replyingTo: string | null
  editingComment: string | null
  onSubmitReply: (data: CommentFormData) => void
  onSubmitEdit: (commentId: string, content: string) => void
}

function CommentItem({
  comment,
  depth,
  maxDepth,
  allowReplies,
  allowVoting,
  onReply,
  onEdit,
  onDelete,
  onVote,
  replyingTo,
  editingComment,
  onSubmitReply,
  onSubmitEdit,
}: CommentItemProps) {
  const [editContent, setEditContent] = useState(comment.content)
  const isReplying = replyingTo === comment.id
  const isEditing = editingComment === comment.id

  const form = useForm<CommentFormData>({
    resolver: zodResolver(commentSchema),
    defaultValues: {
      content: '',
      author: {
        name: '',
        email: '',
        website: '',
      },
    },
  })

  return (
    <div className={cn(
      "space-y-4",
      depth > 0 && "ml-8 pl-4 border-l-2 border-muted"
    )}>
      <Card>
        <CardContent className="pt-6">
          {/* Comment Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={comment.author.image} alt={comment.author.name} />
                <AvatarFallback>
                  {comment.author.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
              
              <div>
                <div className="flex items-center space-x-2">
                  {comment.author.website ? (
                    <a
                      href={comment.author.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-medium hover:text-primary transition-colors"
                    >
                      {comment.author.name}
                    </a>
                  ) : (
                    <span className="font-medium">{comment.author.name}</span>
                  )}
                  
                  {comment.updatedAt && comment.updatedAt > comment.createdAt && (
                    <Badge variant="secondary" className="text-xs">
                      Edited
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Calendar className="h-3 w-3" />
                  <span>{formatDistanceToNow(comment.createdAt, { addSuffix: true })}</span>
                </div>
              </div>
            </div>

            {/* Comment Actions */}
            <div className="flex items-center space-x-1">
              {comment.canEdit && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit(comment.id)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
              )}
              
              {comment.canDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(comment.id)}
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Comment Content */}
          {isEditing ? (
            <div className="space-y-4">
              <Textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className="min-h-[100px]"
              />
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEdit('')}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={() => onSubmitEdit(comment.id, editContent)}
                >
                  Save
                </Button>
              </div>
            </div>
          ) : (
            <div className="prose prose-sm max-w-none mb-4">
              <p>{comment.content}</p>
            </div>
          )}

          {/* Comment Footer */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {allowVoting && (
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onVote(comment.id, 'like')}
                    className={cn(
                      "h-8 px-2",
                      comment.isLiked && "text-green-600 bg-green-50"
                    )}
                  >
                    <ThumbsUp className="h-3 w-3 mr-1" />
                    <span>{comment.likes || 0}</span>
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onVote(comment.id, 'dislike')}
                    className={cn(
                      "h-8 px-2",
                      comment.isDisliked && "text-red-600 bg-red-50"
                    )}
                  >
                    <ThumbsDown className="h-3 w-3 mr-1" />
                    <span>{comment.dislikes || 0}</span>
                  </Button>
                </div>
              )}

              {allowReplies && depth < maxDepth && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onReply(comment.id)}
                >
                  <Reply className="h-3 w-3 mr-1" />
                  Reply
                </Button>
              )}
            </div>

            <Button variant="ghost" size="sm">
              <Flag className="h-3 w-3 mr-1" />
              Report
            </Button>
          </div>

          {/* Reply Form */}
          {isReplying && (
            <div className="mt-4 pt-4 border-t">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmitReply)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="content"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Textarea
                            placeholder={`Reply to ${comment.author.name}...`}
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="author.name"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input placeholder="Your name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="author.email"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input 
                              type="email" 
                              placeholder="<EMAIL>" 
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => onReply('')}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" size="sm">
                      <Send className="h-3 w-3 mr-1" />
                      Reply
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Nested Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="space-y-4">
          {comment.replies.map((reply) => (
            <CommentItem
              key={reply.id}
              comment={reply}
              depth={depth + 1}
              maxDepth={maxDepth}
              allowReplies={allowReplies}
              allowVoting={allowVoting}
              onReply={onReply}
              onEdit={onEdit}
              onDelete={onDelete}
              onVote={onVote}
              replyingTo={replyingTo}
              editingComment={editingComment}
              onSubmitReply={onSubmitReply}
              onSubmitEdit={onSubmitEdit}
            />
          ))}
        </div>
      )}
    </div>
  )
}