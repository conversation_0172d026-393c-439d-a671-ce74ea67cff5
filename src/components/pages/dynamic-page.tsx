'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { <PERSON><PERSON>ender<PERSON> } from './page-renderer'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle, RefreshCw, Home, ArrowLeft } from 'lucide-react'
import { LoadingSpinner } from '../admin/ui/loading-spinner'
import { ErrorBoundary } from 'next/dist/client/components/error-boundary'
import { useAnalytics } from '@/hooks/use-analytics'

interface DynamicPageProps {
  slug: string
  preview?: boolean
  version?: string
  locale?: string
  params?: Record<string, string | string[]>
  searchParams?: Record<string, string | string[] | undefined>
  onError?: (error: string) => void
  onNotFound?: () => void
  onPageLoad?: (page: PageData) => void
}

interface PageData {
  id: string
  title: string
  slug: string
  content?: string
  excerpt?: string
  template: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  seoTitle?: string
  seoDescription?: string
  featuredImage?: string
  author: {
    id: string
    name: string
    image?: string
    bio?: string
  }
  category?: {
    id: string
    name: string
    slug: string
    color?: string
  }
  tags?: Array<{
    id: string
    name: string
    slug: string
    color?: string
  }>
  blocks?: Array<{
    id: string
    type: string
    content: any
    order: number
  }>
  comments?: Array<{
    id: string
    content: string
    author: {
      name: string
      image?: string
    }
    createdAt: Date
    approved: boolean
  }>
  _count?: {
    views: number
    comments: number
    likes: number
  }
}

export function DynamicPage({
  slug,
  preview = false,
  version,
  locale = 'en',
  params = {},
  searchParams = {},
  onError,
  onNotFound,
  onPageLoad,
}: DynamicPageProps) {
  const router = useRouter()
  const [page, setPage] = useState<PageData | null>(null)
  const [relatedPages, setRelatedPages] = useState<PageData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [notFound, setNotFound] = useState(false)

  // Initialize analytics tracking
  const { trackEvent } = useAnalytics({
    enabled: !preview, // Don't track preview views
    trackPageViews: true,
    trackClicks: true,
    trackScrollDepth: true
  })

  useEffect(() => {
    fetchPage()
  }, [slug, preview, version, locale])

  const fetchPage = async () => {
    try {
      setLoading(true)
      setError(null)
      setNotFound(false)

      // Build API URL with parameters
      const url = new URL(`/api/pages/${slug}`, window.location.origin)
      if (preview) url.searchParams.set('preview', 'true')
      if (version) url.searchParams.set('version', version)
      if (locale) url.searchParams.set('locale', locale)

      // Add any additional search params
      Object.entries(searchParams).forEach(([key, value]) => {
        if (typeof value === 'string') {
          url.searchParams.set(key, value)
        } else if (Array.isArray(value)) {
          value.forEach(v => url.searchParams.append(key, v))
        }
      })

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: preview ? 'no-cache' : 'default'
      })

      if (response.status === 404) {
        setNotFound(true)
        onNotFound?.()
        return
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `Failed to fetch page: ${response.statusText}`)
      }

      const data = await response.json()

      if (!data.page) {
        throw new Error('Invalid response format')
      }

      setPage(data.page)
      setRelatedPages(data.relatedPages || [])
      onPageLoad?.(data.page)

      // Track page view for analytics (only for published pages, not previews)
      if (data.page && !preview && data.page.status === 'PUBLISHED') {
        trackEvent('page_view', {
          pageId: data.page.id,
          title: data.page.title,
          template: data.page.template,
          author: data.page.author.name
        })
      }

      // Update browser history for dynamic routing
      if (data.page && !preview) {
        const newUrl = `/${data.page.slug}`
        if (window.location.pathname !== newUrl) {
          window.history.replaceState({}, '', newUrl)
        }
      }

    } catch (err) {
      console.error('Failed to fetch page:', err)
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while loading the page'
      setError(errorMessage)
      onError?.(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = () => {
    if (page) {
      router.push(`/admin/pages/${page.id}/edit`)
    }
  }

  const handleRetry = () => {
    fetchPage()
  }

  const handleGoHome = () => {
    router.push('/')
  }

  const handleGoBack = () => {
    router.back()
  }

  if (loading) {
    return <PageLoadingState />
  }

  if (notFound) {
    return (
      <PageNotFound
        slug={slug}
        onGoHome={handleGoHome}
        onGoBack={handleGoBack}
      />
    )
  }

  if (error) {
    return (
      <PageError
        error={error}
        onRetry={handleRetry}
        onGoHome={handleGoHome}
        onGoBack={handleGoBack}
      />
    )
  }

  if (!page) {
    return (
      <PageNotFound
        slug={slug}
        onGoHome={handleGoHome}
        onGoBack={handleGoBack}
      />
    )
  }

  return (
    <ErrorBoundary
      fallback={
        <PageError
          error="Something went wrong while rendering the page"
          onRetry={handleRetry}
          onGoHome={handleGoHome}
          onGoBack={handleGoBack}
        />
      }
    >
      <PageRenderer
        page={page}
        relatedPages={relatedPages}
        isPreview={preview}
        onEdit={preview ? handleEdit : undefined}
        showComments={!preview}
        showRelated={relatedPages.length > 0}
        showTOC={page.template !== 'landing'}
        showBreadcrumbs={true}
        showShare={!preview}
        showAuthor={true}
        showMeta={true}
      />
    </ErrorBoundary>
  )
}

function PageLoadingState() {
  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto px-4 py-16">
        <div className="space-y-8">
          {/* Header Skeleton */}
          <div className="space-y-4">
            <div className="h-4 bg-muted rounded w-1/4 animate-pulse" />
            <div className="h-12 bg-muted rounded w-3/4 animate-pulse" />
            <div className="h-6 bg-muted rounded w-1/2 animate-pulse" />
            <div className="flex space-x-4">
              <div className="h-4 bg-muted rounded w-24 animate-pulse" />
              <div className="h-4 bg-muted rounded w-24 animate-pulse" />
              <div className="h-4 bg-muted rounded w-24 animate-pulse" />
            </div>
          </div>

          {/* Featured Image Skeleton */}
          <div className="h-64 md:h-96 bg-muted rounded-lg animate-pulse" />

          {/* Content Skeleton */}
          <div className="space-y-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <div
                key={i}
                className={`h-4 bg-muted rounded animate-pulse ${
                  i % 3 === 0 ? 'w-full' : i % 3 === 1 ? 'w-5/6' : 'w-4/5'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Loading Spinner */}
        <div className="flex items-center justify-center mt-8">
          <LoadingSpinner size="lg" />
          <span className="ml-2 text-muted-foreground">Loading page...</span>
        </div>
      </div>
    </div>
  )
}

function PageNotFound({
  slug,
  onGoHome,
  onGoBack,
}: {
  slug: string
  onGoHome: () => void
  onGoBack: () => void
}) {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="max-w-md mx-auto px-4 text-center">
        <Card>
          <CardHeader>
            <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
              <AlertCircle className="h-8 w-8 text-muted-foreground" />
            </div>
            <CardTitle className="text-2xl">Page Not Found</CardTitle>
            <CardDescription>
              The page "{slug}" could not be found. It may have been moved, deleted, or never existed.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={onGoBack} variant="outline" className="flex-1">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </Button>
              <Button onClick={onGoHome} className="flex-1">
                <Home className="h-4 w-4 mr-2" />
                Go Home
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

function PageError({
  error,
  onRetry,
  onGoHome,
  onGoBack,
}: {
  error: string
  onRetry: () => void
  onGoHome: () => void
  onGoBack: () => void
}) {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="max-w-md mx-auto px-4 text-center">
        <Card>
          <CardHeader>
            <div className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
              <AlertCircle className="h-8 w-8 text-destructive" />
            </div>
            <CardTitle className="text-2xl">Something went wrong</CardTitle>
            <CardDescription>
              {error}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={onRetry} className="w-full">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={onGoBack} variant="outline" className="flex-1">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </Button>
              <Button onClick={onGoHome} variant="outline" className="flex-1">
                <Home className="h-4 w-4 mr-2" />
                Go Home
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Hook for using dynamic pages in components
export function useDynamicPage(slug: string, options: {
  preview?: boolean
  version?: string
  locale?: string
  autoRefresh?: boolean
  refreshInterval?: number
} = {}) {
  const [page, setPage] = useState<PageData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const {
    preview = false,
    version,
    locale = 'en',
    autoRefresh = false,
    refreshInterval = 30000,
  } = options

  const fetchPage = async () => {
    try {
      setLoading(true)
      setError(null)

      const url = new URL(`/api/pages/${slug}`, window.location.origin)
      if (preview) url.searchParams.set('preview', 'true')
      if (version) url.searchParams.set('version', version)
      if (locale) url.searchParams.set('locale', locale)

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: preview ? 'no-cache' : 'default'
      })

      if (response.status === 404) {
        setError('Page not found')
        return
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `Failed to fetch page: ${response.statusText}`)
      }

      const data = await response.json()

      if (!data.page) {
        throw new Error('Invalid response format')
      }

      setPage(data.page)

    } catch (err) {
      console.error('Failed to fetch page:', err)
      setError(err instanceof Error ? err.message : 'An error occurred while loading the page')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPage()
  }, [slug, preview, version, locale])

  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(fetchPage, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [autoRefresh, refreshInterval])

  return {
    page,
    loading,
    error,
    refetch: fetchPage,
  }
}

// Component for rendering pages with custom templates
export function TemplatedPage({
  slug,
  template,
  ...props
}: DynamicPageProps & { template: string }) {
  return (
    <DynamicPage
      slug={slug}
      {...props}
    />
  )
}

// Component for preview mode
export function PreviewPage({
  slug,
  version,
  ...props
}: Omit<DynamicPageProps, 'preview'>) {
  return (
    <DynamicPage
      slug={slug}
      preview={true}
      version={version}
      {...props}
    />
  )
}