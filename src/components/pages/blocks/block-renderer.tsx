'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Quote,
  Image as ImageIcon,
  Video,
  FileText,
  Code,
  List,
  Grid3X3,
  Users,
  Star,
  Play,
  Download,
  ExternalLink,
  Calendar,
  MapPin,
  Mail,
  Phone
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface Block {
  id: string
  type: string
  content: any
  order: number
}

interface BlockRendererProps {
  block: Block
  className?: string
  isEditing?: boolean
  onEdit?: (block: Block) => void
  onDelete?: (blockId: string) => void
}

export function BlockRenderer({ 
  block, 
  className, 
  isEditing = false, 
  onEdit, 
  onDelete 
}: BlockRendererProps) {
  const handleEdit = () => {
    if (onEdit) onEdit(block)
  }

  const handleDelete = () => {
    if (onDelete) onDelete(block.id)
  }

  const renderBlock = () => {
    switch (block.type) {
      case 'text':
        return <TextBlock content={block.content} />
      case 'heading':
        return <HeadingBlock content={block.content} />
      case 'paragraph':
        return <ParagraphBlock content={block.content} />
      case 'quote':
        return <QuoteBlock content={block.content} />
      case 'image':
        return <ImageBlock content={block.content} />
      case 'gallery':
        return <GalleryBlock content={block.content} />
      case 'video':
        return <VideoBlock content={block.content} />
      case 'code':
        return <CodeBlock content={block.content} />
      case 'list':
        return <ListBlock content={block.content} />
      case 'table':
        return <TableBlock content={block.content} />
      case 'separator':
        return <SeparatorBlock content={block.content} />
      case 'button':
        return <ButtonBlock content={block.content} />
      case 'card':
        return <CardBlock content={block.content} />
      case 'grid':
        return <GridBlock content={block.content} />
      case 'testimonial':
        return <TestimonialBlock content={block.content} />
      case 'team':
        return <TeamBlock content={block.content} />
      case 'pricing':
        return <PricingBlock content={block.content} />
      case 'faq':
        return <FAQBlock content={block.content} />
      case 'contact':
        return <ContactBlock content={block.content} />
      case 'embed':
        return <EmbedBlock content={block.content} />
      case 'form':
        return <FormBlock content={block.content} />
      default:
        return <UnknownBlock type={block.type} content={block.content} />
    }
  }

  return (
    <div className={cn("relative group", className)}>
      {isEditing && (
        <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
          <div className="flex space-x-1">
            <Button size="sm" variant="outline" onClick={handleEdit}>
              Edit
            </Button>
            <Button size="sm" variant="outline" onClick={handleDelete}>
              Delete
            </Button>
          </div>
        </div>
      )}
      {renderBlock()}
    </div>
  )
}

// Text Blocks
function TextBlock({ content }: { content: any }) {
  return (
    <div 
      className="prose prose-lg max-w-none"
      dangerouslySetInnerHTML={{ __html: content.html || content.text || '' }}
    />
  )
}

function HeadingBlock({ content }: { content: any }) {
  const { text, level = 2, id } = content
  const Tag = `h${level}` as React.ElementType

  return (
    <Tag
      id={id}
      className={cn(
        "font-bold tracking-tight",
        level === 1 && "text-4xl md:text-5xl",
        level === 2 && "text-3xl md:text-4xl",
        level === 3 && "text-2xl md:text-3xl",
        level === 4 && "text-xl md:text-2xl",
        level === 5 && "text-lg md:text-xl",
        level === 6 && "text-base md:text-lg"
      )}
    >
      {text}
    </Tag>
  )
}

function ParagraphBlock({ content }: { content: any }) {
  return (
    <p className="text-lg leading-relaxed text-muted-foreground">
      {content.text}
    </p>
  )
}

function QuoteBlock({ content }: { content: any }) {
  const { text, author, source } = content

  return (
    <Card className="border-l-4 border-l-primary">
      <CardContent className="pt-6">
        <div className="flex items-start space-x-4">
          <Quote className="h-8 w-8 text-primary flex-shrink-0 mt-1" />
          <div className="space-y-2">
            <blockquote className="text-lg italic">
              "{text}"
            </blockquote>
            {(author || source) && (
              <cite className="text-sm text-muted-foreground">
                {author && <span>— {author}</span>}
                {source && <span>, {source}</span>}
              </cite>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Media Blocks
function ImageBlock({ content }: { content: any }) {
  const { src, alt, caption, width, height, alignment = 'center' } = content

  return (
    <figure className={cn(
      "space-y-2",
      alignment === 'left' && "text-left",
      alignment === 'center' && "text-center",
      alignment === 'right' && "text-right"
    )}>
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={cn(
          "rounded-lg",
          alignment === 'left' && "mr-auto",
          alignment === 'center' && "mx-auto",
          alignment === 'right' && "ml-auto"
        )}
      />
      {caption && (
        <figcaption className="text-sm text-muted-foreground">
          {caption}
        </figcaption>
      )}
    </figure>
  )
}

function GalleryBlock({ content }: { content: any }) {
  const { images, columns = 3 } = content

  return (
    <div className={cn(
      "grid gap-4",
      columns === 2 && "grid-cols-1 md:grid-cols-2",
      columns === 3 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
      columns === 4 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
    )}>
      {images?.map((image: any, index: number) => (
        <div key={index} className="space-y-2">
          <img
            src={image.src}
            alt={image.alt}
            className="w-full h-48 object-cover rounded-lg"
          />
          {image.caption && (
            <p className="text-sm text-muted-foreground">{image.caption}</p>
          )}
        </div>
      ))}
    </div>
  )
}

function VideoBlock({ content }: { content: any }) {
  const { src, poster, caption, autoplay = false, controls = true } = content

  return (
    <figure className="space-y-2">
      <div className="relative">
        <video
          src={src}
          poster={poster}
          autoPlay={autoplay}
          controls={controls}
          className="w-full rounded-lg"
        />
        <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg opacity-0 hover:opacity-100 transition-opacity">
          <Play className="h-12 w-12 text-white" />
        </div>
      </div>
      {caption && (
        <figcaption className="text-sm text-muted-foreground text-center">
          {caption}
        </figcaption>
      )}
    </figure>
  )
}

// Code Block
function CodeBlock({ content }: { content: any }) {
  const { code, language, filename, showLineNumbers = false } = content

  return (
    <Card>
      {filename && (
        <CardHeader className="pb-2">
          <div className="flex items-center space-x-2">
            <Code className="h-4 w-4" />
            <span className="text-sm font-medium">{filename}</span>
            {language && (
              <Badge variant="secondary" className="text-xs">
                {language}
              </Badge>
            )}
          </div>
        </CardHeader>
      )}
      <CardContent className="pt-2">
        <pre className={cn(
          "overflow-x-auto p-4 rounded bg-muted text-sm",
          showLineNumbers && "pl-12"
        )}>
          <code className={language ? `language-${language}` : ''}>
            {code}
          </code>
        </pre>
      </CardContent>
    </Card>
  )
}

// List Block
function ListBlock({ content }: { content: any }) {
  const { items, ordered = false, style = 'default' } = content

  const ListTag = ordered ? 'ol' : 'ul'

  return (
    <ListTag className={cn(
      "space-y-2 pl-6",
      ordered ? "list-decimal" : "list-disc",
      style === 'checklist' && "list-none pl-0 space-y-3"
    )}>
      {items?.map((item: any, index: number) => (
        <li key={index} className={cn(
          style === 'checklist' && "flex items-start space-x-2"
        )}>
          {style === 'checklist' && (
            <input
              type="checkbox"
              checked={item.checked}
              readOnly
              className="mt-1"
            />
          )}
          <span>{item.text || item}</span>
        </li>
      ))}
    </ListTag>
  )
}

// Table Block
function TableBlock({ content }: { content: any }) {
  const { headers, rows, caption } = content

  return (
    <Card>
      {caption && (
        <CardHeader>
          <CardTitle>{caption}</CardTitle>
        </CardHeader>
      )}
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            {headers && (
              <thead className="bg-muted">
                <tr>
                  {headers.map((header: string, index: number) => (
                    <th key={index} className="px-4 py-2 text-left font-medium">
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
            )}
            <tbody>
              {rows?.map((row: string[], rowIndex: number) => (
                <tr key={rowIndex} className="border-t">
                  {row.map((cell, cellIndex) => (
                    <td key={cellIndex} className="px-4 py-2">
                      {cell}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  )
}

// UI Blocks
function SeparatorBlock({ content }: { content: any }) {
  const { style = 'line', spacing = 'medium' } = content

  return (
    <div className={cn(
      "flex items-center justify-center",
      spacing === 'small' && "py-4",
      spacing === 'medium' && "py-8",
      spacing === 'large' && "py-12"
    )}>
      {style === 'line' && <Separator className="w-full" />}
      {style === 'dots' && (
        <div className="flex space-x-2">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="w-2 h-2 bg-muted-foreground rounded-full" />
          ))}
        </div>
      )}
      {style === 'star' && <Star className="h-6 w-6 text-muted-foreground" />}
    </div>
  )
}

function ButtonBlock({ content }: { content: any }) {
  const { text, href, variant = 'default', size = 'default', icon } = content

  return (
    <div className="flex justify-center">
      <Button
        variant={variant}
        size={size}
        asChild={!!href}
      >
        {href ? (
          <a href={href} className="flex items-center space-x-2">
            {icon && <span className="w-4 h-4">{icon}</span>}
            <span>{text}</span>
          </a>
        ) : (
          <span className="flex items-center space-x-2">
            {icon && <span className="w-4 h-4">{icon}</span>}
            <span>{text}</span>
          </span>
        )}
      </Button>
    </div>
  )
}

function CardBlock({ content }: { content: any }) {
  const { title, description, image, link, badge } = content

  return (
    <Card className="hover:shadow-lg transition-shadow">
      {image && (
        <div className="aspect-video overflow-hidden rounded-t-lg">
          <img
            src={image.src}
            alt={image.alt}
            className="w-full h-full object-cover"
          />
        </div>
      )}
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <CardTitle>{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          {badge && (
            <Badge variant="secondary">{badge}</Badge>
          )}
        </div>
      </CardHeader>
      {link && (
        <CardContent className="pt-0">
          <Button variant="outline" asChild>
            <a href={link.href}>
              {link.text}
              <ExternalLink className="ml-2 h-4 w-4" />
            </a>
          </Button>
        </CardContent>
      )}
    </Card>
  )
}

function GridBlock({ content }: { content: any }) {
  const { items, columns = 3 } = content

  return (
    <div className={cn(
      "grid gap-6",
      columns === 2 && "grid-cols-1 md:grid-cols-2",
      columns === 3 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
      columns === 4 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
    )}>
      {items?.map((item: any, index: number) => (
        <div key={index}>
          <BlockRenderer block={{ ...item, id: `${item.id}-${index}` }} />
        </div>
      ))}
    </div>
  )
}

// Content Blocks
function TestimonialBlock({ content }: { content: any }) {
  const { quote, author, role, company, avatar } = content

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <blockquote className="text-lg italic">
            "{quote}"
          </blockquote>
          <div className="flex items-center space-x-3">
            <Avatar>
              <AvatarImage src={avatar} alt={author} />
              <AvatarFallback>
                {author?.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{author}</div>
              <div className="text-sm text-muted-foreground">
                {role}{company && ` at ${company}`}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function TeamBlock({ content }: { content: any }) {
  const { members, columns = 3 } = content

  return (
    <div className={cn(
      "grid gap-6",
      columns === 2 && "grid-cols-1 md:grid-cols-2",
      columns === 3 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
      columns === 4 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
    )}>
      {members?.map((member: any, index: number) => (
        <Card key={index} className="text-center">
          <CardContent className="pt-6">
            <Avatar className="w-24 h-24 mx-auto mb-4">
              <AvatarImage src={member.avatar} alt={member.name} />
              <AvatarFallback>
                {member.name?.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <h3 className="font-semibold text-lg">{member.name}</h3>
            <p className="text-muted-foreground">{member.role}</p>
            {member.bio && (
              <p className="text-sm mt-2">{member.bio}</p>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

function PricingBlock({ content }: { content: any }) {
  const { plans, columns = 3 } = content

  return (
    <div className={cn(
      "grid gap-6",
      columns === 2 && "grid-cols-1 md:grid-cols-2",
      columns === 3 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
    )}>
      {plans?.map((plan: any, index: number) => (
        <Card key={index} className={cn(
          "relative",
          plan.featured && "border-primary shadow-lg scale-105"
        )}>
          {plan.featured && (
            <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2">
              Most Popular
            </Badge>
          )}
          <CardHeader className="text-center">
            <CardTitle>{plan.name}</CardTitle>
            <div className="text-3xl font-bold">
              ${plan.price}
              <span className="text-sm font-normal text-muted-foreground">
                /{plan.period}
              </span>
            </div>
            {plan.description && (
              <CardDescription>{plan.description}</CardDescription>
            )}
          </CardHeader>
          <CardContent className="space-y-4">
            <ul className="space-y-2">
              {plan.features?.map((feature: string, featureIndex: number) => (
                <li key={featureIndex} className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  <span className="text-sm">{feature}</span>
                </li>
              ))}
            </ul>
            <Button className="w-full" variant={plan.featured ? 'default' : 'outline'}>
              {plan.buttonText || 'Get Started'}
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

function FAQBlock({ content }: { content: any }) {
  const { questions } = content

  return (
    <div className="space-y-4">
      {questions?.map((faq: any, index: number) => (
        <Card key={index}>
          <CardHeader>
            <CardTitle className="text-lg">{faq.question}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">{faq.answer}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

function ContactBlock({ content }: { content: any }) {
  const { title, description, contacts } = content

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="space-y-4">
        {contacts?.map((contact: any, index: number) => (
          <div key={index} className="flex items-center space-x-3">
            {contact.type === 'email' && <Mail className="h-5 w-5 text-muted-foreground" />}
            {contact.type === 'phone' && <Phone className="h-5 w-5 text-muted-foreground" />}
            {contact.type === 'address' && <MapPin className="h-5 w-5 text-muted-foreground" />}
            <div>
              <div className="font-medium">{contact.label}</div>
              <div className="text-muted-foreground">{contact.value}</div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

function EmbedBlock({ content }: { content: any }) {
  const { html, src, type, title } = content

  if (html) {
    return (
      <div 
        className="w-full"
        dangerouslySetInnerHTML={{ __html: html }}
      />
    )
  }

  if (src) {
    return (
      <div className="aspect-video">
        <iframe
          src={src}
          title={title}
          className="w-full h-full rounded-lg"
          allowFullScreen
        />
      </div>
    )
  }

  return null
}

function FormBlock({ content }: { content: any }) {
  const { formId, title, description } = content

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        {/* Form component would be rendered here based on formId */}
        <p className="text-muted-foreground">
          Form component (ID: {formId}) would be rendered here
        </p>
      </CardContent>
    </Card>
  )
}

function UnknownBlock({ type, content }: { type: string, content: any }) {
  return (
    <Card className="border-dashed border-muted-foreground/50">
      <CardContent className="pt-6 text-center">
        <FileText className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
        <p className="text-muted-foreground">
          Unknown block type: <code className="bg-muted px-1 rounded">{type}</code>
        </p>
        <details className="mt-2">
          <summary className="cursor-pointer text-sm">Show content</summary>
          <pre className="text-xs bg-muted p-2 rounded mt-2 overflow-auto">
            {JSON.stringify(content, null, 2)}
          </pre>
        </details>
      </CardContent>
    </Card>
  )
}