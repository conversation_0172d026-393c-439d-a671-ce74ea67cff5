"use client"

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { PlusCircle, Trash2, Move, Settings, Eye, EyeOff, LayoutGrid } from "lucide-react"
import { useSiteHeaderEditor, ComponentPosition } from '@/hooks/use-site-header-editor'
import { cn } from "@/lib/utils"

export function HeaderEditor() {
  const {
    isVisible,
    show,
    hide,
    title,
    setTitle,
    components,
    addComponent,
    removeComponent,
    updateComponentPosition,
    containerStyle,
    updateContainerStyle,
    resetContainerStyle,
    isEditMode,
    toggleEditMode
  } = useSiteHeaderEditor()

  const [newButtonText, setNewButtonText] = useState('New Button')
  const [selectedPosition, setSelectedPosition] = useState<ComponentPosition>('right')

  // Function to add a new button
  const handleAddButton = () => {
    const buttonComponent = (
      <Button variant="outline" size="sm">
        {newButtonText}
      </Button>
    )
    addComponent(buttonComponent, selectedPosition)
    setNewButtonText('New Button')
  }

  if (!isEditMode) {
    return null
  }

  return (
    <Card className="w-full max-w-3xl mx-auto my-4">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Header Editor</span>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={toggleEditMode}
            className="h-8 px-2"
          >
            <Eye className="h-4 w-4 mr-1" />
            Exit Edit Mode
          </Button>
        </CardTitle>
        <CardDescription>
          Customize the site header appearance and components
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="components">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="components">Components</TabsTrigger>
            <TabsTrigger value="style">Style</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>
          
          {/* Components Tab */}
          <TabsContent value="components" className="space-y-4">
            <div className="space-y-4 pt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="new-button-text">Button Text</Label>
                  <Input
                    id="new-button-text"
                    value={newButtonText}
                    onChange={(e) => setNewButtonText(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="position">Position</Label>
                  <Select 
                    value={selectedPosition} 
                    onValueChange={(value) => setSelectedPosition(value as ComponentPosition)}
                  >
                    <SelectTrigger id="position">
                      <SelectValue placeholder="Select position" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="left">Left</SelectItem>
                      <SelectItem value="center">Center</SelectItem>
                      <SelectItem value="right">Right</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <Button onClick={handleAddButton} className="w-full">
                <PlusCircle className="h-4 w-4 mr-2" />
                Add Button
              </Button>
            </div>
            
            <Separator className="my-4" />
            
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Current Components</h3>
              {components.length === 0 ? (
                <p className="text-sm text-muted-foreground">No custom components added yet.</p>
              ) : (
                <div className="space-y-2">
                  {components.map((comp) => (
                    <div 
                      key={comp.id} 
                      className="flex items-center justify-between p-2 border rounded-md"
                    >
                      <div className="flex items-center gap-2">
                        <div className="bg-muted p-1 rounded">
                          {comp.component}
                        </div>
                        <span className="text-xs text-muted-foreground">
                          Position: {comp.position}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Select 
                          value={comp.position} 
                          onValueChange={(value) => 
                            updateComponentPosition(comp.id, value as ComponentPosition)
                          }
                        >
                          <SelectTrigger className="h-8 w-24">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="left">Left</SelectItem>
                            <SelectItem value="center">Center</SelectItem>
                            <SelectItem value="right">Right</SelectItem>
                          </SelectContent>
                        </Select>
                        <Button 
                          variant="destructive" 
                          size="icon" 
                          className="h-8 w-8"
                          onClick={() => removeComponent(comp.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
          
          {/* Style Tab */}
          <TabsContent value="style" className="space-y-4 pt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bg-color">Background Color</Label>
                <div className="flex gap-2">
                  <Input
                    id="bg-color"
                    type="color"
                    value={containerStyle.backgroundColor !== 'transparent' ? containerStyle.backgroundColor : '#ffffff'}
                    onChange={(e) => updateContainerStyle({ backgroundColor: e.target.value })}
                    className="w-12 h-8 p-1"
                  />
                  <Input
                    value={containerStyle.backgroundColor}
                    onChange={(e) => updateContainerStyle({ backgroundColor: e.target.value })}
                    className="flex-1"
                    placeholder="transparent, #fff, etc."
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="border-color">Border Color</Label>
                <div className="flex gap-2">
                  <Input
                    id="border-color"
                    type="color"
                    value={containerStyle.borderColor || '#e5e7eb'}
                    onChange={(e) => updateContainerStyle({ borderColor: e.target.value })}
                    className="w-12 h-8 p-1"
                  />
                  <Input
                    value={containerStyle.borderColor}
                    onChange={(e) => updateContainerStyle({ borderColor: e.target.value })}
                    className="flex-1"
                    placeholder="#e5e7eb"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="border-width">Border Width</Label>
                <Input
                  id="border-width"
                  value={containerStyle.borderWidth}
                  onChange={(e) => updateContainerStyle({ borderWidth: e.target.value })}
                  placeholder="1px"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="height">Height</Label>
                <Input
                  id="height"
                  value={containerStyle.height}
                  onChange={(e) => updateContainerStyle({ height: e.target.value })}
                  placeholder="56px"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="padding">Padding</Label>
                <Input
                  id="padding"
                  value={containerStyle.padding}
                  onChange={(e) => updateContainerStyle({ padding: e.target.value })}
                  placeholder="0"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="margin">Margin</Label>
                <Input
                  id="margin"
                  value={containerStyle.margin}
                  onChange={(e) => updateContainerStyle({ margin: e.target.value })}
                  placeholder="0"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="box-shadow">Box Shadow</Label>
                <Input
                  id="box-shadow"
                  value={containerStyle.boxShadow}
                  onChange={(e) => updateContainerStyle({ boxShadow: e.target.value })}
                  placeholder="none"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="custom-classes">Custom Classes</Label>
                <Input
                  id="custom-classes"
                  value={containerStyle.customClasses}
                  onChange={(e) => updateContainerStyle({ customClasses: e.target.value })}
                  placeholder="Additional CSS classes"
                />
              </div>
            </div>
            
            <Button 
              variant="outline" 
              onClick={resetContainerStyle}
              className="mt-4"
            >
              Reset to Default Style
            </Button>
          </TabsContent>
          
          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-4 pt-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="title">Header Title</Label>
                  <p className="text-sm text-muted-foreground">
                    Set a custom title for the header (overrides breadcrumbs)
                  </p>
                </div>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-[250px]"
                />
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Header Visibility</Label>
                  <p className="text-sm text-muted-foreground">
                    Show or hide the header component
                  </p>
                </div>
                <Switch
                  checked={isVisible}
                  onCheckedChange={(checked) => checked ? show() : hide()}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={toggleEditMode}>
          Cancel
        </Button>
        <Button onClick={toggleEditMode}>
          Save Changes
        </Button>
      </CardFooter>
    </Card>
  )
}