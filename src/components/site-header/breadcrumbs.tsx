"use client"

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { cn } from "@/lib/utils"
import { ChevronRight, Home } from 'lucide-react'
import { Fragment } from 'react'

type Breadcrumb = {
  label: string
  href: string
  active?: boolean
}

export function Breadcrumbs() {
  const pathname = usePathname()
  
  // Generate breadcrumbs from the current path
  const breadcrumbs = (): Breadcrumb[] => {
    const paths = pathname.split('/').filter(Boolean)
    
    // Don't show breadcrumbs on the home page
    if (paths.length === 0) return []

    // If we're on the admin home page, just show the home breadcrumb
    if (pathname === '/admin') {
      return [
        { label: 'Home', href: '/admin', active: true }
      ]
    }

    // Build breadcrumbs array, skipping the first 'admin' path since it's our home
    const crumbs = paths.slice(1).map((path, i) => {
      const href = `/${paths.slice(0, i + 2).join('/')}`
      return {
        label: path.charAt(0).toUpperCase() + path.slice(1).replace(/-/g, ' '),
        href,
        active: i === paths.slice(1).length - 1
      }
    })

    // Add home as the first breadcrumb
    return [
      { label: 'Home', href: '/admin', active: false },
      ...crumbs
    ]
  }

  return (
    <nav className="flex items-center text-sm" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
        {breadcrumbs().map((breadcrumb, i) => (
          <Fragment key={breadcrumb.href}>
            {i > 0 && (
              <ChevronRight className="mx-1 h-4 w-4 text-muted-foreground" />
            )}
            <li>
              <div className="flex items-center">
                {i === 0 && <Home className="mr-2 h-4 w-4" />}
                <Link
                  href={breadcrumb.href}
                  className={cn(
                    'inline-flex items-center text-sm font-medium',
                    breadcrumb.active
                      ? 'text-foreground'
                      : 'text-muted-foreground hover:text-foreground',
                    'transition-colors duration-200'
                  )}
                  aria-current={breadcrumb.active ? 'page' : undefined}
                >
                  {breadcrumb.label}
                </Link>
              </div>
            </li>
          </Fragment>
        ))}
      </ol>
    </nav>
  )
}
