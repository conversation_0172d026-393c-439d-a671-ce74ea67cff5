"use client"

import { useEffect, useState } from 'react'
import { Command as CommandPrimitive } from 'cmdk'
import { Search, Command as CommandIcon, X } from 'lucide-react'
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"

const COMMANDS = [
  {
    name: 'Dashboard',
    icon: 'LayoutDashboard',
    shortcut: '⌘D',
    href: '/admin'
  },
  {
    name: 'New Page',
    icon: 'FilePlus',
    shortcut: '⌘N',
    href: '/admin/pages/new'
  },
  {
    name: 'New Blog Post',
    icon: 'PenSquare',
    shortcut: '⌘B',
    href: '/admin/blog/new'
  },
  {
    name: 'Media Library',
    icon: 'Image',
    shortcut: '⌘M',
    href: '/admin/media'
  },
  {
    name: 'Setting<PERSON>',
    icon: 'Settings',
    shortcut: '⌘,',
    href: '/admin/settings'
  },
]

export function CommandPalette() {
  const [open, setOpen] = useState(false)

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }
    document.addEventListener('keydown', down)
    return () => document.removeEventListener('keydown', down)
  }, [])

  return (
    <>
      <Button
        variant="outline"
        className={cn(
          "relative h-9 w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64"
        )}
        onClick={() => setOpen(true)}
      >
        <Search className="h-4 w-4" />
        <span className="hidden lg:inline-flex">Search...</span>
        <span className="sr-only">Search</span>
        <kbd className="pointer-events-none absolute right-1.5 top-1.5 hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>

      <CommandDialog open={open} onOpenChange={setOpen}>
        <Command className="rounded-lg border shadow-md">
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <CommandPrimitive.Input
              placeholder="Type a command or search..."
              className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
            />
            <Button
              variant="ghost"
              size="sm"
              className="ml-2 h-8 w-8 p-0"
              onClick={() => setOpen(false)}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </div>
          <CommandList>
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup heading="Suggestions">
              {COMMANDS.map((command) => (
                <CommandItem
                  key={command.href}
                  onSelect={() => {
                    window.location.href = command.href
                    setOpen(false)
                  }}
                  className="cursor-pointer"
                >
                  <CommandIcon className="mr-2 h-4 w-4" />
                  <span>{command.name}</span>
                  <div className="ml-auto flex items-center gap-1">
                    <span className="text-xs text-muted-foreground">
                      {command.shortcut}
                    </span>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </CommandDialog>
    </>
  )
}
