"use client"

import { useState } from 'react'
import { usePathname } from 'next/navigation'
import { 
  IconCirclePlusFilled, 
  IconMail, 
  IconChevronRight,
  IconChevronDown,
  type Icon 
} from "@tabler/icons-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"
import Link from "next/link"

interface NavItem {
  title: string
  url: string
  icon?: Icon
  badge?: string
  items?: Array<{
    title: string
    url: string
    icon?: Icon
    description?: string
  }>
}

export function NavMain({
  items,
}: {
  items: NavItem[]
}) {
  const pathname = usePathname()
  const [openItems, setOpenItems] = useState<string[]>([])

  const isActive = (url: string) => {
    if (url === "/admin") {
      return pathname === url
    }
    return pathname.startsWith(url) && url !== "/"
  }

  const hasActiveChild = (item: NavItem) => {
    if (!item.items) return false
    return item.items.some(child => isActive(child.url))
  }

  const toggleItem = (title: string) => {
    setOpenItems(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    )
  }

  // Auto-expand items with active children
  const isExpanded = (item: NavItem) => {
    return openItems.includes(item.title) || hasActiveChild(item)
  }

  return (
    <SidebarGroup>
      <SidebarGroupContent className="flex flex-col gap-2">
        {/* Quick Actions */}
        <SidebarMenu>
          <SidebarMenuItem className="flex items-center gap-2">
            <Link href="/admin/pages/new" className="flex-1">
              <SidebarMenuButton
                tooltip="Create New Page"
                className="bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground min-w-8 duration-200 ease-linear"
              >
                <IconCirclePlusFilled />
                <span>Quick Create</span>
              </SidebarMenuButton>
            </Link>
            <Button
              size="icon"
              className="size-8 group-data-[collapsible=icon]:opacity-0"
              variant="outline"
              asChild
            >
              <Link href="/admin/messages">
                <IconMail />
                <span className="sr-only">Messages</span>
              </Link>
            </Button>
          </SidebarMenuItem>
        </SidebarMenu>

        {/* Main Navigation */}
        <SidebarMenu>
          {items.map((item) => {
            const active = isActive(item.url)
            const hasChildren = item.items && item.items.length > 0
            const expanded = isExpanded(item)

            if (hasChildren) {
              return (
                <Collapsible
                  key={item.title}
                  open={expanded}
                  onOpenChange={() => toggleItem(item.title)}
                  className="group/collapsible"
                >
                  <SidebarMenuItem>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton
                        tooltip={item.title}
                        className={cn(
                          "w-full justify-between",
                          active || hasActiveChild(item)
                            ? "bg-primary/10 text-primary font-medium" 
                            : "text-muted-foreground hover:bg-muted/50"
                        )}
                      >
                        <div className="flex items-center gap-2">
                          {item.icon && (
                            <item.icon 
                              className={cn(
                                "h-4 w-4", 
                                active || hasActiveChild(item) ? "text-primary" : ""
                              )} 
                            />
                          )}
                          <span>{item.title}</span>
                          {item.badge && (
                            <Badge 
                              variant="secondary" 
                              className="ml-auto text-xs px-1.5 py-0.5 h-5"
                            >
                              {item.badge}
                            </Badge>
                          )}
                        </div>
                        {expanded ? (
                          <IconChevronDown className="h-3 w-3 transition-transform" />
                        ) : (
                          <IconChevronRight className="h-3 w-3 transition-transform" />
                        )}
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <SidebarMenuSub>
                        {item.items?.map((subItem) => {
                          const subActive = isActive(subItem.url)
                          return (
                            <SidebarMenuSubItem key={subItem.title}>
                              <SidebarMenuSubButton 
                                asChild
                                className={cn(
                                  subActive 
                                    ? "bg-primary/10 text-primary font-medium" 
                                    : "text-muted-foreground hover:bg-muted/50"
                                )}
                              >
                                <Link href={subItem.url}>
                                  {subItem.icon && (
                                    <subItem.icon 
                                      className={cn(
                                        "h-3 w-3", 
                                        subActive ? "text-primary" : ""
                                      )} 
                                    />
                                  )}
                                  <span>{subItem.title}</span>
                                </Link>
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                          )
                        })}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </SidebarMenuItem>
                </Collapsible>
              )
            }

            return (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton 
                  asChild
                  tooltip={item.title}
                  className={cn(
                    active 
                      ? "bg-primary/10 text-primary font-medium" 
                      : "text-muted-foreground hover:bg-muted/50"
                  )}
                >
                  <Link href={item.url}>
                    {item.icon && (
                      <item.icon 
                        className={cn(
                          "h-4 w-4", 
                          active ? "text-primary" : ""
                        )} 
                      />
                    )}
                    <span>{item.title}</span>
                    {item.badge && (
                      <Badge 
                        variant="secondary" 
                        className="ml-auto text-xs px-1.5 py-0.5 h-5"
                      >
                        {item.badge}
                      </Badge>
                    )}
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            )
          })}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
