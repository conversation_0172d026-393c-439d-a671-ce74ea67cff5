'use client'

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { FileText, Pencil, Trash2, Search, ChevronDown, X, Plus, Filter, MoreHorizontal, Eye } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { useToast } from "@/hooks/use-toast"
import { BlogPost, BlogCategory, BlogPostFilters, BlogPostListResponse } from "@/types/blog"
import { PostStatus } from "@prisma/client"
import { Skeleton } from "@/components/ui/skeleton"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"

interface EnhancedBlogListProps {
  initialData?: BlogPostListResponse
  categories: BlogCategory[]
  onRefresh?: () => void
}

export function EnhancedBlogList({ initialData, categories, onRefresh }: EnhancedBlogListProps) {
  const { toast } = useToast()
  const [posts, setPosts] = useState<BlogPost[]>(initialData?.posts || [])
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({
    currentPage: initialData?.currentPage || 1,
    totalPages: initialData?.totalPages || 1,
    total: initialData?.total || 0,
    hasNextPage: initialData?.hasNextPage || false,
    hasPreviousPage: initialData?.hasPreviousPage || false
  })
  
  const [filters, setFilters] = useState<BlogPostFilters>({
    status: 'all',
    search: '',
    categoryId: '',
    dateFrom: undefined,
    dateTo: undefined
  })
  
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [postToDelete, setPostToDelete] = useState<string | null>(null)

  const fetchPosts = async (page = 1, newFilters = filters) => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        perPage: '10',
        includeUnpublished: 'true',
        includeDrafts: 'true',
        ...(newFilters.status !== 'all' && { status: newFilters.status }),
        ...(newFilters.search && { search: newFilters.search }),
        ...(newFilters.categoryId && { categoryId: newFilters.categoryId }),
        ...(selectedCategories.length > 0 && { categoryId: selectedCategories[0] }),
      })

      const response = await fetch(`/api/blog?${params}`)
      const result = await response.json()

      if (result.success) {
        setPosts(result.data.posts)
        setPagination({
          currentPage: result.data.currentPage,
          totalPages: result.data.totalPages,
          total: result.data.total,
          hasNextPage: result.data.hasNextPage,
          hasPreviousPage: result.data.hasPreviousPage
        })
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch blog posts",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error fetching posts:', error)
      toast({
        title: "Error",
        description: "Failed to fetch blog posts",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (searchTerm: string) => {
    const newFilters = { ...filters, search: searchTerm }
    setFilters(newFilters)
    fetchPosts(1, newFilters)
  }

  const handleStatusFilter = (status: string) => {
    const newFilters = { ...filters, status: status as any }
    setFilters(newFilters)
    fetchPosts(1, newFilters)
  }

  const toggleCategory = (categoryId: string) => {
    const newCategories = selectedCategories.includes(categoryId)
      ? selectedCategories.filter(c => c !== categoryId)
      : [...selectedCategories, categoryId]
    
    setSelectedCategories(newCategories)
    const newFilters = { ...filters, categoryId: newCategories[0] || '' }
    setFilters(newFilters)
    fetchPosts(1, newFilters)
  }

  const handleDeletePost = async (postId: string) => {
    try {
      const response = await fetch(`/api/blog/${postId}`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Success",
          description: "Blog post deleted successfully",
        })
        fetchPosts(pagination.currentPage)
        onRefresh?.()
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to delete blog post",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error deleting post:', error)
      toast({
        title: "Error",
        description: "Failed to delete blog post",
        variant: "destructive",
      })
    }
  }

  const getStatusBadge = (status: PostStatus) => {
    switch (status) {
      case PostStatus.PUBLISHED:
        return <Badge variant="default">Published</Badge>
      case PostStatus.DRAFT:
        return <Badge variant="outline">Draft</Badge>
      case PostStatus.SCHEDULED:
        return <Badge variant="secondary">Scheduled</Badge>
      case PostStatus.ARCHIVED:
        return <Badge variant="destructive">Archived</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const handlePageChange = (page: number) => {
    fetchPosts(page)
  }

  useEffect(() => {
    if (!initialData) {
      fetchPosts()
    }
  }, [])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Blog Posts</h2>
          <p className="text-muted-foreground">
            Manage your blog posts, categories, and content
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" asChild>
            <Link href="/admin/blog/categories">
              <Filter className="mr-2 h-4 w-4" />
              Manage Categories
            </Link>
          </Button>
          <Button asChild>
            <Link href="/admin/blog/new">
              <Plus className="mr-2 h-4 w-4" />
              New Post
            </Link>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search posts by title, content, or excerpt..."
              className="pl-9"
              value={filters.search}
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
        </div>
        
        <div className="flex space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-1">
                <span>Categories</span>
                <ChevronDown className="h-4 w-4 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              {categories.map((category) => (
                <DropdownMenuCheckboxItem
                  key={category.id}
                  checked={selectedCategories.includes(category.id)}
                  onCheckedChange={() => toggleCategory(category.id)}
                >
                  {category.name}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-1">
                <span>
                  {filters.status === 'all' && "All Posts"}
                  {filters.status === PostStatus.PUBLISHED && "Published"}
                  {filters.status === PostStatus.DRAFT && "Drafts"}
                  {filters.status === PostStatus.SCHEDULED && "Scheduled"}
                  {filters.status === PostStatus.ARCHIVED && "Archived"}
                </span>
                <ChevronDown className="h-4 w-4 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuCheckboxItem
                checked={filters.status === 'all'}
                onCheckedChange={() => handleStatusFilter('all')}
              >
                All Posts
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filters.status === PostStatus.PUBLISHED}
                onCheckedChange={() => handleStatusFilter(PostStatus.PUBLISHED)}
              >
                Published
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filters.status === PostStatus.DRAFT}
                onCheckedChange={() => handleStatusFilter(PostStatus.DRAFT)}
              >
                Drafts
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filters.status === PostStatus.SCHEDULED}
                onCheckedChange={() => handleStatusFilter(PostStatus.SCHEDULED)}
              >
                Scheduled
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Active Filters */}
      {selectedCategories.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedCategories.map(categoryId => {
            const category = categories.find(c => c.id === categoryId)
            return category ? (
              <Badge key={categoryId} variant="secondary" className="gap-1">
                {category.name}
                <button 
                  onClick={() => toggleCategory(categoryId)}
                  className="ml-1 rounded-full hover:bg-muted"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ) : null
          })}
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-6 px-2 text-xs"
            onClick={() => {
              setSelectedCategories([])
              const newFilters = { ...filters, categoryId: '' }
              setFilters(newFilters)
              fetchPosts(1, newFilters)
            }}
          >
            Clear all
          </Button>
        </div>
      )}

      {/* Posts Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[400px]">Title</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Author</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              // Loading skeleton
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <div className="flex items-center space-x-4">
                      <Skeleton className="h-12 w-16 rounded" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-[200px]" />
                        <Skeleton className="h-3 w-[150px]" />
                      </div>
                    </div>
                  </TableCell>
                  <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-16" /></TableCell>
                </TableRow>
              ))
            ) : posts.length > 0 ? (
              posts.map((post) => (
                <TableRow key={post.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-4">
                      {post.featuredImage ? (
                        <div className="relative h-12 w-16 flex-shrink-0 overflow-hidden rounded-md border">
                          <img
                            src={post.featuredImage}
                            alt={post.title}
                            className="h-full w-full object-cover"
                          />
                        </div>
                      ) : (
                        <div className="flex h-12 w-16 flex-shrink-0 items-center justify-center rounded-md bg-muted">
                          <FileText className="h-5 w-5 text-muted-foreground" />
                        </div>
                      )}
                      <div className="space-y-1 min-w-0 flex-1">
                        <div className="font-medium truncate">{post.title}</div>
                        {post.excerpt && (
                          <div className="line-clamp-1 text-sm text-muted-foreground">
                            {post.excerpt}
                          </div>
                        )}
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          {post.readTime && <span>{post.readTime} min read</span>}
                          {post._count?.comments && (
                            <span>• {post._count.comments} comments</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(post.status)}
                  </TableCell>
                  <TableCell>
                    {post.category ? (
                      <Badge
                        variant="outline"
                        style={{
                          backgroundColor: post.category.color ? `${post.category.color}20` : undefined,
                          borderColor: post.category.color || undefined
                        }}
                      >
                        {post.category.name}
                      </Badge>
                    ) : (
                      <span className="text-muted-foreground">No category</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {post.author.image && (
                        <img
                          src={post.author.image}
                          alt={post.author.name || 'Author'}
                          className="h-6 w-6 rounded-full"
                        />
                      )}
                      <span className="text-sm">{post.author.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {post.publishedAt ? (
                        <>
                          <div>{format(new Date(post.publishedAt), 'MMM d, yyyy')}</div>
                          <div className="text-xs text-muted-foreground">
                            {format(new Date(post.publishedAt), 'h:mm a')}
                          </div>
                        </>
                      ) : (
                        <>
                          <div>{format(new Date(post.createdAt), 'MMM d, yyyy')}</div>
                          <div className="text-xs text-muted-foreground">
                            {format(new Date(post.createdAt), 'h:mm a')}
                          </div>
                        </>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/blog/${post.slug}`} target="_blank">
                            <Eye className="mr-2 h-4 w-4" />
                            View Post
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/blog/${post.id}/edit`}>
                            <Pencil className="mr-2 h-4 w-4" />
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={() => {
                            setPostToDelete(post.id)
                            setDeleteDialogOpen(true)
                          }}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <FileText className="h-8 w-8 text-muted-foreground" />
                    <div className="text-sm text-muted-foreground">
                      {filters.search || selectedCategories.length > 0 || filters.status !== 'all'
                        ? "No posts found matching your filters."
                        : "No blog posts yet. Create your first post to get started."}
                    </div>
                    {(!filters.search && selectedCategories.length === 0 && filters.status === 'all') && (
                      <Button asChild size="sm">
                        <Link href="/admin/blog/new">
                          <Plus className="mr-2 h-4 w-4" />
                          Create First Post
                        </Link>
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {((pagination.currentPage - 1) * 10) + 1} to {Math.min(pagination.currentPage * 10, pagination.total)} of {pagination.total} posts
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={!pagination.hasPreviousPage || loading}
            >
              Previous
            </Button>
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                const page = i + 1
                return (
                  <Button
                    key={page}
                    variant={page === pagination.currentPage ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(page)}
                    disabled={loading}
                  >
                    {page}
                  </Button>
                )
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={!pagination.hasNextPage || loading}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the blog post
              and remove all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (postToDelete) {
                  handleDeletePost(postToDelete)
                  setPostToDelete(null)
                  setDeleteDialogOpen(false)
                }
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
