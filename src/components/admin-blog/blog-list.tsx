import { format } from "date-fns"
import { <PERSON>T<PERSON>t, <PERSON><PERSON><PERSON>, Trash2, Search, ChevronDown, X } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface BlogPost {
  id: string
  title: string
  slug: string
  published: boolean
  createdAt: string
  updatedAt: string
  excerpt: string
  categories: string[]
  readTime: number
  featuredImage?: string
}

const mockBlogPosts: BlogPost[] = [
  {
    id: "1",
    title: "Getting Started with Next.js",
    slug: "getting-started-with-nextjs",
    published: true,
    createdAt: "2023-05-15T10:30:00Z",
    updatedAt: "2023-05-15T10:30:00Z",
    excerpt: "Learn the basics of Next.js and how to build modern web applications.",
    categories: ["web-dev", "programming"],
    readTime: 5,
    featuredImage: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80",
  },
  {
    id: "2",
    title: "Advanced TypeScript Patterns",
    slug: "advanced-typescript-patterns",
    published: false,
    createdAt: "2023-05-10T14:45:00Z",
    updatedAt: "2023-05-12T09:15:00Z",
    excerpt: "Explore advanced TypeScript patterns to improve your code quality.",
    categories: ["programming"],
    readTime: 8,
  },
  {
    id: "3",
    title: "The Future of Web Development",
    slug: "future-of-web-dev",
    published: true,
    createdAt: "2023-05-05T08:15:00Z",
    updatedAt: "2023-05-05T08:15:00Z",
    excerpt: "A look at emerging trends and technologies in web development.",
    categories: ["web-dev", "technology"],
    readTime: 7,
    featuredImage: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1469&q=80",
  },
]

const categories = [
  { label: "Technology", value: "technology" },
  { label: "Programming", value: "programming" },
  { label: "Web Development", value: "web-dev" },
  { label: "Mobile Development", value: "mobile-dev" },
  { label: "UI/UX", value: "ui-ux" },
  { label: "DevOps", value: "devops" },
  { label: "Cloud Computing", value: "cloud" },
  { label: "AI/ML", value: "ai-ml" },
] as const

export function BlogList() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [statusFilter, setStatusFilter] = useState<"all" | "published" | "drafts">("all")

  const filteredPosts = mockBlogPosts.filter((post) => {
    // Filter by search query
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase())
    
    // Filter by status
    const matchesStatus = statusFilter === "all" || 
      (statusFilter === "published" && post.published) ||
      (statusFilter === "drafts" && !post.published)
    
    // Filter by categories
    const matchesCategories = selectedCategories.length === 0 ||
      selectedCategories.some(cat => post.categories.includes(cat))
    
    return matchesSearch && matchesStatus && matchesCategories
  })

  const toggleCategory = (category: string) => {
    setSelectedCategories(prev => 
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <h2 className="text-2xl font-bold tracking-tight">Blog Posts</h2>
        <Button asChild>
          <Link href="/admin/blog/new">
            New Post
          </Link>
        </Button>
      </div>

      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search posts..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        
        <div className="flex space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-1">
                <span>Categories</span>
                <ChevronDown className="h-4 w-4 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              {categories.map((category) => (
                <DropdownMenuCheckboxItem
                  key={category.value}
                  checked={selectedCategories.includes(category.value)}
                  onCheckedChange={() => toggleCategory(category.value)}
                >
                  {category.label}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-1">
                <span>
                  {statusFilter === "all" && "All Posts"}
                  {statusFilter === "published" && "Published"}
                  {statusFilter === "drafts" && "Drafts"}
                </span>
                <ChevronDown className="h-4 w-4 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuCheckboxItem
                checked={statusFilter === "all"}
                onCheckedChange={() => setStatusFilter("all")}
              >
                All Posts
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={statusFilter === "published"}
                onCheckedChange={() => setStatusFilter("published")}
              >
                Published
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={statusFilter === "drafts"}
                onCheckedChange={() => setStatusFilter("drafts")}
              >
                Drafts
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {selectedCategories.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedCategories.map(category => (
            <Badge key={category} variant="secondary" className="gap-1">
              {categories.find(c => c.value === category)?.label}
              <button 
                onClick={() => toggleCategory(category)}
                className="ml-1 rounded-full hover:bg-muted"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-6 px-2 text-xs"
            onClick={() => setSelectedCategories([])}
          >
            Clear all
          </Button>
        </div>
      )}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[400px]">Title</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Categories</TableHead>
              <TableHead>Last Updated</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPosts.length > 0 ? (
              filteredPosts.map((post) => (
                <TableRow key={post.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-4">
                      {post.featuredImage ? (
                        <div className="relative h-12 w-16 flex-shrink-0 overflow-hidden rounded-md border">
                          <img
                            src={post.featuredImage}
                            alt={post.title}
                            className="h-full w-full object-cover"
                          />
                        </div>
                      ) : (
                        <div className="flex h-12 w-16 flex-shrink-0 items-center justify-center rounded-md bg-muted">
                          <FileText className="h-5 w-5 text-muted-foreground" />
                        </div>
                      )}
                      <div className="space-y-1">
                        <div className="font-medium">{post.title}</div>
                        <div className="line-clamp-1 text-sm text-muted-foreground">
                          {post.excerpt}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {post.published ? (
                      <Badge variant="default">Published</Badge>
                    ) : (
                      <Badge variant="outline">Draft</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {post.categories.map((category) => {
                        const categoryInfo = categories.find(c => c.value === category)
                        return categoryInfo ? (
                          <Badge key={category} variant="outline">
                            {categoryInfo.label}
                          </Badge>
                        ) : null
                      })}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-muted-foreground">
                      {format(new Date(post.updatedAt), 'MMM d, yyyy')}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {post.readTime} min read
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button variant="ghost" size="icon" asChild>
                        <Link href={`/admin/blog/edit/${post.id}`}>
                          <Pencil className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Link>
                      </Button>
                      <Button variant="ghost" size="icon" className="text-destructive hover:text-destructive">
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  No posts found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
