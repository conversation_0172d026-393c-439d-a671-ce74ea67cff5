// Blog-related TypeScript interfaces that align with Prisma schema and frontend expectations

import { PostStatus, MediaType, UserRole } from '@prisma/client'

// Base interfaces for common fields
export interface BaseEntity {
  id: string
  createdAt: string | Date
  updatedAt: string | Date
}

export interface SEOFields {
  seoTitle?: string
  seoDescription?: string
  seoKeywords?: string[]
}

// User interface for blog context
export interface BlogUser {
  id: string
  name: string | null
  email: string
  image: string | null
  role: UserRole
}

// Category interfaces
export interface BlogCategory extends BaseEntity {
  name: string
  slug: string
  description?: string
  color?: string
  image?: string
  parentId?: string
  parent?: BlogCategory | null
  children?: BlogCategory[]
  creator: BlogUser
  createdBy: string
  _count?: {
    posts: number
  }
}

export interface CreateCategoryData {
  name: string
  slug: string
  description?: string
  color?: string
  image?: string
  parentId?: string
}

// Tag interfaces
export interface BlogTag extends BaseEntity {
  name: string
  slug: string
  color?: string
  creator: Blog<PERSON><PERSON>
  createdBy: string
  _count?: {
    posts: number
  }
}

export interface CreateTagData {
  name: string
  slug: string
  color?: string
}

// Media interfaces for blog
export interface BlogMedia extends BaseEntity {
  filename: string
  originalName: string
  mimeType: string
  size: number
  width?: number
  height?: number
  url: string
  thumbnailUrl?: string
  alt?: string
  caption?: string
  type: MediaType
  uploader: BlogUser
  uploadedBy: string
}

// Comment interfaces
export interface BlogComment extends BaseEntity {
  content: string
  approved: boolean
  author: BlogUser
  authorId: string
  postId: string
  parentId?: string
  parent?: BlogComment | null
  replies?: BlogComment[]
}

export interface CreateCommentData {
  content: string
  postId: string
  parentId?: string
}

// Main blog post interfaces
export interface BlogPost extends BaseEntity, SEOFields {
  title: string
  slug: string
  excerpt?: string
  content?: string
  featuredImage?: string
  status: PostStatus
  publishedAt?: string | Date | null
  scheduledAt?: string | Date | null
  readTime?: number
  viewCount?: number
  author: BlogUser
  authorId: string
  category?: BlogCategory | null
  categoryId?: string | null
  tags: BlogTag[]
  comments?: BlogComment[]
  blocks?: BlogBlock[]
  _count?: {
    comments: number
    tags: number
  }
}

// Blog block interface for rich content
export interface BlogBlock extends BaseEntity {
  type: string
  data: any
  order: number
  creator: BlogUser
  createdBy: string
  updater?: BlogUser | null
  updatedBy?: string | null
  postId?: string | null
  pageId?: string | null
}

// Form data interfaces for creating/updating
export interface CreateBlogPostData {
  title: string
  slug: string
  excerpt?: string
  content?: string
  featuredImage?: string
  status?: PostStatus
  publishedAt?: string | Date | null
  scheduledAt?: string | Date | null
  categoryId?: string | null
  tags?: string[] // Array of tag IDs
  seoTitle?: string
  seoDescription?: string
  seoKeywords?: string[]
}

export interface UpdateBlogPostData extends Partial<CreateBlogPostData> {
  id: string
}

// Query and filter interfaces
export interface BlogPostFilters {
  status?: PostStatus | 'all'
  categoryId?: string
  tagIds?: string[]
  authorId?: string
  search?: string
  dateFrom?: string | Date
  dateTo?: string | Date
}

export interface BlogPostQueryOptions extends BlogPostFilters {
  page?: number
  perPage?: number
  orderBy?: 'createdAt' | 'updatedAt' | 'publishedAt' | 'title' | 'viewCount'
  order?: 'asc' | 'desc'
  includeUnpublished?: boolean
  includeDrafts?: boolean
}

export interface BlogPostListResponse {
  posts: BlogPost[]
  total: number
  totalPages: number
  currentPage: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

// Frontend-specific interfaces for display
export interface BlogPostCard {
  id: string
  title: string
  slug: string
  excerpt?: string
  featuredImage?: string
  publishedAt?: string | Date
  readTime?: number
  author: {
    name: string
    image?: string
  }
  category?: {
    name: string
    slug: string
    color?: string
  }
  tags: Array<{
    name: string
    slug: string
    color?: string
  }>
}

export interface BlogPostDetail extends BlogPost {
  relatedPosts?: BlogPostCard[]
  previousPost?: BlogPostCard | null
  nextPost?: BlogPostCard | null
}

// Analytics and statistics
export interface BlogAnalytics {
  totalPosts: number
  publishedPosts: number
  draftPosts: number
  totalViews: number
  totalComments: number
  popularPosts: BlogPostCard[]
  recentPosts: BlogPostCard[]
  topCategories: Array<BlogCategory & { postCount: number }>
  topTags: Array<BlogTag & { postCount: number }>
}

// Social sharing interfaces
export interface SocialShareData {
  url: string
  title: string
  description?: string
  image?: string
  hashtags?: string[]
}

// Search and pagination
export interface BlogSearchResult {
  posts: BlogPostCard[]
  categories: BlogCategory[]
  tags: BlogTag[]
  total: number
  query: string
}

export interface PaginationInfo {
  currentPage: number
  totalPages: number
  totalItems: number
  itemsPerPage: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

// Form validation schemas (for use with zod)
export interface BlogFormErrors {
  title?: string[]
  slug?: string[]
  excerpt?: string[]
  content?: string[]
  featuredImage?: string[]
  categoryId?: string[]
  tags?: string[]
  seoTitle?: string[]
  seoDescription?: string[]
  general?: string[]
}

// API response interfaces
export interface BlogApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  errors?: BlogFormErrors
}

export interface BlogApiListResponse<T = any> extends BlogApiResponse<T> {
  pagination?: PaginationInfo
}

// Component prop interfaces
export interface BlogListProps {
  posts: BlogPost[]
  loading?: boolean
  error?: string | null
  onLoadMore?: () => void
  hasMore?: boolean
}

export interface BlogCardProps {
  post: BlogPostCard
  variant?: 'default' | 'featured' | 'compact'
  showExcerpt?: boolean
  showCategory?: boolean
  showTags?: boolean
  showAuthor?: boolean
  showDate?: boolean
  showReadTime?: boolean
}

export interface BlogFilterProps {
  categories: BlogCategory[]
  tags: BlogTag[]
  selectedFilters: BlogPostFilters
  onFiltersChange: (filters: BlogPostFilters) => void
}

// Admin-specific interfaces
export interface AdminBlogStats {
  totalPosts: number
  publishedPosts: number
  draftPosts: number
  scheduledPosts: number
  totalCategories: number
  totalTags: number
  totalComments: number
  pendingComments: number
}

export interface BulkActionData {
  action: 'publish' | 'unpublish' | 'delete' | 'move-to-trash'
  postIds: string[]
  categoryId?: string // for bulk category assignment
}

// Blog editor interfaces
export interface BlogEditorProps {
  content?: string
  onChange: (content: string) => void
  placeholder?: string
  readOnly?: boolean
  className?: string
}

export interface BlogFormProps {
  initialData?: Partial<BlogPost>
  onSubmit: (data: CreateBlogPostData | UpdateBlogPostData) => Promise<void>
  onCancel?: () => void
  loading?: boolean
  categories: BlogCategory[]
  tags: BlogTag[]
}

// Blog management interfaces
export interface BlogManagementFilters {
  status: 'all' | 'published' | 'draft' | 'scheduled'
  category: string
  author: string
  dateRange: {
    from?: Date
    to?: Date
  }
  search: string
}

export interface BlogTableColumn {
  key: string
  label: string
  sortable?: boolean
  width?: string
}

// SEO and metadata interfaces
export interface BlogSEOData {
  title: string
  description: string
  keywords: string[]
  ogImage?: string
  canonicalUrl?: string
  structuredData?: any
}

// RSS and feed interfaces
export interface BlogFeedItem {
  title: string
  description: string
  link: string
  pubDate: Date
  author: string
  category?: string
  guid: string
}

export interface BlogFeedOptions {
  title: string
  description: string
  link: string
  language?: string
  copyright?: string
  managingEditor?: string
  webMaster?: string
  category?: string
  ttl?: number
}

// Export all status enums for convenience
export { PostStatus, MediaType, UserRole }
