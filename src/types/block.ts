// Block types for the page builder

export interface BlockImage {
  src: string;
  alt: string;
  fileId?: string; // For Appwrite storage
}

export interface BlockLink {
  href: string;
  text: string;
  className?: string;
  icon?: string;
}

export interface BlockText {
  content: string;
  type: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span';
  className?: string;
}

export interface BlockCounter {
  value: number;
  suffix?: string;
  label: string;
}

export interface BlockIconBox {
  icon: BlockImage;
  title: BlockText;
  description: BlockText;
  link?: BlockLink;
}

// Base block interface
export interface Block {
  id: string;
  type: string;
  componentName: string;
  title: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  content: any; // This will be specific to each block type
  styles?: {
    [key: string]: string;
  };
}

// Specific block types
export interface AboutBlock extends Block {
  type: 'about';
  content: {
    heading: BlockText;
    subheading: BlockText;
    description: BlockText;
    images: BlockImage[];
    counters: BlockCounter[];
    iconBoxes: BlockIconBox[];
    cta?: BlockLink;
  };
}

export interface ServiceBlock extends Block {
  type: 'service';
  content: {
    heading: BlockText;
    subheading: BlockText;
    description: BlockText;
    services: {
      icon: BlockImage;
      title: BlockText;
      description: BlockText;
      link?: BlockLink;
    }[];
  };
}

export interface TeamBlock extends Block {
  type: 'team';
  content: {
    heading: BlockText;
    subheading: BlockText;
    description: BlockText;
    members: {
      image: BlockImage;
      name: BlockText;
      position: BlockText;
      bio: BlockText;
      socialLinks?: BlockLink[];
    }[];
  };
}

export interface TestimonialBlock extends Block {
  type: 'testimonial';
  content: {
    heading: BlockText;
    subheading: BlockText;
    testimonials: {
      quote: BlockText;
      author: BlockText;
      position?: BlockText;
      company?: BlockText;
      image?: BlockImage;
      rating?: number;
    }[];
  };
}

// Union type for all block types
export interface FlexContainerBlock extends Block {
  type: 'flex-container';
  content: {
    children: string[]; // Array of block IDs
    styles: {
      display: 'flex';
      flexDirection?: 'row' | 'column';
      justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
      alignItems?: 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline';
      gap?: string;
      padding?: string;
      backgroundColor?: string;
      borderRadius?: string;
    };
  };
}

// Union type for all block types
export type BlockTypes = AboutBlock | ServiceBlock | TeamBlock | TestimonialBlock | FlexContainerBlock;

// Block component props
export interface BlockComponentProps {
  block: BlockTypes;
  isEditing?: boolean;
  onUpdate?: (updatedBlock: BlockTypes) => void;
}

// Block editor props
export interface BlockEditorProps {
  block: BlockTypes;
  onSave: (updatedBlock: BlockTypes) => void;
  onCancel: () => void;
}

// Block renderer props
export interface BlockRendererProps {
  blockId: string;
  fallback?: React.ReactNode;
}