import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { BlogService } from '@/lib/services/blog.service'
import { PostStatus } from '@prisma/client'

// Mock Prisma
vi.mock('@/lib/prisma', () => ({
  prisma: {
    post: {
      findMany: vi.fn(),
      findFirst: vi.fn(),
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn(),
    },
    category: {
      findMany: vi.fn(),
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn(),
    },
    tag: {
      findMany: vi.fn(),
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn(),
    },
    comment: {
      count: vi.fn(),
    },
  },
}))

const mockPost = {
  id: '1',
  title: 'Test Post',
  slug: 'test-post',
  excerpt: 'Test excerpt',
  content: 'Test content',
  status: PostStatus.PUBLISHED,
  createdAt: new Date(),
  updatedAt: new Date(),
  publishedAt: new Date(),
  readTime: 5,
  viewCount: 100,
  authorId: 'author-1',
  categoryId: 'category-1',
  author: {
    id: 'author-1',
    name: 'Test Author',
    email: '<EMAIL>',
    image: null,
    role: 'USER' as any,
  },
  category: {
    id: 'category-1',
    name: 'Test Category',
    slug: 'test-category',
    color: '#3b82f6',
    description: 'Test category description',
  },
  tags: [
    {
      id: 'tag-1',
      name: 'Test Tag',
      slug: 'test-tag',
      color: '#ef4444',
    },
  ],
  _count: {
    comments: 5,
    tags: 1,
  },
}

const mockCategory = {
  id: 'category-1',
  name: 'Test Category',
  slug: 'test-category',
  description: 'Test category description',
  color: '#3b82f6',
  createdAt: new Date(),
  updatedAt: new Date(),
  createdBy: 'user-1',
  creator: {
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
    image: null,
    role: 'USER' as any,
  },
  _count: {
    posts: 3,
  },
}

const mockTag = {
  id: 'tag-1',
  name: 'Test Tag',
  slug: 'test-tag',
  color: '#ef4444',
  createdAt: new Date(),
  updatedAt: new Date(),
  createdBy: 'user-1',
  creator: {
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
    image: null,
    role: 'USER' as any,
  },
  _count: {
    posts: 2,
  },
}

describe('BlogService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getPosts', () => {
    it('should return paginated posts with default options', async () => {
      const { prisma } = await import('@/lib/prisma')
      
      vi.mocked(prisma.post.findMany).mockResolvedValue([mockPost])
      vi.mocked(prisma.post.count).mockResolvedValue(1)

      const result = await BlogService.getPosts()

      expect(result).toEqual({
        posts: [mockPost],
        total: 1,
        totalPages: 1,
        currentPage: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      })

      expect(prisma.post.findMany).toHaveBeenCalledWith({
        where: { status: PostStatus.PUBLISHED },
        skip: 0,
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: expect.any(Object),
      })
    })

    it('should filter posts by search term', async () => {
      const { prisma } = await import('@/lib/prisma')
      
      vi.mocked(prisma.post.findMany).mockResolvedValue([mockPost])
      vi.mocked(prisma.post.count).mockResolvedValue(1)

      await BlogService.getPosts({ search: 'test' })

      expect(prisma.post.findMany).toHaveBeenCalledWith({
        where: {
          status: PostStatus.PUBLISHED,
          OR: [
            { title: { contains: 'test', mode: 'insensitive' } },
            { excerpt: { contains: 'test', mode: 'insensitive' } },
            { content: { contains: 'test', mode: 'insensitive' } },
          ],
        },
        skip: 0,
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: expect.any(Object),
      })
    })

    it('should filter posts by category', async () => {
      const { prisma } = await import('@/lib/prisma')
      
      vi.mocked(prisma.post.findMany).mockResolvedValue([mockPost])
      vi.mocked(prisma.post.count).mockResolvedValue(1)

      await BlogService.getPosts({ categoryId: 'category-1' })

      expect(prisma.post.findMany).toHaveBeenCalledWith({
        where: {
          status: PostStatus.PUBLISHED,
          categoryId: 'category-1',
        },
        skip: 0,
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: expect.any(Object),
      })
    })
  })

  describe('getPostById', () => {
    it('should return a post by ID', async () => {
      const { prisma } = await import('@/lib/prisma')
      
      vi.mocked(prisma.post.findFirst).mockResolvedValue(mockPost)

      const result = await BlogService.getPostById('1')

      expect(result).toEqual(mockPost)
      expect(prisma.post.findFirst).toHaveBeenCalledWith({
        where: { id: '1', status: PostStatus.PUBLISHED },
        include: expect.any(Object),
      })
    })

    it('should return null if post not found', async () => {
      const { prisma } = await import('@/lib/prisma')
      
      vi.mocked(prisma.post.findFirst).mockResolvedValue(null)

      const result = await BlogService.getPostById('nonexistent')

      expect(result).toBeNull()
    })
  })

  describe('createPost', () => {
    it('should create a new post', async () => {
      const { prisma } = await import('@/lib/prisma')
      
      vi.mocked(prisma.post.findUnique).mockResolvedValue(null) // No existing post with slug
      vi.mocked(prisma.post.create).mockResolvedValue(mockPost)

      const postData = {
        title: 'Test Post',
        slug: 'test-post',
        excerpt: 'Test excerpt',
        content: 'Test content',
        status: PostStatus.DRAFT,
      }

      const result = await BlogService.createPost(postData, 'author-1')

      expect(result).toEqual(mockPost)
      expect(prisma.post.create).toHaveBeenCalledWith({
        data: {
          ...postData,
          readTime: 1,
          authorId: 'author-1',
          tags: undefined,
        },
        include: expect.any(Object),
      })
    })

    it('should throw error if slug already exists', async () => {
      const { prisma } = await import('@/lib/prisma')
      
      vi.mocked(prisma.post.findUnique).mockResolvedValue(mockPost)

      const postData = {
        title: 'Test Post',
        slug: 'test-post',
        excerpt: 'Test excerpt',
        content: 'Test content',
        status: PostStatus.DRAFT,
      }

      await expect(BlogService.createPost(postData, 'author-1')).rejects.toThrow(
        'A post with this slug already exists'
      )
    })
  })

  describe('getCategories', () => {
    it('should return all categories', async () => {
      const { prisma } = await import('@/lib/prisma')
      
      vi.mocked(prisma.category.findMany).mockResolvedValue([mockCategory])

      const result = await BlogService.getCategories()

      expect(result).toEqual([mockCategory])
      expect(prisma.category.findMany).toHaveBeenCalledWith({
        include: expect.any(Object),
        orderBy: { name: 'asc' },
      })
    })
  })

  describe('createCategory', () => {
    it('should create a new category', async () => {
      const { prisma } = await import('@/lib/prisma')
      
      vi.mocked(prisma.category.findUnique).mockResolvedValue(null)
      vi.mocked(prisma.category.create).mockResolvedValue(mockCategory)

      const categoryData = {
        name: 'Test Category',
        slug: 'test-category',
        description: 'Test category description',
        color: '#3b82f6',
      }

      const result = await BlogService.createCategory(categoryData, 'user-1')

      expect(result).toEqual(mockCategory)
      expect(prisma.category.create).toHaveBeenCalledWith({
        data: {
          ...categoryData,
          createdBy: 'user-1',
        },
        include: expect.any(Object),
      })
    })
  })

  describe('utility methods', () => {
    it('should generate slug from title', () => {
      const slug = BlogService.generateSlug('Hello World! This is a Test')
      expect(slug).toBe('hello-world-this-is-a-test')
    })

    it('should calculate read time from content', () => {
      const content = 'This is a test content with exactly twenty words to test the read time calculation function properly working.'
      const readTime = BlogService.calculateReadTime(content)
      expect(readTime).toBe(1) // 20 words / 200 words per minute = 0.1, rounded up to 1
    })

    it('should calculate read time for longer content', () => {
      const content = Array(400).fill('word').join(' ') // 400 words
      const readTime = BlogService.calculateReadTime(content)
      expect(readTime).toBe(2) // 400 words / 200 words per minute = 2
    })
  })
})
