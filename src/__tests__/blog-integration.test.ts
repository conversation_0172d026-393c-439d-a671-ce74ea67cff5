import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import { createMocks } from 'node-mocks-http'
import { GET, POST } from '@/app/api/blog/route'
import { GET as getById, PUT, DELETE } from '@/app/api/blog/[id]/route'
import { GET as getCategories, POST as createCategory } from '@/app/api/blog/categories/route'
import { PostStatus } from '@prisma/client'

// Mock auth
vi.mock('@/auth', () => ({
  auth: vi.fn().mockResolvedValue({
    user: {
      id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>',
    },
  }),
}))

// Mock Prisma with more comprehensive mocks
vi.mock('@/lib/prisma', () => ({
  prisma: {
    post: {
      findMany: vi.fn(),
      findFirst: vi.fn(),
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn(),
    },
    category: {
      findMany: vi.fn(),
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn(),
    },
    tag: {
      findMany: vi.fn(),
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn(),
    },
    comment: {
      count: vi.fn(),
    },
  },
}))

const mockPost = {
  id: 'post-1',
  title: 'Integration Test Post',
  slug: 'integration-test-post',
  excerpt: 'This is a test post for integration testing',
  content: '<p>This is the content of the test post</p>',
  status: PostStatus.PUBLISHED,
  publishedAt: new Date('2024-01-15T10:00:00Z'),
  readTime: 3,
  viewCount: 50,
  authorId: 'test-user-id',
  categoryId: 'category-1',
  createdAt: new Date('2024-01-15T09:00:00Z'),
  updatedAt: new Date('2024-01-15T10:00:00Z'),
  author: {
    id: 'test-user-id',
    name: 'Test User',
    email: '<EMAIL>',
    image: null,
    role: 'USER',
  },
  category: {
    id: 'category-1',
    name: 'Test Category',
    slug: 'test-category',
    color: '#3b82f6',
    description: 'Test category description',
  },
  tags: [
    {
      id: 'tag-1',
      name: 'Test Tag',
      slug: 'test-tag',
      color: '#ef4444',
    },
  ],
  _count: {
    comments: 2,
    tags: 1,
  },
}

const mockCategory = {
  id: 'category-1',
  name: 'Test Category',
  slug: 'test-category',
  description: 'Test category description',
  color: '#3b82f6',
  createdAt: new Date('2024-01-15T09:00:00Z'),
  updatedAt: new Date('2024-01-15T09:00:00Z'),
  createdBy: 'test-user-id',
  creator: {
    id: 'test-user-id',
    name: 'Test User',
    email: '<EMAIL>',
    image: null,
    role: 'USER',
  },
  _count: {
    posts: 1,
  },
}

describe('Blog API Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Blog Posts API', () => {
    describe('GET /api/blog', () => {
      it('should return paginated blog posts', async () => {
        const { prisma } = await import('@/lib/prisma')
        
        vi.mocked(prisma.post.findMany).mockResolvedValue([mockPost])
        vi.mocked(prisma.post.count).mockResolvedValue(1)

        const { req } = createMocks({
          method: 'GET',
          url: '/api/blog?page=1&perPage=10',
        })

        const response = await GET(req as any)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.data.posts).toHaveLength(1)
        expect(data.data.posts[0].title).toBe('Integration Test Post')
        expect(data.data.total).toBe(1)
        expect(data.data.currentPage).toBe(1)
      })

      it('should filter posts by search term', async () => {
        const { prisma } = await import('@/lib/prisma')
        
        vi.mocked(prisma.post.findMany).mockResolvedValue([mockPost])
        vi.mocked(prisma.post.count).mockResolvedValue(1)

        const { req } = createMocks({
          method: 'GET',
          url: '/api/blog?search=integration',
        })

        const response = await GET(req as any)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(prisma.post.findMany).toHaveBeenCalledWith(
          expect.objectContaining({
            where: expect.objectContaining({
              OR: expect.arrayContaining([
                { title: { contains: 'integration', mode: 'insensitive' } },
                { excerpt: { contains: 'integration', mode: 'insensitive' } },
                { content: { contains: 'integration', mode: 'insensitive' } },
              ]),
            }),
          })
        )
      })
    })

    describe('POST /api/blog', () => {
      it('should create a new blog post', async () => {
        const { prisma } = await import('@/lib/prisma')
        
        vi.mocked(prisma.post.findUnique).mockResolvedValue(null) // No existing post
        vi.mocked(prisma.post.create).mockResolvedValue(mockPost)

        const { req } = createMocks({
          method: 'POST',
          body: {
            title: 'New Test Post',
            slug: 'new-test-post',
            excerpt: 'This is a new test post',
            content: '<p>Content of the new post</p>',
            status: PostStatus.DRAFT,
          },
        })

        const response = await POST(req as any)
        const data = await response.json()

        expect(response.status).toBe(201)
        expect(data.success).toBe(true)
        expect(data.data.title).toBe('Integration Test Post')
        expect(prisma.post.create).toHaveBeenCalled()
      })

      it('should return 409 if slug already exists', async () => {
        const { prisma } = await import('@/lib/prisma')
        
        vi.mocked(prisma.post.findUnique).mockResolvedValue(mockPost) // Existing post

        const { req } = createMocks({
          method: 'POST',
          body: {
            title: 'Duplicate Slug Post',
            slug: 'integration-test-post', // Same slug as mockPost
            excerpt: 'This should fail',
            content: '<p>This should not be created</p>',
            status: PostStatus.DRAFT,
          },
        })

        const response = await POST(req as any)
        const data = await response.json()

        expect(response.status).toBe(409)
        expect(data.success).toBe(false)
        expect(data.error).toContain('slug already exists')
      })
    })

    describe('GET /api/blog/[id]', () => {
      it('should return a specific blog post', async () => {
        const { prisma } = await import('@/lib/prisma')
        
        vi.mocked(prisma.post.findFirst).mockResolvedValue(mockPost)

        const { req } = createMocks({
          method: 'GET',
        })

        const response = await getById(req as any, { params: { id: 'post-1' } })
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.data.id).toBe('post-1')
        expect(data.data.title).toBe('Integration Test Post')
      })

      it('should return 404 if post not found', async () => {
        const { prisma } = await import('@/lib/prisma')
        
        vi.mocked(prisma.post.findFirst).mockResolvedValue(null)

        const { req } = createMocks({
          method: 'GET',
        })

        const response = await getById(req as any, { params: { id: 'nonexistent' } })
        const data = await response.json()

        expect(response.status).toBe(404)
        expect(data.success).toBe(false)
        expect(data.error).toBe('Blog post not found')
      })
    })

    describe('PUT /api/blog/[id]', () => {
      it('should update an existing blog post', async () => {
        const { prisma } = await import('@/lib/prisma')
        
        vi.mocked(prisma.post.findUnique).mockResolvedValue(mockPost)
        vi.mocked(prisma.post.update).mockResolvedValue({
          ...mockPost,
          title: 'Updated Test Post',
        })

        const { req } = createMocks({
          method: 'PUT',
          body: {
            title: 'Updated Test Post',
            excerpt: 'Updated excerpt',
          },
        })

        const response = await PUT(req as any, { params: { id: 'post-1' } })
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.data.title).toBe('Updated Test Post')
        expect(prisma.post.update).toHaveBeenCalled()
      })
    })

    describe('DELETE /api/blog/[id]', () => {
      it('should delete a blog post', async () => {
        const { prisma } = await import('@/lib/prisma')
        
        vi.mocked(prisma.post.findUnique).mockResolvedValue(mockPost)
        vi.mocked(prisma.post.delete).mockResolvedValue(mockPost)

        const { req } = createMocks({
          method: 'DELETE',
        })

        const response = await DELETE(req as any, { params: { id: 'post-1' } })
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.data.message).toBe('Blog post deleted successfully')
        expect(prisma.post.delete).toHaveBeenCalledWith({
          where: { id: 'post-1' },
        })
      })
    })
  })

  describe('Categories API', () => {
    describe('GET /api/blog/categories', () => {
      it('should return all categories', async () => {
        const { prisma } = await import('@/lib/prisma')
        
        vi.mocked(prisma.category.findMany).mockResolvedValue([mockCategory])

        const { req } = createMocks({
          method: 'GET',
        })

        const response = await getCategories(req as any)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.data).toHaveLength(1)
        expect(data.data[0].name).toBe('Test Category')
      })
    })

    describe('POST /api/blog/categories', () => {
      it('should create a new category', async () => {
        const { prisma } = await import('@/lib/prisma')
        
        vi.mocked(prisma.category.findUnique).mockResolvedValue(null)
        vi.mocked(prisma.category.create).mockResolvedValue(mockCategory)

        const { req } = createMocks({
          method: 'POST',
          body: {
            name: 'New Category',
            slug: 'new-category',
            description: 'A new test category',
            color: '#10b981',
          },
        })

        const response = await createCategory(req as any)
        const data = await response.json()

        expect(response.status).toBe(201)
        expect(data.success).toBe(true)
        expect(data.data.name).toBe('Test Category')
        expect(prisma.category.create).toHaveBeenCalled()
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const { prisma } = await import('@/lib/prisma')
      
      vi.mocked(prisma.post.findMany).mockRejectedValue(new Error('Database connection failed'))

      const { req } = createMocks({
        method: 'GET',
        url: '/api/blog',
      })

      const response = await GET(req as any)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Failed to fetch blog posts')
    })

    it('should handle validation errors', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: {
          // Missing required fields
          excerpt: 'This should fail validation',
        },
      })

      const response = await POST(req as any)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
    })
  })
})
