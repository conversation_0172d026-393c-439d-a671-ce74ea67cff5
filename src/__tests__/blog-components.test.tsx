import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BlogCard } from '@/components/blog/blog-card'
import { BlogSearch } from '@/components/blog/blog-search'
import { BlogFilters } from '@/components/blog/blog-filters'
import { BlogPagination } from '@/components/blog/blog-pagination'
import { BlogPostCard, BlogCategory, BlogTag } from '@/types/blog'

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
}))

// Mock toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

const mockPost: BlogPostCard = {
  id: '1',
  title: 'Test Blog Post',
  slug: 'test-blog-post',
  excerpt: 'This is a test blog post excerpt that describes the content.',
  featuredImage: 'https://example.com/image.jpg',
  publishedAt: '2024-01-15T10:00:00Z',
  readTime: 5,
  author: {
    name: '<PERSON> Doe',
    image: 'https://example.com/avatar.jpg',
  },
  category: {
    name: 'Technology',
    slug: 'technology',
    color: '#3b82f6',
  },
  tags: [
    {
      name: 'React',
      slug: 'react',
      color: '#61dafb',
    },
    {
      name: 'TypeScript',
      slug: 'typescript',
      color: '#3178c6',
    },
  ],
}

const mockCategories: BlogCategory[] = [
  {
    id: 'cat-1',
    name: 'Technology',
    slug: 'technology',
    description: 'Tech-related posts',
    color: '#3b82f6',
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'user-1',
    creator: {
      id: 'user-1',
      name: 'Admin',
      email: '<EMAIL>',
      image: null,
      role: 'ADMIN' as any,
    },
    _count: { posts: 5 },
  },
  {
    id: 'cat-2',
    name: 'Design',
    slug: 'design',
    description: 'Design-related posts',
    color: '#ef4444',
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'user-1',
    creator: {
      id: 'user-1',
      name: 'Admin',
      email: '<EMAIL>',
      image: null,
      role: 'ADMIN' as any,
    },
    _count: { posts: 3 },
  },
]

const mockTags: BlogTag[] = [
  {
    id: 'tag-1',
    name: 'React',
    slug: 'react',
    color: '#61dafb',
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'user-1',
    creator: {
      id: 'user-1',
      name: 'Admin',
      email: '<EMAIL>',
      image: null,
      role: 'ADMIN' as any,
    },
    _count: { posts: 8 },
  },
  {
    id: 'tag-2',
    name: 'TypeScript',
    slug: 'typescript',
    color: '#3178c6',
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'user-1',
    creator: {
      id: 'user-1',
      name: 'Admin',
      email: '<EMAIL>',
      image: null,
      role: 'ADMIN' as any,
    },
    _count: { posts: 6 },
  },
]

describe('BlogCard', () => {
  it('renders blog post information correctly', () => {
    render(<BlogCard post={mockPost} />)

    expect(screen.getByText('Test Blog Post')).toBeInTheDocument()
    expect(screen.getByText('This is a test blog post excerpt that describes the content.')).toBeInTheDocument()
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Technology')).toBeInTheDocument()
    expect(screen.getByText('5 min')).toBeInTheDocument()
  })

  it('renders featured image when provided', () => {
    render(<BlogCard post={mockPost} />)

    const image = screen.getByAltText('Test Blog Post')
    expect(image).toBeInTheDocument()
    expect(image).toHaveAttribute('src', 'https://example.com/image.jpg')
  })

  it('shows tags when showTags is true', () => {
    render(<BlogCard post={mockPost} showTags={true} />)

    expect(screen.getByText('#React')).toBeInTheDocument()
    expect(screen.getByText('#TypeScript')).toBeInTheDocument()
  })

  it('applies compact variant styling', () => {
    render(<BlogCard post={mockPost} variant="compact" />)

    const title = screen.getByText('Test Blog Post')
    expect(title).toHaveClass('text-lg')
  })

  it('applies featured variant styling', () => {
    render(<BlogCard post={mockPost} variant="featured" />)

    const title = screen.getByText('Test Blog Post')
    expect(title).toHaveClass('text-3xl')
  })
})

describe('BlogSearch', () => {
  it('renders search input with placeholder', () => {
    const mockOnChange = vi.fn()
    render(<BlogSearch value="" onChange={mockOnChange} placeholder="Search posts..." />)

    const input = screen.getByPlaceholderText('Search posts...')
    expect(input).toBeInTheDocument()
  })

  it('calls onChange when typing', async () => {
    const mockOnChange = vi.fn()
    render(<BlogSearch value="" onChange={mockOnChange} />)

    const input = screen.getByRole('textbox')
    fireEvent.change(input, { target: { value: 'test search' } })

    // Wait for debounce
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith('test search')
    }, { timeout: 500 })
  })

  it('shows clear button when there is a value', () => {
    const mockOnChange = vi.fn()
    render(<BlogSearch value="test" onChange={mockOnChange} />)

    const clearButton = screen.getByRole('button', { name: /clear search/i })
    expect(clearButton).toBeInTheDocument()
  })

  it('clears search when clear button is clicked', () => {
    const mockOnChange = vi.fn()
    render(<BlogSearch value="test" onChange={mockOnChange} />)

    const clearButton = screen.getByRole('button', { name: /clear search/i })
    fireEvent.click(clearButton)

    expect(mockOnChange).toHaveBeenCalledWith('')
  })
})

describe('BlogFilters', () => {
  const mockOnFiltersChange = vi.fn()
  const mockFilters = {
    search: '',
    categoryId: '',
    tagIds: [],
    status: 'published' as const,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders categories and tags', () => {
    render(
      <BlogFilters
        categories={mockCategories}
        tags={mockTags}
        selectedFilters={mockFilters}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    expect(screen.getByText('Categories')).toBeInTheDocument()
    expect(screen.getByText('Tags')).toBeInTheDocument()
    expect(screen.getByText('Technology')).toBeInTheDocument()
    expect(screen.getByText('Design')).toBeInTheDocument()
    expect(screen.getByText('#React')).toBeInTheDocument()
    expect(screen.getByText('#TypeScript')).toBeInTheDocument()
  })

  it('shows post counts for categories and tags', () => {
    render(
      <BlogFilters
        categories={mockCategories}
        tags={mockTags}
        selectedFilters={mockFilters}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    expect(screen.getByText('5')).toBeInTheDocument() // Technology posts count
    expect(screen.getByText('3')).toBeInTheDocument() // Design posts count
    expect(screen.getByText('8')).toBeInTheDocument() // React posts count
    expect(screen.getByText('6')).toBeInTheDocument() // TypeScript posts count
  })

  it('calls onFiltersChange when category is selected', () => {
    render(
      <BlogFilters
        categories={mockCategories}
        tags={mockTags}
        selectedFilters={mockFilters}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    const technologyCheckbox = screen.getByLabelText(/technology/i)
    fireEvent.click(technologyCheckbox)

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      ...mockFilters,
      categoryId: 'cat-1',
    })
  })

  it('shows clear all button when filters are active', () => {
    const filtersWithSelection = {
      ...mockFilters,
      categoryId: 'cat-1',
    }

    render(
      <BlogFilters
        categories={mockCategories}
        tags={mockTags}
        selectedFilters={filtersWithSelection}
        onFiltersChange={mockOnFiltersChange}
      />
    )

    expect(screen.getByText('Clear all')).toBeInTheDocument()
  })
})

describe('BlogPagination', () => {
  const mockOnPageChange = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders page numbers correctly', () => {
    render(
      <BlogPagination
        currentPage={2}
        totalPages={5}
        hasNextPage={true}
        hasPreviousPage={true}
        onPageChange={mockOnPageChange}
      />
    )

    expect(screen.getByText('1')).toBeInTheDocument()
    expect(screen.getByText('2')).toBeInTheDocument()
    expect(screen.getByText('3')).toBeInTheDocument()
    expect(screen.getByText('4')).toBeInTheDocument()
    expect(screen.getByText('5')).toBeInTheDocument()
  })

  it('disables previous button on first page', () => {
    render(
      <BlogPagination
        currentPage={1}
        totalPages={5}
        hasNextPage={true}
        hasPreviousPage={false}
        onPageChange={mockOnPageChange}
      />
    )

    const previousButton = screen.getByRole('button', { name: /previous/i })
    expect(previousButton).toBeDisabled()
  })

  it('disables next button on last page', () => {
    render(
      <BlogPagination
        currentPage={5}
        totalPages={5}
        hasNextPage={false}
        hasPreviousPage={true}
        onPageChange={mockOnPageChange}
      />
    )

    const nextButton = screen.getByRole('button', { name: /next/i })
    expect(nextButton).toBeDisabled()
  })

  it('calls onPageChange when page number is clicked', () => {
    render(
      <BlogPagination
        currentPage={2}
        totalPages={5}
        hasNextPage={true}
        hasPreviousPage={true}
        onPageChange={mockOnPageChange}
      />
    )

    const pageThreeButton = screen.getByRole('button', { name: '3' })
    fireEvent.click(pageThreeButton)

    expect(mockOnPageChange).toHaveBeenCalledWith(3)
  })

  it('calls onPageChange when next button is clicked', () => {
    render(
      <BlogPagination
        currentPage={2}
        totalPages={5}
        hasNextPage={true}
        hasPreviousPage={true}
        onPageChange={mockOnPageChange}
      />
    )

    const nextButton = screen.getByRole('button', { name: /next/i })
    fireEvent.click(nextButton)

    expect(mockOnPageChange).toHaveBeenCalledWith(3)
  })
})
