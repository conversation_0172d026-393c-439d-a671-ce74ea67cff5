import { v4 as uuidv4 } from 'uuid';
import { 
  Block, 
  BlockImage, 
  BlockText, 
  BlockLink, 
  BlockCounter, 
  BlockIconBox,
  AboutBlock,
  ServiceBlock,
  TeamBlock,
  TestimonialBlock
} from '../types/block';

/**
 * Extracts text content from a component's JSX
 * @param jsx The JSX string to parse
 * @param tag The HTML tag to extract (h1, p, etc.)
 * @param className Optional class name to filter by
 */
export const extractTextContent = (jsx: string, tag: string, className?: string): string => {
  const regex = className 
    ? new RegExp(`<${tag}[^>]*class(Name)?=["'][^"']*${className}[^"']*["'][^>]*>(.*?)<\\/${tag}>`, 's')
    : new RegExp(`<${tag}[^>]*>(.*?)<\\/${tag}>`, 's');
  
  const match = jsx.match(regex);
  if (match && match[2]) {
    return match[2].trim();
  }
  return '';
};

/**
 * Extracts image sources from a component's JSX
 * @param jsx The JSX string to parse
 */
export const extractImages = (jsx: string): BlockImage[] => {
  const regex = /<img[^>]*src=["']([^"']*)["'][^>]*alt=["']([^"']*)["'][^>]*\/?>/g;
  const images: BlockImage[] = [];
  let match;
  
  while ((match = regex.exec(jsx)) !== null) {
    images.push({
      src: match[1],
      alt: match[2] || ''
    });
  }
  
  return images;
};

/**
 * Extracts links from a component's JSX
 * @param jsx The JSX string to parse
 */
export const extractLinks = (jsx: string): BlockLink[] => {
  const regex = /<Link[^>]*href=["']([^"']*)["'][^>]*>(.*?)<\/Link>/gs;
  const links: BlockLink[] = [];
  let match;
  
  while ((match = regex.exec(jsx)) !== null) {
    // Extract the text content from the link
    const textContent = match[2].replace(/<[^>]*>/g, '').trim();
    
    // Extract class name if present
    const classNameMatch = match[0].match(/className=["']([^"']*)["']/);
    const className = classNameMatch ? classNameMatch[1] : undefined;
    
    links.push({
      href: match[1],
      text: textContent,
      className
    });
  }
  
  return links;
};

/**
 * Extracts counter values from a component's JSX
 * @param jsx The JSX string to parse
 */
export const extractCounters = (jsx: string): BlockCounter[] => {
  const regex = /<span[^>]*class(Name)?=["'][^"']*counter[^"']*["'][^>]*>(.*?)<\/span>(.*?)<\/h3>\\s*<p>(.*?)<\/p>/gs;
  const counters: BlockCounter[] = [];
  let match;
  
  while ((match = regex.exec(jsx)) !== null) {
    counters.push({
      value: parseInt(match[2], 10) || 0,
      suffix: match[3].trim(),
      label: match[4].trim()
    });
  }
  
  return counters;
};

/**
 * Extracts icon boxes from a component's JSX
 * @param jsx The JSX string to parse
 */
export const extractIconBoxes = (jsx: string): BlockIconBox[] => {
  // This is a simplified version - actual implementation would need to be more robust
  const regex = /<div[^>]*class(Name)?=["'][^"']*icon-box[^"']*["'][^>]*>(.*?)<\/div>/gs;
  const iconBoxes: BlockIconBox[] = [];
  let match;
  
  while ((match = regex.exec(jsx)) !== null) {
    const iconContent = match[2];
    
    // Extract icon image
    const iconImgMatch = iconContent.match(/<img[^>]*src=["']([^"']*)["'][^>]*alt=["']([^"']*)["'][^>]*\/?>/);
    const icon: BlockImage = iconImgMatch 
      ? { src: iconImgMatch[1], alt: iconImgMatch[2] || '' }
      : { src: '', alt: '' };
    
    // Extract title
    const titleMatch = iconContent.match(/<h5[^>]*>(.*?)<\/h5>/s);
    const title: BlockText = {
      content: titleMatch ? titleMatch[1].replace(/<[^>]*>/g, '').trim() : '',
      type: 'h5'
    };
    
    // Extract description
    const descMatch = iconContent.match(/<p[^>]*>(.*?)<\/p>/s);
    const description: BlockText = {
      content: descMatch ? descMatch[1].trim() : '',
      type: 'p'
    };
    
    iconBoxes.push({
      icon,
      title,
      description
    });
  }
  
  return iconBoxes;
};

/**
 * Converts an About component to an editable block
 * @param componentName The name of the component (e.g., "Section1")
 * @param componentJSX The JSX content of the component
 */
export const convertAboutComponentToBlock = (componentName: string, componentJSX: string): AboutBlock => {
  const images = extractImages(componentJSX);
  const counters = extractCounters(componentJSX);
  const iconBoxes = extractIconBoxes(componentJSX);
  
  // Extract headings and text
  const heading: BlockText = {
    content: extractTextContent(componentJSX, 'h2') || 'About Us Heading',
    type: 'h2'
  };
  
  const subheading: BlockText = {
    content: extractTextContent(componentJSX, 'span', 'span') || 'About Us',
    type: 'span'
  };
  
  const description: BlockText = {
    content: extractTextContent(componentJSX, 'p') || 'About us description',
    type: 'p'
  };
  
  // Extract CTA link
  const links = extractLinks(componentJSX);
  const cta = links.find(link => link.className?.includes('theme-btn'));
  
  return {
    id: uuidv4(),
    type: 'about',
    componentName,
    title: 'About Section',
    description: 'An about section for your website',
    createdAt: new Date(),
    updatedAt: new Date(),
    content: {
      heading,
      subheading,
      description,
      images,
      counters,
      iconBoxes,
      cta
    }
  };
};

/**
 * Converts a Service component to an editable block
 * @param componentName The name of the component
 * @param componentJSX The JSX content of the component
 */
export const convertServiceComponentToBlock = (componentName: string, componentJSX: string): ServiceBlock => {
  // Implementation similar to convertAboutComponentToBlock but for service components
  // This is a simplified version
  return {
    id: uuidv4(),
    type: 'service',
    componentName,
    title: 'Service Section',
    description: 'A service section for your website',
    createdAt: new Date(),
    updatedAt: new Date(),
    content: {
      heading: {
        content: extractTextContent(componentJSX, 'h2') || 'Our Services',
        type: 'h2'
      },
      subheading: {
        content: extractTextContent(componentJSX, 'span', 'span') || 'Services',
        type: 'span'
      },
      description: {
        content: extractTextContent(componentJSX, 'p') || 'Our services description',
        type: 'p'
      },
      services: []
    }
  };
};

/**
 * Converts a Team component to an editable block
 * @param componentName The name of the component
 * @param componentJSX The JSX content of the component
 */
export const convertTeamComponentToBlock = (componentName: string, componentJSX: string): TeamBlock => {
  // Implementation similar to convertAboutComponentToBlock but for team components
  // This is a simplified version
  return {
    id: uuidv4(),
    type: 'team',
    componentName,
    title: 'Team Section',
    description: 'A team section for your website',
    createdAt: new Date(),
    updatedAt: new Date(),
    content: {
      heading: {
        content: extractTextContent(componentJSX, 'h2') || 'Our Team',
        type: 'h2'
      },
      subheading: {
        content: extractTextContent(componentJSX, 'span', 'span') || 'Team',
        type: 'span'
      },
      description: {
        content: extractTextContent(componentJSX, 'p') || 'Our team description',
        type: 'p'
      },
      members: []
    }
  };
};

/**
 * Converts a Testimonial component to an editable block
 * @param componentName The name of the component
 * @param componentJSX The JSX content of the component
 */
export const convertTestimonialComponentToBlock = (componentName: string, componentJSX: string): TestimonialBlock => {
  // Implementation similar to convertAboutComponentToBlock but for testimonial components
  // This is a simplified version
  return {
    id: uuidv4(),
    type: 'testimonial',
    componentName,
    title: 'Testimonial Section',
    description: 'A testimonial section for your website',
    createdAt: new Date(),
    updatedAt: new Date(),
    content: {
      heading: {
        content: extractTextContent(componentJSX, 'h2') || 'Testimonials',
        type: 'h2'
      },
      subheading: {
        content: extractTextContent(componentJSX, 'span', 'span') || 'What Our Clients Say',
        type: 'span'
      },
      testimonials: []
    }
  };
};

/**
 * Main function to convert a component to an editable block
 * @param componentType The type of component (about, service, team, testimonial)
 * @param componentName The name of the component
 * @param componentJSX The JSX content of the component
 */
export const convertComponentToBlock = (
  componentType: 'about' | 'service' | 'team' | 'testimonial',
  componentName: string,
  componentJSX: string
): Block => {
  switch (componentType) {
    case 'about':
      return convertAboutComponentToBlock(componentName, componentJSX);
    case 'service':
      return convertServiceComponentToBlock(componentName, componentJSX);
    case 'team':
      return convertTeamComponentToBlock(componentName, componentJSX);
    case 'testimonial':
      return convertTestimonialComponentToBlock(componentName, componentJSX);
    default:
      throw new Error(`Unsupported component type: ${componentType}`);
  }
};