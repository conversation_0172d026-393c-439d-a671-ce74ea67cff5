// Service image mapping utility
export const serviceImageMap: Record<string, string> = {
  'boardroom-solutions': '/assets/services-imgs/boardroom-min.png',
  'data-center': '/assets/services-imgs/datacenter-min.png',
  'graphic-design': '/assets/services-imgs/graphic-min.png',
  'it-consulting': '/assets/services-imgs/itconsult-min.png',
  'live-streaming': '/assets/services-imgs/video-min.png', // Using video image for live streaming
  'smart-city': '/assets/services-imgs/smartcity-min.png',
  'software-development': '/assets/services-imgs/software-min.png',
  'training': '/assets/services-imgs/training-min.png',
  'video-production': '/assets/services-imgs/video-min.png',
  // Additional mappings for products/solutions
  'digim': '/assets/services-imgs/digim-min.png',
  'neteco': '/assets/services-imgs/datacenter-min.png', // Using datacenter image for NetEco
  'fusion-module': '/assets/services-imgs/datacenter-min.png', // Using datacenter image for Fusion Module
};

// Service icon mapping for visual consistency
export const serviceIconMap: Record<string, string> = {
  'boardroom-solutions': '🏛️',
  'data-center': '🏢',
  'graphic-design': '🎨',
  'it-consulting': '🔧',
  'live-streaming': '📡',
  'smart-city': '🏙️',
  'software-development': '💻',
  'training': '📚',
  'video-production': '🎥',
  'digim': '📺',
  'neteco': '🧠',
  'fusion-module': '🏗️',
};

// Service category mapping for organization
export const serviceCategoryMap: Record<string, string> = {
  'boardroom-solutions': 'Infrastructure',
  'data-center': 'Infrastructure',
  'graphic-design': 'Creative Services',
  'it-consulting': 'Consulting',
  'live-streaming': 'Media & Broadcasting',
  'smart-city': 'Smart Solutions',
  'software-development': 'Development',
  'training': 'Education',
  'video-production': 'Media & Broadcasting',
  'digim': 'Software Solutions',
  'neteco': 'Management Platforms',
  'fusion-module': 'Infrastructure',
};

// Helper function to get service image
export function getServiceImage(serviceId: string): string {
  return serviceImageMap[serviceId] || '/assets/services-imgs/image-min.png'; // fallback image
}

// Helper function to get service icon
export function getServiceIcon(serviceId: string): string {
  return serviceIconMap[serviceId] || '⚡'; // fallback icon
}

// Helper function to get service category
export function getServiceCategory(serviceId: string): string {
  return serviceCategoryMap[serviceId] || 'Technology Solutions'; // fallback category
}

// Helper function to get all service images for gallery/showcase
export function getAllServiceImages(): Array<{id: string, image: string, icon: string, category: string}> {
  return Object.keys(serviceImageMap).map(id => ({
    id,
    image: serviceImageMap[id],
    icon: serviceIconMap[id] || '⚡',
    category: serviceCategoryMap[id] || 'Technology Solutions'
  }));
}
