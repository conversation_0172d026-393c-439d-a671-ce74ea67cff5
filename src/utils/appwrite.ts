import { Client, Databases, ID, Query, Storage } from 'appwrite';

// Initialize Appwrite client
const client = new Client();

// Set your Appwrite endpoint and project ID
client
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || '')
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '');

// Initialize Appwrite services
export const databases = new Databases(client);
export const storage = new Storage(client);

// Database and collection IDs
export const BLOCKS_DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || '';
export const PAGES_COLLECTION_ID = process.env.NEXT_PUBLIC_APPWRITE_PAGES_COLLECTION_ID || '';
export const BLOCKS_COLLECTION_ID = process.env.NEXT_PUBLIC_APPWRITE_BLOCKS_COLLECTION_ID || '';

// Helper functions for blocks
export const getBlocks = async () => {
  try {
    const response = await databases.listDocuments(
      BLOCKS_DATABASE_ID,
      BLOCKS_COLLECTION_ID,
      [Query.orderDesc('$createdAt')]
    );
    return response.documents;
  } catch (error) {
    console.error('Error fetching blocks:', error);
    return [];
  }
};

export const getBlockById = async (blockId: string) => {
  try {
    const block = await databases.getDocument(
      BLOCKS_DATABASE_ID,
      BLOCKS_COLLECTION_ID,
      blockId
    );
    return block;
  } catch (error) {
    console.error(`Error fetching block with ID ${blockId}:`, error);
    return null;
  }
};

export const createBlock = async (blockData: any) => {
  try {
    const response = await databases.createDocument(
      BLOCKS_DATABASE_ID,
      BLOCKS_COLLECTION_ID,
      ID.unique(),
      blockData
    );
    return response;
  } catch (error) {
    console.error('Error creating block:', error);
    throw error;
  }
};

export const updateBlock = async (blockId: string, blockData: any) => {
  try {
    const response = await databases.updateDocument(
      BLOCKS_DATABASE_ID,
      BLOCKS_COLLECTION_ID,
      blockId,
      blockData
    );
    return response;
  } catch (error) {
    console.error(`Error updating block with ID ${blockId}:`, error);
    throw error;
  }
};

export const deleteBlock = async (blockId: string) => {
  try {
    await databases.deleteDocument(
      BLOCKS_DATABASE_ID,
      BLOCKS_COLLECTION_ID,
      blockId
    );
    return true;
  } catch (error) {
    console.error(`Error deleting block with ID ${blockId}:`, error);
    throw error;
  }
};

// Helper functions for image uploads
export const uploadImage = async (file: File) => {
  try {
    const response = await storage.createFile(
      process.env.NEXT_PUBLIC_APPWRITE_STORAGE_ID || '',
      ID.unique(),
      file
    );
    return response.$id;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};

export const getImagePreview = (fileId: string) => {
  return storage.getFilePreview(
    process.env.NEXT_PUBLIC_APPWRITE_STORAGE_ID || '',
    fileId
  );
};

// Helper functions for pages
export const getPages = async () => {
  try {
    const response = await databases.listDocuments(
      BLOCKS_DATABASE_ID,
      PAGES_COLLECTION_ID,
      [Query.orderDesc('$createdAt')]
    );
    return response.documents;
  } catch (error) {
    console.error('Error fetching pages:', error);
    return [];
  }
};

export const getPage = async (pageId: string) => {
  try {
    const page = await databases.getDocument(
      BLOCKS_DATABASE_ID,
      PAGES_COLLECTION_ID,
      pageId
    );
    return page;
  } catch (error) {
    console.error(`Error fetching page with ID ${pageId}:`, error);
    return null;
  }
};

export const updatePage = async (pageId: string, pageData: any) => {
  try {
    const response = await databases.updateDocument(
      BLOCKS_DATABASE_ID,
      PAGES_COLLECTION_ID,
      pageId,
      pageData
    );
    return response;
  } catch (error) {
    console.error(`Error updating page with ID ${pageId}:`, error);
    throw error;
  }
};

export const deletePage = async (pageId: string) => {
  try {
    await databases.deleteDocument(
      BLOCKS_DATABASE_ID,
      PAGES_COLLECTION_ID,
      pageId
    );
    return true;
  } catch (error) {
    console.error(`Error deleting page with ID ${pageId}:`, error);
    throw error;
  }
};