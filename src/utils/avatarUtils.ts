// Avatar utility functions for generating default avatars

/**
 * Generates initials from a full name
 * @param name - The full name to generate initials from
 * @returns The initials (up to 2 characters)
 */
export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

/**
 * Generates a consistent background color based on the name
 * @param name - The name to generate color from
 * @returns A hex color string
 */
export function getAvatarColor(name: string): string {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
    '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
    '#A3E4D7', '#F9E79F', '#FADBD8', '#D5DBDB', '#2C3E50',
  ];
  
  // Generate a consistent index based on the name
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  return colors[Math.abs(hash) % colors.length];
}

/**
 * Generates a default avatar component props
 * @param name - The person's name
 * @param size - The size of the avatar (default: 60)
 * @returns Object with avatar properties
 */
export function generateDefaultAvatar(name: string, size: number = 60) {
  const initials = getInitials(name);
  const backgroundColor = getAvatarColor(name);
  
  return {
    initials,
    backgroundColor,
    size,
    style: {
      width: `${size}px`,
      height: `${size}px`,
      backgroundColor,
      color: '#ffffff',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: '50%',
      fontSize: `${size * 0.4}px`,
      fontWeight: '600',
      fontFamily: 'system-ui, -apple-system, sans-serif',
    }
  };
}
