import { useState, useEffect } from 'react';

export interface UseHeaderReturn {
  isSticky: boolean;
  isMobileMenuOpen: boolean;
  toggleMobileMenu: () => void;
  closeMobileMenu: () => void;
  isScrolled: boolean;
}

/**
 * Custom hook for header functionality
 * Handles sticky header, mobile menu, and scroll detection
 */
export function useHeader(): UseHeaderReturn {
  const [isSticky, setIsSticky] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const shouldBeSticky = scrollTop > 100;
      const shouldShowScrolled = scrollTop > 50;
      
      setIsSticky(shouldBeSticky);
      setIsScrolled(shouldShowScrolled);
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);
    
    // Check initial scroll position
    handleScroll();

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Close mobile menu when window is resized to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 992 && isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isMobileMenuOpen]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(prev => !prev);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return {
    isSticky,
    isMobileMenuOpen,
    toggleMobileMenu,
    closeMobileMenu,
    isScrolled,
  };
}

/**
 * Hook for managing dropdown menus
 */
export function useDropdown() {
  const [openDropdowns, setOpenDropdowns] = useState<Record<string, boolean>>({});

  const toggleDropdown = (key: string) => {
    setOpenDropdowns(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const closeDropdown = (key: string) => {
    setOpenDropdowns(prev => ({
      ...prev,
      [key]: false
    }));
  };

  const closeAllDropdowns = () => {
    setOpenDropdowns({});
  };

  const isDropdownOpen = (key: string) => {
    return openDropdowns[key] || false;
  };

  return {
    toggleDropdown,
    closeDropdown,
    closeAllDropdowns,
    isDropdownOpen,
  };
}

/**
 * Hook for navigation active states
 */
export function useNavigation() {
  const [activeSection, setActiveSection] = useState<string>('');

  useEffect(() => {
    const handleScroll = () => {
      const sections = document.querySelectorAll('section[id]');
      const scrollPosition = window.scrollY + 100;

      sections.forEach((section) => {
        const element = section as HTMLElement;
        const top = element.offsetTop;
        const height = element.offsetHeight;
        const id = element.getAttribute('id');

        if (scrollPosition >= top && scrollPosition < top + height && id) {
          setActiveSection(id);
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Check initial position

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isActiveSection = (sectionId: string) => {
    return activeSection === sectionId;
  };

  return {
    activeSection,
    isActiveSection,
  };
}
