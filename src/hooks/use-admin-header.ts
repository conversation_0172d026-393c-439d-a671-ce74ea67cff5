import { create } from 'zustand';

export type AdminHeaderState = {
  isVisible: boolean;
  title: string;
  actions: React.ReactNode[];
  show: () => void;
  hide: () => void;
  setTitle: (title: string) => void;
  setActions: (actions: React.ReactNode[]) => void;
};

export const useAdminHeader = create<AdminHeaderState>((set) => ({
  isVisible: true,
  title: '',
  actions: [],
  show: () => set({ isVisible: true }),
  hide: () => set({ isVisible: false }),
  setTitle: (title) => set({ title }),
  setActions: (actions) => set({ actions }),
}));
