'use client'

import { useEffect, useRef } from 'react'
import { usePathname } from 'next/navigation'

interface AnalyticsConfig {
  enabled?: boolean
  trackPageViews?: boolean
  trackClicks?: boolean
  trackScrollDepth?: boolean
  sessionTimeout?: number
}

interface TrackingData {
  path: string
  userAgent?: string
  referer?: string
  sessionId?: string
  userId?: string
}

class AnalyticsTracker {
  private config: AnalyticsConfig
  private sessionId: string
  private userId?: string
  private startTime: number
  private maxScrollDepth: number = 0
  private isTracking: boolean = false

  constructor(config: AnalyticsConfig = {}) {
    this.config = {
      enabled: true,
      trackPageViews: true,
      trackClicks: true,
      trackScrollDepth: true,
      sessionTimeout: 30 * 60 * 1000, // 30 minutes
      ...config
    }

    this.sessionId = this.getOrCreateSessionId()
    this.startTime = Date.now()
    
    if (typeof window !== 'undefined' && this.config.enabled) {
      this.setupEventListeners()
    }
  }

  private getOrCreateSessionId(): string {
    if (typeof window === 'undefined') return ''
    
    const existing = sessionStorage.getItem('analytics_session_id')
    if (existing) {
      const sessionData = JSON.parse(existing)
      const now = Date.now()
      
      // Check if session is still valid
      if (now - sessionData.timestamp < (this.config.sessionTimeout || 30 * 60 * 1000)) {
        return sessionData.id
      }
    }

    // Create new session
    const newSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    sessionStorage.setItem('analytics_session_id', JSON.stringify({
      id: newSessionId,
      timestamp: Date.now()
    }))
    
    return newSessionId
  }

  private setupEventListeners() {
    if (this.config.trackScrollDepth) {
      window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true })
    }

    if (this.config.trackClicks) {
      document.addEventListener('click', this.handleClick.bind(this), true)
    }

    // Track page unload
    window.addEventListener('beforeunload', this.handlePageUnload.bind(this))
  }

  private handleScroll() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const windowHeight = window.innerHeight
    const documentHeight = document.documentElement.scrollHeight
    
    const scrollDepth = Math.round(((scrollTop + windowHeight) / documentHeight) * 100)
    
    if (scrollDepth > this.maxScrollDepth) {
      this.maxScrollDepth = scrollDepth
      
      // Track scroll milestones
      if (scrollDepth >= 25 && scrollDepth < 50) {
        this.trackEvent('scroll_depth', { depth: 25 })
      } else if (scrollDepth >= 50 && scrollDepth < 75) {
        this.trackEvent('scroll_depth', { depth: 50 })
      } else if (scrollDepth >= 75 && scrollDepth < 100) {
        this.trackEvent('scroll_depth', { depth: 75 })
      } else if (scrollDepth >= 100) {
        this.trackEvent('scroll_depth', { depth: 100 })
      }
    }
  }

  private handleClick(event: MouseEvent) {
    const target = event.target as HTMLElement
    if (!target) return

    const tagName = target.tagName.toLowerCase()
    const href = target.getAttribute('href')
    const text = target.textContent?.trim().substring(0, 100)

    this.trackEvent('click', {
      element: tagName,
      href,
      text,
      x: event.clientX,
      y: event.clientY
    })
  }

  private handlePageUnload() {
    const timeOnPage = Date.now() - this.startTime
    this.trackEvent('page_unload', {
      timeOnPage,
      maxScrollDepth: this.maxScrollDepth
    })
  }

  async trackPageView(path: string) {
    if (!this.config.enabled || !this.config.trackPageViews) return

    try {
      const data: TrackingData = {
        path,
        sessionId: this.sessionId,
        userId: this.userId
      }

      await fetch('/api/analytics/track', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      this.startTime = Date.now()
      this.maxScrollDepth = 0
    } catch (error) {
      console.error('Failed to track page view:', error)
    }
  }

  async trackEvent(eventName: string, data: any = {}) {
    if (!this.config.enabled) return

    try {
      await fetch('/api/analytics/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event: eventName,
          data,
          sessionId: this.sessionId,
          userId: this.userId,
          timestamp: Date.now()
        }),
      })
    } catch (error) {
      console.error('Failed to track event:', error)
    }
  }

  setUserId(userId: string) {
    this.userId = userId
  }

  updateConfig(newConfig: Partial<AnalyticsConfig>) {
    this.config = { ...this.config, ...newConfig }
  }
}

// Global tracker instance
let tracker: AnalyticsTracker | null = null

export function useAnalytics(config: AnalyticsConfig = {}) {
  const pathname = usePathname()
  const trackedPaths = useRef(new Set<string>())

  useEffect(() => {
    if (typeof window === 'undefined') return

    // Initialize tracker if not already done
    if (!tracker) {
      tracker = new AnalyticsTracker(config)
    } else {
      tracker.updateConfig(config)
    }

    // Track page view if not already tracked
    if (!trackedPaths.current.has(pathname)) {
      tracker.trackPageView(pathname)
      trackedPaths.current.add(pathname)
    }

    return () => {
      // Cleanup if needed
    }
  }, [pathname, config])

  const trackEvent = (eventName: string, data?: any) => {
    if (tracker) {
      tracker.trackEvent(eventName, data)
    }
  }

  const setUserId = (userId: string) => {
    if (tracker) {
      tracker.setUserId(userId)
    }
  }

  return {
    trackEvent,
    setUserId
  }
}

// Hook for tracking specific events
export function useEventTracking() {
  const trackClick = (element: string, data?: any) => {
    if (tracker) {
      tracker.trackEvent('manual_click', { element, ...data })
    }
  }

  const trackFormSubmit = (formName: string, data?: any) => {
    if (tracker) {
      tracker.trackEvent('form_submit', { formName, ...data })
    }
  }

  const trackDownload = (fileName: string, fileType?: string) => {
    if (tracker) {
      tracker.trackEvent('download', { fileName, fileType })
    }
  }

  const trackSearch = (query: string, results?: number) => {
    if (tracker) {
      tracker.trackEvent('search', { query, results })
    }
  }

  const trackVideoPlay = (videoId: string, title?: string) => {
    if (tracker) {
      tracker.trackEvent('video_play', { videoId, title })
    }
  }

  const trackCustomEvent = (eventName: string, data?: any) => {
    if (tracker) {
      tracker.trackEvent(eventName, data)
    }
  }

  return {
    trackClick,
    trackFormSubmit,
    trackDownload,
    trackSearch,
    trackVideoPlay,
    trackCustomEvent
  }
}

// Hook for performance tracking
export function usePerformanceTracking() {
  useEffect(() => {
    if (typeof window === 'undefined') return

    // Track page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        
        if (navigation && tracker) {
          tracker.trackEvent('page_performance', {
            loadTime: navigation.loadEventEnd - navigation.loadEventStart,
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
            firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
          })
        }
      }, 0)
    })

    // Track Core Web Vitals
    if ('web-vital' in window) {
      // This would integrate with web-vitals library
      // import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'
    }
  }, [])
}
