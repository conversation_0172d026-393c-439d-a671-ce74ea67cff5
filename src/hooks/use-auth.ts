"use client"

import { useSession } from "next-auth/react"

export const useCurrentUser = () => {
  const { data: session } = useSession()
  return session?.user
}

export const useCurrentRole = () => {
  const { data: session } = useSession()
  return session?.user?.role
}

export const useIsAdmin = () => {
  const { data: session } = useSession()
  return session?.user?.role === "ADMIN"
}

export const useIsAuthenticated = () => {
  const { data: session, status } = useSession()
  return {
    isAuthenticated: !!session?.user,
    isLoading: status === "loading",
  }
}