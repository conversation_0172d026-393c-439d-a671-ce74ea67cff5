import { create } from 'zustand';
import { ReactNode } from 'react';

export type ComponentPosition = 'left' | 'center' | 'right';

export type HeaderComponent = {
  id: string;
  component: ReactNode;
  position: ComponentPosition;
};

export type ContainerStyle = {
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: string;
  padding?: string;
  margin?: string;
  height?: string;
  boxShadow?: string;
  customClasses?: string;
};

export type SiteHeaderEditorState = {
  // Visibility
  isVisible: boolean;
  show: () => void;
  hide: () => void;
  
  // Title
  title: string;
  setTitle: (title: string) => void;
  
  // Components
  components: HeaderComponent[];
  addComponent: (component: ReactNode, position: ComponentPosition) => void;
  removeComponent: (id: string) => void;
  updateComponentPosition: (id: string, position: ComponentPosition) => void;
  
  // Container Style
  containerStyle: ContainerStyle;
  updateContainerStyle: (style: Partial<ContainerStyle>) => void;
  resetContainerStyle: () => void;
  
  // Actions (from original useAdminHeader)
  actions: ReactNode[];
  setActions: (actions: ReactNode[]) => void;
  
  // Edit Mode
  isEditMode: boolean;
  toggleEditMode: () => void;
};

// Default container style
const defaultContainerStyle: ContainerStyle = {
  backgroundColor: 'transparent',
  borderColor: '#e5e7eb', // Default border color
  borderWidth: '1px',
  padding: '0',
  margin: '0',
  height: '56px',
  boxShadow: 'none',
  customClasses: '',
};

export const useSiteHeaderEditor = create<SiteHeaderEditorState>((set) => ({
  // Visibility
  isVisible: true,
  show: () => set({ isVisible: true }),
  hide: () => set({ isVisible: false }),
  
  // Title
  title: '',
  setTitle: (title) => set({ title }),
  
  // Components
  components: [],
  addComponent: (component, position) => 
    set((state) => ({
      components: [
        ...state.components,
        {
          id: Math.random().toString(36).substring(2, 9),
          component,
          position,
        },
      ],
    })),
  removeComponent: (id) => 
    set((state) => ({
      components: state.components.filter((comp) => comp.id !== id),
    })),
  updateComponentPosition: (id, position) => 
    set((state) => ({
      components: state.components.map((comp) => 
        comp.id === id ? { ...comp, position } : comp
      ),
    })),
  
  // Container Style
  containerStyle: defaultContainerStyle,
  updateContainerStyle: (style) => 
    set((state) => ({
      containerStyle: { ...state.containerStyle, ...style },
    })),
  resetContainerStyle: () => 
    set({ containerStyle: defaultContainerStyle }),
  
  // Actions (from original useAdminHeader)
  actions: [],
  setActions: (actions) => set({ actions }),
  
  // Edit Mode
  isEditMode: false,
  toggleEditMode: () => set((state) => ({ isEditMode: !state.isEditMode })),
}));