import { useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useSiteHeaderEditor, ComponentPosition } from './use-site-header-editor';
import { Check, ChevronDown, Save, X } from 'lucide-react';

export type PostStatus = 'draft' | 'published' | 'scheduled';

interface UseBlogEditorHeaderProps {
  title?: string;
  status?: PostStatus;
  onSave?: () => void;
  onCancel?: () => void;
  onStatusChange?: (status: PostStatus) => void;
  isNew?: boolean;
}

export function useBlogEditorHeader({
  title = 'Edit Post',
  status = 'draft',
  onSave,
  onCancel,
  onStatusChange,
  isNew = false
}: UseBlogEditorHeaderProps) {
  const router = useRouter();
  const {
    setTitle,
    clearComponents,
    setComponents,
    show,
    updateContainerStyle,
    resetContainerStyle,
    title: currentHeaderTitle, // Get current title from store
    containerStyle: currentContainerStyle, // Get current style from store
    components: currentHeaderComponents // Get current components from store
  } = useSiteHeaderEditor();

  // Default handlers if none provided
  const handleSave = useCallback(onSave || (() => {
    console.log('Save clicked, but no handler provided');
  }), [onSave]);

  const handleCancel = useCallback(onCancel || (() => {
    router.push('/admin/blog');
  }), [onCancel, router]);

  const handleStatusChange = useCallback(onStatusChange || ((newStatus: PostStatus) => {
    console.log(`Status changed to ${newStatus}, but no handler provided`);
  }), [onStatusChange]);

  const statusDropdown = useMemo(() => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="gap-1">
          {status === 'draft' && 'Draft'}
          {status === 'published' && 'Published'}
          {status === 'scheduled' && 'Scheduled'}
          <ChevronDown size={14} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleStatusChange('draft')}>
          <span className="flex items-center gap-2">
            {status === 'draft' && <Check size={14} />}
            Draft
          </span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleStatusChange('published')}>
          <span className="flex items-center gap-2">
            {status === 'published' && <Check size={14} />}
            Published
          </span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleStatusChange('scheduled')}>
          <span className="flex items-center gap-2">
            {status === 'scheduled' && <Check size={14} />}
            Scheduled
          </span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  ), [status, handleStatusChange]);

  const saveButton = useMemo(() => (
    <Button
      size="sm"
      onClick={handleSave}
      className="gap-1"
    >
      <Save size={14} />
      {isNew ? 'Create' : 'Save'}
    </Button>
  ), [handleSave, isNew]);

  const cancelButton = useMemo(() => (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleCancel}
      className="gap-1"
    >
      <X size={14} />
      Cancel
    </Button>
  ), [handleCancel]);

  const headerComponents = useMemo(() => ([
    { id: 'status-dropdown', component: statusDropdown, position: 'right' },
    { id: 'save-button', component: saveButton, position: 'right' },
    { id: 'cancel-button', component: cancelButton, position: 'right' },
  ]), [statusDropdown, saveButton, cancelButton]);

  useEffect(() => {
    // Make sure the header is visible
    show();
    
    // Set the title only if it has changed
    if (currentHeaderTitle !== title) {
      setTitle(title);
    }

    // Update container style only if it has changed
    const newContainerStyle = {
      backgroundColor: '#ffffff',
      boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
      borderColor: '#e5e7eb',
    };
    if (JSON.stringify(currentContainerStyle) !== JSON.stringify(newContainerStyle)) {
      updateContainerStyle(newContainerStyle);
    }

    // Set components only if they have changed (deep comparison for array of objects)
    if (JSON.stringify(currentHeaderComponents) !== JSON.stringify(headerComponents)) {
      setComponents(headerComponents);
    }

    // Cleanup function
    return () => {
      // Reset the header when unmounting
      setTitle('');
      clearComponents();
      // Reset container style
      resetContainerStyle();
    };
  }, [
    setTitle,
    setComponents,
    title,
    show,
    updateContainerStyle,
    resetContainerStyle,
    headerComponents,
    clearComponents,
    currentHeaderTitle, // Add current state to dependencies
    currentContainerStyle,
    currentHeaderComponents
  ]);

  return {
    status,
    setStatus: handleStatusChange,
    save: handleSave,
    cancel: handleCancel
  };
}
