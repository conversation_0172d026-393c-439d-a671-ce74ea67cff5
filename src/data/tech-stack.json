{"title": "Our Technology Stack", "subtitle": "Powered by Innovation", "description": "We leverage cutting-edge technologies to deliver robust, scalable, and future-proof solutions for our clients.", "categories": [{"name": "Infrastructure", "icon": "fa-solid fa-server", "technologies": [{"name": "Fusion Module 2000", "logo": "/img/tech/fusion-module.svg", "description": "Next-generation modular data center solution"}, {"name": "NetCol5000", "logo": "/img/tech/netcol.svg", "description": "Advanced cooling system for data centers"}, {"name": "UPS5000", "logo": "/img/tech/ups5000.svg", "description": "Uninterruptible power supply for critical systems"}, {"name": "NetEco6000", "logo": "/img/tech/neteco.svg", "description": "Comprehensive data center management platform"}]}, {"name": "Security", "icon": "fa-solid fa-shield-alt", "technologies": [{"name": "IVS Network", "logo": "/img/tech/ivs.svg", "description": "Intelligent video surveillance system"}, {"name": "ITS Platform", "logo": "/img/tech/its.svg", "description": "Integrated traffic management system"}, {"name": "Perimeter Defense", "logo": "/img/tech/perimeter.svg", "description": "Advanced physical security solutions"}, {"name": "CyberShield", "logo": "/img/tech/cybershield.svg", "description": "Comprehensive cybersecurity framework"}]}, {"name": "Software", "icon": "fa-solid fa-code", "technologies": [{"name": "DiGiM Platform", "logo": "/img/tech/digim.svg", "description": "Digital media broadcasting solution"}, {"name": "Cloud Services", "logo": "/img/tech/cloud.svg", "description": "Scalable cloud infrastructure and services"}, {"name": "Custom Development", "logo": "/img/tech/custom-dev.svg", "description": "Tailored software solutions"}, {"name": "Mobile Applications", "logo": "/img/tech/mobile.svg", "description": "Cross-platform mobile app development"}]}, {"name": "Media", "icon": "fa-solid fa-photo-film", "technologies": [{"name": "4K Production", "logo": "/img/tech/4k.svg", "description": "Ultra-high-definition video production"}, {"name": "Multi-Stream", "logo": "/img/tech/multi-stream.svg", "description": "Simultaneous multi-platform broadcasting"}, {"name": "Creative Suite", "logo": "/img/tech/creative.svg", "description": "Professional design and editing tools"}, {"name": "Interactive Media", "logo": "/img/tech/interactive.svg", "description": "Engaging interactive content creation"}]}]}