// Debug utility to test project loading
import { getAllProjects } from '@/lib/services/projects';

export async function debugProjects() {
  try {
    console.log('🔍 Testing project database loading...');
    
    const projects = await getAllProjects();
    
    console.log(`✅ Successfully loaded ${projects.length} projects`);
    
    if (projects.length > 0) {
      console.log('📋 Project summary:');
      projects.forEach((project, index) => {
        console.log(`  ${index + 1}. ${project.title} (${project.status})`);
        console.log(`     - Slug: ${project.slug}`);
        console.log(`     - Category: ${project.category || 'None'}`);
        console.log(`     - Featured Image: ${project.featuredImage || 'None'}`);
        console.log(`     - Created: ${project.createdAt}`);
        console.log('');
      });
      
      // Test filtering published projects
      const publishedProjects = projects.filter(p => p.status === 'PUBLISHED');
      console.log(`📊 Published projects: ${publishedProjects.length}/${projects.length}`);
      
      return {
        success: true,
        totalProjects: projects.length,
        publishedProjects: publishedProjects.length,
        projects: publishedProjects.slice(0, 6) // Limit to 6 like the API
      };
    } else {
      console.log('⚠️  No projects found in database');
      return {
        success: true,
        totalProjects: 0,
        publishedProjects: 0,
        projects: []
      };
    }
    
  } catch (error) {
    console.error('❌ Error loading projects:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      totalProjects: 0,
      publishedProjects: 0,
      projects: []
    };
  }
}

// Test individual project loading
export async function debugSingleProject(slug: string) {
  try {
    const { getProjectBySlug } = await import('@/lib/services/projects');
    const project = await getProjectBySlug(slug);
    
    if (project) {
      console.log(`✅ Successfully loaded project: ${project.title}`);
      console.log('Project details:', {
        id: project.id,
        title: project.title,
        slug: project.slug,
        status: project.status,
        category: project.category,
        featuredImage: project.featuredImage,
        excerpt: project.excerpt?.substring(0, 100) + '...',
      });
      return { success: true, project };
    } else {
      console.log(`❌ Project not found: ${slug}`);
      return { success: false, error: 'Project not found' };
    }
  } catch (error) {
    console.error(`❌ Error loading project ${slug}:`, error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}