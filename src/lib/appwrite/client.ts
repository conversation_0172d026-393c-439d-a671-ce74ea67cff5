import { Client, Account, Databases, Storage, ID } from 'appwrite';
import { APPWRITE_CONFIG } from './config';

// Initialize the Appwrite client
const client = new Client()
  .setEndpoint(APPWRITE_CONFIG.endpoint)
  .setProject(APPWRITE_CONFIG.projectId);

// Initialize services
export const account = new Account(client);
export const databases = new Databases(client);
export const storage = new Storage(client);

// Re-export Appwrite constants and types
export { ID };
export type { Models } from 'appwrite';
