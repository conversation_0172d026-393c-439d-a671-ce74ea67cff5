import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { databaseService } from '../services/database';
import { APPWRITE_CONFIG } from '../config';

// Test data
const TEST_COLLECTION = 'test_collection';
const TEST_DOCUMENT = {
  title: 'Test Document',
  content: 'This is a test document',
  isActive: true,
  tags: ['test', 'document'],
  views: 0,
};

describe('Database Service', () => {
  let documentId: string;

  beforeAll(async () => {
    // Create a test collection if it doesn't exist
    try {
      await databaseService.createCollection(
        TEST_COLLECTION,
        'Test Collection',
        ['read("any")', 'write("any")']
      );

      // Add attributes
      await databaseService.createCollectionAttribute(TEST_COLLECTION, {
        key: 'title',
        type: 'string',
        size: 255,
        required: true,
      });

      await databaseService.createCollectionAttribute(TEST_COLLECTION, {
        key: 'content',
        type: 'string',
        size: 1000,
        required: false,
      });

      await databaseService.createCollectionAttribute(TEST_COLLECTION, {
        key: 'isActive',
        type: 'boolean',
        required: false,
        defaultValue: false,
      });

      await databaseService.createCollectionAttribute(TEST_COLLECTION, {
        key: 'tags',
        type: 'string',
        required: false,
        array: true,
      });

      await databaseService.createCollectionAttribute(TEST_COLLECTION, {
        key: 'views',
        type: 'integer',
        required: false,
        min: 0,
      });

      // Create an index
      await databaseService.createIndex(
        TEST_COLLECTION,
        'title_idx',
        'key',
        ['title']
      );
    } catch (error) {
      // Collection might already exist, which is fine
      if (!error.message.includes('already exists')) {
        console.error('Failed to set up test collection:', error);
        throw error;
      }
    }
  });

  it('should create a document', async () => {
    const { success, data, error } = await databaseService.createDocument(
      TEST_COLLECTION,
      TEST_DOCUMENT
    );

    expect(success).toBe(true);
    expect(error).toBeUndefined();
    expect(data).toBeDefined();
    expect(data?.$id).toBeDefined();
    
    documentId = data?.$id;

    // Verify the document was created with the correct data
    expect(data?.title).toBe(TEST_DOCUMENT.title);
    expect(data?.content).toBe(TEST_DOCUMENT.content);
    expect(data?.isActive).toBe(TEST_DOCUMENT.isActive);
    expect(data?.tags).toEqual(TEST_DOCUMENT.tags);
    expect(data?.views).toBe(TEST_DOCUMENT.views);
  });

  it('should get a document by ID', async () => {
    const { success, data, error } = await databaseService.getDocument(
      TEST_COLLECTION,
      documentId
    );

    expect(success).toBe(true);
    expect(error).toBeUndefined();
    expect(data).toBeDefined();
    expect(data?.$id).toBe(documentId);
  });

  it('should list documents', async () => {
    const { success, data, error } = await databaseService.listDocuments(
      TEST_COLLECTION,
      {
        queries: [`equal("$id", "${documentId}")`],
        limit: 1,
      }
    );

    expect(success).toBe(true);
    expect(error).toBeUndefined();
    expect(data).toBeDefined();
    expect(data?.documents.length).toBeGreaterThan(0);
    expect(data?.documents[0].$id).toBe(documentId);
  });

  it('should update a document', async () => {
    const updates = {
      title: 'Updated Title',
      views: 1,
    };

    const { success, data, error } = await databaseService.updateDocument(
      TEST_COLLECTION,
      documentId,
      updates
    );

    expect(success).toBe(true);
    expect(error).toBeUndefined();
    expect(data).toBeDefined();
    expect(data?.title).toBe(updates.title);
    expect(data?.views).toBe(updates.views);
  });

  it('should delete a document', async () => {
    const { success, error } = await databaseService.deleteDocument(
      TEST_COLLECTION,
      documentId
    );

    expect(success).toBe(true);
    expect(error).toBeUndefined();

    // Verify the document was deleted
    const { success: exists } = await databaseService.getDocument(
      TEST_COLLECTION,
      documentId
    );
    expect(exists).toBe(false);
  });

  afterAll(async () => {
    // Clean up test collection (optional)
    try {
      // Note: In a real test environment, you might want to clean up the test collection
      // However, be careful with this in a production-like environment
      // await databaseService.deleteCollection(TEST_COLLECTION);
    } catch (error) {
      console.error('Failed to clean up test collection:', error);
    }
  });
});
