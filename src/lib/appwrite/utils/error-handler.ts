import { AppwriteException } from 'appwrite';

export class AppwriteError extends Error {
  code: number;
  response: string;

  constructor(error: unknown) {
    super();
    
    if (error instanceof AppwriteException) {
      this.name = 'AppwriteError';
      this.code = error.code || 0;
      this.message = error.message || 'An Appwrite error occurred';
      this.response = error.response || '';
    } else if (error instanceof Error) {
      this.name = error.name;
      this.message = error.message;
      this.code = 0;
      this.response = '';
    } else {
      this.name = 'UnknownError';
      this.message = 'An unknown error occurred';
      this.code = 0;
      this.response = '';
    }
  }

  static handle(error: unknown): { message: string; code: number } {
    const appwriteError = new AppwriteError(error);
    
    // Handle specific error codes if needed
    switch (appwriteError.code) {
      case 401:
        return { message: 'Unauthorized. Please log in again.', code: 401 };
      case 403:
        return { message: 'You do not have permission to perform this action.', code: 403 };
      case 404:
        return { message: 'The requested resource was not found.', code: 404 };
      case 409:
        return { message: 'A conflict occurred. Please try again.', code: 409 };
      case 429:
        return { message: 'Too many requests. Please try again later.', code: 429 };
      case 500:
        return { message: 'A server error occurred. Please try again later.', code: 500 };
      default:
        return { 
          message: appwriteError.message || 'An unexpected error occurred', 
          code: appwriteError.code || 0 
        };
    }
  }
}
