// Core exports
export * from './config';
export * from './client';

// Services
export { default as AuthService } from './services/auth';
export { databaseService } from './services/database';
export { storageService } from './services/storage';

// Server Services
export { databaseServer } from './server/database-server';
export { storageServer } from './server/storage-server';

// Utils
export * from './utils/error-handler';

// Types
export type { 
  UserSession,
  DatabaseDocument,
  Collection,
  QueryOptions,
  DatabaseResponse,
  PaginatedResponse 
} from './types/database';

// Constants
export { APPWRITE_CONFIG } from './config';
