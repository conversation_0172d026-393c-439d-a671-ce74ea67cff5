import { storage } from '../client';
import { ID, type Models } from 'appwrite';
import { AppwriteError } from '../utils/error-handler';
import type {
  File as AppFile,
  FileList,
  UploadOptions,
  StorageResponse,
  FilePreviewOptions,
  AppwriteFile,
  FileInput
} from '../types/storage';

// Type guard to check if value is a browser File
const isFile = (value: unknown): value is File => {
  return value instanceof File;
};

// Type guard to check if value is a Blob
const isBlob = (value: unknown): value is Blob => {
  return value instanceof Blob;
};

// Convert array of permissions to comma-separated string if needed
const normalizePermissions = (permissions?: string | string[]): string | undefined => {
  if (!permissions) return undefined;
  return Array.isArray(permissions) ? permissions.join(',') : permissions;
};

export class StorageService {
  private static instance: StorageService;
  private bucketId: string;

  private constructor() {
    this.bucketId = process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID || 'default';
  }

  public static getInstance(): StorageService {
    if (!StorageService.instance) {
      StorageService.instance = new StorageService();
    }
    return StorageService.instance;
  }

  async uploadFile(
    file: FileInput,
    options: UploadOptions = {}
  ): Promise<StorageResponse<AppFile>> {
    try {
      const fileId = ID.unique();
      const readPermissions = normalizePermissions(options.read);
      const writePermissions = normalizePermissions(options.write);
      
      let fileToUpload: File | Blob;
  
      if (isFile(file)) {
        fileToUpload = file;
      } else if (isBlob(file)) {
        fileToUpload = file;
      } else {
        // Handle Buffer/ArrayBuffer/Uint8Array
        fileToUpload = new Blob([file as BlobPart]);
      }
  
      // Create a File object if we have a Blob but not a File
      const uploadFile = isFile(fileToUpload) 
        ? fileToUpload 
        : new File([fileToUpload], fileId, { type: fileToUpload.type || 'application/octet-stream' });
  
      const permissions = [
        ...(readPermissions ? [readPermissions] : []),
        ...(writePermissions ? [writePermissions] : [])
      ].filter(Boolean);

      const uploadedFile = await storage.createFile(
        this.bucketId,
        fileId,
        uploadFile,
        permissions.length ? permissions : undefined,
        options.onProgress
      );
  
      return {
        success: true,
        data: this.mapToAppFile(uploadedFile),
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getFile(fileId: string): Promise<StorageResponse<AppFile>> {
    try {
      const file = await storage.getFile(this.bucketId, fileId);
      return {
        success: true,
        data: this.mapToAppFile(file),
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getFilePreview(
    fileId: string,
    options: FilePreviewOptions = {}
  ): Promise<StorageResponse<string>> {
    try {
      const previewUrl = storage.getFilePreview(
        this.bucketId,
        fileId,
        options.width,
        options.height,
        options.gravity,
        options.quality,
        options.borderWidth,
        options.borderColor,
        options.borderRadius,
        options.opacity,
        options.rotation,
        options.background,
        options.output
      );

      return {
        success: true,
        data: previewUrl.toString(),
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getFileDownload(fileId: string): Promise<StorageResponse<string>> {
    try {
      const downloadUrl = storage.getFileDownload(this.bucketId, fileId);
      return {
        success: true,
        data: downloadUrl.toString(),
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getFileView(fileId: string): Promise<StorageResponse<string>> {
    try {
      const viewUrl = storage.getFileView(this.bucketId, fileId);
      return {
        success: true,
        data: viewUrl.toString(),
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async listFiles(
    queries: string[] = [],
    search?: string
  ): Promise<StorageResponse<FileList>> {
    try {
      const response = await storage.listFiles(this.bucketId, queries, search);
      return {
        success: true,
        data: {
          total: response.total,
          files: response.files.map(file => this.mapToAppFile(file)),
        },
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async updateFile(
    fileId: string,
    read?: string[],
    write?: string[]
  ): Promise<StorageResponse<AppFile>> {
    try {
      // Combine read and write permissions into a single permissions array
      const permissions = [
        ...(read?.map(perm => `read(${perm})`) || []),
        ...(write?.map(perm => `write(${perm})`) || [])
      ];

      const updatedFile = await storage.updateFile(
        this.bucketId,
        fileId,
        undefined, // name (optional)
        permissions.length > 0 ? permissions : undefined
      );

      return {
        success: true,
        data: this.mapToAppFile(updatedFile),
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async deleteFile(fileId: string): Promise<StorageResponse<boolean>> {
    try {
      await storage.deleteFile(this.bucketId, fileId);
      return {
        success: true,
        data: true,
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  private mapToAppFile(file: Models.File): AppFile {
    return {
      ...file,
      name: file.name,
      size: file.sizeOriginal,
      type: file.mimeType,
      lastModified: new Date(file.$createdAt).getTime(),
      chunksTotal: 1,
      chunksUploaded: 1
    } as AppFile;
  }

  private handleError(error: unknown): StorageResponse<never> {
    if (error instanceof Error) {
      return {
        success: false,
        error: error.message,
        code: 'code' in error ? (error as any).code : 500
      };
    }
    return {
      success: false,
      error: 'An unknown error occurred',
      code: 500
    };
  }
}

export const storageService = StorageService.getInstance();
