import { account } from '../client';
import { ID } from 'appwrite';

export interface UserSession {
  $id: string;
  email: string;
  name: string;
  // Add other user fields as needed
}

export interface AuthResponse {
  success: boolean;
  error?: string;
  user?: UserSession;
}

class AuthService {
  static async register(email: string, password: string, name: string): Promise<AuthResponse> {
    try {
      const session = await account.create(ID.unique(), email, password, name);
      return {
        success: true,
        user: {
          $id: session.$id,
          email: session.email,
          name: session.name,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Registration failed',
      };
    }
  }

  static async login(email: string, password: string): Promise<AuthResponse> {
    try {
      await account.createSession(email, password);
      const user = await this.getCurrentUser();
      
      if (!user) {
        throw new Error('Failed to fetch user data');
      }

      return {
        success: true,
        user,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed',
      };
    }
  }

  static async logout(): Promise<{ success: boolean; error?: string }> {
    try {
      await account.deleteSession('current');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Logout failed',
      };
    }
  }

  static async getCurrentUser(): Promise<UserSession | null> {
    try {
      const user = await account.get();
      return {
        $id: user.$id,
        email: user.email,
        name: user.name,
      };
    } catch (error) {
      return null;
    }
  }

  static async isLoggedIn(): Promise<boolean> {
    try {
      const session = await account.getSession('current');
      return !!session?.$id;
    } catch (error) {
      return false;
    }
  }
}

export default AuthService;
