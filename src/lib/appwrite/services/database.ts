import { databases, ID } from '../client';
import { APPWRITE_CONFIG } from '../config';
import { AppwriteError } from '../utils/error-handler';
import type { 
  DatabaseDocument, 
  Collection, 
  QueryOptions, 
  DatabaseResponse,
  PaginatedResponse,
  CollectionAttribute,
  CollectionIndex
} from '../types/database';

// Extend the Window interface to include Appwrite types
declare global {
  interface Window {
    _appwrite?: {
      databases?: any;
    };
  }
}

export class DatabaseService {
  private static instance: DatabaseService;
  private databaseId: string;

  private constructor() {
    this.databaseId = APPWRITE_CONFIG.databaseId;
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  // Collection Operations
  async createCollection(
    collectionId: string,
    name: string,
    permissions: string[] = [],
    documentSecurity: boolean = true
  ): Promise<DatabaseResponse<Collection>> {
    try {
      // Use type assertion to bypass type checking for now
      const db = (databases as any);
      const collection = await db.createCollection(
        this.databaseId,
        collectionId,
        name,
        documentSecurity,
        permissions
      );
      
      return {
        success: true,
        data: collection as Collection
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async listCollections(queries: string[] = []): Promise<DatabaseResponse<Collection[]>> {
    try {
      // Use type assertion to bypass type checking for now
      const db = (databases as any);
      const response = await db.listCollections(this.databaseId, queries);
      
      return {
        success: true,
        data: response.collections as Collection[]
      };
    } catch (error) {
      return this.handleError(error);
    }
  }
  
  async createCollectionAttribute(
    collectionId: string,
    attribute: CollectionAttribute
  ): Promise<DatabaseResponse<boolean>> {
    try {
      const db = (databases as any);
      
      switch (attribute.type) {
        case 'string':
          await db.createStringAttribute(
            this.databaseId,
            collectionId,
            attribute.key,
            attribute.size || 255,
            attribute.required,
            attribute.defaultValue,
            attribute.array
          );
          break;
        case 'integer':
          await db.createIntegerAttribute(
            this.databaseId,
            collectionId,
            attribute.key,
            attribute.required,
            attribute.defaultValue,
            attribute.min,
            attribute.max,
            attribute.array
          );
          break;
        case 'float':
          await db.createFloatAttribute(
            this.databaseId,
            collectionId,
            attribute.key,
            attribute.required,
            attribute.defaultValue,
            attribute.min,
            attribute.max,
            attribute.array
          );
          break;
        case 'boolean':
          await db.createBooleanAttribute(
            this.databaseId,
            collectionId,
            attribute.key,
            attribute.required,
            attribute.defaultValue,
            attribute.array
          );
          break;
        case 'datetime':
          await db.createDatetimeAttribute(
            this.databaseId,
            collectionId,
            attribute.key,
            attribute.required,
            attribute.defaultValue,
            attribute.array
          );
          break;
        case 'email':
          await db.createEmailAttribute(
            this.databaseId,
            collectionId,
            attribute.key,
            attribute.required,
            attribute.defaultValue,
            attribute.array
          );
          break;
        case 'url':
          await db.createUrlAttribute(
            this.databaseId,
            collectionId,
            attribute.key,
            attribute.required,
            attribute.defaultValue,
            attribute.array
          );
          break;
        case 'ip':
          await db.createIpAttribute(
            this.databaseId,
            collectionId,
            attribute.key,
            attribute.required,
            attribute.defaultValue,
            attribute.array
          );
          break;
        default:
          throw new Error(`Unsupported attribute type: ${attribute.type}`);
      }
      
      return {
        success: true,
        data: true
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Document Operations
  async createDocument<T extends DatabaseDocument>(
    collectionId: string,
    data: Omit<T, keyof DatabaseDocument>,
    permissions: string[] = []
  ): Promise<DatabaseResponse<T>> {
    try {
      const document = await databases.createDocument<T>(
        this.databaseId,
        collectionId,
        ID.unique(),
        data,
        permissions
      );
      
      return {
        success: true,
        data: document
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getDocument<T extends DatabaseDocument>(
    collectionId: string,
    documentId: string
  ): Promise<DatabaseResponse<T>> {
    try {
      const document = await databases.getDocument<T>(
        this.databaseId,
        collectionId,
        documentId
      );
      
      return {
        success: true,
        data: document
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async listDocuments<T extends DatabaseDocument>(
    collectionId: string,
    options: QueryOptions = {}
  ): Promise<DatabaseResponse<PaginatedResponse<T>>> {
    try {
      const {
        queries = [],
        limit = 25,
        offset = 0,
        cursor,
        cursorDirection = 'after',
        orderAttributes = ['$createdAt'],
        orderTypes = ['DESC']
      } = options;

      // Add pagination queries
      const paginationQueries = [
        ...queries,
        `limit(${limit})`,
        `offset(${offset})`,
        ...orderAttributes.map((attr, index) => 
          `order${attr.startsWith('$') ? '' : 'By'}("${attr}", ${orderTypes[index] || 'DESC'})`
        )
      ];

      if (cursor) {
        paginationQueries.push(`cursor${cursorDirection}(${cursor})`);
      }

      const response = await databases.listDocuments<T>(
        this.databaseId,
        collectionId,
        paginationQueries
      );

      return {
        success: true,
        data: {
          total: response.total,
          documents: response.documents
        }
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async updateDocument<T extends DatabaseDocument>(
    collectionId: string,
    documentId: string,
    data: Partial<Omit<T, keyof DatabaseDocument>>,
    permissions?: string[]
  ): Promise<DatabaseResponse<T>> {
    try {
      const document = await databases.updateDocument<T>(
        this.databaseId,
        collectionId,
        documentId,
        data,
        permissions
      );
      
      return {
        success: true,
        data: document
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async deleteDocument(
    collectionId: string,
    documentId: string
  ): Promise<DatabaseResponse<boolean>> {
    try {
      await databases.deleteDocument(
        this.databaseId,
        collectionId,
        documentId
      );
      
      return {
        success: true,
        data: true
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Index Operations
  async createIndex(
    collectionId: string,
    key: string,
    type: 'key' | 'fulltext' | 'unique' | 'spatial',
    attributes: string[],
    orders: string[] = []
  ): Promise<DatabaseResponse<boolean>> {
    try {
      // Use type assertion to bypass type checking for now
      const db = (databases as any);
      await db.createIndex(
        this.databaseId,
        collectionId,
        key,
        type,
        attributes,
        orders
      );
      
      return {
        success: true,
        data: true
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Error handling
  private handleError<T>(error: unknown): DatabaseResponse<T> {
    const appwriteError = new AppwriteError(error);
    return {
      success: false,
      error: appwriteError.message,
      code: appwriteError.code
    };
  }
}

export const databaseService = DatabaseService.getInstance();
