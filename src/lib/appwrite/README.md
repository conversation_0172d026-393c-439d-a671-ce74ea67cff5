# Appwrite Backend Services

This directory contains the Appwrite backend integration for the application, including authentication and database services.

## Structure

```
lib/appwrite/
├── config.ts           # Appwrite configuration
├── client.ts           # Appwrite client initialization
├── types/              # TypeScript type definitions
│   └── database.ts     # Database-related types
├── services/           # Service implementations
│   ├── auth.ts         # Authentication service
│   └── database.ts     # Database service
├── server/             # Server-side services
│   └── database-server.ts
└── utils/              # Utility functions
    └── error-handler.ts # Error handling utilities
```

## Setup

1. Create a `.env.local` file in your project root with the following variables:

```env
NEXT_PUBLIC_APPWRITE_ENDPOINT=your_appwrite_endpoint
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id
APPWRITE_API_KEY=your_server_api_key
```

2. Install required dependencies:

```bash
pnpm add appwrite
pnpm add -D node-appwrite @types/node
```

## Database Initialization

To initialize the database with default collections and indexes, run:

```bash
# Set your Appwrite API key as an environment variable
export APPWRITE_API_KEY='your-api-key-here'

# Run the initialization script
pnpm tsx scripts/init-database.ts
```

## Usage Examples

### Authentication

```typescript
import { AuthService } from '@/lib/appwrite';

// Register a new user
const { success, user, error } = await AuthService.register(
  '<EMAIL>',
  'password123',
  'John Doe'
);

// Login
const { success, user, error } = await AuthService.login(
  '<EMAIL>',
  'password123'
);

// Get current user
const user = await AuthService.getCurrentUser();

// Logout
await AuthService.logout();
```

### Database Operations

```typescript
import { databaseService } from '@/lib/appwrite';

// Create a document
const { success, data, error } = await databaseService.createDocument(
  'collectionId',
  { title: 'Hello', content: 'World' },
  ['read("any")', 'write("any")']
);

// Get a document
const { success, data, error } = await databaseService.getDocument(
  'collectionId',
  'documentId'
);

// List documents with pagination
const { success, data, error } = await databaseService.listDocuments(
  'collectionId',
  {
    queries: ['equal("status", "published")'],
    limit: 10,
    offset: 0,
    orderAttributes: ['$createdAt'],
    orderTypes: ['DESC']
  }
);

// Update a document
const { success, data, error } = await databaseService.updateDocument(
  'collectionId',
  'documentId',
  { title: 'Updated Title' }
);

// Delete a document
const { success, error } = await databaseService.deleteDocument(
  'collectionId',
  'documentId'
);
```

### Server-Side Database Operations

For server-side operations (e.g., in API routes):

```typescript
import { databaseServer } from '@/lib/appwrite';

// In your API route handler
export async function GET() {
  try {
    const { data } = await databaseServer.listDocuments('collectionId');
    return Response.json({ data });
  } catch (error) {
    return Response.json(
      { error: 'Failed to fetch data' },
      { status: 500 }
    );
  }
}
```

## Error Handling

All service methods return a standardized response format:

```typescript
{
  success: boolean;    // Whether the operation was successful
  data?: T;            // Response data (if successful)
  error?: string;      // Error message (if failed)
  code?: number;       // Error code (if available)
}
```

## Best Practices

1. Always check the `success` flag before accessing `data`.
2. Handle errors appropriately in your UI.
3. Use the `useAuth` hook for client-side authentication state.
4. Use the `ProtectedRoute` component to protect routes that require authentication.
5. For complex queries, use the query builder pattern with the `QueryOptions` interface.

## Testing

Run the test suite with:

```bash
pnpm test
```

## License

MIT
