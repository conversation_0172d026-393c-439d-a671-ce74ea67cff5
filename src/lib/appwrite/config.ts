// Appwrite Configuration

// Get environment variables with fallbacks
const getEnv = (key: string, defaultValue: string = ''): string => {
  if (typeof process !== 'undefined' && process.env) {
    return process.env[`NEXT_PUBLIC_${key}`] || process.env[key] || defaultValue;
  }
  return (typeof window !== 'undefined' && (window as any).env?.[key]) || defaultValue;
};

// Type definitions for the config
type ImageSize = {
  width: number;
  height: number;
  crop?: boolean;
};

type ImageSizes = {
  [key: string]: ImageSize;
};

type UploadsConfig = {
  maxFileSize: number;
  allowedMimeTypes: string[];
  imageResize: {
    maxWidth: number;
    maxHeight: number;
    quality: number;
  };
};

type UserConfig = {
  defaultRole: string;
  defaultStatus: string;
  passwordMinLength: number;
  usernameMinLength: number;
  usernameMaxLength: number;
};

type ContentConfig = {
  defaultStatus: string;
  defaultCommentStatus: string;
  defaultPingStatus: string;
  postsPerPage: number;
  commentsPerPage: number;
  excerptLength: number;
  excerptMore: string;
  feedType: string;
  feedItems: number;
  rss2: string;
  rss: string;
  rdf: string;
  atom: string;
};

type MediaConfig = {
  sizes: ImageSizes;
  defaultSize: string;
};

type SearchConfig = {
  minQueryLength: number;
  maxSuggestions: number;
  highlight: {
    preTag: string;
    postTag: string;
    fragmentSize: number;
    numberOfFragments: number;
  };
};

type SecurityConfig = {
  jwtSecret: string;
  passwordSaltRounds: number;
  rateLimit: {
    windowMs: number;
    max: number;
  };
  cors: {
    origin: string;
    methods: string[];
    allowedHeaders: string[];
    credentials: boolean;
  };
};

type Config = {
  appwriteEndpoint: string;
  appwriteProjectId: string;
  appwriteDatabaseId: string;
  appwriteMediaBucketId: string;
  defaultLocale: string;
  siteUrl: string;
  apiPrefix: string;
  pagination: {
    defaultPageSize: number;
    maxPageSize: number;
  };
  cache: {
    defaultTtl: number;
    longTtl: number;
    shortTtl: number;
  };
  uploads: UploadsConfig;
  user: UserConfig;
  content: ContentConfig;
  media: MediaConfig;
  search: SearchConfig;
  security: SecurityConfig;
  debug: boolean;
};

// Appwrite configuration
const config: Config = {
  // Appwrite settings
  appwriteEndpoint: getEnv('APPWRITE_ENDPOINT', 'http://localhost/v1'),
  appwriteProjectId: getEnv('APPWRITE_PROJECT_ID', ''),
  appwriteDatabaseId: getEnv('APPWRITE_DATABASE_ID', 'cms'),
  appwriteMediaBucketId: getEnv('APPWRITE_MEDIA_BUCKET_ID', 'media'),
  
  // Application settings
  defaultLocale: getEnv('DEFAULT_LOCALE', 'en-US'),
  siteUrl: getEnv('SITE_URL', 'http://localhost:3000'),
  apiPrefix: '/api',
  debug: process.env.NODE_ENV !== 'production',
  
  // Pagination
  pagination: {
    defaultPageSize: 10,
    maxPageSize: 100
  },
  
  // Cache settings
  cache: {
    defaultTtl: 3600, // 1 hour
    longTtl: 86400,   // 1 day
    shortTtl: 300     // 5 minutes
  },
  
  // Upload settings
  uploads: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ],
    imageResize: {
      maxWidth: 1920,
      maxHeight: 1080,
      quality: 85
    }
  },
  
  // User settings
  user: {
    defaultRole: 'subscriber',
    defaultStatus: 'active',
    passwordMinLength: 8,
    usernameMinLength: 3,
    usernameMaxLength: 50
  },
  
  // Content settings
  content: {
    defaultStatus: 'draft',
    defaultCommentStatus: 'open',
    defaultPingStatus: 'open',
    postsPerPage: 10,
    commentsPerPage: 50,
    excerptLength: 55,
    excerptMore: '...',
    feedType: 'rss2',
    feedItems: 10,
    rss2: '/feed',
    rss: '/feed/rss',
    rdf: '/feed/rdf',
    atom: '/feed/atom'
  },
  
  // Media settings
  media: {
    sizes: {
      thumbnail: { width: 150, height: 150, crop: true },
      medium: { width: 300, height: 300 },
      large: { width: 1024, height: 1024 },
      full: { width: 0, height: 0 } // Original size
    },
    defaultSize: 'large'
  },
  
  // Search settings
  search: {
    minQueryLength: 2,
    maxSuggestions: 5,
    highlight: {
      preTag: '<mark>',
      postTag: '</mark>',
      fragmentSize: 100,
      numberOfFragments: 3
    }
  },
  
  // Security settings
  security: {
    jwtSecret: getEnv('JWT_SECRET', 'your-secret-key-change-this-in-production'),
    passwordSaltRounds: 10,
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100 // limit each IP to 100 requests per windowMs
    },
    cors: {
      origin: getEnv('CORS_ORIGIN', '*'),
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization'],
      credentials: true
    }
  },
};

// Export individual config sections for easier imports
export const appwriteConfig = {
  endpoint: config.appwriteEndpoint,
  projectId: config.appwriteProjectId,
  databaseId: config.appwriteDatabaseId,
  bucketId: config.appwriteMediaBucketId
};

export const appConfig = {
  defaultLocale: config.defaultLocale,
  siteUrl: config.siteUrl,
  apiPrefix: config.apiPrefix,
  debug: config.debug
};

export const paginationConfig = { ...config.pagination };
export const cacheConfig = { ...config.cache };
export const uploadsConfig = { ...config.uploads };
export const userConfig = { ...config.user };
export const contentConfig = { ...config.content };
export const mediaConfig = { ...config.media };
export const searchConfig = { ...config.search };
export const securityConfig = { ...config.security };

// Export the full config as default
export default config;