import { Client, Databases, Query, ID, Permission, Role } from 'node-appwrite';
import config from '../config';

// Types for database responses
export interface DatabaseResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  code?: number;
  type?: string;
  message?: string;
}

export interface DatabaseDocument {
  $id: string;
  $collectionId: string;
  $databaseId: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  [key: string]: any;
}

export interface DatabaseList<T> {
  total: number;
  documents: T[];
}

class DatabaseServer {
  private static instance: DatabaseServer;
  private client: Client;
  private databases: Databases;
  private databaseId: string;

  private constructor() {
    this.client = new Client()
      .setEndpoint(config.appwriteEndpoint)
      .setProject(config.appwriteProjectId)
      .setKey(process.env.APPWRITE_API_KEY || '');

    this.databases = new Databases(this.client);
    this.databaseId = config.appwriteDatabaseId;
  }

  public static getInstance(): DatabaseServer {
    if (!DatabaseServer.instance) {
      DatabaseServer.instance = new DatabaseServer();
    }
    return DatabaseServer.instance;
  }

  /**
   * Create a new document in the specified collection
   */
  async createDocument<T extends DatabaseDocument>(
    collectionId: string,
    data: Omit<T, keyof DatabaseDocument>,
    permissions: string[] = [
      Permission.read(Role.any()),
      Permission.update(Role.any()),
      Permission.delete(Role.any())
    ]
  ): Promise<DatabaseResponse<T>> {
    try {
      const document = await this.databases.createDocument<T>(
        this.databaseId,
        collectionId,
        ID.unique(),
        data,
        permissions
      );

      return {
        success: true,
        data: document
      };
    } catch (error: any) {
      return this.handleError<T>(error);
    }
  }

  /**
   * Get a document by ID
   */
  async getDocument<T extends DatabaseDocument>(
    collectionId: string,
    documentId: string
  ): Promise<DatabaseResponse<T>> {
    try {
      const document = await this.databases.getDocument<T>(
        this.databaseId,
        collectionId,
        documentId
      );

      return {
        success: true,
        data: document
      };
    } catch (error: any) {
      return this.handleError<T>(error);
    }
  }

  /**
   * List documents with optional queries
   */
  async listDocuments<T extends DatabaseDocument>(
    collectionId: string,
    options: {
      queries?: string[];
      limit?: number;
      offset?: number;
      orderAttributes?: string[];
      orderTypes?: string[];
      cursor?: string;
      cursorDirection?: 'after' | 'before';
    } = {}
  ): Promise<DatabaseResponse<DatabaseList<T>>> {
    const {
      queries = [],
      limit = 25,
      offset = 0,
      orderAttributes = ['$createdAt'],
      orderTypes = ['DESC'],
      cursor,
      cursorDirection = 'after'
    } = options;

    try {
      // Add pagination queries
      const allQueries = [
        ...queries,
        Query.limit(limit),
        Query.offset(offset),
        ...orderAttributes.map((attr, index) =>
          Query.orderDesc(attr, orderTypes[index] === 'DESC')
        )
      ];

      // Add cursor-based pagination if cursor is provided
      if (cursor) {
        allQueries.push(
          cursorDirection === 'after'
            ? Query.cursorAfter(cursor)
            : Query.cursorBefore(cursor)
        );
      }

      const response = await this.databases.listDocuments<T>(
        this.databaseId,
        collectionId,
        allQueries
      );

      return {
        success: true,
        data: {
          total: response.total,
          documents: response.documents
        }
      };
    } catch (error: any) {
      return this.handleError<DatabaseList<T>>(error);
    }
  }

  /**
   * Update a document
   */
  async updateDocument<T extends DatabaseDocument>(
    collectionId: string,
    documentId: string,
    data: Partial<Omit<T, keyof DatabaseDocument>>,
    permissions?: string[]
  ): Promise<DatabaseResponse<T>> {
    try {
      const document = await this.databases.updateDocument<T>(
        this.databaseId,
        collectionId,
        documentId,
        data,
        permissions
      );

      return {
        success: true,
        data: document
      };
    } catch (error: any) {
      return this.handleError<T>(error);
    }
  }

  /**
   * Delete a document
   */
  async deleteDocument(
    collectionId: string,
    documentId: string
  ): Promise<DatabaseResponse<boolean>> {
    try {
      await this.databases.deleteDocument(
        this.databaseId,
        collectionId,
        documentId
      );

      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      return this.handleError<boolean>(error);
    }
  }

  /**
   * Create a new collection
   */
  async createCollection(
    collectionId: string,
    name: string,
    permissions: string[] = [
      Permission.read(Role.any()),
      Permission.create(Role.any()),
      Permission.update(Role.any()),
      Permission.delete(Role.any())
    ],
    documentSecurity: boolean = true
  ): Promise<DatabaseResponse<boolean>> {
    try {
      await this.databases.createCollection(
        this.databaseId,
        collectionId,
        name,
        permissions,
        documentSecurity
      );

      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      return this.handleError<boolean>(error);
    }
  }

  /**
   * Create a string attribute in a collection
   */
  async createStringAttribute(
    collectionId: string,
    key: string,
    size: number,
    required: boolean,
    defaultValue?: string,
    array: boolean = false
  ): Promise<DatabaseResponse<boolean>> {
    try {
      await this.databases.createStringAttribute(
        this.databaseId,
        collectionId,
        key,
        size,
        required,
        defaultValue,
        array
      );

      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      return this.handleError<boolean>(error);
    }
  }

  /**
   * Create an integer attribute in a collection
   */
  async createIntegerAttribute(
    collectionId: string,
    key: string,
    required: boolean,
    min?: number,
    max?: number,
    defaultValue?: number,
    array: boolean = false
  ): Promise<DatabaseResponse<boolean>> {
    try {
      await this.databases.createIntegerAttribute(
        this.databaseId,
        collectionId,
        key,
        required,
        min,
        max,
        defaultValue,
        array
      );

      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      return this.handleError<boolean>(error);
    }
  }

  /**
   * Create a float attribute in a collection
   */
  async createFloatAttribute(
    collectionId: string,
    key: string,
    required: boolean,
    min?: number,
    max?: number,
    defaultValue?: number,
    array: boolean = false
  ): Promise<DatabaseResponse<boolean>> {
    try {
      await this.databases.createFloatAttribute(
        this.databaseId,
        collectionId,
        key,
        required,
        min,
        max,
        defaultValue,
        array
      );

      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      return this.handleError<boolean>(error);
    }
  }

  /**
   * Create a boolean attribute in a collection
   */
  async createBooleanAttribute(
    collectionId: string,
    key: string,
    required: boolean,
    defaultValue?: boolean,
    array: boolean = false
  ): Promise<DatabaseResponse<boolean>> {
    try {
      await this.databases.createBooleanAttribute(
        this.databaseId,
        collectionId,
        key,
        required,
        defaultValue,
        array
      );

      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      return this.handleError<boolean>(error);
    }
  }

  /**
   * Create an email attribute in a collection
   */
  async createEmailAttribute(
    collectionId: string,
    key: string,
    required: boolean,
    defaultValue?: string,
    array: boolean = false
  ): Promise<DatabaseResponse<boolean>> {
    try {
      await this.databases.createEmailAttribute(
        this.databaseId,
        collectionId,
        key,
        required,
        defaultValue,
        array
      );

      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      return this.handleError<boolean>(error);
    }
  }

  /**
   * Create a URL attribute in a collection
   */
  async createUrlAttribute(
    collectionId: string,
    key: string,
    required: boolean,
    defaultValue?: string,
    array: boolean = false
  ): Promise<DatabaseResponse<boolean>> {
    try {
      await this.databases.createUrlAttribute(
        this.databaseId,
        collectionId,
        key,
        required,
        defaultValue,
        array
      );

      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      return this.handleError<boolean>(error);
    }
  }

  /**
   * Create an IP address attribute in a collection
   */
  async createIpAddressAttribute(
    collectionId: string,
    key: string,
    required: boolean,
    defaultValue?: string,
    array: boolean = false
  ): Promise<DatabaseResponse<boolean>> {
    try {
      await this.databases.createIpAddressAttribute(
        this.databaseId,
        collectionId,
        key,
        required,
        defaultValue,
        array
      );

      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      return this.handleError<boolean>(error);
    }
  }

  /**
   * Create a relationship between collections
   */
  async createRelationship(
    collectionId: string,
    relatedCollectionId: string,
    type: 'oneToOne' | 'oneToMany' | 'manyToMany',
    twoWay: boolean = false,
    twoWayKey?: string,
    onDelete: 'cascade' | 'restrict' | 'setNull' = 'setNull'
  ): Promise<DatabaseResponse<boolean>> {
    try {
      await this.databases.createRelationship(
        this.databaseId,
        collectionId,
        relatedCollectionId,
        type,
        twoWay,
        twoWayKey,
        onDelete
      );

      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      return this.handleError<boolean>(error);
    }
  }

  /**
   * Create an index on a collection
   */
  async createIndex(
    collectionId: string,
    key: string,
    type: 'key' | 'fulltext' | 'unique' | 'array',
    attributes: string[],
    orders: ('ASC' | 'DESC')[] = []
  ): Promise<DatabaseResponse<boolean>> {
    try {
      await this.databases.createIndex(
        this.databaseId,
        collectionId,
        key,
        type,
        attributes,
        orders
      );

      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      return this.handleError<boolean>(error);
    }
  }

  /**
   * Handle errors consistently
   */
  private handleError<T>(error: any): DatabaseResponse<T> {
    console.error('Database Error:', error);

    // Default error response
    const errorResponse: DatabaseResponse<T> = {
      success: false,
      error: 'An unexpected error occurred',
      code: 500
    };

    // Handle Appwrite errors
    if (error.response) {
      const { message, code, type } = error.response;
      errorResponse.error = message || error.message;
      errorResponse.code = code || 500;
      errorResponse.type = type;
    } else if (error.message) {
      errorResponse.error = error.message;
    }

    return errorResponse;
  }
}

export const databaseServer = DatabaseServer.getInstance();

export * from './types/database';
