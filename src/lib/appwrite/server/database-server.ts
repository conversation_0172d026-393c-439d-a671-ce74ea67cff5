// @ts-nocheck
import { Client, Databases, ID, Models } from 'node-appwrite';
import { APPWRITE_CONFIG } from '../config';
import { AppwriteError } from '../utils/error-handler';
import type { 
  DatabaseDocument, 
  DatabaseResponse,
  PaginatedResponse,
  QueryOptions
} from '../types/database';

// Extend the Models namespace to include missing types
declare module 'node-appwrite' {
  namespace Models {
    interface Collection {
      $id: string;
      name: string;
      $createdAt: string;
      $updatedAt: string;
      $permissions: string[];
      documentSecurity: boolean;
      enabled: boolean;
    }
  }
}

export class DatabaseServer {
  private static instance: DatabaseServer;
  private client: Client;
  private databases: Databases;
  private databaseId: string;

  private constructor() {
    this.client = new Client()
      .setEndpoint(APPWRITE_CONFIG.endpoint)
      .setProject(APPWRITE_CONFIG.projectId)
      .setKey(process.env.APPWRITE_API_KEY || '');

    this.databases = new Databases(this.client);
    this.databaseId = APPWRITE_CONFIG.databaseId;
  }

  public static getInstance(): DatabaseServer {
    if (!DatabaseServer.instance) {
      DatabaseServer.instance = new DatabaseServer();
    }
    return DatabaseServer.instance;
  }

  // Document Operations
  async createDocument<T extends DatabaseDocument>(
    collectionId: string,
    data: Omit<T, keyof DatabaseDocument>,
    permissions: string[] = []
  ): Promise<DatabaseResponse<T>> {
    try {
      const document = await this.databases.createDocument<T>(
        this.databaseId,
        collectionId,
        ID.unique(),
        data,
        permissions
      );
      
      return {
        success: true,
        data: document
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getDocument<T extends DatabaseDocument>(
    collectionId: string,
    documentId: string
  ): Promise<DatabaseResponse<T>> {
    try {
      const document = await this.databases.getDocument<T>(
        this.databaseId,
        collectionId,
        documentId
      );
      
      return {
        success: true,
        data: document
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async listDocuments<T extends DatabaseDocument>(
    collectionId: string,
    options: QueryOptions = {}
  ): Promise<DatabaseResponse<PaginatedResponse<T>>> {
    const {
      queries = [],
      limit = 25,
      offset = 0,
      cursor,
      cursorDirection = 'after',
      orderAttributes = ['$createdAt'],
      orderTypes = ['DESC']
    } = options;

    // Add pagination queries
    const paginationQueries = [
      ...queries,
      `limit(${limit})`,
      `offset(${offset})`,
      ...orderAttributes.map((attr, index) => 
        `order${attr.startsWith('$') ? '' : 'By'}("${attr}", ${orderTypes[index] || 'DESC'})`
      )
    ];

    if (cursor) {
      paginationQueries.push(`cursor${cursorDirection}(${cursor})`);
    }
    try {
      const response = await this.databases.listDocuments<T>(
        this.databaseId,
        collectionId,
        queries
      );

      return {
        success: true,
        data: {
          total: response.total,
          documents: response.documents
        }
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async updateDocument<T extends DatabaseDocument>(
    collectionId: string,
    documentId: string,
    data: Partial<Omit<T, keyof DatabaseDocument>>,
    permissions?: string[]
  ): Promise<DatabaseResponse<T>> {
    try {
      const document = await this.databases.updateDocument<T>(
        this.databaseId,
        collectionId,
        documentId,
        data,
        permissions
      );
      
      return {
        success: true,
        data: document
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async deleteDocument(
    collectionId: string,
    documentId: string
  ): Promise<DatabaseResponse<boolean>> {
    try {
      await this.databases.deleteDocument(
        this.databaseId,
        collectionId,
        documentId
      );
      
      return {
        success: true,
        data: true
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Error handling
  private handleError<T>(error: unknown): DatabaseResponse<T> {
    const appwriteError = new AppwriteError(error);
    return {
      success: false,
      error: appwriteError.message,
      code: appwriteError.code
    };
  }
}

export const databaseServer = DatabaseServer.getInstance();
