import { Client, Storage, ID, Permission, Role } from 'node-appwrite';
import config from '../config';

// Types for storage responses
export interface StorageResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  code?: number;
  type?: string;
  message?: string;
}

export interface StorageFile {
  $id: string;
  bucketId: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  name: string;
  mimeType: string;
  sizeOriginal: number;
  signature: string;
  chunksTotal: number;
  chunksUploaded: number;
}

export interface StorageList<T> {
  total: number;
  files: T[];
}

class StorageServer {
  private static instance: StorageServer;
  private client: Client;
  private storage: Storage;
  private defaultBucketId: string;

  private constructor() {
    this.client = new Client()
      .setEndpoint(config.appwriteEndpoint)
      .setProject(config.appwriteProjectId)
      .setKey(process.env.APPWRITE_API_KEY || '');

    this.storage = new Storage(this.client);
    this.defaultBucketId = 'default';
  }

  public static getInstance(): StorageServer {
    if (!StorageServer.instance) {
      StorageServer.instance = new StorageServer();
    }
    return StorageServer.instance;
  }

  /**
   * Create a new file in the specified bucket
   */
  async createFile(
    bucketId: string,
    file: Buffer | File | Blob,
    fileName: string,
    mimeType: string,
    permissions: string[] = [
      Permission.read(Role.any()),
      Permission.update(Role.any()),
      Permission.delete(Role.any())
    ]
  ): Promise<StorageResponse<StorageFile>> {
    try {
      const fileId = ID.unique();
      
      const result = await this.storage.createFile(
        bucketId || this.defaultBucketId,
        fileId,
        file,
        permissions,
        fileName
      );

      return {
        success: true,
        data: result as unknown as StorageFile
      };
    } catch (error: any) {
      return this.handleError<StorageFile>(error);
    }
  }

  /**
   * Get file by ID
   */
  async getFile(
    bucketId: string,
    fileId: string
  ): Promise<StorageResponse<StorageFile>> {
    try {
      const file = await this.storage.getFile(
        bucketId || this.defaultBucketId,
        fileId
      );

      return {
        success: true,
        data: file as unknown as StorageFile
      };
    } catch (error: any) {
      return this.handleError<StorageFile>(error);
    }
  }

  /**
   * Get file content for download
   */
  async getFileDownload(
    bucketId: string,
    fileId: string
  ): Promise<StorageResponse<ArrayBuffer>> {
    try {
      const result = await this.storage.getFileDownload(
        bucketId || this.defaultBucketId,
        fileId
      );

      return {
        success: true,
        data: result as unknown as ArrayBuffer
      };
    } catch (error: any) {
      return this.handleError<ArrayBuffer>(error);
    }
  }

  /**
   * Get file preview
   */
  async getFilePreview(
    bucketId: string,
    fileId: string,
    width?: number,
    height?: number,
    gravity?: 'center' | 'top' | 'bottom' | 'left' | 'right' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right',
    quality?: number,
    borderWidth?: number,
    borderColor?: string,
    borderRadius?: number,
    opacity?: number,
    rotation?: number,
    background?: string,
    output?: 'jpg' | 'jpeg' | 'png' | 'gif' | 'webp'
  ): Promise<StorageResponse<ArrayBuffer>> {
    try {
      const result = await this.storage.getFilePreview(
        bucketId || this.defaultBucketId,
        fileId,
        width,
        height,
        gravity,
        quality,
        borderWidth,
        borderColor,
        borderRadius,
        opacity,
        rotation,
        background,
        output
      );

      return {
        success: true,
        data: result as unknown as ArrayBuffer
      };
    } catch (error: any) {
      return this.handleError<ArrayBuffer>(error);
    }
  }

  /**
   * Get file for view (browser compatible)
   */
  async getFileView(
    bucketId: string,
    fileId: string
  ): Promise<StorageResponse<string>> {
    try {
      const result = await this.storage.getFileView(
        bucketId || this.defaultBucketId,
        fileId
      );

      return {
        success: true,
        data: result.toString()
      };
    } catch (error: any) {
      return this.handleError<string>(error);
    }
  }

  /**
   * List files in a bucket
   */
  async listFiles(
    bucketId: string,
    queries: string[] = [],
    search?: string
  ): Promise<StorageResponse<StorageList<StorageFile>>> {
    try {
      const result = await this.storage.listFiles(
        bucketId || this.defaultBucketId,
        queries,
        search
      );

      return {
        success: true,
        data: {
          total: result.total,
          files: result.files as unknown as StorageFile[]
        }
      };
    } catch (error: any) {
      return this.handleError<StorageList<StorageFile>>(error);
    }
  }

  /**
   * Update file
   */
  async updateFile(
    bucketId: string,
    fileId: string,
    name?: string,
    permissions?: string[]
  ): Promise<StorageResponse<StorageFile>> {
    try {
      const result = await this.storage.updateFile(
        bucketId || this.defaultBucketId,
        fileId,
        name,
        permissions
      );

      return {
        success: true,
        data: result as unknown as StorageFile
      };
    } catch (error: any) {
      return this.handleError<StorageFile>(error);
    }
  }

  /**
   * Delete file
   */
  async deleteFile(
    bucketId: string,
    fileId: string
  ): Promise<StorageResponse<boolean>> {
    try {
      await this.storage.deleteFile(
        bucketId || this.defaultBucketId,
        fileId
      );

      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      return this.handleError<boolean>(error);
    }
  }

  /**
   * Get file download URL
   */
  getFileDownloadUrl(
    bucketId: string,
    fileId: string
  ): string {
    return `${this.client.config.endpoint}/storage/buckets/${bucketId || this.defaultBucketId}/files/${fileId}/download?project=${this.client.config.project}`;
  }

  /**
   * Get file preview URL
   */
  getFilePreviewUrl(
    bucketId: string,
    fileId: string,
    width?: number,
    height?: number,
    gravity?: string,
    quality?: number,
    borderWidth?: number,
    borderColor?: string,
    borderRadius?: number,
    opacity?: number,
    rotation?: number,
    background?: string,
    output?: string
  ): string {
    const params = new URLSearchParams({
      project: this.client.config.project,
      ...(width && { width: width.toString() }),
      ...(height && { height: height.toString() }),
      ...(gravity && { gravity }),
      ...(quality && { quality: quality.toString() }),
      ...(borderWidth && { 'border-width': borderWidth.toString() }),
      ...(borderColor && { 'border-color': borderColor }),
      ...(borderRadius && { 'border-radius': borderRadius.toString() }),
      ...(opacity && { opacity: opacity.toString() }),
      ...(rotation && { rotation: rotation.toString() }),
      ...(background && { background }),
      ...(output && { output })
    }).toString();

    return `${this.client.config.endpoint}/storage/buckets/${bucketId || this.defaultBucketId}/files/${fileId}/preview?${params}`;
  }

  /**
   * Create a new bucket
   */
  async createBucket(
    bucketId: string,
    name: string,
    permissions: string[] = [
      Permission.read(Role.any()),
      Permission.create(Role.any()),
      Permission.update(Role.any()),
      Permission.delete(Role.any())
    ],
    fileSecurity: boolean = true,
    enabled: boolean = true,
    maximumFileSize: number = 30 * 1024 * 1024, // 30MB
    allowedFileExtensions: string[] = [
      'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'ico',
      'mp3', 'wav', 'ogg', 'mp4', 'webm', 'mov', 'avi', 'mkv',
      'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt',
      'zip', 'rar', '7z', 'tar', 'gz'
    ],
    encryption: boolean = false,
    antivirus: boolean = true
  ): Promise<StorageResponse<boolean>> {
    try {
      await this.storage.createBucket(
        bucketId,
        name,
        permissions,
        fileSecurity,
        enabled,
        maximumFileSize,
        allowedFileExtensions,
        encryption,
        antivirus
      );

      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      return this.handleError<boolean>(error);
    }
  }

  /**
   * Delete a bucket
   */
  async deleteBucket(bucketId: string): Promise<StorageResponse<boolean>> {
    try {
      await this.storage.deleteBucket(bucketId);

      return {
        success: true,
        data: true
      };
    } catch (error: any) {
      return this.handleError<boolean>(error);
    }
  }

  /**
   * Handle errors consistently
   */
  private handleError<T>(error: any): StorageResponse<T> {
    console.error('Storage Error:', error);

    // Default error response
    const errorResponse: StorageResponse<T> = {
      success: false,
      error: 'An unexpected error occurred',
      code: 500
    };

    // Handle Appwrite errors
    if (error.response) {
      const { message, code, type } = error.response;
      errorResponse.error = message || error.message;
      errorResponse.code = code || 500;
      errorResponse.type = type;
    } else if (error.message) {
      errorResponse.error = error.message;
    }

    return errorResponse;
  }
}

export const storageServer = StorageServer.getInstance();
