import { Storage } from 'node-appwrite';
import { ID } from 'appwrite';
import { createReadStream, unlinkSync } from 'fs';
import { join } from 'path';
import { APPWRITE_CONFIG } from '../config';
import { AppwriteError } from '../utils/error-handler';
import type {
  File,
  FileList,
  StorageResponse,
  FilePreviewOptions,
} from '../types/storage';

export class StorageServer {
  private static instance: StorageServer;
  private storage: Storage;
  private bucketId: string;

  private constructor() {
    const client = new (require('node-appwrite').Client)()
      .setEndpoint(APPWRITE_CONFIG.endpoint)
      .setProject(APPWRITE_CONFIG.projectId)
      .setKey(process.env.APPWRITE_API_KEY || '');

    this.storage = new Storage(client);
    this.bucketId = process.env.APPWRITE_STORAGE_BUCKET_ID || 'default';
  }

  public static getInstance(): StorageServer {
    if (!StorageServer.instance) {
      StorageServer.instance = new StorageServer();
    }
    return StorageServer.instance;
  }

  async uploadFile(
    file: Buffer,
    filename: string,
    mimeType: string,
    read: string[] = [],
    write: string[] = []
  ): Promise<StorageResponse<File>> {
    const tempFilePath = `/tmp/${Date.now()}_${filename}`;
    
    try {
      // Write the buffer to a temporary file
      require('fs').writeFileSync(tempFilePath, file);
      
      // Create a File object from the buffer
      const fileToUpload = new (require('node:buffer').File)(
        [file],
        filename,
        { type: mimeType }
      );
      
      // Generate a unique file ID
      const fileId = ID.unique();
      
      // Convert permissions to the correct format
      const permissions = [
        ...read.map(role => `read(${role})`),
        ...write.map(role => `write(${role})`)
      ];
      
      // Upload the file
      const uploadedFile = await this.storage.createFile(
        this.bucketId,
        fileId,
        fileToUpload,
        permissions,
      );

      // Clean up the temporary file
      try {
        unlinkSync(tempFilePath);
      } catch (cleanupError) {
        console.error('Error cleaning up temporary file:', cleanupError);
      }
      
      return {
        success: true,
        data: {
          ...uploadedFile,
          name: filename,
          type: mimeType,
          size: file.length,
          lastModified: Date.now()
        } as File,
      };
    } catch (error) {
      // Clean up the temporary file in case of error
      try {
        if (require('fs').existsSync(tempFilePath)) {
          unlinkSync(tempFilePath);
        }
      } catch (cleanupError) {
        console.error('Error cleaning up temporary file after error:', cleanupError);
      }
      
      return this.handleError(error);
    }
  }

  async getFile(fileId: string): Promise<StorageResponse<Buffer>> {
    try {
      const buffer = await this.storage.getFileDownload(this.bucketId, fileId);
      return {
        success: true,
        data: buffer as unknown as Buffer,
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getFilePreview(
    fileId: string,
    options: FilePreviewOptions = {}
  ): Promise<StorageResponse<Buffer>> {
    try {
      const buffer = await this.storage.getFilePreview(
        this.bucketId,
        fileId,
        options.width,
        options.height,
        options.gravity,
        options.quality,
        options.borderWidth,
        options.borderColor,
        options.borderRadius,
        options.opacity,
        options.rotation,
        options.background,
        options.output
      );

      return {
        success: true,
        data: buffer as unknown as Buffer,
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async listFiles(
    queries: string[] = [],
    search?: string
  ): Promise<StorageResponse<FileList>> {
    try {
      const response = await this.storage.listFiles(
        this.bucketId,
        queries,
        search
      );

      return {
        success: true,
        data: {
          total: response.total,
          files: response.files as unknown as File[],
        },
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async updateFile(
    fileId: string,
    read?: string | string[],
    write?: string | string[]
  ): Promise<StorageResponse<File>> {
    try {
      // Convert to comma-separated strings if needed
      const readPermissions = Array.isArray(read) ? read.join(',') : (read || '');
      const writePermissions = Array.isArray(write) ? write.join(',') : (write || '');

      // Use type assertion to handle the Appwrite Storage client's type mismatch
      const storage = this.storage as unknown as {
        updateFile: (
          bucketId: string,
          fileId: string,
          read: string,
          write: string
        ) => Promise<unknown>
      };

      const updatedFile = await storage.updateFile(
        this.bucketId,
        fileId,
        readPermissions,
        writePermissions
      ) as File;

      return {
        success: true,
        data: updatedFile as unknown as File,
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async deleteFile(fileId: string): Promise<StorageResponse<boolean>> {
    try {
      await this.storage.deleteFile(this.bucketId, fileId);
      return {
        success: true,
        data: true,
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  private handleError<T>(error: unknown): StorageResponse<T> {
    const appwriteError = new AppwriteError(error);
    return {
      success: false,
      error: appwriteError.message,
      code: appwriteError.code,
    };
  }
}

export const storageServer = StorageServer.getInstance();
