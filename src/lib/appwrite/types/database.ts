// Base document interface
export interface DatabaseDocument {
  $id: string;
  $collectionId: string;
  $databaseId: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  [key: string]: any;
}

// Collection interface
export interface Collection {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  name: string;
  enabled: boolean;
  documentSecurity: boolean;
  attributes: Attribute[];
  indexes: Index[];
}

// Attribute types
export type Attribute = 
  | StringAttribute
  | IntegerAttribute
  | FloatAttribute
  | BooleanAttribute
  | DatetimeAttribute
  | EmailAttribute
  | UrlAttribute
  | IpAttribute;

interface BaseAttribute {
  key: string;
  type: string;
  status: string;
  required: boolean;
  array?: boolean;
  default?: any;
}

interface StringAttribute extends BaseAttribute {
  type: 'string';
  size: number;
}

interface IntegerAttribute extends BaseAttribute {
  type: 'integer';
  min?: number;
  max?: number;
}

interface FloatAttribute extends BaseAttribute {
  type: 'float';
  min?: number;
  max?: number;
}

interface BooleanAttribute extends BaseAttribute {
  type: 'boolean';
}

interface DatetimeAttribute extends BaseAttribute {
  type: 'datetime';
}

interface EmailAttribute extends BaseAttribute {
  type: 'email';
}

interface UrlAttribute extends BaseAttribute {
  type: 'url';
}

interface IpAttribute extends BaseAttribute {
  type: 'ip';
}

// Index types
export interface Index {
  key: string;
  type: 'key' | 'unique' | 'fulltext' | 'spatial';
  status: string;
  attributes: string[];
  orders?: ('ASC' | 'DESC')[];
}

// Query options for listing documents
export interface QueryOptions {
  queries?: string[];
  limit?: number;
  offset?: number;
  cursor?: string;
  cursorDirection?: 'after' | 'before';
  orderAttributes?: string[];
  orderTypes?: ('ASC' | 'DESC')[];
}

// Standard response format for database operations
export interface DatabaseResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: number;
}

// Paginated response format
export interface PaginatedResponse<T> {
  total: number;
  documents: T[];
}

// User session type
export interface UserSession {
  $id: string;
  email: string;
  name: string;
  // Add other user fields as needed
}

// Collection attribute definition
export interface CollectionAttribute {
  key: string;
  type: 'string' | 'integer' | 'float' | 'boolean' | 'datetime' | 'email' | 'url' | 'ip';
  size?: number;
  required: boolean;
  defaultValue?: any;
  array?: boolean;
  format?: string;
  min?: number;
  max?: number;
}

// Index definition
export interface CollectionIndex {
  key: string;
  type: 'key' | 'fulltext' | 'unique' | 'spatial';
  attributes: string[];
  orders?: ('ASC' | 'DESC')[];
}
