import type { Models } from 'appwrite';
import type { ImageGravity as AppwriteImageGravity } from 'node-appwrite';

export type AppwriteFile = Models.File;

export type ImageGravity = AppwriteImageGravity;

// Extended File interface that includes both browser File and Appwrite File properties
export interface File extends Omit<Models.File, 'mimeType' | 'sizeOriginal' | 'chunksTotal' | 'chunksUploaded'> {
  // Browser File properties
  name: string;
  size: number;
  type: string;
  lastModified?: number;
  webkitRelativePath?: string;
  
  // Appwrite File properties
  $id: string;
  bucketId: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  mimeType: string;
  sizeOriginal: number;
  signature: string;
  chunksTotal: number;
  chunksUploaded: number;
  
  // Additional methods from browser File
  arrayBuffer?(): Promise<ArrayBuffer>;
  slice?(start?: number, end?: number, contentType?: string): Blob;
  stream?(): ReadableStream<Uint8Array>;
  text?(): Promise<string>;
}

export interface FileList {
  total: number;
  files: File[];
}

export interface UploadProgress {
  $id: string;
  progress: number;
  sizeUploaded: number;
  chunksTotal: number;
  chunksUploaded: number;
}

export interface UploadOptions {
  read?: string | string[];
  write?: string | string[];
  onProgress?: (progress: UploadProgress) => void;
}

export interface FilePreviewOptions {
  width?: number;
  height?: number;
  gravity?: ImageGravity;
  quality?: number;
  borderWidth?: number;
  borderColor?: string;
  borderRadius?: number;
  opacity?: number;
  rotation?: number;
  background?: string;
  output?: import('node-appwrite').ImageFormat;
}

export interface StorageResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: number;
}

export type FileInput = File | Blob | ArrayBuffer | Uint8Array;