import { prisma } from '@/lib/prisma'

export interface PageViewData {
  id: string
  path: string
  userAgent: string
  ip: string
  referer: string
  createdAt: Date
}

export interface AnalyticsData {
  totalViews: number
  uniqueViews: number
  avgTimeOnPage: number
  bounceRate: number
  topPages: Array<{
    path: string
    views: number
    uniqueViews: number
  }>
  topReferrers: Array<{
    referer: string
    views: number
  }>
  deviceTypes: Array<{
    type: string
    count: number
    percentage: number
  }>
  browsers: Array<{
    browser: string
    count: number
    percentage: number
  }>
  countries: Array<{
    country: string
    count: number
    percentage: number
  }>
  dailyViews: Array<{
    date: string
    views: number
    uniqueViews: number
  }>
}

export interface PagePerformanceMetrics {
  pageId: string
  path: string
  title: string
  views: number
  uniqueViews: number
  avgTimeOnPage: number
  bounceRate: number
  conversionRate: number
  seoScore: number
  loadTime: number
  lastUpdated: Date
}

export class AnalyticsService {
  /**
   * Track a page view
   */
  static async trackPageView(data: {
    path: string
    userAgent?: string
    ip?: string
    referer?: string
    sessionId?: string
    userId?: string
  }): Promise<void> {
    try {
      await prisma.pageView.create({
        data: {
          path: data.path,
          userAgent: data.userAgent || '',
          ip: data.ip || '',
          referer: data.referer || '',
          sessionId: data.sessionId,
          userId: data.userId
        }
      })
    } catch (error) {
      console.error('Failed to track page view:', error)
      // Don't throw error to avoid breaking the page
    }
  }

  /**
   * Get analytics data for a date range
   */
  static async getAnalytics(options: {
    startDate?: Date
    endDate?: Date
    path?: string
  } = {}): Promise<AnalyticsData> {
    const { startDate, endDate, path } = options
    
    const where: any = {}
    
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = startDate
      if (endDate) where.createdAt.lte = endDate
    }
    
    if (path) {
      where.path = path
    }

    // Get total views
    const totalViews = await prisma.pageView.count({ where })

    // Get unique views (by IP)
    const uniqueViewsResult = await prisma.pageView.groupBy({
      by: ['ip'],
      where,
      _count: { ip: true }
    })
    const uniqueViews = uniqueViewsResult.length

    // Get top pages
    const topPagesResult = await prisma.pageView.groupBy({
      by: ['path'],
      where,
      _count: { path: true },
      orderBy: { _count: { path: 'desc' } },
      take: 10
    })

    const topPages = await Promise.all(
      topPagesResult.map(async (item) => {
        const uniqueForPage = await prisma.pageView.groupBy({
          by: ['ip'],
          where: { ...where, path: item.path },
          _count: { ip: true }
        })
        
        return {
          path: item.path,
          views: item._count.path,
          uniqueViews: uniqueForPage.length
        }
      })
    )

    // Get top referrers
    const topReferrersResult = await prisma.pageView.groupBy({
      by: ['referer'],
      where: {
        ...where,
        referer: { not: '' }
      },
      _count: { referer: true },
      orderBy: { _count: { referer: 'desc' } },
      take: 10
    })

    const topReferrers = topReferrersResult.map(item => ({
      referer: item.referer,
      views: item._count.referer
    }))

    // Get device types from user agent
    const userAgents = await prisma.pageView.findMany({
      where,
      select: { userAgent: true }
    })

    const deviceTypes = this.analyzeDeviceTypes(userAgents.map(ua => ua.userAgent))
    const browsers = this.analyzeBrowsers(userAgents.map(ua => ua.userAgent))

    // Get daily views for the last 30 days
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const dailyViewsResult = await prisma.pageView.groupBy({
      by: ['createdAt'],
      where: {
        ...where,
        createdAt: { gte: thirtyDaysAgo }
      },
      _count: { createdAt: true }
    })

    const dailyViews = this.groupByDay(dailyViewsResult)

    return {
      totalViews,
      uniqueViews,
      avgTimeOnPage: 0, // Would need session tracking for this
      bounceRate: 0, // Would need session tracking for this
      topPages,
      topReferrers,
      deviceTypes,
      browsers,
      countries: [], // Would need IP geolocation for this
      dailyViews
    }
  }

  /**
   * Get performance metrics for pages
   */
  static async getPagePerformanceMetrics(options: {
    startDate?: Date
    endDate?: Date
    limit?: number
  } = {}): Promise<PagePerformanceMetrics[]> {
    const { startDate, endDate, limit = 50 } = options

    const pages = await prisma.page.findMany({
      where: {
        status: 'PUBLISHED'
      },
      include: {
        _count: {
          select: {
            blocks: true
          }
        }
      },
      take: limit,
      orderBy: { updatedAt: 'desc' }
    })

    const metrics = await Promise.all(
      pages.map(async (page) => {
        const where: any = { path: `/${page.slug}` }
        
        if (startDate || endDate) {
          where.createdAt = {}
          if (startDate) where.createdAt.gte = startDate
          if (endDate) where.createdAt.lte = endDate
        }

        const views = await prisma.pageView.count({ where })
        
        const uniqueViewsResult = await prisma.pageView.groupBy({
          by: ['ip'],
          where,
          _count: { ip: true }
        })
        const uniqueViews = uniqueViewsResult.length

        return {
          pageId: page.id,
          path: `/${page.slug}`,
          title: page.title,
          views,
          uniqueViews,
          avgTimeOnPage: 0, // Would need session tracking
          bounceRate: 0, // Would need session tracking
          conversionRate: 0, // Would need goal tracking
          seoScore: 0, // Would integrate with SEO service
          loadTime: 0, // Would need performance tracking
          lastUpdated: page.updatedAt
        }
      })
    )

    return metrics.sort((a, b) => b.views - a.views)
  }

  /**
   * Get real-time analytics
   */
  static async getRealTimeAnalytics(): Promise<{
    activeUsers: number
    currentPageViews: Array<{
      path: string
      views: number
      timestamp: Date
    }>
    recentViews: PageViewData[]
  }> {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000)

    // Active users (unique IPs in last 5 minutes)
    const activeUsersResult = await prisma.pageView.groupBy({
      by: ['ip'],
      where: {
        createdAt: { gte: fiveMinutesAgo }
      },
      _count: { ip: true }
    })
    const activeUsers = activeUsersResult.length

    // Current page views (last minute)
    const currentPageViewsResult = await prisma.pageView.groupBy({
      by: ['path'],
      where: {
        createdAt: { gte: oneMinuteAgo }
      },
      _count: { path: true },
      orderBy: { _count: { path: 'desc' } }
    })

    const currentPageViews = currentPageViewsResult.map(item => ({
      path: item.path,
      views: item._count.path,
      timestamp: new Date()
    }))

    // Recent views
    const recentViews = await prisma.pageView.findMany({
      where: {
        createdAt: { gte: fiveMinutesAgo }
      },
      orderBy: { createdAt: 'desc' },
      take: 20
    })

    return {
      activeUsers,
      currentPageViews,
      recentViews
    }
  }

  /**
   * Get analytics for a specific page
   */
  static async getPageAnalytics(slug: string, options: {
    startDate?: Date
    endDate?: Date
  } = {}): Promise<{
    views: number
    uniqueViews: number
    referrers: Array<{ referer: string; count: number }>
    devices: Array<{ type: string; count: number }>
    dailyViews: Array<{ date: string; views: number }>
  }> {
    const { startDate, endDate } = options
    const path = `/${slug}`
    
    const where: any = { path }
    
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = startDate
      if (endDate) where.createdAt.lte = endDate
    }

    const views = await prisma.pageView.count({ where })
    
    const uniqueViewsResult = await prisma.pageView.groupBy({
      by: ['ip'],
      where,
      _count: { ip: true }
    })
    const uniqueViews = uniqueViewsResult.length

    const referrersResult = await prisma.pageView.groupBy({
      by: ['referer'],
      where: {
        ...where,
        referer: { not: '' }
      },
      _count: { referer: true },
      orderBy: { _count: { referer: 'desc' } },
      take: 10
    })

    const referrers = referrersResult.map(item => ({
      referer: item.referer,
      count: item._count.referer
    }))

    const userAgents = await prisma.pageView.findMany({
      where,
      select: { userAgent: true }
    })

    const devices = this.analyzeDeviceTypes(userAgents.map(ua => ua.userAgent))

    // Daily views for last 30 days
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const dailyViewsResult = await prisma.pageView.groupBy({
      by: ['createdAt'],
      where: {
        ...where,
        createdAt: { gte: thirtyDaysAgo }
      },
      _count: { createdAt: true }
    })

    const dailyViews = this.groupByDay(dailyViewsResult)

    return {
      views,
      uniqueViews,
      referrers,
      devices,
      dailyViews
    }
  }

  /**
   * Private helper methods
   */
  private static analyzeDeviceTypes(userAgents: string[]): Array<{
    type: string
    count: number
    percentage: number
  }> {
    const deviceCounts: Record<string, number> = {}
    const total = userAgents.length

    userAgents.forEach(ua => {
      const device = this.getDeviceType(ua)
      deviceCounts[device] = (deviceCounts[device] || 0) + 1
    })

    return Object.entries(deviceCounts).map(([type, count]) => ({
      type,
      count,
      percentage: Math.round((count / total) * 100)
    }))
  }

  private static analyzeBrowsers(userAgents: string[]): Array<{
    browser: string
    count: number
    percentage: number
  }> {
    const browserCounts: Record<string, number> = {}
    const total = userAgents.length

    userAgents.forEach(ua => {
      const browser = this.getBrowser(ua)
      browserCounts[browser] = (browserCounts[browser] || 0) + 1
    })

    return Object.entries(browserCounts).map(([browser, count]) => ({
      browser,
      count,
      percentage: Math.round((count / total) * 100)
    }))
  }

  private static getDeviceType(userAgent: string): string {
    const ua = userAgent.toLowerCase()
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      return 'Mobile'
    }
    if (ua.includes('tablet') || ua.includes('ipad')) {
      return 'Tablet'
    }
    return 'Desktop'
  }

  private static getBrowser(userAgent: string): string {
    const ua = userAgent.toLowerCase()
    if (ua.includes('chrome')) return 'Chrome'
    if (ua.includes('firefox')) return 'Firefox'
    if (ua.includes('safari')) return 'Safari'
    if (ua.includes('edge')) return 'Edge'
    if (ua.includes('opera')) return 'Opera'
    return 'Other'
  }

  private static groupByDay(data: any[]): Array<{ date: string; views: number; uniqueViews: number }> {
    const grouped: Record<string, number> = {}
    
    data.forEach(item => {
      const date = new Date(item.createdAt).toISOString().split('T')[0]
      grouped[date] = (grouped[date] || 0) + item._count.createdAt
    })

    return Object.entries(grouped).map(([date, views]) => ({
      date,
      views,
      uniqueViews: views // Simplified - would need proper unique counting
    }))
  }
}
