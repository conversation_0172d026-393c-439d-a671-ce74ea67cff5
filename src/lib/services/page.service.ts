import { prisma } from '@/lib/prisma'
import { PageStatus, UserRole } from '@prisma/client'
import { z } from 'zod'

// Types
export interface PageData {
  id: string
  title: string
  slug: string
  content?: string
  excerpt?: string
  template?: string
  status: PageStatus
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  seoTitle?: string
  seoDescription?: string
  featuredImage?: string
  author: {
    id: string
    name: string
    email?: string
    image?: string
    bio?: string
  }
  blocks?: Array<{
    id: string
    type: string
    data: any
    order: number
    createdAt: Date
    updatedAt: Date
  }>
  _count?: {
    views: number
    blocks: number
  }
}

export interface PageFilters {
  status?: PageStatus
  template?: string
  authorId?: string
  search?: string
  tags?: string[]
  dateFrom?: Date
  dateTo?: Date
}

export interface PageCreateData {
  title: string
  slug: string
  content?: string
  excerpt?: string
  template?: string
  status?: PageStatus
  seoTitle?: string
  seoDescription?: string
  featuredImage?: string
  authorId: string
}

export interface PageUpdateData {
  title?: string
  slug?: string
  content?: string
  excerpt?: string
  template?: string
  status?: PageStatus
  seoTitle?: string
  seoDescription?: string
  featuredImage?: string
}

// Validation schemas
const slugSchema = z.string()
  .min(1, 'Slug is required')
  .max(200, 'Slug must be less than 200 characters')
  .regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens')
  .refine(slug => !slug.startsWith('-') && !slug.endsWith('-'), 'Slug cannot start or end with a hyphen')

const pageCreateSchema = z.object({
  title: z.string().min(1, 'Title is required').max(500, 'Title must be less than 500 characters'),
  slug: slugSchema,
  content: z.string().optional(),
  excerpt: z.string().max(1000, 'Excerpt must be less than 1000 characters').optional(),
  template: z.string().optional().default('default'),
  status: z.nativeEnum(PageStatus).optional().default(PageStatus.DRAFT),
  seoTitle: z.string().max(60, 'SEO title should be less than 60 characters').optional(),
  seoDescription: z.string().max(160, 'SEO description should be less than 160 characters').optional(),
  featuredImage: z.string().url('Featured image must be a valid URL').optional(),
  authorId: z.string().cuid('Invalid author ID'),
})

const pageUpdateSchema = pageCreateSchema.partial().omit({ authorId: true })

export class PageService {
  /**
   * Get a page by slug
   */
  static async getBySlug(slug: string, options: {
    includeBlocks?: boolean
    includeDrafts?: boolean
    includeViews?: boolean
  } = {}): Promise<PageData | null> {
    const { includeBlocks = true, includeDrafts = false, includeViews = false } = options

    const where: any = { slug }
    if (!includeDrafts) {
      where.status = PageStatus.PUBLISHED
    }

    const page = await prisma.page.findFirst({
      where,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        blocks: includeBlocks ? {
          orderBy: { order: 'asc' }
        } : false,
        _count: includeViews ? {
          select: {
            blocks: true
          }
        } : false
      }
    })

    if (!page) return null

    // Get view count if requested
    let viewCount = 0
    if (includeViews) {
      const views = await prisma.pageView.count({
        where: { path: `/${slug}` }
      })
      viewCount = views
    }

    return {
      ...page,
      _count: includeViews ? {
        views: viewCount,
        blocks: page._count?.blocks || 0
      } : undefined
    } as PageData
  }

  /**
   * Get a page by ID
   */
  static async getById(id: string, options: {
    includeBlocks?: boolean
    includeViews?: boolean
  } = {}): Promise<PageData | null> {
    const { includeBlocks = true, includeViews = false } = options

    const page = await prisma.page.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        blocks: includeBlocks ? {
          orderBy: { order: 'asc' }
        } : false,
        _count: includeViews ? {
          select: {
            blocks: true
          }
        } : false
      }
    })

    if (!page) return null

    // Get view count if requested
    let viewCount = 0
    if (includeViews) {
      const views = await prisma.pageView.count({
        where: { path: `/${page.slug}` }
      })
      viewCount = views
    }

    return {
      ...page,
      _count: includeViews ? {
        views: viewCount,
        blocks: page._count?.blocks || 0
      } : undefined
    } as PageData
  }

  /**
   * Get multiple pages with filtering and pagination
   */
  static async getMany(options: {
    filters?: PageFilters
    page?: number
    limit?: number
    orderBy?: 'createdAt' | 'updatedAt' | 'publishedAt' | 'title'
    orderDirection?: 'asc' | 'desc'
    includeBlocks?: boolean
  } = {}) {
    const {
      filters = {},
      page = 1,
      limit = 10,
      orderBy = 'updatedAt',
      orderDirection = 'desc',
      includeBlocks = false
    } = options

    // Build where clause
    const where: any = {}

    if (filters.status) {
      where.status = filters.status
    }

    if (filters.template) {
      where.template = filters.template
    }

    if (filters.authorId) {
      where.authorId = filters.authorId
    }

    if (filters.search) {
      where.OR = [
        { title: { contains: filters.search, mode: 'insensitive' } },
        { content: { contains: filters.search, mode: 'insensitive' } },
        { excerpt: { contains: filters.search, mode: 'insensitive' } },
        { seoTitle: { contains: filters.search, mode: 'insensitive' } },
        { seoDescription: { contains: filters.search, mode: 'insensitive' } }
      ]
    }

    if (filters.dateFrom || filters.dateTo) {
      where.createdAt = {}
      if (filters.dateFrom) {
        where.createdAt.gte = filters.dateFrom
      }
      if (filters.dateTo) {
        where.createdAt.lte = filters.dateTo
      }
    }

    // Get total count
    const total = await prisma.page.count({ where })

    // Get pages
    const pages = await prisma.page.findMany({
      where,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        blocks: includeBlocks ? {
          orderBy: { order: 'asc' }
        } : false,
        _count: {
          select: {
            blocks: true
          }
        }
      },
      orderBy: { [orderBy]: orderDirection },
      skip: (page - 1) * limit,
      take: limit
    })

    return {
      pages: pages as PageData[],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      }
    }
  }

  /**
   * Create a new page
   */
  static async create(data: PageCreateData): Promise<PageData> {
    // Validate data
    const validatedData = pageCreateSchema.parse(data)

    // Check if slug already exists
    const existingPage = await prisma.page.findUnique({
      where: { slug: validatedData.slug }
    })

    if (existingPage) {
      throw new Error('A page with this slug already exists')
    }

    // Create the page
    const page = await prisma.page.create({
      data: {
        ...validatedData,
        publishedAt: validatedData.status === PageStatus.PUBLISHED ? new Date() : null
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        blocks: {
          orderBy: { order: 'asc' }
        }
      }
    })

    return page as PageData
  }

  /**
   * Update a page
   */
  static async update(id: string, data: PageUpdateData, userId: string, userRole: UserRole): Promise<PageData> {
    // Validate data
    const validatedData = pageUpdateSchema.parse(data)

    // Find existing page
    const existingPage = await prisma.page.findUnique({
      where: { id }
    })

    if (!existingPage) {
      throw new Error('Page not found')
    }

    // Check permissions
    if (existingPage.authorId !== userId && userRole !== UserRole.ADMIN) {
      throw new Error('Permission denied')
    }

    // Check if new slug already exists (if slug is being changed)
    if (validatedData.slug && validatedData.slug !== existingPage.slug) {
      const slugExists = await prisma.page.findUnique({
        where: { slug: validatedData.slug }
      })

      if (slugExists) {
        throw new Error('A page with this slug already exists')
      }
    }

    // Prepare update data
    const updateData: any = { ...validatedData }

    // Handle publishedAt field
    if (validatedData.status === PageStatus.PUBLISHED && existingPage.status !== PageStatus.PUBLISHED) {
      updateData.publishedAt = new Date()
    } else if (validatedData.status && validatedData.status !== PageStatus.PUBLISHED) {
      updateData.publishedAt = null
    }

    // Update the page
    const page = await prisma.page.update({
      where: { id },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        blocks: {
          orderBy: { order: 'asc' }
        }
      }
    })

    return page as PageData
  }

  /**
   * Delete a page
   */
  static async delete(id: string, userId: string, userRole: UserRole): Promise<void> {
    // Find existing page
    const existingPage = await prisma.page.findUnique({
      where: { id }
    })

    if (!existingPage) {
      throw new Error('Page not found')
    }

    // Check permissions
    if (existingPage.authorId !== userId && userRole !== UserRole.ADMIN) {
      throw new Error('Permission denied')
    }

    // Delete the page (blocks will be deleted automatically due to cascade)
    await prisma.page.delete({
      where: { id }
    })
  }

  /**
   * Generate a unique slug from title
   */
  static async generateSlug(title: string, excludeId?: string): Promise<string> {
    let baseSlug = title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens

    if (!baseSlug) {
      baseSlug = 'page'
    }

    let slug = baseSlug
    let counter = 1

    while (true) {
      const where: any = { slug }
      if (excludeId) {
        where.id = { not: excludeId }
      }

      const existingPage = await prisma.page.findUnique({ where })
      
      if (!existingPage) {
        break
      }

      slug = `${baseSlug}-${counter}`
      counter++
    }

    return slug
  }

  /**
   * Get related pages
   */
  static async getRelated(pageId: string, options: {
    template?: string
    limit?: number
  } = {}): Promise<PageData[]> {
    const { template, limit = 3 } = options

    const page = await prisma.page.findUnique({
      where: { id: pageId },
      select: { template: true }
    })

    if (!page) return []

    const where: any = {
      status: PageStatus.PUBLISHED,
      id: { not: pageId }
    }

    if (template || page.template) {
      where.template = template || page.template
    }

    const relatedPages = await prisma.page.findMany({
      where,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      },
      take: limit,
      orderBy: { updatedAt: 'desc' }
    })

    return relatedPages as PageData[]
  }

  /**
   * Track page view
   */
  static async trackView(slug: string, metadata: {
    userAgent?: string
    ip?: string
    referer?: string
  } = {}): Promise<void> {
    try {
      await prisma.pageView.create({
        data: {
          path: `/${slug}`,
          userAgent: metadata.userAgent || '',
          ip: metadata.ip || '',
          referer: metadata.referer || ''
        }
      })
    } catch (error) {
      // Don't throw error if view tracking fails
      console.error('Failed to track page view:', error)
    }
  }

  /**
   * Get page analytics
   */
  static async getAnalytics(slug: string, options: {
    dateFrom?: Date
    dateTo?: Date
  } = {}) {
    const { dateFrom, dateTo } = options

    const where: any = { path: `/${slug}` }

    if (dateFrom || dateTo) {
      where.createdAt = {}
      if (dateFrom) {
        where.createdAt.gte = dateFrom
      }
      if (dateTo) {
        where.createdAt.lte = dateTo
      }
    }

    const [totalViews, uniqueViews, recentViews] = await Promise.all([
      // Total views
      prisma.pageView.count({ where }),
      
      // Unique views (by IP)
      prisma.pageView.groupBy({
        by: ['ip'],
        where,
        _count: { ip: true }
      }).then(results => results.length),
      
      // Recent views (last 7 days)
      prisma.pageView.count({
        where: {
          ...where,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      })
    ])

    return {
      totalViews,
      uniqueViews,
      recentViews
    }
  }

  /**
   * Validate slug format
   */
  static validateSlug(slug: string): { valid: boolean; error?: string } {
    try {
      slugSchema.parse(slug)
      return { valid: true }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { valid: false, error: error.errors[0].message }
      }
      return { valid: false, error: 'Invalid slug format' }
    }
  }
}
