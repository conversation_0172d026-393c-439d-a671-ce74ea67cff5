import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'
import { z } from 'zod'

// Block type definitions
export interface BlockType {
  id: string
  name: string
  label: string
  description: string
  icon: string
  category: string
  schema: any
  defaultData: any
  preview?: string
}

export interface BlockData {
  id: string
  type: string
  data: any
  order: number
  createdAt: Date
  updatedAt: Date
  creator: {
    id: string
    name: string
    image?: string
  }
  updater?: {
    id: string
    name: string
    image?: string
  }
}

// Predefined block types
export const BLOCK_TYPES: Record<string, BlockType> = {
  hero: {
    id: 'hero',
    name: 'hero',
    label: 'Hero Section',
    description: 'Large banner section with title, subtitle, and call-to-action',
    icon: 'layout-dashboard',
    category: 'layout',
    schema: {
      title: { type: 'string', required: true, label: 'Title' },
      subtitle: { type: 'string', label: 'Subtitle' },
      backgroundImage: { type: 'image', label: 'Background Image' },
      ctaText: { type: 'string', label: 'CTA Button Text' },
      ctaLink: { type: 'string', label: 'CTA Button Link' },
      alignment: { type: 'select', options: ['left', 'center', 'right'], default: 'center', label: 'Text Alignment' }
    },
    defaultData: {
      title: 'Welcome to Our Website',
      subtitle: 'Discover amazing content and services',
      ctaText: 'Get Started',
      ctaLink: '#',
      alignment: 'center'
    }
  },
  text: {
    id: 'text',
    name: 'text',
    label: 'Text Content',
    description: 'Rich text content with formatting options',
    icon: 'type',
    category: 'content',
    schema: {
      content: { type: 'richtext', required: true, label: 'Content' },
      alignment: { type: 'select', options: ['left', 'center', 'right'], default: 'left', label: 'Text Alignment' }
    },
    defaultData: {
      content: '<p>Enter your text content here...</p>',
      alignment: 'left'
    }
  },
  image: {
    id: 'image',
    name: 'image',
    label: 'Image',
    description: 'Single image with caption and alt text',
    icon: 'image',
    category: 'media',
    schema: {
      src: { type: 'image', required: true, label: 'Image' },
      alt: { type: 'string', required: true, label: 'Alt Text' },
      caption: { type: 'string', label: 'Caption' },
      width: { type: 'select', options: ['small', 'medium', 'large', 'full'], default: 'full', label: 'Width' },
      alignment: { type: 'select', options: ['left', 'center', 'right'], default: 'center', label: 'Alignment' }
    },
    defaultData: {
      alt: 'Image description',
      width: 'full',
      alignment: 'center'
    }
  },
  gallery: {
    id: 'gallery',
    name: 'gallery',
    label: 'Image Gallery',
    description: 'Collection of images in a grid layout',
    icon: 'images',
    category: 'media',
    schema: {
      images: { type: 'array', items: { type: 'image' }, required: true, label: 'Images' },
      columns: { type: 'select', options: [2, 3, 4], default: 3, label: 'Columns' },
      spacing: { type: 'select', options: ['small', 'medium', 'large'], default: 'medium', label: 'Spacing' }
    },
    defaultData: {
      images: [],
      columns: 3,
      spacing: 'medium'
    }
  },
  video: {
    id: 'video',
    name: 'video',
    label: 'Video',
    description: 'Embedded video player',
    icon: 'play',
    category: 'media',
    schema: {
      url: { type: 'string', required: true, label: 'Video URL' },
      title: { type: 'string', label: 'Video Title' },
      autoplay: { type: 'boolean', default: false, label: 'Autoplay' },
      controls: { type: 'boolean', default: true, label: 'Show Controls' }
    },
    defaultData: {
      controls: true,
      autoplay: false
    }
  },
  cta: {
    id: 'cta',
    name: 'cta',
    label: 'Call to Action',
    description: 'Prominent call-to-action section',
    icon: 'mouse-pointer',
    category: 'marketing',
    schema: {
      title: { type: 'string', required: true, label: 'Title' },
      description: { type: 'string', label: 'Description' },
      buttonText: { type: 'string', required: true, label: 'Button Text' },
      buttonLink: { type: 'string', required: true, label: 'Button Link' },
      style: { type: 'select', options: ['primary', 'secondary', 'outline'], default: 'primary', label: 'Button Style' },
      size: { type: 'select', options: ['small', 'medium', 'large'], default: 'medium', label: 'Size' }
    },
    defaultData: {
      title: 'Ready to get started?',
      description: 'Join thousands of satisfied customers',
      buttonText: 'Get Started',
      buttonLink: '#',
      style: 'primary',
      size: 'medium'
    }
  },
  features: {
    id: 'features',
    name: 'features',
    label: 'Features Grid',
    description: 'Grid of features with icons and descriptions',
    icon: 'grid',
    category: 'layout',
    schema: {
      title: { type: 'string', label: 'Section Title' },
      features: {
        type: 'array',
        items: {
          title: { type: 'string', required: true },
          description: { type: 'string', required: true },
          icon: { type: 'string', required: true }
        },
        label: 'Features'
      },
      columns: { type: 'select', options: [2, 3, 4], default: 3, label: 'Columns' }
    },
    defaultData: {
      title: 'Our Features',
      features: [
        {
          title: 'Feature 1',
          description: 'Description of feature 1',
          icon: 'check'
        },
        {
          title: 'Feature 2',
          description: 'Description of feature 2',
          icon: 'star'
        },
        {
          title: 'Feature 3',
          description: 'Description of feature 3',
          icon: 'heart'
        }
      ],
      columns: 3
    }
  },
  testimonial: {
    id: 'testimonial',
    name: 'testimonial',
    label: 'Testimonial',
    description: 'Customer testimonial with quote and author',
    icon: 'quote',
    category: 'marketing',
    schema: {
      quote: { type: 'string', required: true, label: 'Quote' },
      author: { type: 'string', required: true, label: 'Author Name' },
      position: { type: 'string', label: 'Author Position' },
      company: { type: 'string', label: 'Company' },
      avatar: { type: 'image', label: 'Author Avatar' },
      rating: { type: 'number', min: 1, max: 5, label: 'Rating' }
    },
    defaultData: {
      quote: 'This service has been amazing for our business.',
      author: 'John Doe',
      position: 'CEO',
      company: 'Example Corp',
      rating: 5
    }
  },
  contact: {
    id: 'contact',
    name: 'contact',
    label: 'Contact Form',
    description: 'Contact form with customizable fields',
    icon: 'mail',
    category: 'forms',
    schema: {
      title: { type: 'string', label: 'Form Title' },
      fields: {
        type: 'array',
        items: {
          name: { type: 'string', required: true },
          label: { type: 'string', required: true },
          type: { type: 'select', options: ['text', 'email', 'tel', 'textarea'], required: true },
          required: { type: 'boolean', default: false }
        },
        label: 'Form Fields'
      },
      submitText: { type: 'string', default: 'Send Message', label: 'Submit Button Text' }
    },
    defaultData: {
      title: 'Contact Us',
      fields: [
        { name: 'name', label: 'Name', type: 'text', required: true },
        { name: 'email', label: 'Email', type: 'email', required: true },
        { name: 'message', label: 'Message', type: 'textarea', required: true }
      ],
      submitText: 'Send Message'
    }
  }
}

// Validation schemas
const blockDataSchema = z.object({
  type: z.string().min(1, 'Block type is required'),
  data: z.record(z.any()),
  order: z.number().int().min(0).optional().default(0),
})

export class BlockService {
  /**
   * Get all available block types
   */
  static getBlockTypes(): BlockType[] {
    return Object.values(BLOCK_TYPES)
  }

  /**
   * Get block types by category
   */
  static getBlockTypesByCategory(): Record<string, BlockType[]> {
    const categories: Record<string, BlockType[]> = {}
    
    Object.values(BLOCK_TYPES).forEach(blockType => {
      if (!categories[blockType.category]) {
        categories[blockType.category] = []
      }
      categories[blockType.category].push(blockType)
    })

    return categories
  }

  /**
   * Get a specific block type
   */
  static getBlockType(type: string): BlockType | null {
    return BLOCK_TYPES[type] || null
  }

  /**
   * Validate block data against its schema
   */
  static validateBlockData(type: string, data: any): { valid: boolean; errors?: string[] } {
    const blockType = this.getBlockType(type)
    if (!blockType) {
      return { valid: false, errors: ['Invalid block type'] }
    }

    const errors: string[] = []

    // Basic validation based on schema
    Object.entries(blockType.schema).forEach(([key, field]: [string, any]) => {
      const value = data[key]

      if (field.required && (value === undefined || value === null || value === '')) {
        errors.push(`${field.label || key} is required`)
      }

      if (value !== undefined && value !== null) {
        switch (field.type) {
          case 'string':
            if (typeof value !== 'string') {
              errors.push(`${field.label || key} must be a string`)
            }
            break
          case 'number':
            if (typeof value !== 'number') {
              errors.push(`${field.label || key} must be a number`)
            } else {
              if (field.min !== undefined && value < field.min) {
                errors.push(`${field.label || key} must be at least ${field.min}`)
              }
              if (field.max !== undefined && value > field.max) {
                errors.push(`${field.label || key} must be at most ${field.max}`)
              }
            }
            break
          case 'boolean':
            if (typeof value !== 'boolean') {
              errors.push(`${field.label || key} must be a boolean`)
            }
            break
          case 'array':
            if (!Array.isArray(value)) {
              errors.push(`${field.label || key} must be an array`)
            }
            break
          case 'select':
            if (field.options && !field.options.includes(value)) {
              errors.push(`${field.label || key} must be one of: ${field.options.join(', ')}`)
            }
            break
        }
      }
    })

    return { valid: errors.length === 0, errors: errors.length > 0 ? errors : undefined }
  }

  /**
   * Get blocks for a page
   */
  static async getPageBlocks(pageId: string): Promise<BlockData[]> {
    const blocks = await prisma.block.findMany({
      where: { pageId },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        updater: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      },
      orderBy: { order: 'asc' }
    })

    return blocks as BlockData[]
  }

  /**
   * Create a new block
   */
  static async createBlock(data: {
    type: string
    data: any
    order?: number
    pageId?: string
    postId?: string
    createdBy: string
  }): Promise<BlockData> {
    // Validate block data
    const validation = this.validateBlockData(data.type, data.data)
    if (!validation.valid) {
      throw new Error(`Block validation failed: ${validation.errors?.join(', ')}`)
    }

    // Get next order if not provided
    let order = data.order
    if (order === undefined) {
      const lastBlock = await prisma.block.findFirst({
        where: {
          ...(data.pageId ? { pageId: data.pageId } : {}),
          ...(data.postId ? { postId: data.postId } : {})
        },
        orderBy: { order: 'desc' }
      })
      order = (lastBlock?.order ?? -1) + 1
    }

    const block = await prisma.block.create({
      data: {
        type: data.type,
        data: data.data,
        order,
        pageId: data.pageId,
        postId: data.postId,
        createdBy: data.createdBy,
        updatedBy: data.createdBy
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        updater: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return block as BlockData
  }

  /**
   * Update a block
   */
  static async updateBlock(
    id: string,
    data: { type?: string; data?: any; order?: number },
    updatedBy: string
  ): Promise<BlockData> {
    const existingBlock = await prisma.block.findUnique({
      where: { id }
    })

    if (!existingBlock) {
      throw new Error('Block not found')
    }

    // Validate block data if type or data is being updated
    const type = data.type || existingBlock.type
    const blockData = data.data || existingBlock.data

    const validation = this.validateBlockData(type, blockData)
    if (!validation.valid) {
      throw new Error(`Block validation failed: ${validation.errors?.join(', ')}`)
    }

    const block = await prisma.block.update({
      where: { id },
      data: {
        ...data,
        updatedBy
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        updater: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return block as BlockData
  }

  /**
   * Delete a block
   */
  static async deleteBlock(id: string): Promise<void> {
    const existingBlock = await prisma.block.findUnique({
      where: { id }
    })

    if (!existingBlock) {
      throw new Error('Block not found')
    }

    // Delete the block
    await prisma.block.delete({
      where: { id }
    })

    // Reorder remaining blocks
    await prisma.block.updateMany({
      where: {
        ...(existingBlock.pageId ? { pageId: existingBlock.pageId } : {}),
        ...(existingBlock.postId ? { postId: existingBlock.postId } : {}),
        order: { gt: existingBlock.order }
      },
      data: {
        order: { decrement: 1 }
      }
    })
  }

  /**
   * Reorder blocks
   */
  static async reorderBlocks(
    blocks: Array<{ id: string; order: number }>,
    pageId?: string,
    postId?: string
  ): Promise<void> {
    await prisma.$transaction(async (tx) => {
      for (const block of blocks) {
        await tx.block.update({
          where: { id: block.id },
          data: { order: block.order }
        })
      }
    })
  }

  /**
   * Duplicate a block
   */
  static async duplicateBlock(id: string, createdBy: string): Promise<BlockData> {
    const existingBlock = await prisma.block.findUnique({
      where: { id }
    })

    if (!existingBlock) {
      throw new Error('Block not found')
    }

    // Get next order
    const lastBlock = await prisma.block.findFirst({
      where: {
        ...(existingBlock.pageId ? { pageId: existingBlock.pageId } : {}),
        ...(existingBlock.postId ? { postId: existingBlock.postId } : {})
      },
      orderBy: { order: 'desc' }
    })

    const newOrder = (lastBlock?.order ?? -1) + 1

    const block = await prisma.block.create({
      data: {
        type: existingBlock.type,
        data: existingBlock.data,
        order: newOrder,
        pageId: existingBlock.pageId,
        postId: existingBlock.postId,
        createdBy,
        updatedBy: createdBy
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        updater: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return block as BlockData
  }
}
