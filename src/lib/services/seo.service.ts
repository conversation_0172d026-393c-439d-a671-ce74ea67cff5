import { prisma } from '@/lib/prisma'

export interface SEOData {
  title: string
  description: string
  keywords?: string[]
  image?: string
  url: string
  type?: 'website' | 'article' | 'product' | 'profile'
  siteName?: string
  locale?: string
  publishedTime?: Date
  modifiedTime?: Date
  author?: string
  section?: string
  tags?: string[]
  canonicalUrl?: string
  noindex?: boolean
  nofollow?: boolean
}

export interface StructuredData {
  '@context': string
  '@type': string
  [key: string]: any
}

export interface SitemapEntry {
  url: string
  lastModified: Date
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority: number
}

export class SEOService {
  /**
   * Generate meta tags for a page
   */
  static generateMetaTags(seo: SEOData): Record<string, string> {
    const meta: Record<string, string> = {}

    // Basic meta tags
    meta['title'] = seo.title
    meta['description'] = seo.description
    if (seo.keywords && seo.keywords.length > 0) {
      meta['keywords'] = seo.keywords.join(', ')
    }
    if (seo.canonicalUrl) {
      meta['canonical'] = seo.canonicalUrl
    }

    // Robots meta
    const robots = []
    if (seo.noindex) robots.push('noindex')
    if (seo.nofollow) robots.push('nofollow')
    if (robots.length > 0) {
      meta['robots'] = robots.join(', ')
    }

    // Open Graph tags
    meta['og:title'] = seo.title
    meta['og:description'] = seo.description
    meta['og:url'] = seo.url
    meta['og:type'] = seo.type || 'website'
    if (seo.siteName) {
      meta['og:site_name'] = seo.siteName
    }
    if (seo.image) {
      meta['og:image'] = seo.image
      meta['og:image:alt'] = seo.title
    }
    if (seo.locale) {
      meta['og:locale'] = seo.locale
    }

    // Article-specific Open Graph tags
    if (seo.type === 'article') {
      if (seo.publishedTime) {
        meta['article:published_time'] = seo.publishedTime.toISOString()
      }
      if (seo.modifiedTime) {
        meta['article:modified_time'] = seo.modifiedTime.toISOString()
      }
      if (seo.author) {
        meta['article:author'] = seo.author
      }
      if (seo.section) {
        meta['article:section'] = seo.section
      }
      if (seo.tags && seo.tags.length > 0) {
        seo.tags.forEach((tag, index) => {
          meta[`article:tag:${index}`] = tag
        })
      }
    }

    // Twitter Card tags
    meta['twitter:card'] = seo.image ? 'summary_large_image' : 'summary'
    meta['twitter:title'] = seo.title
    meta['twitter:description'] = seo.description
    if (seo.image) {
      meta['twitter:image'] = seo.image
      meta['twitter:image:alt'] = seo.title
    }

    return meta
  }

  /**
   * Generate structured data for a page
   */
  static generateStructuredData(type: string, data: any): StructuredData {
    const baseStructuredData: StructuredData = {
      '@context': 'https://schema.org',
      '@type': type
    }

    switch (type) {
      case 'WebPage':
        return {
          ...baseStructuredData,
          name: data.title,
          description: data.description,
          url: data.url,
          ...(data.image && { image: data.image }),
          ...(data.datePublished && { datePublished: data.datePublished }),
          ...(data.dateModified && { dateModified: data.dateModified }),
          ...(data.author && {
            author: {
              '@type': 'Person',
              name: data.author
            }
          })
        }

      case 'Article':
        return {
          ...baseStructuredData,
          headline: data.title,
          description: data.description,
          url: data.url,
          ...(data.image && { image: data.image }),
          ...(data.datePublished && { datePublished: data.datePublished }),
          ...(data.dateModified && { dateModified: data.dateModified }),
          ...(data.author && {
            author: {
              '@type': 'Person',
              name: data.author
            }
          }),
          ...(data.publisher && {
            publisher: {
              '@type': 'Organization',
              name: data.publisher,
              ...(data.publisherLogo && {
                logo: {
                  '@type': 'ImageObject',
                  url: data.publisherLogo
                }
              })
            }
          }),
          ...(data.wordCount && { wordCount: data.wordCount }),
          ...(data.keywords && { keywords: data.keywords })
        }

      case 'Organization':
        return {
          ...baseStructuredData,
          name: data.name,
          description: data.description,
          url: data.url,
          ...(data.logo && { logo: data.logo }),
          ...(data.contactPoint && {
            contactPoint: {
              '@type': 'ContactPoint',
              telephone: data.contactPoint.telephone,
              contactType: data.contactPoint.contactType,
              email: data.contactPoint.email
            }
          }),
          ...(data.address && {
            address: {
              '@type': 'PostalAddress',
              streetAddress: data.address.streetAddress,
              addressLocality: data.address.addressLocality,
              addressRegion: data.address.addressRegion,
              postalCode: data.address.postalCode,
              addressCountry: data.address.addressCountry
            }
          }),
          ...(data.sameAs && { sameAs: data.sameAs })
        }

      case 'BreadcrumbList':
        return {
          ...baseStructuredData,
          itemListElement: data.items.map((item: any, index: number) => ({
            '@type': 'ListItem',
            position: index + 1,
            name: item.name,
            item: item.url
          }))
        }

      default:
        return {
          ...baseStructuredData,
          ...data
        }
    }
  }

  /**
   * Generate sitemap entries for all published pages
   */
  static async generateSitemap(): Promise<SitemapEntry[]> {
    const pages = await prisma.page.findMany({
      where: {
        status: 'PUBLISHED'
      },
      select: {
        slug: true,
        updatedAt: true,
        template: true
      },
      orderBy: {
        updatedAt: 'desc'
      }
    })

    const sitemapEntries: SitemapEntry[] = pages.map(page => ({
      url: `/${page.slug}`,
      lastModified: page.updatedAt,
      changeFrequency: this.getChangeFrequency(page.template),
      priority: this.getPriority(page.template, page.slug)
    }))

    // Add static pages
    const staticPages: SitemapEntry[] = [
      {
        url: '/',
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1.0
      }
    ]

    return [...staticPages, ...sitemapEntries]
  }

  /**
   * Generate robots.txt content
   */
  static generateRobotsTxt(siteUrl: string): string {
    return `User-agent: *
Allow: /

# Sitemap
Sitemap: ${siteUrl}/sitemap.xml

# Disallow admin and API routes
Disallow: /admin/
Disallow: /api/

# Disallow preview pages
Disallow: /*?preview=true

# Crawl delay
Crawl-delay: 1`
  }

  /**
   * Validate SEO data
   */
  static validateSEO(seo: Partial<SEOData>): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // Title validation
    if (!seo.title) {
      errors.push('Title is required')
    } else if (seo.title.length > 60) {
      errors.push('Title should be 60 characters or less')
    } else if (seo.title.length < 10) {
      errors.push('Title should be at least 10 characters')
    }

    // Description validation
    if (!seo.description) {
      errors.push('Description is required')
    } else if (seo.description.length > 160) {
      errors.push('Description should be 160 characters or less')
    } else if (seo.description.length < 50) {
      errors.push('Description should be at least 50 characters')
    }

    // URL validation
    if (!seo.url) {
      errors.push('URL is required')
    } else if (!this.isValidUrl(seo.url)) {
      errors.push('URL must be a valid URL')
    }

    // Image validation
    if (seo.image && !this.isValidUrl(seo.image)) {
      errors.push('Image must be a valid URL')
    }

    // Keywords validation
    if (seo.keywords && seo.keywords.length > 10) {
      errors.push('Maximum 10 keywords allowed')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Get SEO score for a page
   */
  static calculateSEOScore(seo: SEOData, content?: string): { score: number; suggestions: string[] } {
    let score = 0
    const suggestions: string[] = []

    // Title scoring (25 points)
    if (seo.title) {
      if (seo.title.length >= 10 && seo.title.length <= 60) {
        score += 25
      } else if (seo.title.length > 60) {
        suggestions.push('Title is too long (over 60 characters)')
        score += 10
      } else {
        suggestions.push('Title is too short (under 10 characters)')
        score += 10
      }
    } else {
      suggestions.push('Missing title')
    }

    // Description scoring (25 points)
    if (seo.description) {
      if (seo.description.length >= 50 && seo.description.length <= 160) {
        score += 25
      } else if (seo.description.length > 160) {
        suggestions.push('Description is too long (over 160 characters)')
        score += 10
      } else {
        suggestions.push('Description is too short (under 50 characters)')
        score += 10
      }
    } else {
      suggestions.push('Missing description')
    }

    // Image scoring (15 points)
    if (seo.image) {
      score += 15
    } else {
      suggestions.push('Missing featured image')
    }

    // Keywords scoring (10 points)
    if (seo.keywords && seo.keywords.length > 0) {
      score += 10
    } else {
      suggestions.push('Add relevant keywords')
    }

    // Content analysis (25 points)
    if (content) {
      const wordCount = content.replace(/<[^>]*>/g, '').split(/\s+/).length
      if (wordCount >= 300) {
        score += 25
      } else if (wordCount >= 150) {
        score += 15
        suggestions.push('Content could be longer (aim for 300+ words)')
      } else {
        score += 5
        suggestions.push('Content is too short (aim for 300+ words)')
      }
    } else {
      suggestions.push('Add content to improve SEO')
    }

    return { score, suggestions }
  }

  /**
   * Private helper methods
   */
  private static getChangeFrequency(template: string): SitemapEntry['changeFrequency'] {
    switch (template) {
      case 'landing':
        return 'weekly'
      case 'blog':
        return 'monthly'
      case 'contact':
        return 'monthly'
      case 'about':
        return 'monthly'
      default:
        return 'weekly'
    }
  }

  private static getPriority(template: string, slug: string): number {
    if (slug === 'home' || slug === '') return 1.0
    if (template === 'landing') return 0.9
    if (template === 'about') return 0.8
    if (template === 'contact') return 0.7
    return 0.6
  }

  private static isValidUrl(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }
}
