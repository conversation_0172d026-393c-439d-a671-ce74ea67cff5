import { DatabaseDocument } from '@/lib/appwrite/types/database';

export interface ContentType extends DatabaseDocument {
  name: string;
  slug: string;
  description?: string;
  isPublic: boolean;
  hasArchive: boolean;
  supports: {
    title: boolean;
    editor: boolean;
    excerpt: boolean;
    thumbnail: boolean;
    author: boolean;
    comments: boolean;
  };
  fields: string[]; // Array of field IDs
  templates?: {
    single?: string;
    archive?: string;
  };
  labels?: {
    name: string;
    singularName: string;
    addNew: string;
    addNewItem: string;
    editItem: string;
    newItem: string;
    viewItem: string;
    viewItems: string;
    searchItems: string;
    notFound: string;
    notFoundInTrash: string;
    parentItemColon: string;
    allItems: string;
    archives: string;
    attributes: string;
    insertIntoItem: string;
    uploadedToThisItem: string;
    featuredImage: string;
    setFeaturedImage: string;
    removeFeaturedImage: string;
    useFeaturedImage: string;
    menuName: string;
    filterItemsList: string;
    itemsListNavigation: string;
    itemsList: string;
  };
  statuses?: Record<string, { label: string; public?: boolean; protected?: boolean; private?: boolean }>;
}
