import { DatabaseDocument } from '@/lib/appwrite/types/database';

export type FieldType =
  | 'text'
  | 'textarea'
  | 'number'
  | 'email'
  | 'url'
  | 'password'
  | 'date'
  | 'datetime-local'
  | 'time'
  | 'select'
  | 'multiselect'
  | 'checkbox'
  | 'radio'
  | 'color'
  | 'image'
  | 'file'
  | 'wysiwyg'
  | 'code'
  | 'repeater'
  | 'group';

export interface FieldOption {
  label: string;
  value: string | number;
  selected?: boolean;
}

export interface FieldValidation {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  min?: number;
  max?: number;
  step?: number;
  accept?: string;
}

export interface Field extends DatabaseDocument {
  name: string;
  label: string;
  type: FieldType;
  description?: string;
  placeholder?: string;
  defaultValue?: any;
  options?: FieldOption[];
  validation?: FieldValidation;
  conditionalLogic?: {
    field: string;
    operator: '==' | '!=' | '>' | '<' | '>=' | '<=' | 'contains' | 'not_contains' | 'starts_with' | 'ends_with' | 'in' | 'not_in';
    value: any;
  }[];
  fields?: Field[]; // For group and repeater fields
  isRepeatable?: boolean;
  maxRepeat?: number;
  minRepeat?: number;
  contentTypeId: string; // The content type this field belongs to
  order: number;
  width?: string; // For layout purposes (e.g., '1/2', '1/3', 'full')
  wrapper?: {
    width?: string;
    class?: string;
    id?: string;
  };
  attributes?: Record<string, any>; // Additional HTML attributes
}
