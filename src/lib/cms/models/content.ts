import { DatabaseDocument } from '@/lib/appwrite/types/database';

export interface Content extends DatabaseDocument {
  contentTypeId: string;
  title: string;
  slug: string;
  status: 'draft' | 'pending' | 'publish' | 'future' | 'private' | 'trash';
  content: string;
  excerpt?: string;
  authorId: string;
  parentId?: string;
  menuOrder?: number;
  commentStatus: 'open' | 'closed';
  pingStatus: 'open' | 'closed';
  password?: string;
  publishedAt?: string;
  modifiedAt: string;
  commentCount: number;
  featuredImageId?: string;
  meta: Record<string, any>; // For custom fields
  // Built-in fields that might be enabled/disabled per content type
  fields?: {
    [key: string]: any;
  };
  // For hierarchical content types
  ancestors?: string[];
  // For non-hierarchical content types
  terms?: {
    [taxonomy: string]: string[];
  };
  // For content that can be translated
  translations?: {
    [locale: string]: string; // contentId in other languages
  };
  // For content that has a specific template
  template?: string;
  // For content that has a specific format
  format?: 'standard' | 'aside' | 'image' | 'video' | 'quote' | 'link' | 'gallery' | 'audio' | 'status' | 'chat';
  // For content that has a specific mime type
  mimeType?: string;
  // For content that has a specific media type
  mediaType?: 'image' | 'video' | 'audio' | 'document' | 'archive' | 'spreadsheet' | 'interactive' | 'text' | 'font';
  // For content that has a specific file size
  fileSize?: number;
  // For content that has a specific file URL
  fileUrl?: string;
  // For content that has a specific file name
  fileName?: string;
  // For content that has a specific file extension
  fileExtension?: string;
  // For content that has a specific file mime type
  fileMimeType?: string;
  // For content that has a specific file dimensions
  fileDimensions?: {
    width: number;
    height: number;
  };
  // For content that has a specific file duration (for audio/video)
  fileDuration?: number;
  // For content that has a specific file bitrate (for audio/video)
  fileBitrate?: number;
  // For content that has a specific file framerate (for video)
  fileFramerate?: number;
  // For content that has a specific file channels (for audio)
  fileChannels?: number;
  // For content that has a specific file sample rate (for audio)
  fileSampleRate?: number;
  // For content that has a specific file bit depth (for audio)
  fileBitDepth?: number;
  // For content that has a specific file color space (for images)
  fileColorSpace?: string;
  // For content that has a specific file orientation (for images)
  fileOrientation?: number;
  // For content that has a specific file has alpha (for images)
  fileHasAlpha?: boolean;
  // For content that has a specific file is animated (for images)
  fileIsAnimated?: boolean;
  // For content that has a specific file frame count (for images)
  fileFrameCount?: number;
}
