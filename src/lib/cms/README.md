# Appwrite CMS Setup Guide

This document provides instructions for setting up the necessary Appwrite collections for the CMS service to function correctly.

## 1. Update Configuration

Before creating the collections, make sure to update the following placeholder values in `src/lib/cms/services/cms.service.ts`:

- `YOUR_PROJECT_ID`: Replace with your Appwrite project ID.
- `YOUR_DATABASE_ID`: Replace with the ID of the Appwrite database you want to use.

## 2. Create Collections

You need to create three collections in your Appwrite database. The following sections detail the required attributes for each.

### Collection: `content_types`

**Attributes:**

| Key         | Type   | Size | Required | Notes                               |
|-------------|--------|------|----------|-------------------------------------|
| `name`      | String | 255  | Yes      | The name of the content type (e.g., "Blog Post"). |
| `slug`      | String | 255  | Yes      | A unique slug for the content type (e.g., "blog-post"). |
| `description` | String | 500  | No       | A brief description of the content type. |

### Collection: `content_fields`

**Attributes:**

| Key             | Type   | Size | Required | Notes                               |
|-----------------|--------|------|----------|-------------------------------------|
| `contentTypeId` | String | 255  | Yes      | The ID of the parent content type.  |
| `name`          | String | 255  | Yes      | The field name (e.g., "title").     |
| `type`          | String | 50   | Yes      | `text`, `textarea`, `number`, `boolean`, `datetime`, `media`. |
| `label`         | String | 255  | Yes      | The display label for the field.    |
| `required`      | Boolean|      | Yes      | Whether the field is required.      |

### Collection: `content_entries`

**Attributes:**

| Key             | Type   | Size | Required | Notes                               |
|-----------------|--------|------|----------|-------------------------------------|
| `contentTypeId` | String | 255  | Yes      | The ID of the parent content type.  |
| `authorId`      | String | 255  | Yes      | The ID of the user who created the entry. |
| `status`        | String | 50   | Yes      | `published` or `draft`.             |
| `data`          | String |      | Yes      | A JSON string containing the entry's field data. Set size to max. |

Once you have created these collections and updated the configuration file, the CMS service will be ready to use.
