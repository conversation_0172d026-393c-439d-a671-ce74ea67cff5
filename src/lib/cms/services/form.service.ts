import { ContentTypeService } from './cms.service';
import { ContentFieldService } from './cms.service';
import { ContentType } from '../models/content-type.model';
import { ContentField } from '../models/content-field.model';

export interface FormSchema {
    contentType: ContentType;
    fields: ContentField[];
}

export class FormService {
    constructor(
        private contentTypeService: ContentTypeService,
        private contentFieldService: ContentFieldService
    ) {}

    async getFormSchema(contentTypeId: string): Promise<FormSchema | null> {
        const contentType = await this.contentTypeService.get(contentTypeId);
        if (!contentType) {
            console.error('Content type not found');
            return null;
        }

        const fields = await this.contentFieldService.getForContentType(contentTypeId);

        return {
            contentType,
            fields,
        };
    }
}
