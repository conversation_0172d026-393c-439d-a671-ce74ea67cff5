import { databaseServer } from '@/lib/appwrite/server/database';
import { DatabaseDocument, DatabaseResponse } from '@/lib/appwrite/types/database';
import { cmsService } from './cms.service';
import { mediaService } from './media.service';
import { settingsService } from './settings.service';

export interface ContentQueryOptions {
  // Basic query parameters
  contentType?: string;
  status?: string | string[];
  author?: string | string[];
  search?: string;
  
  // Pagination
  page?: number;
  perPage?: number;
  offset?: number;
  
  // Ordering
  orderby?: 'date' | 'modified' | 'title' | 'author' | 'comment_count' | 'menu_order' | 'meta_value' | 'meta_value_num' | 'rand' | 'relevance' | string;
  order?: 'ASC' | 'DESC';
  
  // Date queries
  dateQuery?: {
    year?: number;
    month?: number;
    day?: number;
    after?: string | Date;
    before?: string | Date;
    inclusive?: boolean;
    compare?: '=' | '!=' | '>' | '>=' | '<' | '<=' | 'IN' | 'NOT IN' | 'BETWEEN' | 'NOT BETWEEN';
  };
  
  // Taxonomy queries
  taxQuery?: {
    taxonomy: string;
    field?: 'term_id' | 'name' | 'slug' | 'term_taxonomy_id';
    terms: string | number | (string | number)[];
    operator?: 'AND' | 'IN' | 'NOT IN' | 'EXISTS' | 'NOT EXISTS';
    include_children?: boolean;
  }[];
  
  // Meta queries
  metaQuery?: {
    key: string;
    value?: any;
    compare?: '=' | '!=' | '>' | '>=' | '<' | '<=' | 'LIKE' | 'NOT LIKE' | 'IN' | 'NOT IN' | 'BETWEEN' | 'NOT BETWEEN' | 'EXISTS' | 'NOT EXISTS' | 'REGEXP' | 'NOT REGEXP' | 'RLIKE';
    type?: 'NUMERIC' | 'BINARY' | 'CHAR' | 'DATE' | 'DATETIME' | 'DECIMAL' | 'SIGNED' | 'TIME' | 'UNSIGNED';
  }[];
  
  // Custom fields
  metaKey?: string;
  metaValue?: any;
  metaCompare?: string;
  metaType?: string;
  
  // Content relationships
  parent?: number | 'any' | 'none';
  postParentIn?: number[];
  postParentNotIn?: number[];
  postIn?: string[];
  postNotIn?: string[];
  postNameIn?: string[];
  
  // Performance
  cacheResults?: boolean;
  updatePostTermCache?: boolean;
  updatePostMetaCache?: boolean;
  
  // Custom queries
  customQueries?: string[];
}

export class ContentService {
  private static instance: ContentService;
  private readonly CACHE_PREFIX = 'content_';
  private cache: Map<string, any> = new Map();
  
  private constructor() {}
  
  public static getInstance(): ContentService {
    if (!ContentService.instance) {
      ContentService.instance = new ContentService();
    }
    return ContentService.instance;
  }
  
  private getCacheKey(options: ContentQueryOptions): string {
    return `${this.CACHE_PREFIX}${JSON.stringify(options)}`;
  }
  
  private async getContentTypeCollection(contentType: string): Promise<string> {
    const { data: contentTypeData } = await cmsService.getContentTypeBySlug(contentType);
    if (!contentTypeData) {
      throw new Error(`Content type '${contentType}' not found`);
    }
    return `content_${contentTypeData.$id}`;
  }
  
  private buildQuery(options: ContentQueryOptions): string[] {
    const queries: string[] = [];
    const {
      status = 'publish',
      search,
      author,
      page = 1,
      perPage = 10,
      orderby = 'date',
      order = 'DESC',
      dateQuery,
      taxQuery,
      metaQuery,
      metaKey,
      metaValue,
      metaCompare = '=',
      metaType,
      parent,
      postIn,
      postNotIn,
      postParentIn,
      postParentNotIn,
      customQueries = []
    } = options;
    
    // Status
    if (status) {
      const statuses = Array.isArray(status) ? status : [status];
      if (statuses.length > 0) {
        queries.push(`equal("status", [${statuses.map(s => `"${s}"`).join(',')}])`);
      }
    }
    
    // Search
    if (search) {
      queries.push(`search("${search}", ["title", "content", "excerpt"])`);
    }
    
    // Author
    if (author) {
      const authors = Array.isArray(author) ? author : [author];
      if (authors.length > 0) {
        queries.push(`equal("authorId", [${authors.map(a => `"${a}"`).join(',')}])`);
      }
    }
    
    // Date query
    if (dateQuery) {
      const dateQueries: string[] = [];
      const { year, month, day, after, before, compare = '=' } = dateQuery;
      
      if (year) {
        dateQueries.push(`equal("YEAR($createdAt)", ${year})`);
      }
      if (month) {
        dateQueries.push(`equal("MONTH($createdAt)", ${month})`);
      }
      if (day) {
        dateQueries.push(`equal("DAY($createdAt)", ${day})`);
      }
      if (after) {
        const date = after instanceof Date ? after : new Date(after);
        dateQueries.push(`greaterThanEqual("$createdAt", "${date.toISOString()}")`);
      }
      if (before) {
        const date = before instanceof Date ? before : new Date(before);
        dateQueries.push(`lessThanEqual("$createdAt", "${date.toISOString()}")`);
      }
      
      if (dateQueries.length > 0) {
        queries.push(`and([${dateQueries.join(', ')}])`);
      }
    }
    
    // Meta query
    if (metaQuery && metaQuery.length > 0) {
      const metaQueries = metaQuery.map(meta => {
        const { key, value, compare = '=', type } = meta;
        const metaKey = `meta.${key}`;
        
        if (compare === 'EXISTS' || compare === 'NOT EXISTS') {
          return `queryNotEqual("${metaKey}", "")`;
        }
        
        if (Array.isArray(value)) {
          return `${compare === '!=' ? 'notEqual' : 'equal'}("${metaKey}", [${value.map(v => `"${v}"`).join(',')}])`;
        }
        
        return `${compare === '!=' ? 'notEqual' : 'equal'}("${metaKey}", ${typeof value === 'string' ? `"${value}"` : value})`;
      });
      
      if (metaQueries.length > 0) {
        queries.push(`and([${metaQueries.join(', ')}])`);
      }
    } else if (metaKey) {
      // Legacy meta query
      const metaCompareOp = metaCompare === '!=' ? 'notEqual' : 'equal';
      queries.push(`${metaCompareOp}("meta.${metaKey}", ${typeof metaValue === 'string' ? `"${metaValue}"` : metaValue})`);
    }
    
    // Taxonomy query
    if (taxQuery && taxQuery.length > 0) {
      const taxQueries = taxQuery.map(tax => {
        const { taxonomy, field = 'term_id', terms, operator = 'IN' } = tax;
        const termIds = Array.isArray(terms) ? terms : [terms];
        
        if (operator === 'NOT IN' || operator === 'NOT EXISTS') {
          return `notEqual("taxonomies.${taxonomy}", [${termIds.map(id => `"${id}"`).join(',')}])`;
        }
        
        return `contains("taxonomies.${taxonomy}", [${termIds.map(id => `"${id}"`).join(',')}])`;
      });
      
      if (taxQueries.length > 0) {
        queries.push(`and([${taxQueries.join(', ')}])`);
      }
    }
    
    // Post relationships
    if (parent !== undefined) {
      if (parent === 'none') {
        queries.push('isNull("parentId")');
      } else if (parent !== 'any') {
        queries.push(`equal("parentId", "${parent}")`);
      }
    }
    
    if (postIn?.length) {
      queries.push(`in("$id", [${postIn.map(id => `"${id}"`).join(',')}])`);
    }
    
    if (postNotIn?.length) {
      queries.push(`notIn("$id", [${postNotIn.map(id => `"${id}"`).join(',')}])`);
    }
    
    if (postParentIn?.length) {
      queries.push(`in("parentId", [${postParentIn.map(id => `"${id}"`).join(',')}])`);
    }
    
    if (postParentNotIn?.length) {
      queries.push(`notIn("parentId", [${postParentNotIn.map(id => `"${id}"`).join(',')}])`);
    }
    
    // Ordering
    let orderField = '$createdAt';
    let orderDirection = order === 'ASC' ? 'ASC' : 'DESC';
    
    switch (orderby) {
      case 'date':
        orderField = '$createdAt';
        break;
      case 'modified':
        orderField = '$updatedAt';
        break;
      case 'title':
        orderField = 'title';
        break;
      case 'author':
        orderField = 'authorId';
        break;
      case 'comment_count':
        orderField = 'commentCount';
        break;
      case 'menu_order':
        orderField = 'menuOrder';
        break;
      case 'rand':
        orderField = 'RAND()';
        break;
      case 'meta_value':
      case 'meta_value_num':
        if (metaKey) {
          orderField = `meta.${metaKey}`;
        }
        break;
      default:
        // Allow custom orderby fields
        orderField = orderby;
    }
    
    queries.push(`orderBy("${orderField}", "${orderDirection}")`);
    
    // Pagination
    const limit = perPage;
    const offset = options.offset !== undefined ? options.offset : (page - 1) * perPage;
    queries.push(`limit(${limit})`);
    queries.push(`offset(${offset})`);
    
    // Add any custom queries
    if (customQueries.length > 0) {
      queries.push(...customQueries);
    }
    
    return queries;
  }
  
  async getPosts(options: ContentQueryOptions = {}) {
    const { cacheResults = true } = options;
    const cacheKey = this.getCacheKey(options);
    
    // Try to get from cache
    if (cacheResults && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    // Default to 'post' content type if not specified
    const contentType = options.contentType || 'post';
    const collectionName = await this.getContentTypeCollection(contentType);
    
    // Build the query
    const queries = this.buildQuery(options);
    
    // Execute the query
    const response = await databaseServer.listDocuments(collectionName, { queries });
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch posts');
    }
    
    const result = {
      posts: response.data?.documents || [],
      total: response.data?.total || 0,
      totalPages: Math.ceil((response.data?.total || 0) / (options.perPage || 10)),
      currentPage: options.page || 1
    };
    
    // Cache the result
    if (cacheResults) {
      this.cache.set(cacheKey, result);
    }
    
    return result;
  }
  
  async getPost(id: string, contentType: string = 'post') {
    const collectionName = await this.getContentTypeCollection(contentType);
    const response = await databaseServer.getDocument(collectionName, id);
    
    if (!response.success) {
      throw new Error(response.error || 'Post not found');
    }
    
    return response.data;
  }
  
  async createPost(data: any, contentType: string = 'post') {
    const collectionName = await this.getContentTypeCollection(contentType);
    const now = new Date().toISOString();
    
    const postData = {
      ...data,
      status: data.status || 'draft',
      commentStatus: data.commentStatus || 'open',
      pingStatus: data.pingStatus || 'open',
      commentCount: 0,
      $createdAt: now,
      $updatedAt: now
    };
    
    const response = await databaseServer.createDocument(collectionName, postData);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to create post');
    }
    
    // Clear relevant caches
    this.clearCache();
    
    return response.data;
  }
  
  async updatePost(id: string, data: any, contentType: string = 'post') {
    const collectionName = await this.getContentTypeCollection(contentType);
    
    const updateData = {
      ...data,
      $updatedAt: new Date().toISOString()
    };
    
    const response = await databaseServer.updateDocument(collectionName, id, updateData);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to update post');
    }
    
    // Clear relevant caches
    this.clearCache();
    
    return response.data;
  }
  
  async deletePost(id: string, contentType: string = 'post', forceDelete: boolean = false) {
    const collectionName = await this.getContentTypeCollection(contentType);
    
    if (forceDelete) {
      const response = await databaseServer.deleteDocument(collectionName, id);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to delete post');
      }
      
      // Clear relevant caches
      this.clearCache();
      
      return true;
    } else {
      // Move to trash
      return this.updatePost(id, { status: 'trash' }, contentType);
    }
  }
  
  // Clear cache for all or specific content types
  clearCache(contentType?: string) {
    if (contentType) {
      // Clear cache for specific content type
      for (const key of this.cache.keys()) {
        if (key.includes(`"contentType":"${contentType}"`)) {
          this.cache.delete(key);
        }
      }
    } else {
      // Clear all cache
      this.cache.clear();
    }
  }
  
  // WordPress-like helper methods
  async getLatestPosts(limit: number = 5, options: Omit<ContentQueryOptions, 'perPage' | 'page'> = {}) {
    return this.getPosts({
      ...options,
      perPage: limit,
      page: 1,
      orderby: 'date',
      order: 'DESC'
    });
  }
  
  async getPostsByAuthor(authorId: string, options: Omit<ContentQueryOptions, 'author'> = {}) {
    return this.getPosts({
      ...options,
      author: authorId
    });
  }
  
  async getPostsByCategory(categoryId: string, options: Omit<ContentQueryOptions, 'taxQuery'> = {}) {
    return this.getPosts({
      ...options,
      taxQuery: [
        {
          taxonomy: 'category',
          terms: categoryId
        }
      ]
    });
  }
  
  async getPostsByTag(tagId: string, options: Omit<ContentQueryOptions, 'taxQuery'> = {}) {
    return this.getPosts({
      ...options,
      taxQuery: [
        {
          taxonomy: 'post_tag',
          terms: tagId
        }
      ]
    });
  }
  
  async getRelatedPosts(postId: string, limit: number = 3) {
    // Get the post to find related content
    const post = await this.getPost(postId);
    
    if (!post) {
      return [];
    }
    
    // Get categories and tags of the current post
    const categories = post.taxonomies?.category || [];
    const tags = post.taxonomies?.post_tag || [];
    
    if (categories.length === 0 && tags.length === 0) {
      return [];
    }
    
    // Build a query to find related posts
    const taxQueries = [];
    
    if (categories.length > 0) {
      taxQueries.push({
        taxonomy: 'category',
        terms: categories,
        field: 'term_id',
        operator: 'IN'
      });
    }
    
    if (tags.length > 0) {
      taxQueries.push({
        taxonomy: 'post_tag',
        terms: tags,
        field: 'term_id',
        operator: 'IN'
      });
    }
    
    const { posts } = await this.getPosts({
      perPage: limit,
      postNotIn: [postId],
      taxQuery: taxQueries,
      orderby: 'rand'
    });
    
    return posts;
  }
  
  async getAdjacentPost(postId: string, contentType: string = 'post', previous: boolean = true) {
    const post = await this.getPost(postId, contentType);
    if (!post) return null;
    
    const order = previous ? 'DESC' : 'ASC';
    const compare = previous ? '<' : '>';
    
    const { posts } = await this.getPosts({
      contentType,
      perPage: 1,
      orderby: 'date',
      order,
      customQueries: [
        `${compare}("$createdAt", "${post.$createdAt}")`,
        `notEqual("$id", "${postId}")`
      ]
    });
    
    return posts[0] || null;
  }
  
  // WordPress template tags equivalents
  async getTheTitle(post: any) {
    return post?.title || '';
  }
  
  async getTheContent(post: any) {
    return post?.content || '';
  }
  
  async getTheExcerpt(post: any, length: number = 55, more: string = '...') {
    if (post?.excerpt) {
      return post.excerpt;
    }
    
    const content = await this.getTheContent(post);
    const text = content.replace(/<[^>]*>?/gm, ''); // Strip HTML
    const words = text.split(/\s+/);
    
    if (words.length <= length) {
      return text;
    }
    
    return words.slice(0, length).join(' ') + more;
  }
  
  async getThePermalink(post: any) {
    if (!post) return '';
    
    const settings = await settingsService.getSettings();
    if (!settings.success) {
      return '';
    }
    
    const { siteUrl } = settings.data;
    const permalink = `/${post.slug || post.$id}`;
    
    return `${siteUrl}${permalink}`.replace(/([^:]\/)\/+/g, '$1');
  }
  
  async getTheDate(post: any, format: string = 'F j, Y') {
    if (!post?.$createdAt) return '';
    
    const date = new Date(post.$createdAt);
    
    // Simple formatting - in a real app, use a proper date formatting library
    const formats: Record<string, string> = {
      'F j, Y': date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
      'Y-m-d': date.toISOString().split('T')[0],
      'm/d/Y': date.toLocaleDateString('en-US'),
      'd/m/Y': date.toLocaleDateString('en-GB')
    };
    
    return formats[format] || date.toLocaleDateString();
  }
  
  async getTheAuthor(post: any) {
    if (!post?.authorId) return '';
    
    try {
      const userResponse = await userService.getUserProfile(post.authorId);
      if (userResponse.success) {
        return userResponse.data.name || '';
      }
    } catch (error) {
      console.error('Error fetching author:', error);
    }
    
    return '';
  }
  
  async getTheThumbnail(post: any, size: string = 'medium') {
    if (!post?.featuredImageId) return '';
    
    try {
      const mediaResponse = await mediaService.getFile(post.featuredImageId);
      if (mediaResponse.success) {
        const sizes = {
          thumbnail: mediaService.getFilePreview(mediaResponse.data.$id, 150, 150, 80, 'cover'),
          medium: mediaService.getFilePreview(mediaResponse.data.$id, 300, 200, 80, 'cover'),
          large: mediaService.getFilePreview(mediaResponse.data.$id, 1024, 1024, 80, 'contain'),
          full: mediaService.getFileView(mediaResponse.data.$id)
        };
        
        return sizes[size as keyof typeof sizes] || sizes.medium;
      }
    } catch (error) {
      console.error('Error fetching thumbnail:', error);
    }
    
    return '';
  }
  
  // Comments and meta
  async getCommentCount(postId: string) {
    // In a real implementation, you would query the comments collection
    // This is a simplified version
    return 0;
  }
  
  async getPostMeta(postId: string, key: string = '', single: boolean = true) {
    // In a real implementation, you would query the post meta collection
    // This is a simplified version
    return null;
  }
}

export const contentService = ContentService.getInstance();
