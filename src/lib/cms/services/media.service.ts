import { storageServer } from '@/lib/appwrite/server/storage';
import { DatabaseResponse } from '@/lib/appwrite/types/database';

export interface MediaFile {
  $id: string;
  bucketId: string;
  name: string;
  mimeType: string;
  size: number;
  url: string;
  width?: number;
  height?: number;
  duration?: number;
  metadata?: Record<string, any>;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
}

export class MediaService {
  private static instance: MediaService;
  private readonly MEDIA_BUCKET = 'media';

  private constructor() {}

  public static getInstance(): MediaService {
    if (!MediaService.instance) {
      MediaService.instance = new MediaService();
    }
    return MediaService.instance;
  }

  async uploadFile(
    file: File,
    folder: string = '',
    metadata: Record<string, any> = {}
  ): Promise<DatabaseResponse<MediaFile>> {
    try {
      // Ensure the folder ends with a slash
      const folderPath = folder ? `${folder.replace(/\/+$/, '')}/` : '';
      const fileName = `${folderPath}${Date.now()}-${file.name}`;

      const result = await storageServer.uploadFile(this.MEDIA_BUCKET, fileName, file);
      
      // Add metadata to the file
      if (Object.keys(metadata).length > 0) {
        await storageServer.updateFileMetadata(this.MEDIA_BUCKET, result.$id, metadata);
      }

      return {
        success: true,
        data: {
          ...result,
          metadata,
          url: storageServer.getFileView(this.MEDIA_BUCKET, result.$id)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to upload file',
        code: 500
      };
    }
  }

  async getFile(fileId: string): Promise<DatabaseResponse<MediaFile>> {
    try {
      const file = await storageServer.getFile(this.MEDIA_BUCKET, fileId);
      return {
        success: true,
        data: {
          ...file,
          url: storageServer.getFileView(this.MEDIA_BUCKET, fileId)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: 'File not found',
        code: 404
      };
    }
  }

  async deleteFile(fileId: string): Promise<DatabaseResponse<boolean>> {
    try {
      await storageServer.deleteFile(this.MEDIA_BUCKET, fileId);
      return { success: true, data: true };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to delete file',
        code: 500
      };
    }
  }

  async listFiles(
    folder: string = '',
    limit: number = 100,
    offset: number = 0,
    search: string = ''
  ): Promise<DatabaseResponse<{ total: number; files: MediaFile[] }>> {
    try {
      const queries = [];
      
      if (folder) {
        queries.push(`search("${folder}", ["filename"])`);
      }
      
      if (search) {
        queries.push(`search("${search}", ["filename"])`);
      }

      const response = await storageServer.listFiles(this.MEDIA_BUCKET, {
        queries: [
          ...queries,
          `limit(${limit})`,
          `offset(${offset})`,
          'orderBy("$createdAt", "DESC")'
        ]
      });

      const files = response.documents.map(file => ({
        ...file,
        url: storageServer.getFileView(this.MEDIA_BUCKET, file.$id)
      }));

      return {
        success: true,
        data: {
          total: response.total,
          files
        }
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to list files',
        code: 500,
        data: { total: 0, files: [] }
      };
    }
  }

  getFileUrl(fileId: string): string {
    return storageServer.getFileView(this.MEDIA_BUCKET, fileId);
  }

  getFilePreview(
    fileId: string,
    width: number = 300,
    height: number = 200,
    quality: number = 80
  ): string {
    return storageServer.getFilePreview(this.MEDIA_BUCKET, fileId, width, height, undefined, undefined, quality);
  }

  async createFolder(folderPath: string): Promise<DatabaseResponse<boolean>> {
    try {
      // In Appwrite, folders are virtual and created automatically when files are uploaded with paths
      // So we just need to upload an empty file to create the folder
      const folderName = folderPath.endsWith('/') ? folderPath : `${folderPath}/`;
      const tempFile = new File([], '.keep', { type: 'text/plain' });
      
      await storageServer.uploadFile(this.MEDIA_BUCKET, `${folderName}.keep`, tempFile);
      
      return { success: true, data: true };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to create folder',
        code: 500
      };
    }
  }
}

export const mediaService = MediaService.getInstance();
