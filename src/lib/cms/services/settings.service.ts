import { databaseServer } from '@/lib/appwrite/server/database';
import { DatabaseResponse } from '@/lib/appwrite/types/database';

export interface SiteSettings {
  title: string;
  tagline: string;
  description: string;
  siteUrl: string;
  adminEmail: string;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
  startOfWeek: 0 | 1 | 2 | 3 | 4 | 5 | 6; // 0 = Sunday, 6 = Saturday
  language: string;
  defaultRole: string;
  registration: 'none' | 'user' | 'admin';
  defaultUserStatus: 'active' | 'pending' | 'inactive';
  commentStatus: 'open' | 'closed' | 'members_only';
  commentRegistration: boolean;
  commentModeration: boolean;
  commentBlacklist: string[];
  commentMaxLinks: number;
  commentApprovalNotification: boolean;
  commentModeratorNotification: boolean;
  mediaSizes: {
    thumbnail: { width: number; height: number; crop: boolean };
    medium: { width: number; height: number; crop: boolean };
    large: { width: number; height: number; crop: boolean };
  };
  permalinkStructure: string;
  activeTheme: string;
  activePlugins: string[];
  maintenanceMode: boolean;
  maintenanceMessage: string;
  googleAnalyticsId?: string;
  socialLinks: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
    youtube?: string;
    github?: string;
  };
  seo: {
    metaDescription: string;
    metaKeywords: string;
    metaAuthor: string;
    metaViewport: string;
    canonicalUrl: string;
    openGraph: {
      enabled: boolean;
      defaultImage?: string;
      siteName: string;
      type: string;
    };
    twitterCard: {
      enabled: boolean;
      site?: string;
      creator?: string;
      cardType: 'summary' | 'summary_large_image' | 'app' | 'player';
    };
    robotsTxt: string;
  };
  email: {
    fromName: string;
    fromEmail: string;
    smtp: {
      host: string;
      port: number;
      secure: boolean;
      auth: {
        user: string;
        pass: string;
      };
    };
  };
  customCss?: string;
  customJs?: string;
  customHeadHtml?: string;
  customFooterHtml?: string;
  updatedAt: string;
  updatedBy: string;
}

export class SettingsService {
  private static instance: SettingsService;
  private readonly SETTINGS_COLLECTION = 'settings';
  private settingsCache: Map<string, any> = new Map();

  private constructor() {}

  public static getInstance(): SettingsService {
    if (!SettingsService.instance) {
      SettingsService.instance = new SettingsService();
    }
    return SettingsService.instance;
  }

  private async ensureSettingsDocument(): Promise<DatabaseResponse<{ $id: string }>> {
    try {
      // Try to get the settings document
      const response = await databaseServer.listDocuments<{ $id: string }>(this.SETTINGS_COLLECTION, {
        limit: 1
      });

      if (response.success && response.data?.documents.length) {
        return { success: true, data: response.data.documents[0] };
      }

      // Create default settings if not exists
      const defaultSettings: Omit<SiteSettings, keyof DatabaseDocument> = {
        title: 'My CMS',
        tagline: 'A modern content management system',
        description: 'A modern content management system built with Next.js and Appwrite',
        siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
        adminEmail: process.env.ADMIN_EMAIL || '<EMAIL>',
        timezone: 'UTC',
        dateFormat: 'F j, Y',
        timeFormat: 'g:i a',
        startOfWeek: 0, // Sunday
        language: 'en',
        defaultRole: 'user',
        registration: 'user',
        defaultUserStatus: 'pending',
        commentStatus: 'open',
        commentRegistration: false,
        commentModeration: false,
        commentBlacklist: [],
        commentMaxLinks: 2,
        commentApprovalNotification: true,
        commentModeratorNotification: true,
        mediaSizes: {
          thumbnail: { width: 150, height: 150, crop: true },
          medium: { width: 300, height: 200, crop: false },
          large: { width: 1024, height: 1024, crop: false }
        },
        permalinkStructure: '/%postname%/',
        activeTheme: 'default',
        activePlugins: [],
        maintenanceMode: false,
        maintenanceMessage: 'Site is under maintenance. Please check back soon.',
        socialLinks: {},
        seo: {
          metaDescription: '',
          metaKeywords: '',
          metaAuthor: '',
          metaViewport: 'width=device-width, initial-scale=1',
          canonicalUrl: '',
          openGraph: {
            enabled: true,
            siteName: 'My CMS',
            type: 'website'
          },
          twitterCard: {
            enabled: true,
            cardType: 'summary_large_image'
          },
          robotsTxt: 'User-agent: *\nDisallow: /admin/\nAllow: /'
        },
        email: {
          fromName: 'My CMS',
          fromEmail: '<EMAIL>',
          smtp: {
            host: '',
            port: 587,
            secure: false,
            auth: {
              user: '',
              pass: ''
            }
          }
        },
        updatedAt: new Date().toISOString(),
        updatedBy: 'system'
      };

      const createResponse = await databaseServer.createDocument<SiteSettings>(
        this.SETTINGS_COLLECTION,
        defaultSettings
      );

      if (!createResponse.success) {
        throw new Error('Failed to create default settings');
      }

      return { success: true, data: { $id: createResponse.data.$id } };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to initialize settings',
        code: 500
      };
    }
  }

  async getSettings(): Promise<DatabaseResponse<SiteSettings>> {
    // Check cache first
    if (this.settingsCache.has('site')) {
      return { success: true, data: this.settingsCache.get('site') };
    }

    const { success, data, error, code } = await this.ensureSettingsDocument();
    if (!success) {
      return { success: false, error, code };
    }

    const settings = await databaseServer.getDocument<SiteSettings>(
      this.SETTINGS_COLLECTION,
      data.$id
    );

    if (settings.success && settings.data) {
      this.settingsCache.set('site', settings.data);
    }

    return settings;
  }

  async updateSettings(
    updates: Partial<SiteSettings>,
    userId: string = 'system'
  ): Promise<DatabaseResponse<SiteSettings>> {
    const settings = await this.getSettings();
    if (!settings.success || !settings.data) {
      return settings;
    }

    const updatedSettings: SiteSettings = {
      ...settings.data,
      ...updates,
      updatedAt: new Date().toISOString(),
      updatedBy: userId
    };

    const response = await databaseServer.updateDocument<SiteSettings>(
      this.SETTINGS_COLLECTION,
      settings.data.$id,
      updatedSettings
    );

    if (response.success) {
      this.settingsCache.set('site', response.data);
    }

    return response;
  }

  async getSetting<T>(key: keyof SiteSettings): Promise<DatabaseResponse<T>> {
    const settings = await this.getSettings();
    if (!settings.success || !settings.data) {
      return {
        success: false,
        error: settings.error || 'Failed to load settings',
        code: settings.code
      };
    }

    const value = settings.data[key];
    if (value === undefined) {
      return {
        success: false,
        error: `Setting '${key}' not found`,
        code: 404
      };
    }

    return {
      success: true,
      data: value as unknown as T
    };
  }

  async updateSetting<T>(
    key: keyof SiteSettings,
    value: any,
    userId: string = 'system'
  ): Promise<DatabaseResponse<SiteSettings>> {
    const updates = { [key]: value } as Partial<SiteSettings>;
    return this.updateSettings(updates, userId);
  }

  // Helper methods for common settings
  async getSiteInfo() {
    const settings = await this.getSettings();
    if (!settings.success || !settings.data) {
      return {
        success: false as const,
        error: settings.error || 'Failed to load site info',
        code: settings.code
      };
    }

    const { title, tagline, description, siteUrl, socialLinks } = settings.data;
    return {
      success: true as const,
      data: { title, tagline, description, siteUrl, socialLinks }
    };
  }

  async isMaintenanceMode(): Promise<boolean> {
    const response = await this.getSetting<boolean>('maintenanceMode');
    return response.success ? response.data : false;
  }

  async getSeoSettings() {
    const response = await this.getSetting<SiteSettings['seo']>('seo');
    if (!response.success) {
      return {
        success: false as const,
        error: response.error || 'Failed to load SEO settings',
        code: response.code
      };
    }

    return { success: true as const, data: response.data };
  }

  async getMediaSizes() {
    const response = await this.getSetting<SiteSettings['mediaSizes']>('mediaSizes');
    if (!response.success) {
      return {
        success: false as const,
        error: response.error || 'Failed to load media sizes',
        code: response.code
      };
    }

    return { success: true as const, data: response.data };
  }

  // Cache management
  clearCache() {
    this.settingsCache.clear();
  }
}

export const settingsService = SettingsService.getInstance();
