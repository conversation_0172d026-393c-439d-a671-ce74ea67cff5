import { Client, Databases, ID, Query, Models } from 'appwrite';
import { ContentType } from '../models/content-type.model';
import { ContentField } from '../models/content-field.model';
import { ContentEntry } from '../models/content-entry.model';
import { ContentTypeSchema } from '../models/content-type.schema';
import { ContentFieldSchema } from '../models/content-field.schema';
import { ContentEntrySchema } from '../models/content-entry.schema';
import { FormService } from './form.service';

// --- Appwrite Configuration ---
// TODO: Replace with your Appwrite endpoint and project ID
const client = new Client()
    .setEndpoint('https://cloud.appwrite.io/v1') 
    .setProject('YOUR_PROJECT_ID');

const databases = new Databases(client);

// TODO: Replace with your Database ID
const databaseId = 'YOUR_DATABASE_ID';



// --- Service for ContentTypes ---

export class ContentTypeService {
    private collectionId = 'content_types'; // TODO: Create this collection in Appwrite

    async create(contentType: Omit<ContentType, keyof Models.Document>): Promise<ContentType> {
        ContentTypeSchema.parse(contentType);
        const response = await databases.createDocument<ContentType>(
            databaseId,
            this.collectionId,
            ID.unique(),
            contentType
        );
        return response;
    }

    async get(id: string): Promise<ContentType | null> {
        try {
            return await databases.getDocument<ContentType>(databaseId, this.collectionId, id);
        } catch (error) {
            console.error('Failed to get content type:', error);
            return null;
        }
    }

    async list(): Promise<ContentType[]> {
        const response = await databases.listDocuments<ContentType>(databaseId, this.collectionId);
        return response.documents;
    }

    async update(id: string, data: Partial<Omit<ContentType, keyof Models.Document>>): Promise<ContentType> {
        ContentTypeSchema.partial().parse(data);
        // The Appwrite SDK's type for `updateDocument` data is strict. We cast to allow partial updates.
        return await databases.updateDocument<ContentType>(databaseId, this.collectionId, id, data as any);
    }

    async delete(id: string): Promise<void> {
        await databases.deleteDocument(databaseId, this.collectionId, id);
    }
}

// --- Service for ContentFields ---

export class ContentFieldService {
    private collectionId = 'content_fields'; // TODO: Create this collection in Appwrite

    async create(field: Omit<ContentField, keyof Models.Document>): Promise<ContentField> {
        ContentFieldSchema.parse(field);
        return await databases.createDocument<ContentField>(
            databaseId,
            this.collectionId,
            ID.unique(),
            field
        );
    }

    async getForContentType(contentTypeId: string): Promise<ContentField[]> {
        const response = await databases.listDocuments<ContentField>(
            databaseId,
            this.collectionId,
            [Query.equal('contentTypeId', contentTypeId)]
        );
        return response.documents;
    }

    async update(id: string, data: Partial<Omit<ContentField, keyof Models.Document>>): Promise<ContentField> {
        ContentFieldSchema.partial().parse(data);
        // The Appwrite SDK's type for `updateDocument` data is strict. We cast to allow partial updates.
        return await databases.updateDocument<ContentField>(databaseId, this.collectionId, id, data as any);
    }

    async delete(id: string): Promise<void> {
        await databases.deleteDocument(databaseId, this.collectionId, id);
    }
}

// --- Service for ContentEntries ---

export class ContentEntryService {
    private collectionId = 'content_entries'; // TODO: Create this collection in Appwrite

    async create(entry: Omit<ContentEntry, keyof Models.Document>): Promise<ContentEntry> {
        ContentEntrySchema.parse(entry);
        return await databases.createDocument<ContentEntry>(
            databaseId,
            this.collectionId,
            ID.unique(),
            entry
        );
    }

    async get(id: string): Promise<ContentEntry | null> {
        try {
            return await databases.getDocument<ContentEntry>(databaseId, this.collectionId, id);
        } catch (error) {
            console.error('Failed to get content entry:', error);
            return null;
        }
    }

    async list(contentTypeId: string, queries: string[] = []): Promise<ContentEntry[]> {
        const response = await databases.listDocuments<ContentEntry>(
            databaseId,
            this.collectionId,
            [Query.equal('contentTypeId', contentTypeId), ...queries]
        );
        return response.documents;
    }

    async update(id: string, data: Partial<Omit<ContentEntry, keyof Models.Document>>): Promise<ContentEntry> {
        ContentEntrySchema.partial().parse(data);
        // The Appwrite SDK's type for `updateDocument` data is strict. We cast to allow partial updates.
        return await databases.updateDocument<ContentEntry>(databaseId, this.collectionId, id, data as any);
    }

    async delete(id: string): Promise<void> {
        await databases.deleteDocument(databaseId, this.collectionId, id);
    }
}

// --- Main CMS Service ---

export class CmsService {
    public contentTypes = new ContentTypeService();
    public contentFields = new ContentFieldService();
    public contentEntries = new ContentEntryService();
    public forms: FormService;

    constructor() {
        this.forms = new FormService(this.contentTypes, this.contentFields);
    }
}

export const cms = new CmsService();
