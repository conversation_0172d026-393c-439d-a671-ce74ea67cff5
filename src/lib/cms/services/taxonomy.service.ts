import { databaseServer } from '@/lib/appwrite/server/database';
import { DatabaseDocument, DatabaseResponse } from '@/lib/appwrite/types/database';
import { contentService } from './content.service';

export interface Term extends DatabaseDocument {
  name: string;
  slug: string;
  description?: string;
  taxonomy: string;
  parentId?: string;
  count: number;
  meta?: Record<string, any>;
  ancestors?: string[];
  children?: Term[];
}

export interface Taxonomy {
  name: string;
  slug: string;
  description?: string;
  hierarchical: boolean;
  showInNavMenus: boolean;
  showInAdminUI: boolean;
  showInRest: boolean;
  restBase: string;
  showTagcloud: boolean;
  showInQuickEdit: boolean;
  showAdminColumn: boolean;
  meta?: Record<string, any>;
  objectType: string[];
}

export class TaxonomyService {
  private static instance: TaxonomyService;
  private readonly TAXONOMY_COLLECTION = 'taxonomies';
  private readonly TERM_COLLECTION = 'terms';
  private readonly TERM_RELATIONSHIP_COLLECTION = 'term_relationships';
  
  // Built-in taxonomies
  private readonly BUILT_IN_TAXONOMIES: Record<string, Taxonomy> = {
    category: {
      name: 'Categories',
      slug: 'category',
      description: 'Categories provide a helpful way to group related posts together.',
      hierarchical: true,
      showInNavMenus: true,
      showInAdminUI: true,
      showInRest: true,
      restBase: 'categories',
      showTagcloud: false,
      showInQuickEdit: true,
      showAdminColumn: true,
      objectType: ['post']
    },
    post_tag: {
      name: 'Tags',
      slug: 'post_tag',
      description: 'Tags provide a way to create flexible connections between posts.',
      hierarchical: false,
      showInNavMenus: true,
      showInAdminUI: true,
      showInRest: true,
      restBase: 'tags',
      showTagcloud: true,
      showInQuickEdit: true,
      showAdminColumn: true,
      objectType: ['post']
    }
  };
  
  private taxonomies: Map<string, Taxonomy> = new Map();
  
  private constructor() {}
  
  public static getInstance(): TaxonomyService {
    if (!TaxonomyService.instance) {
      TaxonomyService.instance = new TaxonomyService();
    }
    return TaxonomyService.instance;
  }
  
  // Taxonomy Methods
  async registerTaxonomy(taxonomy: Omit<Taxonomy, 'slug'>, objectTypes: string[] = ['post']) {
    const slug = taxonomy.name.toLowerCase().replace(/\s+/g, '_');
    
    const newTaxonomy: Taxonomy = {
      ...taxonomy,
      slug,
      objectType: objectTypes
    };
    
    // Check if taxonomy already exists
    const existing = await this.getTaxonomy(slug);
    
    if (existing) {
      // Update existing taxonomy
      await this.updateTaxonomy(slug, newTaxonomy);
      return slug;
    }
    
    // Create new taxonomy
    const response = await databaseServer.createDocument<Taxonomy>(
      this.TAXONOMY_COLLECTION,
      newTaxonomy
    );
    
    if (response.success) {
      this.taxonomies.set(slug, response.data);
      return slug;
    }
    
    throw new Error(response.error || 'Failed to register taxonomy');
  }
  
  async getTaxonomy(slug: string): Promise<Taxonomy | null> {
    // Check cache first
    if (this.taxonomies.has(slug)) {
      return this.taxonomies.get(slug) || null;
    }
    
    // Check built-in taxonomies
    if (this.BUILT_IN_TAXONOMIES[slug]) {
      return this.BUILT_IN_TAXONOMIES[slug];
    }
    
    // Query database
    const response = await databaseServer.listDocuments<Taxonomy>(
      this.TAXONOMY_COLLECTION,
      { queries: [`equal("slug", "${slug}")`, 'limit(1)'] }
    );
    
    if (response.success && response.data?.documents.length > 0) {
      const taxonomy = response.data.documents[0];
      this.taxonomies.set(slug, taxonomy);
      return taxonomy;
    }
    
    return null;
  }
  
  async getTaxonomies(args: {
    objectType?: string;
    hierarchical?: boolean;
    showInNavMenus?: boolean;
  } = {}): Promise<Taxonomy[]> {
    const { objectType, hierarchical, showInNavMenus } = args;
    const queries: string[] = [];
    
    if (objectType) {
      queries.push(`search("${objectType}", ["objectType"])`);
    }
    
    if (hierarchical !== undefined) {
      queries.push(`equal("hierarchical", ${hierarchical})`);
    }
    
    if (showInNavMenus !== undefined) {
      queries.push(`equal("showInNavMenus", ${showInNavMenus})`);
    }
    
    // Get built-in taxonomies
    const builtInTaxonomies = Object.values(this.BUILT_IN_TAXONOMIES);
    
    // Get custom taxonomies from database
    const response = await databaseServer.listDocuments<Taxonomy>(
      this.TAXONOMY_COLLECTION,
      { queries }
    );
    
    const customTaxonomies = response.success ? response.data?.documents || [] : [];
    
    // Merge and filter
    const allTaxonomies = [...builtInTaxonomies, ...customTaxonomies];
    
    // Filter by objectType if provided
    if (objectType) {
      return allTaxonomies.filter(taxonomy => 
        taxonomy.objectType.includes(objectType)
      );
    }
    
    return allTaxonomies;
  }
  
  async updateTaxonomy(slug: string, data: Partial<Omit<Taxonomy, 'slug'>>): Promise<boolean> {
    const taxonomy = await this.getTaxonomy(slug);
    
    if (!taxonomy) {
      throw new Error(`Taxonomy '${slug}' not found`);
    }
    
    // Built-in taxonomies can't be updated
    if (this.BUILT_IN_TAXONOMIES[slug]) {
      throw new Error('Cannot update built-in taxonomies');
    }
    
    const response = await databaseServer.updateDocument<Taxonomy>(
      this.TAXONOMY_COLLECTION,
      slug,
      { ...data, $updatedAt: new Date().toISOString() }
    );
    
    if (response.success) {
      // Update cache
      this.taxonomies.set(slug, { ...taxonomy, ...data, slug });
      return true;
    }
    
    return false;
  }
  
  async deleteTaxonomy(slug: string, force: boolean = false): Promise<boolean> {
    // Built-in taxonomies can't be deleted
    if (this.BUILT_IN_TAXONOMIES[slug]) {
      throw new Error('Cannot delete built-in taxonomies');
    }
    
    if (!force) {
      // Check if taxonomy has terms
      const terms = await this.getTerms({ taxonomy: slug, hideEmpty: false });
      if (terms.length > 0) {
        throw new Error('Cannot delete taxonomy that has terms. Set force=true to delete anyway.');
      }
    }
    
    const response = await databaseServer.deleteDocument(
      this.TAXONOMY_COLLECTION,
      slug
    );
    
    if (response.success) {
      // Clear cache
      this.taxonomies.delete(slug);
      return true;
    }
    
    return false;
  }
  
  // Term Methods
  async createTerm(
    name: string,
    taxonomy: string,
    args: {
      description?: string;
      parentId?: string;
      slug?: string;
      meta?: Record<string, any>;
    } = {}
  ): Promise<Term> {
    // Verify taxonomy exists
    const taxonomyExists = await this.getTaxonomy(taxonomy);
    if (!taxonomyExists) {
      throw new Error(`Taxonomy '${taxonomy}' does not exist`);
    }
    
    // Generate slug if not provided
    const slug = args.slug || name.toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .trim()
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .toLowerCase();
    
    // Check if term with this slug already exists
    const existingTerm = await this.getTermBySlug(slug, taxonomy);
    if (existingTerm) {
      throw new Error(`A term with the slug '${slug}' already exists in this taxonomy`);
    }
    
    // Handle parent term if provided
    if (args.parentId) {
      if (!taxonomyExists.hierarchical) {
        throw new Error(`Taxonomy '${taxonomy}' does not support hierarchical terms`);
      }
      
      const parentTerm = await this.getTerm(args.parentId, taxonomy);
      if (!parentTerm) {
        throw new Error(`Parent term with ID '${args.parentId}' not found`);
      }
    }
    
    const termData: Omit<Term, keyof DatabaseDocument> = {
      name,
      slug,
      description: args.description || '',
      taxonomy,
      parentId: args.parentId,
      count: 0,
      meta: args.meta || {},
      ancestors: args.parentId ? await this.getTermAncestors(args.parentId) : []
    };
    
    const response = await databaseServer.createDocument<Term>(
      this.TERM_COLLECTION,
      termData
    );
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to create term');
    }
    
    return response.data;
  }
  
  async getTerm(termId: string, taxonomy?: string): Promise<Term | null> {
    const response = await databaseServer.getDocument<Term>(
      this.TERM_COLLECTION,
      termId
    );
    
    if (!response.success || !response.data) {
      return null;
    }
    
    // Filter by taxonomy if provided
    if (taxonomy && response.data.taxonomy !== taxonomy) {
      return null;
    }
    
    return response.data;
  }
  
  async getTermBySlug(slug: string, taxonomy: string): Promise<Term | null> {
    const response = await databaseServer.listDocuments<Term>(
      this.TERM_COLLECTION,
      { 
        queries: [
          `equal("slug", "${slug}")`,
          `equal("taxonomy", "${taxonomy}")`,
          'limit(1)'
        ]
      }
    );
    
    if (response.success && response.data?.documents.length > 0) {
      return response.data.documents[0];
    }
    
    return null;
  }
  
  async getTerms(args: {
    taxonomy?: string | string[];
    objectIds?: string[];
    orderby?: 'name' | 'count' | 'slug' | 'term_order' | 'include' | 'term_id' | 'none';
    order?: 'ASC' | 'DESC';
    hideEmpty?: boolean;
    include?: string[];
    exclude?: string[];
    number?: number;
    offset?: number;
    fields?: 'all' | 'ids' | 'id=>parent' | 'id=>name' | 'id=>slug' | 'names';
    name?: string | string[];
    slug?: string | string[];
    hierarchical?: boolean;
    search?: string;
    nameLike?: string;
    descriptionLike?: string;
    padCounts?: boolean;
    get?: 'all' | 'all_with_object_id';
    childOf?: string;
    parent?: string;
    childless?: boolean;
    cacheDomain?: string;
    updateTermMetaCache?: boolean;
    metaQuery?: Record<string, any>;
    metaKey?: string;
    metaValue?: any;
    metaCompare?: string;
    metaType?: string;
  } = {}): Promise<Term[]> {
    const {
      taxonomy,
      orderby = 'name',
      order = 'ASC',
      hideEmpty = false,
      number = 100,
      offset = 0,
      search,
      parent,
      childOf,
      name,
      slug: slugs,
      include,
      exclude,
      metaQuery,
      metaKey,
      metaValue,
      metaCompare = '=',
      metaType
    } = args;
    
    const queries: string[] = [];
    
    // Filter by taxonomy
    if (taxonomy) {
      const taxonomies = Array.isArray(taxonomy) ? taxonomy : [taxonomy];
      if (taxonomies.length > 0) {
        queries.push(`equal("taxonomy", [${taxonomies.map(t => `"${t}"`).join(',')}])`);
      }
    }
    
    // Filter by parent
    if (parent !== undefined) {
      queries.push(`equal("parentId", "${parent}")`);
    } else if (childOf !== undefined) {
      // Get all descendant terms
      const descendants = await this.getTermChildren(childOf, true);
      const termIds = [childOf, ...descendants.map(t => t.$id)];
      queries.push(`in("$id", [${termIds.map(id => `"${id}"`).join(',')}])`);
    }
    
    // Filter by name
    if (name) {
      const names = Array.isArray(name) ? name : [name];
      if (names.length > 0) {
        queries.push(`in("name", [${names.map(n => `"${n}"`).join(',')}])`);
      }
    }
    
    // Filter by slug
    if (slugs) {
      const slugList = Array.isArray(slugs) ? slugs : [slugs];
      if (slugList.length > 0) {
        queries.push(`in("slug", [${slugList.map(s => `"${s}"`).join(',')}])`);
      }
    }
    
    // Include specific terms
    if (include?.length) {
      queries.push(`in("$id", [${include.map(id => `"${id}"`).join(',')}])`);
    }
    
    // Exclude specific terms
    if (exclude?.length) {
      queries.push(`notIn("$id", [${exclude.map(id => `"${id}"`).join(',')}])`);
    }
    
    // Search
    if (search) {
      queries.push(`search("${search}", ["name", "description"])`);
    }
    
    // Meta query
    if (metaQuery) {
      // Simplified meta query implementation
      // In a real app, you'd want to handle all the different compare operations
      Object.entries(metaQuery).forEach(([key, value]) => {
        if (value !== undefined) {
          queries.push(`equal("meta.${key}", ${typeof value === 'string' ? `"${value}"` : value})`);
        }
      });
    } else if (metaKey) {
      // Legacy meta query
      const compare = metaCompare === '!=' ? 'notEqual' : 'equal';
      queries.push(`${compare}("meta.${metaKey}", ${typeof metaValue === 'string' ? `"${metaValue}"` : metaValue})`);
    }
    
    // Ordering
    let orderField = 'name';
    
    switch (orderby) {
      case 'count':
        orderField = 'count';
        break;
      case 'slug':
        orderField = 'slug';
        break;
      case 'term_id':
        orderField = '$id';
        break;
      case 'include':
        // Order by include array if provided
        if (include?.length) {
          // This would require custom sorting after fetching
          // For now, we'll just sort by name
          orderField = 'name';
        }
        break;
      case 'none':
        orderField = '$id';
        break;
      case 'name':
      default:
        orderField = 'name';
    }
    
    queries.push(`orderBy("${orderField}", "${order}")`);
    
    // Pagination
    queries.push(`limit(${number})`);
    queries.push(`offset(${offset})`);
    
    // Execute query
    const response = await databaseServer.listDocuments<Term>(
      this.TERM_COLLECTION,
      { queries }
    );
    
    if (!response.success) {
      console.error('Error fetching terms:', response.error);
      return [];
    }
    
    let terms = response.data?.documents || [];
    
    // Handle hideEmpty
    if (hideEmpty) {
      terms = terms.filter(term => term.count > 0);
    }
    
    // Handle hierarchical
    if (args.hierarchical) {
      return this.buildTermTree(terms);
    }
    
    return terms;
  }
  
  async updateTerm(
    termId: string,
    args: {
      name?: string;
      description?: string;
      parentId?: string | null;
      slug?: string;
      meta?: Record<string, any>;
    }
  ): Promise<Term | null> {
    const term = await this.getTerm(termId);
    if (!term) {
      throw new Error(`Term with ID '${termId}' not found`);
    }
    
    const updateData: Partial<Term> = {};
    
    if (args.name !== undefined) updateData.name = args.name;
    if (args.description !== undefined) updateData.description = args.description;
    if (args.slug !== undefined) updateData.slug = args.slug;
    if (args.meta !== undefined) updateData.meta = { ...term.meta, ...args.meta };
    
    // Handle parent change
    if ('parentId' in args) {
      if (args.parentId === null || args.parentId === '') {
        updateData.parentId = undefined;
        updateData.ancestors = [];
      } else if (args.parentId !== term.parentId) {
        const parentTerm = await this.getTerm(args.parentId, term.taxonomy);
        if (!parentTerm) {
          throw new Error(`Parent term with ID '${args.parentId}' not found`);
        }
        
        // Check for circular reference
        if (await this.isTermAncestor(termId, args.parentId)) {
          throw new Error('Cannot set a term as a child of one of its descendants');
        }
        
        updateData.parentId = args.parentId;
        updateData.ancestors = [
          ...(parentTerm.ancestors || []),
          parentTerm.$id
        ];
      }
    }
    
    // Only update if there are changes
    if (Object.keys(updateData).length === 0) {
      return term;
    }
    
    updateData.$updatedAt = new Date().toISOString();
    
    const response = await databaseServer.updateDocument<Term>(
      this.TERM_COLLECTION,
      termId,
      updateData
    );
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to update term');
    }
    
    // Update child terms if parent changed
    if ('parentId' in args) {
      await this.updateTermAncestors(termId, updateData.ancestors || []);
    }
    
    return response.data;
  }
  
  async deleteTerm(termId: string, taxonomy?: string, force: boolean = false): Promise<boolean> {
    const term = await this.getTerm(termId, taxonomy);
    if (!term) {
      throw new Error(`Term with ID '${termId}' not found`);
    }
    
    // Check if term has children
    const children = await this.getTermChildren(termId);
    if (children.length > 0 && !force) {
      throw new Error('Cannot delete term with children. Set force=true to delete anyway.');
    }
    
    // Reassign children to parent if force deleting
    if (children.length > 0 && force) {
      for (const child of children) {
        await this.updateTerm(child.$id, { parentId: term.parentId });
      }
    }
    
    // Delete term relationships
    await this.deleteTermRelationships(termId);
    
    // Delete the term
    const response = await databaseServer.deleteDocument(
      this.TERM_COLLECTION,
      termId
    );
    
    return response.success;
  }
  
  // Term Relationships
  async setObjectTerms(
    objectId: string,
    termIds: string | string[],
    taxonomy: string,
    append: boolean = false
  ): Promise<boolean> {
    const terms = Array.isArray(termIds) ? termIds : [termIds];
    
    // Validate terms
    for (const termId of terms) {
      const term = await this.getTerm(termId, taxonomy);
      if (!term) {
        throw new Error(`Term with ID '${termId}' not found in taxonomy '${taxonomy}'`);
      }
    }
    
    if (!append) {
      // Remove existing relationships for this object and taxonomy
      await this.deleteObjectRelationships(objectId, taxonomy);
    }
    
    // Create new relationships
    const created = await Promise.all(
      terms.map(termId => 
        this.createTermRelationship(objectId, termId, taxonomy)
      )
    );
    
    // Update term counts
    await Promise.all(
      terms.map(termId => this.updateTermCount(termId))
    );
    
    return created.every(Boolean);
  }
  
  async getObjectTerms(
    objectId: string,
    taxonomy?: string | string[]
  ): Promise<Term[]> {
    const queries = [`equal("objectId", "${objectId}")`];
    
    if (taxonomy) {
      const taxonomies = Array.isArray(taxonomy) ? taxonomy : [taxonomy];
      if (taxonomies.length > 0) {
        queries.push(`equal("taxonomy", [${taxonomies.map(t => `"${t}"`).join(',')}])`);
      }
    }
    
    const response = await databaseServer.listDocuments<{
      $id: string;
      objectId: string;
      termId: string;
      taxonomy: string;
      termOrder: number;
    }>(
      this.TERM_RELATIONSHIP_COLLECTION,
      { queries }
    );
    
    if (!response.success || !response.data?.documents.length) {
      return [];
    }
    
    // Get the actual terms
    const termIds = response.data.documents.map(r => r.termId);
    return this.getTerms({ include: termIds });
  }
  
  async getTermObjects(
    termId: string,
    contentType: string = 'post',
    limit: number = 10,
    offset: number = 0
  ): Promise<{ objects: any[]; total: number }> {
    // Get relationships for this term
    const relationships = await databaseServer.listDocuments<{
      $id: string;
      objectId: string;
      termId: string;
      taxonomy: string;
    }>(
      this.TERM_RELATIONSHIP_COLLECTION,
      { 
        queries: [
          `equal("termId", "${termId}")`,
          `limit(${limit})`,
          `offset(${offset})`,
          'orderBy("$createdAt", "DESC")'
        ]
      }
    );
    
    if (!relationships.success || !relationships.data?.documents.length) {
      return { objects: [], total: 0 };
    }
    
    // Get the actual content objects
    const objectIds = relationships.data.documents.map(r => r.objectId);
    const { posts, total } = await contentService.getPosts({
      postIn: objectIds,
      perPage: limit,
      page: 1
    });
    
    return {
      objects: posts,
      total: relationships.data.total
    };
  }
  
  // Helper Methods
  private async createTermRelationship(
    objectId: string,
    termId: string,
    taxonomy: string
  ): Promise<boolean> {
    // Check if relationship already exists
    const existing = await databaseServer.listDocuments(
      this.TERM_RELATIONSHIP_COLLECTION,
      {
        queries: [
          `equal("objectId", "${objectId}")`,
          `equal("termId", "${termId}")`,
          'limit(1)'
        ]
      }
    );
    
    if (existing.success && existing.data?.documents.length > 0) {
      return true; // Relationship already exists
    }
    
    // Create new relationship
    const response = await databaseServer.createDocument(
      this.TERM_RELATIONSHIP_COLLECTION,
      {
        objectId,
        termId,
        taxonomy,
        termOrder: 0
      }
    );
    
    return response.success;
  }
  
  private async deleteTermRelationships(termId: string): Promise<boolean> {
    // Get all relationships for this term
    const response = await databaseServer.listDocuments(
      this.TERM_RELATIONSHIP_COLLECTION,
      { queries: [`equal("termId", "${termId}")`] }
    );
    
    if (!response.success) {
      return false;
    }
    
    // Delete all relationships
    const deletePromises = response.data.documents.map(doc =>
      databaseServer.deleteDocument(this.TERM_RELATIONSHIP_COLLECTION, doc.$id)
    );
    
    await Promise.all(deletePromises);
    return true;
  }
  
  private async deleteObjectRelationships(
    objectId: string,
    taxonomy?: string
  ): Promise<boolean> {
    const queries = [`equal("objectId", "${objectId}")`];
    
    if (taxonomy) {
      queries.push(`equal("taxonomy", "${taxonomy}")`);
    }
    
    const response = await databaseServer.listDocuments(
      this.TERM_RELATIONSHIP_COLLECTION,
      { queries }
    );
    
    if (!response.success) {
      return false;
    }
    
    // Delete all relationships
    const deletePromises = response.data.documents.map(doc =>
      databaseServer.deleteDocument(this.TERM_RELATIONSHIP_COLLECTION, doc.$id)
    );
    
    await Promise.all(deletePromises);
    return true;
  }
  
  private async updateTermCount(termId: string): Promise<void> {
    // Count relationships for this term
    const response = await databaseServer.listDocuments(
      this.TERM_RELATIONSHIP_COLLECTION,
      { 
        queries: [
          `equal("termId", "${termId}")`,
          'select(["$id"])',
          'limit(1)'
        ]
      }
    );
    
    if (response.success) {
      await databaseServer.updateDocument(
        this.TERM_COLLECTION,
        termId,
        { count: response.data?.total || 0 }
      );
    }
  }
  
  private async getTermAncestors(termId: string): Promise<string[]> {
    const ancestors: string[] = [];
    let currentId = termId;
    
    // Prevent infinite loops
    let maxDepth = 50;
    
    while (currentId && maxDepth-- > 0) {
      const term = await this.getTerm(currentId);
      if (!term || !term.parentId) break;
      
      ancestors.unshift(term.parentId);
      currentId = term.parentId;
    }
    
    return ancestors;
  }
  
  private async getTermChildren(termId: string, recursive: boolean = false): Promise<Term[]> {
    const queries = [`equal("parentId", "${termId}")`];
    
    const response = await databaseServer.listDocuments<Term>(
      this.TERM_COLLECTION,
      { queries }
    );
    
    if (!response.success || !response.data?.documents.length) {
      return [];
    }
    
    let children = response.data.documents;
    
    if (recursive) {
      const grandChildren = await Promise.all(
        children.map(child => this.getTermChildren(child.$id, true))
      );
      
      children = [...children, ...grandChildren.flat()];
    }
    
    return children;
  }
  
  private async isTermAncestor(termId: string, potentialAncestorId: string): Promise<boolean> {
    if (termId === potentialAncestorId) {
      return true;
    }
    
    const term = await this.getTerm(termId);
    if (!term || !term.ancestors) {
      return false;
    }
    
    return term.ancestors.includes(potentialAncestorId);
  }
  
  private buildTermTree(terms: Term[], parentId?: string): Term[] {
    const tree: Term[] = [];
    const children = terms.filter(term => 
      (term.parentId === parentId) || 
      (!term.parentId && !parentId)
    );
    
    for (const child of children) {
      const grandchildren = this.buildTermTree(terms, child.$id);
      if (grandchildren.length > 0) {
        child.children = grandchildren;
      }
      tree.push(child);
    }
    
    return tree;
  }
  
  private async updateTermAncestors(termId: string, ancestors: string[]): Promise<void> {
    // Get all children
    const children = await this.getTermChildren(termId, true);
    
    // Update each child's ancestors
    await Promise.all(
      children.map(child => 
        databaseServer.updateDocument(
          this.TERM_COLLECTION,
          child.$id,
          { 
            ancestors: [...ancestors, termId],
            $updatedAt: new Date().toISOString()
          }
        )
      )
    );
  }
}

export const taxonomyService = TaxonomyService.getInstance();
