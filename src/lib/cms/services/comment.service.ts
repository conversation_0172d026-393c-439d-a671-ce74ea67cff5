import { databaseServer } from '@/lib/appwrite/server/database';
import { DatabaseDocument } from '@/lib/appwrite/types/database';
import { contentService } from './content.service';
import { userService, User } from './user.service';

export interface Comment extends DatabaseDocument {
  content: string;
  contentId: string; // ID of the content this comment is on
  parentId?: string; // For threaded comments
  userId?: string; // If user is logged in
  authorName?: string; // For guest comments
  authorEmail?: string; // For guest comments
  authorUrl?: string; // For guest comments
  authorIp: string;
  authorUserAgent: string;
  karma: number; // Comment karma
  approved: boolean;
  type?: string; // 'comment', 'pingback', 'trackback', etc.
  meta?: Record<string, any>;
  // For nested comments
  children?: Comment[];
  // For UI
  user?: User | null;
  contentTitle?: string;
}

export interface CommentCount {
  approved: number;
  awaitingModeration: number;
  spam: number;
  trash: number;
  postTrashed: number;
  total: number;
}

export class CommentService {
  private static instance: CommentService;
  private readonly COLLECTION = 'comments';
  
  private constructor() {}
  
  public static getInstance(): CommentService {
    if (!CommentService.instance) {
      CommentService.instance = new CommentService();
    }
    return CommentService.instance;
  }
  
  // Create a new comment
  async createComment(data: Omit<Comment, keyof DatabaseDocument | 'karma' | 'approved' | 'children' | 'user' | 'contentTitle'>, approved: boolean = false): Promise<Comment> {
    // Basic validation
    if (!data.content || !data.content.trim()) {
      throw new Error('Comment content is required');
    }
    
    if (!data.contentId) {
      throw new Error('Content ID is required');
    }
    
    // Verify content exists if needed
    const content = await contentService.getPost(data.contentId);
    if (!content) {
      throw new Error('Content not found');
    }
    
    // Verify parent comment exists if provided
    if (data.parentId) {
      const parentComment = await this.getComment(data.parentId);
      if (!parentComment) {
        throw new Error('Parent comment not found');
      }
      
      // Prevent deep nesting
      const depth = await this.getCommentDepth(data.parentId);
      if (depth >= 5) {
        throw new Error('Maximum comment depth reached');
      }
    }
    
    // Check for duplicate comments (simple spam prevention)
    const duplicate = await this.checkForDuplicateComment(data);
    if (duplicate) {
      throw new Error('Duplicate comment detected');
    }
    
    // Create the comment
    const commentData: Omit<Comment, keyof DatabaseDocument | 'children' | 'user' | 'contentTitle'> = {
      ...data,
      content: this.sanitizeComment(data.content),
      karma: 0,
      approved,
      type: data.type || 'comment',
      meta: data.meta || {}
    };
    
    const response = await databaseServer.createDocument<Comment>(
      this.COLLECTION,
      commentData
    );
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to create comment');
    }
    
    // Update comment count on the content
    await this.updateCommentCount(data.contentId);
    
    return this.enrichComment(response.data);
  }
  
  // Get a single comment by ID
  async getComment(commentId: string): Promise<Comment | null> {
    const response = await databaseServer.getDocument<Comment>(
      this.COLLECTION,
      commentId
    );
    
    if (!response.success || !response.data) {
      return null;
    }
    
    return this.enrichComment(response.data);
  }
  
  // Get comments with various filters (similar to WP_Comment_Query)
  async getComments(args: {
    contentId?: string;
    parentId?: string | 'all' | 'none';
    userId?: string;
    authorEmail?: string;
    authorUrl?: string;
    status?: 'all' | 'hold' | 'approve' | 'spam' | 'trash';
    type?: string;
    typeIn?: string[];
    typeNotIn?: string[];
    include?: string[];
    exclude?: string[];
    search?: string;
    authorIn?: string[];
    authorNotIn?: string[];
    authorName?: string;
    authorIp?: string;
    karma?: number;
    dateQuery?: {
      after?: Date;
      before?: Date;
      inclusive?: boolean;
      column?: string;
    };
    orderby?: 'comment_date' | 'comment_date_gmt' | 'comment_karma' | 'comment_parent' | 'comment_post_ID' | 'comment_author' | 'comment_author_email' | 'comment_author_url' | 'comment_content' | 'comment_ID' | 'user_id' | 'comment_agent' | 'comment_type' | 'comment_approved';
    order?: 'ASC' | 'DESC';
    number?: number;
    offset?: number;
    paged?: number;
    count?: boolean;
    metaQuery?: Record<string, any>;
    updateCommentMetaCache?: boolean;
    updateCommentPostCache?: boolean;
    fields?: 'all' | 'ids';
    hierarchical?: boolean;
  } = {}): Promise<{ comments: Comment[]; total: number }> {
    const {
      contentId,
      parentId,
      status = 'approve',
      type = 'comment',
      typeIn,
      typeNotIn,
      include,
      exclude,
      search,
      authorIn,
      authorNotIn,
      authorName,
      authorEmail,
      authorUrl,
      authorIp,
      karma,
      dateQuery,
      orderby = 'comment_date',
      order = 'DESC',
      number = 20,
      offset = 0,
      paged,
      hierarchical = false,
      fields = 'all'
    } = args;
    
    const queries: string[] = [];
    
    // Content ID
    if (contentId) {
      queries.push(`equal("contentId", "${contentId}")`);
    }
    
    // Parent ID
    if (parentId === 'none') {
      queries.push('isNull("parentId")');
    } else if (parentId && parentId !== 'all') {
      queries.push(`equal("parentId", "${parentId}")`);
    }
    
    // Status
    if (status === 'approve') {
      queries.push('equal("approved", true)');
    } else if (status === 'hold') {
      queries.push('equal("approved", false)');
    } else if (status === 'spam') {
      queries.push('equal("type", "spam")');
    } else if (status === 'trash') {
      queries.push('equal("type", "trash")');
    }
    
    // Type
    if (typeIn) {
      queries.push(`in("type", [${typeIn.map(t => `"${t}"`).join(',')}])`);
    } else if (typeNotIn) {
      queries.push(`notIn("type", [${typeNotIn.map(t => `"${t}"`).join(',')}])`);
    } else if (type !== 'all') {
      queries.push(`equal("type", "${type}")`);
    }
    
    // Include/exclude
    if (include?.length) {
      queries.push(`in("$id", [${include.map(id => `"${id}"`).join(',')}])`);
    }
    
    if (exclude?.length) {
      queries.push(`notIn("$id", [${exclude.map(id => `"${id}"`).join(',')}])`);
    }
    
    // Search
    if (search) {
      queries.push(`search("${search}", ["content", "authorName", "authorEmail"])`);
    }
    
    // Author filters
    if (authorIn?.length) {
      queries.push(`in("userId", [${authorIn.map(id => `"${id}"`).join(',')}])`);
    }
    
    if (authorNotIn?.length) {
      queries.push(`notIn("userId", [${authorNotIn.map(id => `"${id}"`).join(',')}])`);
    }
    
    if (authorName) {
      queries.push(`search("${authorName}", ["authorName"])`);
    }
    
    if (authorEmail) {
      queries.push(`equal("authorEmail", "${authorEmail}")`);
    }
    
    if (authorUrl) {
      queries.push(`equal("authorUrl", "${authorUrl}")`);
    }
    
    if (authorIp) {
      queries.push(`equal("authorIp", "${authorIp}")`);
    }
    
    // Karma
    if (karma !== undefined) {
      queries.push(`equal("karma", ${karma})`);
    }
    
    // Date query
    if (dateQuery) {
      const { after, before, inclusive = true, column = 'createdAt' } = dateQuery;
      
      if (after) {
        queries.push(`greaterThan${inclusive ? 'Equal' : ''}("${column}", "${after.toISOString()}")`);
      }
      
      if (before) {
        queries.push(`lessThan${inclusive ? 'Equal' : ''}("${column}", "${before.toISOString()}")`);
      }
    }
    
    // Ordering
    let orderField = 'createdAt';
    
    switch (orderby) {
      case 'comment_date':
      case 'comment_date_gmt':
        orderField = 'createdAt';
        break;
      case 'comment_karma':
        orderField = 'karma';
        break;
      case 'comment_parent':
        orderField = 'parentId';
        break;
      case 'comment_author':
        orderField = 'authorName';
        break;
      case 'comment_author_email':
        orderField = 'authorEmail';
        break;
      case 'comment_author_url':
        orderField = 'authorUrl';
        break;
      case 'comment_content':
        orderField = 'content';
        break;
      case 'comment_ID':
        orderField = '$id';
        break;
      case 'user_id':
        orderField = 'userId';
        break;
      case 'comment_agent':
        orderField = 'authorUserAgent';
        break;
      case 'comment_type':
        orderField = 'type';
        break;
      case 'comment_approved':
        orderField = 'approved';
        break;
      default:
        orderField = 'createdAt';
    }
    
    queries.push(`orderBy("${orderField}", "${order}")`);
    
    // Pagination
    const page = paged || 1;
    const pageOffset = offset || (page - 1) * number;
    
    queries.push(`limit(${number})`);
    queries.push(`offset(${pageOffset})`);
    
    // Execute query
    const response = await databaseServer.listDocuments<Comment>(
      this.COLLECTION,
      { queries }
    );
    
    if (!response.success) {
      console.error('Error fetching comments:', response.error);
      return { comments: [], total: 0 };
    }
    
    let comments = response.data?.documents || [];
    const total = response.data?.total || 0;
    
    // Enrich comments with user data
    comments = await Promise.all(comments.map(comment => this.enrichComment(comment)));
    
    // Handle hierarchical comments
    if (hierarchical) {
      const commentMap = new Map<string, Comment>();
      const rootComments: Comment[] = [];
      
      // First pass: Create a map of comments
      for (const comment of comments) {
        commentMap.set(comment.$id, { ...comment, children: [] });
      }
      
      // Second pass: Build the tree
      for (const comment of commentMap.values()) {
        if (comment.parentId && commentMap.has(comment.parentId)) {
          const parent = commentMap.get(comment.parentId)!;
          if (!parent.children) parent.children = [];
          parent.children.push(comment);
        } else if (!comment.parentId) {
          rootComments.push(comment);
        }
      }
      
      return { 
        comments: rootComments, 
        total 
      };
    }
    
    return { comments, total };
  }
  
  // Update a comment
  async updateComment(
    commentId: string,
    data: Partial<Omit<Comment, keyof DatabaseDocument | 'children' | 'user' | 'contentTitle'>>
  ): Promise<Comment | null> {
    const comment = await this.getComment(commentId);
    if (!comment) {
      throw new Error('Comment not found');
    }
    
    // Handle content sanitization
    if (data.content) {
      data.content = this.sanitizeComment(data.content);
    }
    
    // Update the comment
    const response = await databaseServer.updateDocument<Comment>(
      this.COLLECTION,
      commentId,
      {
        ...data,
        $updatedAt: new Date().toISOString()
      }
    );
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to update comment');
    }
    
    // If approval status changed, update comment count
    if (data.approved !== undefined && data.approved !== comment.approved) {
      await this.updateCommentCount(comment.contentId);
    }
    
    return this.enrichComment(response.data);
  }
  
  // Delete a comment (moves to trash by default)
  async deleteComment(commentId: string, force: boolean = false): Promise<boolean> {
    const comment = await this.getComment(commentId);
    if (!comment) {
      throw new Error('Comment not found');
    }
    
    if (force) {
      // Permanently delete the comment
      const response = await databaseServer.deleteDocument(
        this.COLLECTION,
        commentId
      );
      
      if (response.success) {
        // Update comment count
        await this.updateCommentCount(comment.contentId);
        return true;
      }
      
      return false;
    } else {
      // Move to trash
      const response = await databaseServer.updateDocument(
        this.COLLECTION,
        commentId,
        {
          type: 'trash',
          $updatedAt: new Date().toISOString()
        }
      );
      
      if (response.success) {
        // Update comment count
        await this.updateCommentCount(comment.contentId);
        return true;
      }
      
      return false;
    }
  }
  
  // Approve a comment
  async approveComment(commentId: string): Promise<Comment | null> {
    return this.updateComment(commentId, { approved: true });
  }
  
  // Unapprove a comment
  async unapproveComment(commentId: string): Promise<Comment | null> {
    return this.updateComment(commentId, { approved: false });
  }
  
  // Mark a comment as spam
  async markAsSpam(commentId: string): Promise<Comment | null> {
    return this.updateComment(commentId, { type: 'spam' });
  }
  
  // Mark a comment as not spam
  async unmarkAsSpam(commentId: string): Promise<Comment | null> {
    return this.updateComment(commentId, { type: 'comment' });
  }
  
  // Update comment karma
  async updateCommentKarma(commentId: string, delta: number): Promise<number | null> {
    const comment = await this.getComment(commentId);
    if (!comment) {
      throw new Error('Comment not found');
    }
    
    const newKarma = comment.karma + delta;
    
    await databaseServer.updateDocument(
      this.COLLECTION,
      commentId,
      {
        karma: newKarma,
        $updatedAt: new Date().toISOString()
      }
    );
    
    return newKarma;
  }
  
  // Get comment count for a post
  async getCommentCount(contentId: string, args: {
    type?: string;
    user_id?: string;
    status?: string;
    post_author?: string;
    post_type?: string;
    post_status?: string;
    post_author__in?: string[];
    post_author__not_in?: string[];
  } = {}): Promise<number> {
    const { status = 'approve' } = args;
    
    const queries = [`equal("contentId", "${contentId}")`];
    
    // Status filter
    if (status === 'approve') {
      queries.push('equal("approved", true)');
    } else if (status === 'hold') {
      queries.push('equal("approved", false)');
    } else if (status === 'spam') {
      queries.push('equal("type", "spam")');
    } else if (status === 'trash') {
      queries.push('equal("type", "trash")');
    }
    
    // Type filter
    if (args.type) {
      queries.push(`equal("type", "${args.type}")`);
    }
    
    // User filter
    if (args.user_id) {
      queries.push(`equal("userId", "${args.user_id}")`);
    }
    
    const response = await databaseServer.listDocuments(
      this.COLLECTION,
      { 
        queries: [
          ...queries,
          'select(["$id"])',
          'limit(1)'
        ]
      }
    );
    
    return response.success ? response.data?.total || 0 : 0;
  }
  
  // Get comment counts by status for a post
  async getCommentCounts(contentId: string): Promise<CommentCount> {
    const [
      approved,
      awaitingModeration,
      spam,
      trash,
      postTrashed
    ] = await Promise.all([
      this.getCommentCount(contentId, { status: 'approve' }),
      this.getCommentCount(contentId, { status: 'hold' }),
      this.getCommentCount(contentId, { status: 'spam' }),
      this.getCommentCount(contentId, { status: 'trash' }),
      0 // postTrashed would require checking if the post is trashed
    ]);
    
    return {
      approved,
      awaitingModeration,
      spam,
      trash,
      postTrashed,
      total: approved + awaitingModeration + spam + trash
    };
  }
  
  // Get comments for a specific user
  async getUserComments(userId: string, args: {
    status?: string;
    type?: string;
    number?: number;
    offset?: number;
  } = {}): Promise<{ comments: Comment[]; total: number }> {
    return this.getComments({
      userId,
      status: args.status,
      type: args.type,
      number: args.number,
      offset: args.offset
    });
  }
  
  // Check for duplicate comments (spam prevention)
  private async checkForDuplicateComment(data: {
    content: string;
    contentId: string;
    userId?: string;
    authorEmail?: string;
    authorIp?: string;
  }): Promise<boolean> {
    const { content, contentId, userId, authorEmail, authorIp } = data;
    
    // Simple check for duplicate content
    const queries = [
      `equal("contentId", "${contentId}")`,
      `search("${content.substring(0, 50)}", ["content"])`,
      'limit(1)'
    ];
    
    if (userId) {
      queries.push(`equal("userId", "${userId}")`);
    } else if (authorEmail) {
      queries.push(`equal("authorEmail", "${authorEmail}")`);
    } else if (authorIp) {
      queries.push(`equal("authorIp", "${authorIp}")`);
    }
    
    const response = await databaseServer.listDocuments(
      this.COLLECTION,
      { queries }
    );
    
    return response.success && (response.data?.total || 0) > 0;
  }
  
  // Sanitize comment content
  private sanitizeComment(content: string): string {
    // Basic XSS protection - in a real app, you'd want to use a proper sanitizer
    return content
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }
  
  // Get comment depth (for threaded comments)
  private async getCommentDepth(commentId: string, depth: number = 0): Promise<number> {
    if (depth >= 10) return depth; // Prevent infinite loops
    
    const comment = await this.getComment(commentId);
    if (!comment || !comment.parentId) return depth;
    
    return this.getCommentDepth(comment.parentId, depth + 1);
  }
  
  // Update comment count for a post
  private async updateCommentCount(contentId: string): Promise<void> {
    // In a real app, you might want to update a comment_count field on the post
    // or implement a caching mechanism for comment counts
    // This is a placeholder for that logic
    const count = await this.getCommentCount(contentId);
    
    // Here you would update the content's comment count
    // For example:
    // await contentService.updatePost(contentId, { commentCount: count });
  }
  
  // Enrich comment with additional data (user, content title, etc.)
  private async enrichComment(comment: Comment): Promise<Comment> {
    const enriched: Comment = { ...comment };
    
    // Add user data if available
    if (comment.userId) {
      try {
        const user = await userService.getUserById(comment.userId);
        if (user) {
          enriched.user = user;
          enriched.authorName = user.name;
          enriched.authorEmail = user.email;
        }
      } catch (error) {
        console.error('Error fetching user for comment:', error);
      }
    }
    
    // Add content title if not already present
    if (!enriched.contentTitle) {
      try {
        const content = await contentService.getPost(comment.contentId);
        if (content) {
          enriched.contentTitle = content.title;
        }
      } catch (error) {
        console.error('Error fetching content for comment:', error);
      }
    }
    
    return enriched;
  }
}

export const commentService = CommentService.getInstance();
