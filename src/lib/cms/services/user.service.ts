import { databaseServer } from '@/lib/appwrite/server/database';
import { DatabaseDocument, DatabaseResponse } from '@/lib/appwrite/types/database';

export interface UserProfile extends DatabaseDocument {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  name: string;
  email: string;
  avatar?: string;
  bio?: string;
  website?: string;
  location?: string;
  socials?: {
    twitter?: string;
    facebook?: string;
    instagram?: string;
    linkedin?: string;
    github?: string;
  };
  preferences?: {
    language?: string;
    timezone?: string;
    theme?: 'light' | 'dark' | 'system';
    notifications?: {
      email?: boolean;
      push?: boolean;
    };
  };
  roles: string[];
  status: 'active' | 'inactive' | 'suspended' | 'pending';
  lastLoginAt?: string;
  metadata?: Record<string, any>;
}

export class UserService {
  private static instance: UserService;
  private readonly USERS_COLLECTION = 'users';
  private readonly USER_PROFILES_COLLECTION = 'user_profiles';

  private constructor() {}

  public static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
    }
    return UserService.instance;
  }

  // User Profile Methods
  async createUserProfile(
    userId: string,
    data: Omit<UserProfile, keyof DatabaseDocument | 'status' | 'roles' | 'lastLoginAt' | '$id' | '$collectionId' | '$databaseId' | '$createdAt' | '$updatedAt' | '$permissions'>
  ): Promise<DatabaseResponse<UserProfile>> {
    const now = new Date().toISOString();
    const profileData: Omit<UserProfile, keyof DatabaseDocument> & { 
      $id: string;
      status: 'active';
      roles: string[];
      lastLoginAt: string;
      $createdAt: string;
      $updatedAt: string;
    } = {
      ...data,
      $id: userId,
      status: 'active',
      roles: ['user'],
      lastLoginAt: now,
      $createdAt: now,
      $updatedAt: now
    };

    return databaseServer.createDocument<UserProfile>(this.USER_PROFILES_COLLECTION, profileData);
  }

  async getUserProfile(userId: string): Promise<DatabaseResponse<UserProfile>> {
    return databaseServer.getDocument<UserProfile>(this.USER_PROFILES_COLLECTION, userId);
  }

  async updateUserProfile(
    userId: string,
    data: Partial<Omit<UserProfile, keyof DatabaseDocument | '$id' | '$collectionId' | '$databaseId' | '$createdAt' | '$updatedAt' | '$permissions'>>
  ): Promise<DatabaseResponse<UserProfile>> {
    const updateData: Partial<Omit<UserProfile, keyof DatabaseDocument>> & { $updatedAt: string } = {
      ...data,
      $updatedAt: new Date().toISOString()
    };
    
    return databaseServer.updateDocument<UserProfile>(
      this.USER_PROFILES_COLLECTION,
      userId,
      updateData
    );
  }

  async deleteUserProfile(userId: string): Promise<DatabaseResponse<boolean>> {
    return databaseServer.deleteDocument(this.USER_PROFILES_COLLECTION, userId);
  }

  async listUserProfiles(
    options: {
      search?: string;
      status?: string[];
      roles?: string[];
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<DatabaseResponse<{ total: number; users: UserProfile[] }>> {
    const { search, status, roles, limit = 20, offset = 0 } = options;
    const queries: string[] = [];

    if (search) {
      queries.push(`search("${search}", ["name", "email", "bio"])`);
    }

    if (status?.length) {
      queries.push(`equal("status", [${status.map(s => `"${s}"`).join(',')}])`);
    }

    if (roles?.length) {
      // This requires a custom attribute in Appwrite that stores roles as an array
      queries.push(`contains("roles", [${roles.map(r => `"${r}"`).join(',')}])`);
    }

    queries.push(`limit(${limit})`);
    queries.push(`offset(${offset})`);
    queries.push('orderBy("$createdAt", "DESC")');

    const response = await databaseServer.listDocuments<UserProfile>(this.USER_PROFILES_COLLECTION, {
      queries
    });

    if (!response.success) {
      return response;
    }

    return {
      success: true,
      data: {
        total: response.data?.total || 0,
        users: response.data?.documents || []
      }
    };
  }

  async updateLastLogin(userId: string): Promise<DatabaseResponse<UserProfile>> {
    try {
      return await databaseServer.updateDocument<UserProfile>(
        this.USER_PROFILES_COLLECTION,
        userId,
        {
          lastLoginAt: new Date().toISOString(),
          $updatedAt: new Date().toISOString()
        }
      );
    } catch (error) {
      console.error('Failed to update last login:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update last login',
        code: 500
      };
    }
  }

  async updateUserRoles(userId: string, roles: string[]): Promise<DatabaseResponse<UserProfile>> {
    return this.updateUserProfile(userId, { roles });
  }

  async updateUserStatus(
    userId: string,
    status: 'active' | 'inactive' | 'suspended' | 'pending'
  ): Promise<DatabaseResponse<UserProfile>> {
    return this.updateUserProfile(userId, { status });
  }

  async getUserByEmail(email: string): Promise<DatabaseResponse<UserProfile>> {
    const response = await databaseServer.listDocuments<UserProfile>(this.USER_PROFILES_COLLECTION, {
      queries: [`equal("email", "${email}")`],
      limit: 1
    });

    if (!response.success || !response.data?.documents.length) {
      return {
        success: false,
        error: 'User not found',
        code: 404
      };
    }

    return {
      success: true,
      data: response.data.documents[0]
    };
  }

  async searchUsers(
    query: string,
    fields: string[] = ['name', 'email', 'bio', 'location'],
    limit: number = 10
  ): Promise<DatabaseResponse<UserProfile[]>> {
    const searchQueries = fields.map(field => `search("${query}", ["${field}"])`);
    
    const response = await databaseServer.listDocuments<UserProfile>(this.USER_PROFILES_COLLECTION, {
      queries: [
        `or(${searchQueries.join(', ')})`,
        `limit(${limit})`,
        'orderBy("$createdAt", "DESC")'
      ]
    });

    if (!response.success) {
      return response;
    }

    return {
      success: true,
      data: response.data?.documents || []
    };
  }
}

export const userService = UserService.getInstance();
