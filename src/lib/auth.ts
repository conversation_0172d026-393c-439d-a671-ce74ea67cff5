import { auth } from "../../auth"
import { redirect } from "next/navigation"

export const getCurrentUser = async () => {
  const session = await auth()
  return session?.user
}

export const requireAuth = async () => {
  const session = await auth()
  if (!session?.user) {
    redirect("/auth/signin")
  }
  return session.user
}

export const requireAdmin = async () => {
  const session = await auth()
  if (!session?.user) {
    redirect("/auth/signin")
  }
  if (session.user.role !== "ADMIN") {
    redirect("/")
  }
  return session.user
}

export const isAdmin = async () => {
  const session = await auth()
  return session?.user?.role === "ADMIN"
}

export const isAuthenticated = async () => {
  const session = await auth()
  return !!session?.user
}