import { z } from 'zod'

// Enums
export const PageStatusSchema = z.enum(['DRAFT', 'PUBLISHED', 'ARCHIVED'])

// Block schema
export const BlockSchema = z.object({
  id: z.string(),
  type: z.string(),
  content: z.record(z.any()).optional(),
  styles: z.record(z.any()).optional(),
})

// SEO schema
export const SEOSchema = z.object({
  title: z.string().max(60, 'SEO title should be 60 characters or less').optional(),
  description: z.string().max(160, 'SEO description should be 160 characters or less').optional(),
  keywords: z.array(z.string()).optional(),
  noindex: z.boolean().optional(),
  nofollow: z.boolean().optional(),
})

// Page settings schema
export const PageSettingsSchema = z.object({
  allowComments: z.boolean().optional(),
  showAuthor: z.boolean().optional(),
  showDate: z.boolean().optional(),
  showShare: z.boolean().optional(),
  showRelated: z.boolean().optional(),
  showTOC: z.boolean().optional(),
  showBreadcrumbs: z.boolean().optional(),
  featuredInSidebar: z.boolean().optional(),
  featuredOnHomepage: z.boolean().optional(),
})

// Page schemas
export const CreatePageSchema = z.object({
  title: z.string().min(2, 'Title must be at least 2 characters').max(200, 'Title must be less than 200 characters'),
  slug: z.string()
    .min(2, 'Slug must be at least 2 characters')
    .max(200, 'Slug must be less than 200 characters')
    .regex(/^[a-z0-9-\/]+$/, 'Slug can only contain lowercase letters, numbers, hyphens, and forward slashes'),
  excerpt: z.string().max(500, 'Excerpt is too long').optional(),
  content: z.string().optional(),
  template: z.string().default('default'),
  status: PageStatusSchema.default('DRAFT'),
  publishedAt: z.date().optional(),
  featuredImage: z.string().optional(),
  authorId: z.string().optional(),
  categoryId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  blocks: z.array(BlockSchema).optional(),
  seo: SEOSchema.optional(),
  settings: PageSettingsSchema.optional(),
})

export const UpdatePageSchema = CreatePageSchema.extend({
  id: z.string(),
}).partial().required({ id: true })

export const PageFilterSchema = z.object({
  status: PageStatusSchema.optional(),
  authorId: z.string().optional(),
  template: z.string().optional(),
  search: z.string().optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  sortBy: z.enum(['createdAt', 'updatedAt', 'publishedAt', 'title']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
})

export const BulkPageActionSchema = z.object({
  pageIds: z.array(z.string()),
  action: z.enum(['delete', 'publish', 'unpublish', 'archive', 'duplicate']),
  template: z.string().optional(),
})

// Page template schemas
export const PageTemplateSchema = z.object({
  name: z.string().min(2, 'Template name must be at least 2 characters'),
  description: z.string().optional(),
  fields: z.array(z.object({
    name: z.string(),
    type: z.enum(['text', 'textarea', 'rich-text', 'image', 'gallery', 'select', 'checkbox', 'date']),
    label: z.string(),
    required: z.boolean().default(false),
    options: z.array(z.string()).optional(),
    defaultValue: z.any().optional(),
  })).default([]),
})

// Type exports
export type CreatePageInput = z.infer<typeof CreatePageSchema>
export type UpdatePageInput = z.infer<typeof UpdatePageSchema>
export type PageFilterInput = z.infer<typeof PageFilterSchema>
export type BulkPageActionInput = z.infer<typeof BulkPageActionSchema>
export type PageTemplateInput = z.infer<typeof PageTemplateSchema>
export type PageStatus = z.infer<typeof PageStatusSchema>

// Additional type exports for the page builder
export type PageFormData = z.infer<typeof CreatePageSchema> & { id?: string }
export type BlockData = z.infer<typeof BlockSchema>
export type SEOData = z.infer<typeof SEOSchema>
export type PageSettingsData = z.infer<typeof PageSettingsSchema>