import { z } from 'zod'

// Enums
export const PostStatusSchema = z.enum(['DRAFT', 'PUBLISHED', 'ARCHIVED', 'SCHEDULED'])

// Post schemas
export const CreatePostSchema = z.object({
  title: z.string().min(2, 'Title must be at least 2 characters').max(200, 'Title must be less than 200 characters'),
  slug: z.string()
    .min(2, 'Slug must be at least 2 characters')
    .max(200, 'Slug must be less than 200 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  excerpt: z.string().min(10, 'Excerpt must be at least 10 characters').max(500, 'Excerpt must be less than 500 characters').optional(),
  content: z.string().min(50, 'Content must be at least 50 characters').optional(),
  featuredImage: z.string().url('Please enter a valid image URL').optional().or(z.literal('')),
  status: PostStatusSchema.default('DRAFT'),
  publishedAt: z.date().optional(),
  scheduledAt: z.date().optional(),
  seoTitle: z.string().max(60, 'SEO title should be less than 60 characters').optional(),
  seoDescription: z.string().max(160, 'SEO description should be less than 160 characters').optional(),
  authorId: z.string().cuid('Invalid author ID'),
  categoryId: z.string().cuid('Invalid category ID').optional(),
  tagIds: z.array(z.string().cuid()).default([]),
})

export const UpdatePostSchema = CreatePostSchema.extend({
  id: z.string().cuid('Invalid post ID'),
}).partial().required({ id: true })

export const PostFilterSchema = z.object({
  status: PostStatusSchema.optional(),
  authorId: z.string().cuid().optional(),
  categoryId: z.string().cuid().optional(),
  tagIds: z.array(z.string().cuid()).optional(),
  search: z.string().optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  sortBy: z.enum(['createdAt', 'updatedAt', 'publishedAt', 'title']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
})

export const BulkPostActionSchema = z.object({
  postIds: z.array(z.string().cuid()),
  action: z.enum(['delete', 'publish', 'unpublish', 'archive', 'duplicate']),
  categoryId: z.string().cuid().optional(),
})

export const PostSEOSchema = z.object({
  id: z.string().cuid('Invalid post ID'),
  seoTitle: z.string().max(60, 'SEO title should be less than 60 characters').optional(),
  seoDescription: z.string().max(160, 'SEO description should be less than 160 characters').optional(),
  featuredImage: z.string().url('Please enter a valid image URL').optional(),
})

// Type exports
export type CreatePostInput = z.infer<typeof CreatePostSchema>
export type UpdatePostInput = z.infer<typeof UpdatePostSchema>
export type PostFilterInput = z.infer<typeof PostFilterSchema>
export type BulkPostActionInput = z.infer<typeof BulkPostActionSchema>
export type PostSEOInput = z.infer<typeof PostSEOSchema>
export type PostStatus = z.infer<typeof PostStatusSchema>