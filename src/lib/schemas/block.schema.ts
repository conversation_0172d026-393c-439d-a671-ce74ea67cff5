import { z } from 'zod'

// Block type definitions
export const BlockTypeSchema = z.enum([
  'hero',
  'text',
  'image',
  'gallery',
  'video',
  'quote',
  'code',
  'divider',
  'spacer',
  'button',
  'form',
  'map',
  'social',
  'testimonial',
  'faq',
  'pricing',
  'team',
  'contact',
  'newsletter',
  'custom'
])

// Base block data schemas
export const HeroBlockDataSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  subtitle: z.string().optional(),
  backgroundImage: z.string().url().optional(),
  backgroundColor: z.string().optional(),
  textColor: z.string().optional(),
  buttonText: z.string().optional(),
  buttonUrl: z.string().optional(),
  alignment: z.enum(['left', 'center', 'right']).default('center'),
  height: z.enum(['small', 'medium', 'large', 'full']).default('medium'),
})

export const TextBlockDataSchema = z.object({
  content: z.string().min(1, 'Content is required'),
  alignment: z.enum(['left', 'center', 'right', 'justify']).default('left'),
  fontSize: z.enum(['small', 'medium', 'large']).default('medium'),
  color: z.string().optional(),
})

export const ImageBlockDataSchema = z.object({
  src: z.string().url('Please enter a valid image URL'),
  alt: z.string().min(1, 'Alt text is required'),
  caption: z.string().optional(),
  width: z.number().min(1).optional(),
  height: z.number().min(1).optional(),
  alignment: z.enum(['left', 'center', 'right']).default('center'),
  link: z.string().url().optional(),
})

export const GalleryBlockDataSchema = z.object({
  images: z.array(z.object({
    src: z.string().url(),
    alt: z.string(),
    caption: z.string().optional(),
  })).min(1, 'At least one image is required'),
  columns: z.number().min(1).max(6).default(3),
  spacing: z.enum(['small', 'medium', 'large']).default('medium'),
  lightbox: z.boolean().default(true),
})

export const VideoBlockDataSchema = z.object({
  src: z.string().url('Please enter a valid video URL'),
  poster: z.string().url().optional(),
  autoplay: z.boolean().default(false),
  muted: z.boolean().default(false),
  controls: z.boolean().default(true),
  loop: z.boolean().default(false),
  width: z.number().min(1).optional(),
  height: z.number().min(1).optional(),
})

export const QuoteBlockDataSchema = z.object({
  quote: z.string().min(1, 'Quote is required'),
  author: z.string().optional(),
  authorTitle: z.string().optional(),
  authorImage: z.string().url().optional(),
  alignment: z.enum(['left', 'center', 'right']).default('center'),
  style: z.enum(['default', 'bordered', 'highlighted']).default('default'),
})

export const ButtonBlockDataSchema = z.object({
  text: z.string().min(1, 'Button text is required'),
  url: z.string().url('Please enter a valid URL'),
  style: z.enum(['primary', 'secondary', 'outline', 'ghost']).default('primary'),
  size: z.enum(['small', 'medium', 'large']).default('medium'),
  alignment: z.enum(['left', 'center', 'right']).default('center'),
  openInNewTab: z.boolean().default(false),
})

// Generic block data schema
export const BlockDataSchema = z.union([
  HeroBlockDataSchema,
  TextBlockDataSchema,
  ImageBlockDataSchema,
  GalleryBlockDataSchema,
  VideoBlockDataSchema,
  QuoteBlockDataSchema,
  ButtonBlockDataSchema,
  z.record(z.any()), // For custom blocks
])

// Block schemas
const BlockObjectSchema = z.object({
  type: BlockTypeSchema,
  data: BlockDataSchema,
  order: z.number().min(0).default(0),
  postId: z.string().cuid('Invalid post ID').optional(),
  pageId: z.string().cuid('Invalid page ID').optional(),
  createdBy: z.string().cuid('Invalid user ID'),
})

export const CreateBlockSchema = BlockObjectSchema.refine((data) => {
  return data.postId || data.pageId
}, {
  message: 'Block must be associated with either a post or a page',
  path: ['postId'],
})

export const UpdateBlockSchema = BlockObjectSchema.extend({
  id: z.string().cuid('Invalid block ID'),
  updatedBy: z.string().cuid('Invalid user ID'),
}).partial().required({ id: true })

export const BlockFilterSchema = z.object({
  type: BlockTypeSchema.optional(),
  postId: z.string().cuid().optional(),
  pageId: z.string().cuid().optional(),
  createdBy: z.string().cuid().optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  sortBy: z.enum(['order', 'createdAt', 'updatedAt', 'type']).default('order'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
})

export const BulkBlockActionSchema = z.object({
  blockIds: z.array(z.string().cuid()),
  action: z.enum(['delete', 'duplicate', 'reorder']),
  newOrder: z.array(z.object({
    id: z.string().cuid(),
    order: z.number().min(0),
  })).optional(),
})

export const BlockReorderSchema = z.object({
  blocks: z.array(z.object({
    id: z.string().cuid(),
    order: z.number().min(0),
  })).min(1, 'At least one block is required'),
})

// Type exports
export type CreateBlockInput = z.infer<typeof CreateBlockSchema>
export type UpdateBlockInput = z.infer<typeof UpdateBlockSchema>
export type BlockFilterInput = z.infer<typeof BlockFilterSchema>
export type BulkBlockActionInput = z.infer<typeof BulkBlockActionSchema>
export type BlockReorderInput = z.infer<typeof BlockReorderSchema>
export type BlockType = z.infer<typeof BlockTypeSchema>
export type BlockData = z.infer<typeof BlockDataSchema>
export type HeroBlockData = z.infer<typeof HeroBlockDataSchema>
export type TextBlockData = z.infer<typeof TextBlockDataSchema>
export type ImageBlockData = z.infer<typeof ImageBlockDataSchema>
export type GalleryBlockData = z.infer<typeof GalleryBlockDataSchema>
export type VideoBlockData = z.infer<typeof VideoBlockDataSchema>
export type QuoteBlockData = z.infer<typeof QuoteBlockDataSchema>
export type ButtonBlockData = z.infer<typeof ButtonBlockDataSchema>