import { z } from 'zod'

// Comment schemas
export const CreateCommentSchema = z.object({
  content: z.string().min(5, 'Comment must be at least 5 characters').max(2000, 'Comment must be less than 2000 characters'),
  authorId: z.string().cuid('Invalid author ID'),
  postId: z.string().cuid('Invalid post ID'),
  parentId: z.string().cuid('Invalid parent comment ID').optional(),
  approved: z.boolean().default(false),
})

export const UpdateCommentSchema = CreateCommentSchema.extend({
  id: z.string().cuid('Invalid comment ID'),
}).partial().required({ id: true })

export const CommentFilterSchema = z.object({
  postId: z.string().cuid().optional(),
  authorId: z.string().cuid().optional(),
  approved: z.boolean().optional(),
  parentId: z.string().cuid().optional(),
  search: z.string().optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  sortBy: z.enum(['createdAt', 'updatedAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
})

export const BulkCommentActionSchema = z.object({
  commentIds: z.array(z.string().cuid()),
  action: z.enum(['delete', 'approve', 'unapprove', 'spam']),
})

export const CommentModerationSchema = z.object({
  id: z.string().cuid('Invalid comment ID'),
  approved: z.boolean(),
  moderationNote: z.string().max(500, 'Moderation note must be less than 500 characters').optional(),
})

export const CommentReplySchema = z.object({
  content: z.string().min(5, 'Reply must be at least 5 characters').max(2000, 'Reply must be less than 2000 characters'),
  parentId: z.string().cuid('Invalid parent comment ID'),
  postId: z.string().cuid('Invalid post ID'),
  authorId: z.string().cuid('Invalid author ID'),
})

// Type exports
export type CreateCommentInput = z.infer<typeof CreateCommentSchema>
export type UpdateCommentInput = z.infer<typeof UpdateCommentSchema>
export type CommentFilterInput = z.infer<typeof CommentFilterSchema>
export type BulkCommentActionInput = z.infer<typeof BulkCommentActionSchema>
export type CommentModerationInput = z.infer<typeof CommentModerationSchema>
export type CommentReplyInput = z.infer<typeof CommentReplySchema>