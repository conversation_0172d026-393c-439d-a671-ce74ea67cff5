import { z } from 'zod'

// Category schemas
export const CreateCategorySchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be less than 100 characters'),
  slug: z.string()
    .min(2, 'Slug must be at least 2 characters')
    .max(100, 'Slug must be less than 100 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Please enter a valid hex color').optional(),
  image: z.string().url('Please enter a valid image URL').optional().or(z.literal('')),
  parentId: z.string().cuid('Invalid parent category ID').optional(),
  createdBy: z.string().cuid('Invalid user ID'),
})

export const UpdateCategorySchema = CreateCategorySchema.extend({
  id: z.string().cuid('Invalid category ID'),
}).partial().required({ id: true })

export const CategoryFilterSchema = z.object({
  parentId: z.string().cuid().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
})

export const BulkCategoryActionSchema = z.object({
  categoryIds: z.array(z.string().cuid()),
  action: z.enum(['delete', 'move']),
  newParentId: z.string().cuid().optional(),
})

export const CategoryHierarchySchema = z.object({
  categories: z.array(z.object({
    id: z.string().cuid(),
    parentId: z.string().cuid().optional(),
    order: z.number().min(0),
  })),
})

// Type exports
export type CreateCategoryInput = z.infer<typeof CreateCategorySchema>
export type UpdateCategoryInput = z.infer<typeof UpdateCategorySchema>
export type CategoryFilterInput = z.infer<typeof CategoryFilterSchema>
export type BulkCategoryActionInput = z.infer<typeof BulkCategoryActionSchema>
export type CategoryHierarchyInput = z.infer<typeof CategoryHierarchySchema>