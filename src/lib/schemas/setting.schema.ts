import { z } from 'zod'

// Setting type schema
export const SettingTypeSchema = z.enum(['string', 'number', 'boolean', 'json', 'array', 'object'])

// Setting group schema
export const SettingGroupSchema = z.enum([
  'general',
  'seo',
  'social',
  'email',
  'appearance',
  'security',
  'performance',
  'analytics',
  'integrations',
  'custom'
])

// Base setting schema
export const CreateSettingSchema = z.object({
  key: z.string()
    .min(2, 'Key must be at least 2 characters')
    .max(100, 'Key must be less than 100 characters')
    .regex(/^[a-z0-9_]+$/, 'Key can only contain lowercase letters, numbers, and underscores'),
  value: z.any(),
  type: SettingTypeSchema.default('string'),
  group: SettingGroupSchema.default('general'),
})

export const UpdateSettingSchema = CreateSettingSchema.extend({
  id: z.string().cuid('Invalid setting ID'),
}).partial().required({ id: true })

// Specific setting schemas
export const GeneralSettingsSchema = z.object({
  site_name: z.string().min(1, 'Site name is required').max(100),
  site_description: z.string().max(500).optional(),
  site_url: z.string().url('Please enter a valid URL'),
  admin_email: z.string().email('Please enter a valid email address'),
  timezone: z.string().default('UTC'),
  date_format: z.string().default('YYYY-MM-DD'),
  time_format: z.string().default('HH:mm'),
  language: z.string().default('en'),
  maintenance_mode: z.boolean().default(false),
  maintenance_message: z.string().optional(),
})

export const SEOSettingsSchema = z.object({
  meta_title: z.string().max(60, 'Meta title should be less than 60 characters').optional(),
  meta_description: z.string().max(160, 'Meta description should be less than 160 characters').optional(),
  meta_keywords: z.string().optional(),
  og_image: z.string().url().optional(),
  twitter_card: z.enum(['summary', 'summary_large_image', 'app', 'player']).default('summary_large_image'),
  robots_txt: z.string().optional(),
  sitemap_enabled: z.boolean().default(true),
  schema_markup_enabled: z.boolean().default(true),
})

export const SocialSettingsSchema = z.object({
  facebook_url: z.string().url().optional().or(z.literal('')),
  twitter_url: z.string().url().optional().or(z.literal('')),
  instagram_url: z.string().url().optional().or(z.literal('')),
  linkedin_url: z.string().url().optional().or(z.literal('')),
  youtube_url: z.string().url().optional().or(z.literal('')),
  github_url: z.string().url().optional().or(z.literal('')),
  social_sharing_enabled: z.boolean().default(true),
  social_login_enabled: z.boolean().default(false),
})

export const EmailSettingsSchema = z.object({
  smtp_host: z.string().optional(),
  smtp_port: z.number().min(1).max(65535).optional(),
  smtp_username: z.string().optional(),
  smtp_password: z.string().optional(),
  smtp_secure: z.boolean().default(true),
  from_email: z.string().email().optional(),
  from_name: z.string().optional(),
  reply_to_email: z.string().email().optional(),
  email_notifications_enabled: z.boolean().default(true),
})

export const AppearanceSettingsSchema = z.object({
  theme: z.string().default('default'),
  primary_color: z.string().regex(/^#[0-9A-F]{6}$/i).default('#3B82F6'),
  secondary_color: z.string().regex(/^#[0-9A-F]{6}$/i).default('#64748B'),
  accent_color: z.string().regex(/^#[0-9A-F]{6}$/i).default('#10B981'),
  logo_url: z.string().url().optional().or(z.literal('')),
  favicon_url: z.string().url().optional().or(z.literal('')),
  custom_css: z.string().optional(),
  custom_js: z.string().optional(),
  dark_mode_enabled: z.boolean().default(false),
})

export const SecuritySettingsSchema = z.object({
  two_factor_enabled: z.boolean().default(false),
  password_min_length: z.number().min(6).max(128).default(8),
  password_require_uppercase: z.boolean().default(true),
  password_require_lowercase: z.boolean().default(true),
  password_require_numbers: z.boolean().default(true),
  password_require_symbols: z.boolean().default(false),
  login_attempts_limit: z.number().min(1).max(20).default(5),
  login_lockout_duration: z.number().min(1).max(1440).default(15), // minutes
  session_timeout: z.number().min(15).max(10080).default(1440), // minutes
  ip_whitelist: z.array(z.string()).default([]),
  ip_blacklist: z.array(z.string()).default([]),
})

export const PerformanceSettingsSchema = z.object({
  cache_enabled: z.boolean().default(true),
  cache_duration: z.number().min(1).max(86400).default(3600), // seconds
  image_optimization_enabled: z.boolean().default(true),
  image_quality: z.number().min(1).max(100).default(80),
  lazy_loading_enabled: z.boolean().default(true),
  minify_css: z.boolean().default(true),
  minify_js: z.boolean().default(true),
  gzip_compression: z.boolean().default(true),
})

export const AnalyticsSettingsSchema = z.object({
  google_analytics_id: z.string().optional(),
  google_tag_manager_id: z.string().optional(),
  facebook_pixel_id: z.string().optional(),
  hotjar_id: z.string().optional(),
  analytics_enabled: z.boolean().default(false),
  cookie_consent_enabled: z.boolean().default(true),
  track_page_views: z.boolean().default(true),
  track_events: z.boolean().default(true),
})

// Setting filter schema
export const SettingFilterSchema = z.object({
  group: SettingGroupSchema.optional(),
  type: SettingTypeSchema.optional(),
  search: z.string().optional(),
  sortBy: z.enum(['key', 'group', 'createdAt', 'updatedAt']).default('group'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(50),
})

export const BulkSettingActionSchema = z.object({
  settingIds: z.array(z.string().cuid()),
  action: z.enum(['delete', 'export', 'reset']),
})

export const SettingBackupSchema = z.object({
  name: z.string().min(1, 'Backup name is required'),
  description: z.string().optional(),
  settings: z.record(z.any()),
})

export const SettingImportSchema = z.object({
  settings: z.record(z.any()),
  overwrite: z.boolean().default(false),
  backup: z.boolean().default(true),
})

// Type exports
export type CreateSettingInput = z.infer<typeof CreateSettingSchema>
export type UpdateSettingInput = z.infer<typeof UpdateSettingSchema>
export type SettingFilterInput = z.infer<typeof SettingFilterSchema>
export type BulkSettingActionInput = z.infer<typeof BulkSettingActionSchema>
export type SettingBackupInput = z.infer<typeof SettingBackupSchema>
export type SettingImportInput = z.infer<typeof SettingImportSchema>
export type SettingType = z.infer<typeof SettingTypeSchema>
export type SettingGroup = z.infer<typeof SettingGroupSchema>
export type GeneralSettings = z.infer<typeof GeneralSettingsSchema>
export type SEOSettings = z.infer<typeof SEOSettingsSchema>
export type SocialSettings = z.infer<typeof SocialSettingsSchema>
export type EmailSettings = z.infer<typeof EmailSettingsSchema>
export type AppearanceSettings = z.infer<typeof AppearanceSettingsSchema>
export type SecuritySettings = z.infer<typeof SecuritySettingsSchema>
export type PerformanceSettings = z.infer<typeof PerformanceSettingsSchema>
export type AnalyticsSettings = z.infer<typeof AnalyticsSettingsSchema>