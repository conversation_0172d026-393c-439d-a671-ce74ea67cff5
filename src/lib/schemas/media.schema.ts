import { z } from 'zod'

// Enums
export const MediaTypeSchema = z.enum(['IMAGE', 'VIDEO', 'AUDIO', 'DOCUMENT', 'OTHER'])

// Media schemas
export const CreateMediaSchema = z.object({
  filename: z.string().min(1, 'Filename is required'),
  originalName: z.string().min(1, 'Original name is required'),
  mimeType: z.string().min(1, 'MIME type is required'),
  size: z.number().min(1, 'File size must be greater than 0'),
  width: z.number().min(1).optional(),
  height: z.number().min(1).optional(),
  url: z.string().url('Please enter a valid URL'),
  thumbnailUrl: z.string().url('Please enter a valid thumbnail URL').optional(),
  alt: z.string().max(200, 'Alt text must be less than 200 characters').optional(),
  caption: z.string().max(500, 'Caption must be less than 500 characters').optional(),
  type: MediaTypeSchema.default('OTHER'),
  uploadedBy: z.string().cuid('Invalid user ID'),
})

export const UpdateMediaSchema = CreateMediaSchema.extend({
  id: z.string().cuid('Invalid media ID'),
}).partial().required({ id: true })

export const MediaFilterSchema = z.object({
  type: MediaTypeSchema.optional(),
  uploadedBy: z.string().cuid().optional(),
  search: z.string().optional(),
  mimeType: z.string().optional(),
  sizeMin: z.number().min(0).optional(),
  sizeMax: z.number().min(0).optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  sortBy: z.enum(['createdAt', 'updatedAt', 'filename', 'size']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
})

export const BulkMediaActionSchema = z.object({
  mediaIds: z.array(z.string().cuid()),
  action: z.enum(['delete', 'updateType', 'generateThumbnails']),
  newType: MediaTypeSchema.optional(),
})

export const MediaUploadSchema = z.object({
  files: z.array(z.instanceof(File)).min(1, 'Please select at least one file'),
  alt: z.string().max(200, 'Alt text must be less than 200 characters').optional(),
  caption: z.string().max(500, 'Caption must be less than 500 characters').optional(),
  folder: z.string().optional(),
})

export const MediaOptimizationSchema = z.object({
  id: z.string().cuid('Invalid media ID'),
  quality: z.number().min(1).max(100).default(80),
  width: z.number().min(1).optional(),
  height: z.number().min(1).optional(),
  format: z.enum(['webp', 'jpeg', 'png']).optional(),
  generateThumbnail: z.boolean().default(true),
  thumbnailSize: z.number().min(50).max(500).default(150),
})

// Type exports
export type CreateMediaInput = z.infer<typeof CreateMediaSchema>
export type UpdateMediaInput = z.infer<typeof UpdateMediaSchema>
export type MediaFilterInput = z.infer<typeof MediaFilterSchema>
export type BulkMediaActionInput = z.infer<typeof BulkMediaActionSchema>
export type MediaUploadInput = z.infer<typeof MediaUploadSchema>
export type MediaOptimizationInput = z.infer<typeof MediaOptimizationSchema>
export type MediaType = z.infer<typeof MediaTypeSchema>