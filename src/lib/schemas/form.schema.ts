import { z } from 'zod'

// Form field types
export const FormFieldTypeSchema = z.enum([
  'text',
  'email',
  'tel',
  'number',
  'textarea',
  'select',
  'radio',
  'checkbox',
  'file',
  'date',
  'time',
  'datetime',
  'url',
  'hidden'
])

// Form field validation schema
export const FormFieldValidationSchema = z.object({
  required: z.boolean().default(false),
  minLength: z.number().min(0).optional(),
  maxLength: z.number().min(0).optional(),
  min: z.number().optional(),
  max: z.number().optional(),
  pattern: z.string().optional(),
  customMessage: z.string().optional(),
})

// Form field schema
export const FormFieldSchema = z.object({
  id: z.string().min(1, 'Field ID is required'),
  type: FormFieldTypeSchema,
  label: z.string().min(1, 'Label is required'),
  placeholder: z.string().optional(),
  helpText: z.string().optional(),
  defaultValue: z.any().optional(),
  options: z.array(z.object({
    label: z.string(),
    value: z.string(),
  })).optional(),
  validation: FormFieldValidationSchema.optional(),
  order: z.number().min(0).default(0),
  width: z.enum(['full', 'half', 'third', 'quarter']).default('full'),
  conditional: z.object({
    fieldId: z.string(),
    operator: z.enum(['equals', 'not_equals', 'contains', 'not_contains']),
    value: z.any(),
  }).optional(),
})

// Form settings schema
export const FormSettingsSchema = z.object({
  submitButtonText: z.string().default('Submit'),
  successMessage: z.string().default('Thank you for your submission!'),
  errorMessage: z.string().default('There was an error submitting the form. Please try again.'),
  redirectUrl: z.string().url().optional(),
  emailNotifications: z.object({
    enabled: z.boolean().default(false),
    recipients: z.array(z.string().email()).default([]),
    subject: z.string().optional(),
    template: z.string().optional(),
  }).optional(),
  autoResponder: z.object({
    enabled: z.boolean().default(false),
    subject: z.string().optional(),
    message: z.string().optional(),
    fromName: z.string().optional(),
    fromEmail: z.string().email().optional(),
  }).optional(),
  captcha: z.object({
    enabled: z.boolean().default(false),
    type: z.enum(['recaptcha', 'hcaptcha']).default('recaptcha'),
    siteKey: z.string().optional(),
  }).optional(),
  allowMultipleSubmissions: z.boolean().default(true),
  saveSubmissions: z.boolean().default(true),
  requireAuth: z.boolean().default(false),
})

// Form schemas
export const CreateFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be less than 100 characters'),
  title: z.string().min(2, 'Title must be at least 2 characters').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  fields: z.array(FormFieldSchema).min(1, 'At least one field is required'),
  settings: FormSettingsSchema.optional(),
})

export const UpdateFormSchema = CreateFormSchema.extend({
  id: z.string().cuid('Invalid form ID'),
}).partial().required({ id: true })

export const FormFilterSchema = z.object({
  search: z.string().optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  sortBy: z.enum(['name', 'title', 'createdAt', 'updatedAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
})

export const BulkFormActionSchema = z.object({
  formIds: z.array(z.string().cuid()),
  action: z.enum(['delete', 'duplicate', 'export']),
})

// Form submission schemas
export const FormSubmissionSchema = z.object({
  formId: z.string().cuid('Invalid form ID'),
  data: z.record(z.any()),
  ip: z.string().optional(),
  userAgent: z.string().optional(),
})

export const FormSubmissionFilterSchema = z.object({
  formId: z.string().cuid().optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  ip: z.string().optional(),
  sortBy: z.enum(['createdAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
})

export const BulkSubmissionActionSchema = z.object({
  submissionIds: z.array(z.string().cuid()),
  action: z.enum(['delete', 'export']),
})

// Form builder schemas
export const FormBuilderStateSchema = z.object({
  fields: z.array(FormFieldSchema),
  settings: FormSettingsSchema,
  preview: z.boolean().default(false),
})

export const FormFieldTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  description: z.string().optional(),
  field: FormFieldSchema,
  category: z.string().default('custom'),
})

// Type exports
export type CreateFormInput = z.infer<typeof CreateFormSchema>
export type UpdateFormInput = z.infer<typeof UpdateFormSchema>
export type FormFilterInput = z.infer<typeof FormFilterSchema>
export type BulkFormActionInput = z.infer<typeof BulkFormActionSchema>
export type FormSubmissionInput = z.infer<typeof FormSubmissionSchema>
export type FormSubmissionFilterInput = z.infer<typeof FormSubmissionFilterSchema>
export type BulkSubmissionActionInput = z.infer<typeof BulkSubmissionActionInput>
export type FormBuilderStateInput = z.infer<typeof FormBuilderStateSchema>
export type FormFieldTemplateInput = z.infer<typeof FormFieldTemplateSchema>
export type FormFieldType = z.infer<typeof FormFieldTypeSchema>
export type FormField = z.infer<typeof FormFieldSchema>
export type FormSettings = z.infer<typeof FormSettingsSchema>
export type FormFieldValidation = z.infer<typeof FormFieldValidationSchema>