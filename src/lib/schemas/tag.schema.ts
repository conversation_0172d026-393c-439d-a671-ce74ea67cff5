import { z } from 'zod'

// Tag schemas
export const CreateTagSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(50, 'Name must be less than 50 characters'),
  slug: z.string()
    .min(2, 'Slug must be at least 2 characters')
    .max(50, 'Slug must be less than 50 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Please enter a valid hex color').optional(),
  createdBy: z.string().cuid('Invalid user ID'),
})

export const UpdateTagSchema = CreateTagSchema.extend({
  id: z.string().cuid('Invalid tag ID'),
}).partial().required({ id: true })

export const TagFilterSchema = z.object({
  search: z.string().optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
})

export const BulkTagActionSchema = z.object({
  tagIds: z.array(z.string().cuid()),
  action: z.enum(['delete', 'merge']),
  targetTagId: z.string().cuid().optional(),
})

export const TagMergeSchema = z.object({
  sourceTagIds: z.array(z.string().cuid()).min(1, 'Select at least one tag to merge'),
  targetTagId: z.string().cuid('Invalid target tag ID'),
  deleteSource: z.boolean().default(true),
})

// Type exports
export type CreateTagInput = z.infer<typeof CreateTagSchema>
export type UpdateTagInput = z.infer<typeof UpdateTagSchema>
export type TagFilterInput = z.infer<typeof TagFilterSchema>
export type BulkTagActionInput = z.infer<typeof BulkTagActionSchema>
export type TagMergeInput = z.infer<typeof TagMergeSchema>