import { z } from 'zod'

// Menu schemas
export const CreateMenuSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be less than 100 characters'),
  location: z.string().max(50, 'Location must be less than 50 characters').optional(),
})

export const UpdateMenuSchema = CreateMenuSchema.extend({
  id: z.string().cuid('Invalid menu ID'),
}).partial().required({ id: true })

// Menu item schemas
export const CreateMenuItemSchema = z.object({
  label: z.string().min(1, 'Label is required').max(100, 'Label must be less than 100 characters'),
  url: z.string().optional(),
  target: z.enum(['_self', '_blank', '_parent', '_top']).default('_self'),
  order: z.number().min(0).default(0),
  parentId: z.string().cuid('Invalid parent menu item ID').optional(),
  menuId: z.string().cuid('Invalid menu ID'),
})

export const UpdateMenuItemSchema = CreateMenuItemSchema.extend({
  id: z.string().cuid('Invalid menu item ID'),
}).partial().required({ id: true })

export const MenuFilterSchema = z.object({
  location: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
})

export const MenuItemFilterSchema = z.object({
  menuId: z.string().cuid().optional(),
  parentId: z.string().cuid().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['order', 'label']).default('order'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
})

export const BulkMenuActionSchema = z.object({
  menuIds: z.array(z.string().cuid()),
  action: z.enum(['delete', 'duplicate']),
})

export const BulkMenuItemActionSchema = z.object({
  menuItemIds: z.array(z.string().cuid()),
  action: z.enum(['delete', 'move', 'reorder']),
  targetMenuId: z.string().cuid().optional(),
  newParentId: z.string().cuid().optional(),
})

export const MenuStructureSchema = z.object({
  menuId: z.string().cuid('Invalid menu ID'),
  items: z.array(z.object({
    id: z.string().cuid(),
    parentId: z.string().cuid().optional(),
    order: z.number().min(0),
  })),
})

export const MenuItemReorderSchema = z.object({
  menuItems: z.array(z.object({
    id: z.string().cuid(),
    order: z.number().min(0),
    parentId: z.string().cuid().optional(),
  })).min(1, 'At least one menu item is required'),
})

// Navigation link types
export const NavigationLinkSchema = z.object({
  type: z.enum(['page', 'post', 'category', 'tag', 'custom', 'external']),
  value: z.string(), // ID for internal links, URL for external
  label: z.string().optional(), // Override label
})

export const MenuItemWithLinkSchema = CreateMenuItemSchema.extend({
  link: NavigationLinkSchema.optional(),
})

// Type exports
export type CreateMenuInput = z.infer<typeof CreateMenuSchema>
export type UpdateMenuInput = z.infer<typeof UpdateMenuSchema>
export type CreateMenuItemInput = z.infer<typeof CreateMenuItemSchema>
export type UpdateMenuItemInput = z.infer<typeof UpdateMenuItemSchema>
export type MenuFilterInput = z.infer<typeof MenuFilterSchema>
export type MenuItemFilterInput = z.infer<typeof MenuItemFilterSchema>
export type BulkMenuActionInput = z.infer<typeof BulkMenuActionSchema>
export type BulkMenuItemActionInput = z.infer<typeof BulkMenuItemActionSchema>
export type MenuStructureInput = z.infer<typeof MenuStructureSchema>
export type MenuItemReorderInput = z.infer<typeof MenuItemReorderSchema>
export type NavigationLinkInput = z.infer<typeof NavigationLinkSchema>
export type MenuItemWithLinkInput = z.infer<typeof MenuItemWithLinkSchema>