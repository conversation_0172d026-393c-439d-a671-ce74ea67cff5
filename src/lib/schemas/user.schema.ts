import { z } from 'zod'

// Enums
export const UserRoleSchema = z.enum(['ADMIN', 'EDITOR', 'AUTHOR', 'USER'])

// User schemas
export const CreateUserSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters').optional(),
  role: UserRoleSchema.default('USER'),
  image: z.string().url('Please enter a valid image URL').optional().or(z.literal('')),
})

export const UpdateUserSchema = CreateUserSchema.extend({
  id: z.string().cuid('Invalid user ID'),
  password: z.string().min(8, 'Password must be at least 8 characters').optional(),
  emailVerified: z.date().optional(),
}).partial().required({ id: true })

export const UserProfileSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Please enter a valid email address'),
  image: z.string().url('Please enter a valid image URL').optional().or(z.literal('')),
  currentPassword: z.string().min(1, 'Current password is required').optional(),
  newPassword: z.string().min(8, 'Password must be at least 8 characters').optional(),
  confirmPassword: z.string().optional(),
}).refine((data) => {
  if (data.newPassword && !data.currentPassword) {
    return false
  }
  if (data.newPassword && data.newPassword !== data.confirmPassword) {
    return false
  }
  return true
}, {
  message: 'Passwords do not match or current password is required',
  path: ['confirmPassword'],
})

export const BulkUserActionSchema = z.object({
  userIds: z.array(z.string().cuid()),
  action: z.enum(['delete', 'activate', 'deactivate', 'changeRole']),
  newRole: UserRoleSchema.optional(),
})

// Type exports
export type CreateUserInput = z.infer<typeof CreateUserSchema>
export type UpdateUserInput = z.infer<typeof UpdateUserSchema>
export type UserProfileInput = z.infer<typeof UserProfileSchema>
export type BulkUserActionInput = z.infer<typeof BulkUserActionSchema>
export type UserRole = z.infer<typeof UserRoleSchema>