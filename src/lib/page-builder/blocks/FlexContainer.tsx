import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { FlexContainerBlock, Block, BlockTypes } from '../../../types/block';
import CanvasBlockItem from '../CanvasBlockItem';

interface FlexContainerProps {
  block: FlexContainerBlock;
  availableBlocks: Block[];
  selectedBlockId: string | null;
  isEditing: boolean;
  onSelectBlock: (blockId: string) => void;
  onRemoveBlock: (blockId: string, parentId?: string) => void;
  onUpdateBlock: (blockId: string, updatedBlock: BlockTypes) => void;
}

// Type guard to check if a block is a valid BlockTypes
function isBlockType(block: Block): block is BlockTypes {
    const validTypes = ['about', 'service', 'team', 'testimonial', 'flex-container'];
    return validTypes.includes(block.type);
}

export default function FlexContainer({
  block,
  availableBlocks,
  selectedBlockId,
  isEditing,
  onSelectBlock,
  onRemoveBlock,
  onUpdateBlock,
}: FlexContainerProps) {
  const { setNodeRef, isOver } = useDroppable({
    id: block.id,
    data: {
      accepts: ['BLOCK', 'CANVAS_BLOCK'],
      parentId: block.id,
      isContainer: true,
    },
  });

  const renderBlockItem = (blockId: string, index: number) => {
    const childBlock = availableBlocks.find(b => b.id === blockId);

    if (!childBlock) {
      console.warn(`Block with id ${blockId} not found in available blocks`);
      return null;
    }
    
    if (!isBlockType(childBlock)) {
        console.warn(`Block with id ${blockId} has invalid type: ${childBlock.type}`);
        return null;
    }

    return (
      <CanvasBlockItem
        key={`${blockId}-${index}`}
        blockId={blockId}
        block={childBlock}
        index={index}
        isSelected={blockId === selectedBlockId}
        isEditing={isEditing}
        onSelect={() => onSelectBlock(blockId)}
        onRemove={() => onRemoveBlock(blockId, block.id)}
        onUpdate={(updatedBlock) => onUpdateBlock(blockId, updatedBlock)}
        parentId={block.id}
      />
    );
  };

  const containerStyles: React.CSSProperties = {
    display: 'flex',
    flexDirection: block.content.styles.flexDirection || 'column',
    justifyContent: block.content.styles.justifyContent || 'flex-start',
    alignItems: block.content.styles.alignItems || 'stretch',
    gap: block.content.styles.gap || '16px',
    padding: isEditing ? '20px' : (block.content.styles.padding || '0'),
    backgroundColor: isOver ? '#eff6ff' : (block.content.styles.backgroundColor || 'transparent'),
    borderRadius: block.content.styles.borderRadius || '8px',
    border: isEditing ? `2px dashed ${isOver ? '#3b82f6' : '#cbd5e1'}` : 'none',
    minHeight: isEditing ? '100px' : 'auto',
  };

  return (
    <div ref={setNodeRef} style={containerStyles} className="transition-colors">
      <SortableContext items={block.content.children} strategy={verticalListSortingStrategy}>
        <div className="w-full">
            {block.content.children.length > 0 ? (
                block.content.children.map(renderBlockItem)
            ) : (
                isEditing && (
                    <div className="text-center text-gray-400 p-4">
                        Drop blocks here
                    </div>
                )
            )}
        </div>
      </SortableContext>
    </div>
  );
}
