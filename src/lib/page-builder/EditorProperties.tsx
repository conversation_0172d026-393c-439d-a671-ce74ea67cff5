import React, { useState, useEffect } from 'react';
import { BlockTypes, AboutBlock, ServiceBlock, TeamBlock, TestimonialBlock } from '../../types/block';

interface EditorPropertiesProps {
  selectedBlock: BlockTypes | null;
  onUpdateBlock: (blockId: string, updatedBlock: BlockTypes) => void;
}

export default function EditorProperties({ selectedBlock, onUpdateBlock }: EditorPropertiesProps) {
  const [editedBlock, setEditedBlock] = useState<BlockTypes | null>(null);

  // Update local state when selected block changes
  useEffect(() => {
    setEditedBlock(selectedBlock ? { ...selectedBlock } : null);
  }, [selectedBlock]);

  if (!editedBlock) {
    return (
      <div className="w-80 bg-gray-50 border-l border-gray-200 p-4 overflow-y-auto">
        <div className="text-center py-8 text-gray-500">
          <p>Select a block to edit its properties</p>
        </div>
      </div>
    );
  }

  // Handle saving changes
  const handleSave = () => {
    if (editedBlock) {
      onUpdateBlock(editedBlock.id, editedBlock);
    }
  };

  // Update block title
  const updateBlockTitle = (title: string) => {
    setEditedBlock(prev => prev ? { ...prev, title } : null);
  };

  // Update block description
  const updateBlockDescription = (description: string) => {
    setEditedBlock(prev => prev ? { ...prev, description } : null);
  };

  // Render different property editors based on block type
  const renderBlockProperties = () => {
    switch (editedBlock.type) {
      case 'about':
        return renderAboutBlockProperties(editedBlock as AboutBlock);
      case 'service':
        return renderServiceBlockProperties(editedBlock as ServiceBlock);
      case 'team':
        return renderTeamBlockProperties(editedBlock as TeamBlock);
      case 'testimonial':
        return renderTestimonialBlockProperties(editedBlock as TestimonialBlock);
      default:
        return (
          <div className="text-center py-4 text-gray-500">
            <p>No properties available for this block type</p>
          </div>
        );
    }
  };

  // Render properties for About blocks
  const renderAboutBlockProperties = (block: AboutBlock) => {
    // Update heading content
    const updateHeading = (content: string) => {
      setEditedBlock({
        ...block,
        content: {
          ...block.content,
          heading: {
            ...block.content.heading,
            content
          }
        }
      });
    };

    // Update subheading content
    const updateSubheading = (content: string) => {
      setEditedBlock({
        ...block,
        content: {
          ...block.content,
          subheading: {
            ...block.content.subheading,
            content
          }
        }
      });
    };

    // Update description content
    const updateDescription = (content: string) => {
      setEditedBlock({
        ...block,
        content: {
          ...block.content,
          description: {
            ...block.content.description,
            content
          }
        }
      });
    };

    return (
      <>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Heading
          </label>
          <input
            type="text"
            value={block.content.heading.content}
            onChange={(e) => updateHeading(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Subheading
          </label>
          <input
            type="text"
            value={block.content.subheading.content}
            onChange={(e) => updateSubheading(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            value={block.content.description.content}
            onChange={(e) => updateDescription(e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Counters section */}
        <div className="mb-4">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Counters</h3>
          {block.content.counters.map((counter, index) => (
            <div key={index} className="mb-2 p-2 border border-gray-200 rounded-md">
              <div className="flex items-center mb-2">
                <input
                  type="number"
                  value={counter.value}
                  onChange={(e) => {
                    const counters = [...block.content.counters];
                    counters[index] = {
                      ...counters[index],
                      value: parseInt(e.target.value, 10) || 0
                    };
                    setEditedBlock({
                      ...block,
                      content: {
                        ...block.content,
                        counters
                      }
                    });
                  }}
                  className="w-20 px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 mr-2"
                />
                <input
                  type="text"
                  value={counter.suffix || ''}
                  onChange={(e) => {
                    const counters = [...block.content.counters];
                    counters[index] = {
                      ...counters[index],
                      suffix: e.target.value
                    };
                    setEditedBlock({
                      ...block,
                      content: {
                        ...block.content,
                        counters
                      }
                    });
                  }}
                  placeholder="Suffix"
                  className="w-16 px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <input
                type="text"
                value={counter.label}
                onChange={(e) => {
                  const counters = [...block.content.counters];
                  counters[index] = {
                    ...counters[index],
                    label: e.target.value
                  };
                  setEditedBlock({
                    ...block,
                    content: {
                      ...block.content,
                      counters
                    }
                  });
                }}
                placeholder="Label"
                className="w-full px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          ))}
        </div>

        {/* Icon boxes section */}
        <div className="mb-4">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Icon Boxes</h3>
          {block.content.iconBoxes.map((iconBox, index) => (
            <div key={index} className="mb-2 p-2 border border-gray-200 rounded-md">
              <div className="mb-2">
                <label className="block text-xs text-gray-500 mb-1">Title</label>
                <input
                  type="text"
                  value={iconBox.title.content}
                  onChange={(e) => {
                    const iconBoxes = [...block.content.iconBoxes];
                    iconBoxes[index] = {
                      ...iconBoxes[index],
                      title: {
                        ...iconBoxes[index].title,
                        content: e.target.value
                      }
                    };
                    setEditedBlock({
                      ...block,
                      content: {
                        ...block.content,
                        iconBoxes
                      }
                    });
                  }}
                  className="w-full px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Description</label>
                <textarea
                  value={iconBox.description.content}
                  onChange={(e) => {
                    const iconBoxes = [...block.content.iconBoxes];
                    iconBoxes[index] = {
                      ...iconBoxes[index],
                      description: {
                        ...iconBoxes[index].description,
                        content: e.target.value
                      }
                    };
                    setEditedBlock({
                      ...block,
                      content: {
                        ...block.content,
                        iconBoxes
                      }
                    });
                  }}
                  rows={2}
                  className="w-full px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          ))}
        </div>
      </>
    );
  };

  // Render properties for Service blocks
  const renderServiceBlockProperties = (block: ServiceBlock) => {
    // Similar implementation to renderAboutBlockProperties but for service blocks
    return (
      <div className="text-center py-4 text-gray-500">
        <p>Service block properties editor</p>
        <p className="text-xs mt-2">Properties editor for this block type is coming soon</p>
      </div>
    );
  };

  // Render properties for Team blocks
  const renderTeamBlockProperties = (block: TeamBlock) => {
    // Similar implementation to renderAboutBlockProperties but for team blocks
    return (
      <div className="text-center py-4 text-gray-500">
        <p>Team block properties editor</p>
        <p className="text-xs mt-2">Properties editor for this block type is coming soon</p>
      </div>
    );
  };

  // Render properties for Testimonial blocks
  const renderTestimonialBlockProperties = (block: TestimonialBlock) => {
    // Similar implementation to renderAboutBlockProperties but for testimonial blocks
    return (
      <div className="text-center py-4 text-gray-500">
        <p>Testimonial block properties editor</p>
        <p className="text-xs mt-2">Properties editor for this block type is coming soon</p>
      </div>
    );
  };

  return (
    <div className="w-80 bg-gray-50 border-l border-gray-200 overflow-y-auto">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-800">Block Properties</h2>
        <p className="text-xs text-gray-500 mt-1">
          {editedBlock.type} • {editedBlock.componentName}
        </p>
      </div>

      <div className="p-4">
        {/* Block metadata */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Block Title
          </label>
          <input
            type="text"
            value={editedBlock.title}
            onChange={(e) => updateBlockTitle(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Block Description
          </label>
          <textarea
            value={editedBlock.description || ''}
            onChange={(e) => updateBlockDescription(e.target.value)}
            rows={2}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <hr className="my-4 border-gray-200" />

        {/* Block-specific properties */}
        {renderBlockProperties()}

        {/* Save button */}
        <div className="mt-6">
          <button
            type="button"
            onClick={handleSave}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Apply Changes
          </button>
        </div>
      </div>
    </div>
  );
}