import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

/**
 * Generates an editable block component from an existing component
 * @param componentPath Path to the original component
 * @param blockType Type of block (about, service, team, testimonial)
 */
async function generateBlockComponent(
  componentPath: string,
  blockType: 'about' | 'service' | 'team' | 'testimonial'
) {
  try {
    // Read the original component
    const componentContent = await readFile(componentPath, 'utf8');
    
    // Extract component name from path
    const componentName = path.basename(componentPath, '.tsx');
    
    // Create the block component directory if it doesn't exist
    const blocksDir = path.join(process.cwd(), 'components', 'blocks');
    const blockTypeDir = path.join(blocksDir, blockType);
    
    try {
      await mkdir(blocksDir, { recursive: true });
      await mkdir(blockTypeDir, { recursive: true });
    } catch (err) {
      // Directory might already exist, which is fine
    }
    
    // Generate the block component content
    const blockComponentContent = generateBlockComponentContent(componentContent, componentName, blockType);
    
    // Write the block component file
    const blockComponentPath = path.join(blockTypeDir, `${componentName}Block.tsx`);
    await writeFile(blockComponentPath, blockComponentContent);
    
    console.log(`✅ Generated block component: ${blockComponentPath}`);
    
    // Generate the block editor component
    const editorComponentContent = generateEditorComponentContent(componentName, blockType);
    const editorComponentPath = path.join(blockTypeDir, `${componentName}Editor.tsx`);
    await writeFile(editorComponentPath, editorComponentContent);
    
    console.log(`✅ Generated editor component: ${editorComponentPath}`);
    
    // Generate the block renderer component
    const rendererComponentContent = generateRendererComponentContent(componentName, blockType);
    const rendererComponentPath = path.join(blockTypeDir, `${componentName}Renderer.tsx`);
    await writeFile(rendererComponentPath, rendererComponentContent);
    
    console.log(`✅ Generated renderer component: ${rendererComponentPath}`);
    
    return {
      blockComponentPath,
      editorComponentPath,
      rendererComponentPath
    };
  } catch (error) {
    console.error('Error generating block component:', error);
    throw error;
  }
}

/**
 * Generates the content for a block component
 * @param originalContent Original component content
 * @param componentName Component name
 * @param blockType Block type
 */
function generateBlockComponentContent(
  originalContent: string,
  componentName: string,
  blockType: string
): string {
  // Extract the component's JSX content
  const jsxMatch = originalContent.match(/return\s*\(\s*(<[\s\S]*>)\s*\)\s*;/m);
  const jsxContent = jsxMatch ? jsxMatch[1] : '';
  
  return `import React, { useState } from 'react';
import Link from 'next/link';
import { ${capitalizeFirstLetter(blockType)}Block, BlockComponentProps } from '../../../types/block';
import ${componentName}Editor from './${componentName}Editor';

interface ${componentName}BlockProps extends BlockComponentProps {
  block: ${capitalizeFirstLetter(blockType)}Block;
}

export default function ${componentName}Block({ block, isEditing = false, onUpdate }: ${componentName}BlockProps) {
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  
  // Handle opening the editor
  const handleEdit = () => {
    setIsEditorOpen(true);
  };
  
  // Handle saving changes from the editor
  const handleSave = (updatedBlock: ${capitalizeFirstLetter(blockType)}Block) => {
    setIsEditorOpen(false);
    if (onUpdate) {
      onUpdate(updatedBlock);
    }
  };
  
  // Handle canceling the editor
  const handleCancel = () => {
    setIsEditorOpen(false);
  };
  
  // If the editor is open, render the editor component
  if (isEditorOpen) {
    return <${componentName}Editor block={block} onSave={handleSave} onCancel={handleCancel} />;
  }
  
  // Destructure content from the block
  const { content } = block;
  
  return (
    <div className="relative">
      {/* Edit button shown only when isEditing is true */}
      {isEditing && (
        <button
          onClick={handleEdit}
          className="absolute top-2 right-2 z-10 bg-blue-500 text-white px-3 py-1 rounded-md shadow-md hover:bg-blue-600 transition-colors"
        >
          Edit
        </button>
      )}
      
      {/* Component content with dynamic data */}
      <div className="about-page-sec1 sp">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-6">
              <div className="about3-images">
                <div className="row">
                  <div className="col-lg-6">
                    <div className="image overlay-anim">
                      <img 
                        src={content.images[0]?.src || "assets/img/about/about3-img1.png"} 
                        alt={content.images[0]?.alt || ""} 
                      />
                    </div>
                    {content.counters[0] && (
                      <div className="conter-box conter-box1">
                        <h3>
                          <span className="counter">{content.counters[0].value}</span>
                          {content.counters[0].suffix}
                        </h3>
                        <p>{content.counters[0].label}</p>
                      </div>
                    )}
                  </div>
                  <div className="col-lg-6">
                    {content.counters[1] && (
                      <div className="conter-box conter-box2">
                        <h3>
                          <span className="counter">{content.counters[1].value}</span>
                          {content.counters[1].suffix}
                        </h3>
                        <p>{content.counters[1].label}</p>
                      </div>
                    )}
                    <div className="image overlay-anim">
                      <img 
                        src={content.images[1]?.src || "assets/img/about/about3-img2.png"} 
                        alt={content.images[1]?.alt || ""} 
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-6">
              <div className="heading1 about3-heading">
                <span className="span">{content.subheading.content}</span>
                <h2>{content.heading.content}</h2>
                <div className="space16" />
                <p>{content.description.content}</p>
                
                {content.iconBoxes.map((iconBox, index) => (
                  <div className="about3-icon-box" key={index}>
                    <div className="">
                      <div className="icon">
                        <img src={iconBox.icon.src} alt={iconBox.icon.alt} />
                      </div>
                    </div>
                    <div className="heading1">
                      <h5>
                        {iconBox.link ? (
                          <Link href={iconBox.link.href}>{iconBox.title.content}</Link>
                        ) : (
                          iconBox.title.content
                        )}
                      </h5>
                      <p>{iconBox.description.content}</p>
                    </div>
                  </div>
                ))}
                
                <div className="space30" />
                {content.cta && (
                  <div className="">
                    <Link className={content.cta.className || "theme-btn1"} href={content.cta.href}>
                      {content.cta.text}
                      <span>
                        <i className="fa-solid fa-arrow-right" />
                      </span>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}`;
}

/**
 * Generates the content for an editor component
 * @param componentName Component name
 * @param blockType Block type
 */
function generateEditorComponentContent(
  componentName: string,
  blockType: string
): string {
  return `import React, { useState } from 'react';
import { ${capitalizeFirstLetter(blockType)}Block, BlockEditorProps } from '../../../types/block';

interface ${componentName}EditorProps extends BlockEditorProps {
  block: ${capitalizeFirstLetter(blockType)}Block;
}

export default function ${componentName}Editor({ block, onSave, onCancel }: ${componentName}EditorProps) {
  // Create a copy of the block to edit
  const [editedBlock, setEditedBlock] = useState<${capitalizeFirstLetter(blockType)}Block>({...block});
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(editedBlock);
  };
  
  // Update heading text
  const updateHeading = (value: string) => {
    setEditedBlock({
      ...editedBlock,
      content: {
        ...editedBlock.content,
        heading: {
          ...editedBlock.content.heading,
          content: value
        }
      }
    });
  };
  
  // Update subheading text
  const updateSubheading = (value: string) => {
    setEditedBlock({
      ...editedBlock,
      content: {
        ...editedBlock.content,
        subheading: {
          ...editedBlock.content.subheading,
          content: value
        }
      }
    });
  };
  
  // Update description text
  const updateDescription = (value: string) => {
    setEditedBlock({
      ...editedBlock,
      content: {
        ...editedBlock.content,
        description: {
          ...editedBlock.content.description,
          content: value
        }
      }
    });
  };
  
  // Update counter value
  const updateCounterValue = (index: number, value: string) => {
    const counters = [...editedBlock.content.counters];
    counters[index] = {
      ...counters[index],
      value: parseInt(value, 10) || 0
    };
    
    setEditedBlock({
      ...editedBlock,
      content: {
        ...editedBlock.content,
        counters
      }
    });
  };
  
  // Update counter label
  const updateCounterLabel = (index: number, value: string) => {
    const counters = [...editedBlock.content.counters];
    counters[index] = {
      ...counters[index],
      label: value
    };
    
    setEditedBlock({
      ...editedBlock,
      content: {
        ...editedBlock.content,
        counters
      }
    });
  };
  
  // Update counter suffix
  const updateCounterSuffix = (index: number, value: string) => {
    const counters = [...editedBlock.content.counters];
    counters[index] = {
      ...counters[index],
      suffix: value
    };
    
    setEditedBlock({
      ...editedBlock,
      content: {
        ...editedBlock.content,
        counters
      }
    });
  };
  
  // Update icon box title
  const updateIconBoxTitle = (index: number, value: string) => {
    const iconBoxes = [...editedBlock.content.iconBoxes];
    iconBoxes[index] = {
      ...iconBoxes[index],
      title: {
        ...iconBoxes[index].title,
        content: value
      }
    };
    
    setEditedBlock({
      ...editedBlock,
      content: {
        ...editedBlock.content,
        iconBoxes
      }
    });
  };
  
  // Update icon box description
  const updateIconBoxDescription = (index: number, value: string) => {
    const iconBoxes = [...editedBlock.content.iconBoxes];
    iconBoxes[index] = {
      ...iconBoxes[index],
      description: {
        ...iconBoxes[index].description,
        content: value
      }
    };
    
    setEditedBlock({
      ...editedBlock,
      content: {
        ...editedBlock.content,
        iconBoxes
      }
    });
  };
  
  // Update CTA text
  const updateCtaText = (value: string) => {
    if (!editedBlock.content.cta) return;
    
    setEditedBlock({
      ...editedBlock,
      content: {
        ...editedBlock.content,
        cta: {
          ...editedBlock.content.cta,
          text: value
        }
      }
    });
  };
  
  // Update CTA link
  const updateCtaLink = (value: string) => {
    if (!editedBlock.content.cta) return;
    
    setEditedBlock({
      ...editedBlock,
      content: {
        ...editedBlock.content,
        cta: {
          ...editedBlock.content.cta,
          href: value
        }
      }
    });
  };
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">Edit ${componentName} Block</h2>
      
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          {/* Heading and Subheading */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Subheading
              </label>
              <input
                type="text"
                value={editedBlock.content.subheading.content}
                onChange={(e) => updateSubheading(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Heading
              </label>
              <input
                type="text"
                value={editedBlock.content.heading.content}
                onChange={(e) => updateHeading(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={editedBlock.content.description.content}
              onChange={(e) => updateDescription(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          {/* Counters */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Counters</h3>
            <div className="space-y-4">
              {editedBlock.content.counters.map((counter, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border border-gray-200 rounded-md">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Value
                    </label>
                    <input
                      type="number"
                      value={counter.value}
                      onChange={(e) => updateCounterValue(index, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Suffix
                    </label>
                    <input
                      type="text"
                      value={counter.suffix || ''}
                      onChange={(e) => updateCounterSuffix(index, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Label
                    </label>
                    <input
                      type="text"
                      value={counter.label}
                      onChange={(e) => updateCounterLabel(index, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Icon Boxes */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Icon Boxes</h3>
            <div className="space-y-4">
              {editedBlock.content.iconBoxes.map((iconBox, index) => (
                <div key={index} className="grid grid-cols-1 gap-4 p-4 border border-gray-200 rounded-md">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Title
                    </label>
                    <input
                      type="text"
                      value={iconBox.title.content}
                      onChange={(e) => updateIconBoxTitle(index, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      value={iconBox.description.content}
                      onChange={(e) => updateIconBoxDescription(index, e.target.value)}
                      rows={2}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* CTA */}
          {editedBlock.content.cta && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Call to Action</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Text
                  </label>
                  <input
                    type="text"
                    value={editedBlock.content.cta.text}
                    onChange={(e) => updateCtaText(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Link
                  </label>
                  <input
                    type="text"
                    value={editedBlock.content.cta.href}
                    onChange={(e) => updateCtaLink(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          )}
          
          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Save Changes
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}`;
}

/**
 * Generates the content for a renderer component
 * @param componentName Component name
 * @param blockType Block type
 */
function generateRendererComponentContent(
  componentName: string,
  blockType: string
): string {
  return `import React, { useEffect, useState } from 'react';
import { ${capitalizeFirstLetter(blockType)}Block, BlockRendererProps } from '../../../types/block';
import { getBlockById } from '../../../utils/appwrite';
import ${componentName}Block from './${componentName}Block';

export default function ${componentName}Renderer({ blockId, fallback }: BlockRendererProps) {
  const [block, setBlock] = useState<${capitalizeFirstLetter(blockType)}Block | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchBlock = async () => {
      try {
        setLoading(true);
        const fetchedBlock = await getBlockById(blockId);
        
        if (fetchedBlock && fetchedBlock.type === '${blockType}') {
          setBlock(fetchedBlock as ${capitalizeFirstLetter(blockType)}Block);
        } else {
          setError('Block not found or is not a ${blockType} block');
        }
      } catch (err) {
        setError('Failed to load block');
        console.error('Error fetching block:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchBlock();
  }, [blockId]);
  
  if (loading) {
    return <div className="p-8 text-center">Loading...</div>;
  }
  
  if (error || !block) {
    return fallback ? <>{fallback}</> : <div className="p-8 text-center text-red-500">{error || 'Block not found'}</div>;
  }
  
  return <${componentName}Block block={block} />;
}`;
}

/**
 * Utility function to capitalize the first letter of a string
 * @param str String to capitalize
 */
function capitalizeFirstLetter(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

// Export the function for use in other scripts
export default generateBlockComponent;