#!/usr/bin/env node
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import readline from 'readline';
import generateBlockComponent from './generateBlockComponent';
import { convertComponentToBlock } from '../../../utils/componentToBlock';
import { createBlock } from '../../../utils/appwrite';

const readFile = promisify(fs.readFile);
const readdir = promisify(fs.readdir);
const exists = promisify(fs.exists);

// Create readline interface for CLI
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Promisify readline question
const question = (query: string): Promise<string> => {
  return new Promise((resolve) => {
    rl.question(query, (answer) => {
      resolve(answer);
    });
  });
};

/**
 * Main CLI function
 */
async function main() {
  console.log('🧩 Block Component Generator 🧩');
  console.log('===============================');
  
  try {
    // Get component type
    const componentType = await getComponentType();
    
    // Get component path
    const componentPath = await getComponentPath(componentType);
    
    // Generate block components
    console.log(`\nGenerating block components for ${componentPath}...`);
    const result = await generateBlockComponent(componentPath, componentType);
    
    // Ask if user wants to create the block in Appwrite
    const createInAppwrite = await question('\nDo you want to create this block in Appwrite? (y/n): ');
    
    if (createInAppwrite.toLowerCase() === 'y') {
      // Read the component content
      const componentContent = await readFile(componentPath, 'utf8');
      
      // Extract component name from path
      const componentName = path.basename(componentPath, '.tsx');
      
      // Convert component to block
      const block = convertComponentToBlock(componentType, componentName, componentContent);
      
      // Create block in Appwrite
      console.log('\nCreating block in Appwrite...');
      const createdBlock = await createBlock(block);
      
      console.log(`\n✅ Block created in Appwrite with ID: ${createdBlock.$id}`);
    }
    
    console.log('\n✅ All done! Your block components are ready to use.');
  } catch (error) {
    console.error('\n❌ Error:', error);
  } finally {
    rl.close();
  }
}

/**
 * Get component type from user
 */
async function getComponentType(): Promise<'about' | 'service' | 'team' | 'testimonial'> {
  console.log('\nSelect component type:');
  console.log('1. About');
  console.log('2. Service');
  console.log('3. Team');
  console.log('4. Testimonial');
  
  const answer = await question('\nEnter your choice (1-4): ');
  
  switch (answer) {
    case '1':
      return 'about';
    case '2':
      return 'service';
    case '3':
      return 'team';
    case '4':
      return 'testimonial';
    default:
      console.log('\n❌ Invalid choice. Please try again.');
      return getComponentType();
  }
}

/**
 * Get component path from user
 */
async function getComponentPath(componentType: string): Promise<string> {
  // Get all components of the selected type
  const componentsDir = path.join(process.cwd(), 'components', 'sections', componentType);
  
  // Check if directory exists
  if (!(await exists(componentsDir))) {
    throw new Error(`Directory not found: ${componentsDir}`);
  }
  
  // Get all component files
  const files = await readdir(componentsDir);
  const componentFiles = files.filter(file => file.endsWith('.tsx'));
  
  if (componentFiles.length === 0) {
    throw new Error(`No component files found in ${componentsDir}`);
  }
  
  console.log(`\nAvailable ${componentType} components:`);
  componentFiles.forEach((file, index) => {
    console.log(`${index + 1}. ${file}`);
  });
  
  const answer = await question(`\nEnter your choice (1-${componentFiles.length}): `);
  const index = parseInt(answer, 10) - 1;
  
  if (isNaN(index) || index < 0 || index >= componentFiles.length) {
    console.log('\n❌ Invalid choice. Please try again.');
    return getComponentPath(componentType);
  }
  
  return path.join(componentsDir, componentFiles[index]);
}

// Run the main function
main().catch(console.error);