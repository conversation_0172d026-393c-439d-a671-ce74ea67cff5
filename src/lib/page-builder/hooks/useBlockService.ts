import { useCallback, useEffect, useState } from 'react';
import { BlockTypes } from '../../../types/block';
import { blockService, BlockStyleConfig } from '../services/BlockService';

export function useBlockService(block: BlockTypes | null) {
  const [initialized, setInitialized] = useState(false);

  // Initialize block when component mounts
  useEffect(() => {
    if (block && !initialized) {
      blockService.initBlock(block);
      setInitialized(true);
    }

    // Cleanup when unmounting
    return () => {
      if (block) {
        blockService.removeBlock(block.id);
      }
    };
  }, [block?.id, initialized]);

  // Get block configuration
  const getBlockConfig = useCallback((blockId: string) => {
    return blockService.getBlockConfig(blockId);
  }, []);

  // Update block content
  const updateBlockContent = useCallback(async (blockId: string, content: any) => {
    try {
      await blockService.updateBlockContent(blockId, content);
    } catch (error) {
      console.error('Error updating block content:', error);
      throw error;
    }
  }, []);

  // Update block styles
  const updateBlockStyles = useCallback(async (blockId: string, styles: Partial<BlockStyleConfig>) => {
    try {
      await blockService.updateBlockStyles(blockId, styles);
    } catch (error) {
      console.error('Error updating block styles:', error);
      throw error;
    }
  }, []);

  // Toggle edit mode
  const toggleEditMode = useCallback((blockId: string) => {
    blockService.toggleEditMode(blockId);
  }, []);

  // Get computed styles string
  const getComputedStyles = useCallback((blockId: string) => {
    return blockService.getComputedStyles(blockId);
  }, []);

  // Get block classes array
  const getBlockClasses = useCallback((blockId: string) => {
    return blockService.getBlockClasses(blockId);
  }, []);

  // Combined update method for both content and styles
  const updateBlock = useCallback(async (blockId: string, updates: { 
    content?: any;
    styles?: Partial<BlockStyleConfig>;
  }) => {
    try {
      if (updates.content) {
        await updateBlockContent(blockId, updates.content);
      }
      if (updates.styles) {
        await updateBlockStyles(blockId, updates.styles);
      }
    } catch (error) {
      console.error('Error updating block:', error);
      throw error;
    }
  }, [updateBlockContent, updateBlockStyles]);

  return {
    getBlockConfig,
    updateBlockContent,
    updateBlockStyles,
    updateBlock,
    toggleEditMode,
    getComputedStyles,
    getBlockClasses
  };
}