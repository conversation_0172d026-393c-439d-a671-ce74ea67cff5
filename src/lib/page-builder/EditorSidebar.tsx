"use client"

import React, { useState } from 'react';
import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { Block } from '@/types/block';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Search, GripVertical, Plus } from 'lucide-react';

interface BlockItemProps {
  block: Block;
  onAddBlock: (blockId: string) => void;
}

// Draggable block item component
const BlockItem = ({ block, onAddBlock }: BlockItemProps) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: `library-${block.id}`,
    data: {
      type: 'BLOCK',
      id: block.id,
      origin: 'library'
    }
  });

  const style = transform ? {
    transform: CSS.Transform.toString(transform),
  } : undefined;

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={`mb-3 cursor-move hover:shadow-md transition-all ${isDragging ? 'opacity-50 border-dashed' : 'opacity-100'
        }`}
      {...attributes}
    >
      <CardHeader className="p-3 pb-0">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">{block.title}</CardTitle>
          <div {...listeners}>
            <GripVertical className="h-4 w-4 text-muted-foreground cursor-grab" />
          </div>
        </div>
        <CardDescription className="text-xs flex items-center gap-2">
          <Badge variant="outline" className="text-[10px] h-4 px-1">
            {block.type}
          </Badge>
          <span className="text-muted-foreground">{block.componentName}</span>
        </CardDescription>
      </CardHeader>

      {block.description && (
        <CardContent className="p-3 pt-2">
          <p className="text-xs text-muted-foreground truncate">{block.description}</p>
        </CardContent>
      )}

      <CardFooter className="p-2 flex justify-end">
        <Button
          onClick={() => onAddBlock(block.id)}
          variant="ghost"
          size="sm"
          className="h-7 text-xs"
        >
          <Plus className="h-3 w-3 mr-1" />
          Add
        </Button>
      </CardFooter>
    </Card>
  );
};

interface EditorSidebarProps {
  blocks: Block[];
  onAddBlock: (blockId: string) => void;
}

export default function EditorSidebar({ blocks, onAddBlock }: EditorSidebarProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');

  // Get unique block types for filter
  const blockTypes = ['all', ...new Set(blocks.map(block => block.type))];

  // Filter blocks based on search term and type
  const filteredBlocks = blocks.filter(block => {
    const matchesSearch =
      block.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      block.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      block.componentName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = filterType === 'all' || block.type === filterType;

    return matchesSearch && matchesType;
  });

  return (
    <div className="h-full flex flex-col bg-background border-r">
      <div className="p-4 border-b">
        <h2 className="text-lg font-semibold mb-3">Block Library</h2>

        <div className="relative mb-2">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search blocks..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <Select
          value={filterType}
          onValueChange={setFilterType}
        >
          <SelectTrigger>
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            {blockTypes.map(type => (
              <SelectItem key={type} value={type}>
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex-1 overflow-hidden">
        <div className="p-4 pb-0 flex items-center justify-between">
          <span className="text-sm text-muted-foreground">
            {filteredBlocks.length} {filteredBlocks.length === 1 ? 'block' : 'blocks'} found
          </span>
        </div>

        <ScrollArea className="h-[calc(100%-2.5rem)] px-4 pb-4">
          {filteredBlocks.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>No blocks match your search</p>
            </div>
          ) : (
            <div className="pt-2">
              {filteredBlocks.map(block => (
                <BlockItem
                  key={block.id}
                  block={block}
                  onAddBlock={onAddBlock}
                />
              ))}
            </div>
          )}
        </ScrollArea>
      </div>

      <div className="p-3 border-t bg-muted/50">
        <p className="text-xs text-muted-foreground text-center">
          Drag blocks to the canvas or click to add
        </p>
      </div>
    </div>
  );
}