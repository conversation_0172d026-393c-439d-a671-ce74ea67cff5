import React, { forwardRef } from 'react';
import { useDroppable } from '@dnd-kit/core';
import { Block, BlockTypes, FlexContainerBlock } from '../../types/block';
import FlexContainer from './blocks/FlexContainer';
import CanvasBlockItem from './CanvasBlockItem';

interface EditorCanvasProps {
  blocks: string[];
  availableBlocks: Block[];
  selectedBlockId: string | null;
  isEditing: boolean;
  onSelectBlock: (blockId: string) => void;
  onRemoveBlock: (blockId: string, parentId?: string) => void;
  onUpdateBlock: (blockId: string, updatedBlock: BlockTypes) => void;
}

// Type guard to check if a block is a valid BlockTypes
function isBlockType(block: Block): block is BlockTypes {
  const validTypes = ['about', 'service', 'team', 'testimonial', 'flex-container'];
  return validTypes.includes(block.type);
}

const EditorCanvas = forwardRef<HTMLDivElement, EditorCanvasProps>(
  ({
    blocks,
    availableBlocks,
    selectedBlockId,
    isEditing,
    onSelectBlock,
    onRemoveBlock,
    onUpdateBlock
  }, ref) => {
    // Set up droppable area for the canvas
    const { setNodeRef, isOver } = useDroppable({
      id: 'canvas-drop-area',
      data: {
        accepts: ['BLOCK', 'CANVAS_BLOCK'],
      },
    });

    // Combine refs
    const setRefs = (node: HTMLDivElement | null) => {
      setNodeRef(node);
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    };

    const renderBlockItem = (blockId: string, index: number) => {
      const block = availableBlocks.find(b => b.id === blockId);

      if (!block) {
        console.warn(`Block with id ${blockId} not found in available blocks`);
        return null;
      }

      if (!isBlockType(block)) {
        console.warn(`Block with id ${blockId} has invalid type: ${block.type}`);
        return null;
      }

      if (block.type === 'flex-container') {
        return (
          <FlexContainer
            key={block.id}
            block={block as FlexContainerBlock}
            availableBlocks={availableBlocks}
            selectedBlockId={selectedBlockId}
            isEditing={isEditing}
            onSelectBlock={onSelectBlock}
            onRemoveBlock={onRemoveBlock}
            onUpdateBlock={onUpdateBlock}
          />
        );
      }

      return (
        <CanvasBlockItem
          key={`${blockId}-${index}`}
          blockId={blockId}
          block={block}
          index={index}
          isSelected={blockId === selectedBlockId}
          isEditing={isEditing}
          onSelect={() => onSelectBlock(blockId)}
          onRemove={() => onRemoveBlock(blockId)}
          onUpdate={(updatedBlock) => onUpdateBlock(blockId, updatedBlock)}
        />
      );
    };

    return (
      <div
        ref={setRefs}
        className={`flex-1 bg-gray-100 overflow-y-auto p-6 ${isOver ? 'bg-blue-50' : ''}`}
      >
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm p-4 min-h-[calc(100vh-12rem)]">
            {blocks.length === 0 ? (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
                <p className="text-gray-500 mb-4">
                  Drag and drop blocks here to build your page
                </p>
                <p className="text-sm text-gray-400">
                  Or select blocks from the library on the left
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {blocks.map((blockId, index) => renderBlockItem(blockId, index))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
);

EditorCanvas.displayName = 'EditorCanvas';

export default EditorCanvas;