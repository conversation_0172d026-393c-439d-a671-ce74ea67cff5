import React, { useCallback } from 'react';
import { ServiceBlock, BlockText } from '../../../../types/block';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { TextEditor } from '../base/TextEditor';
import { ImageEditor } from '../base/ImageEditor';
import { LinkEditor } from '../base/LinkEditor';
import { Plus, Trash2 } from 'lucide-react';

interface ServiceBlockEditorProps {
  value: ServiceBlock;
  onChange: (value: ServiceBlock) => void;
  onUpload?: (file: File) => Promise<{ url: string; fileId: string }>;
}

export function ServiceBlockEditor({
  value,
  onChange,
  onUpload
}: ServiceBlockEditorProps) {
  const updateContent = useCallback((updates: Partial<ServiceBlock['content']>) => {
    onChange({
      ...value,
      content: {
        ...value.content,
        ...updates
      }
    });
  }, [value, onChange]);

  // Handlers for text content
  const handleHeadingChange = useCallback((heading: BlockText) => {
    updateContent({ heading });
  }, [updateContent]);

  const handleSubheadingChange = useCallback((subheading: BlockText) => {
    updateContent({ subheading });
  }, [updateContent]);

  const handleDescriptionChange = useCallback((description: BlockText) => {
    updateContent({ description });
  }, [updateContent]);

  // Handlers for services array
  const handleAddService = useCallback(() => {
    updateContent({
      services: [
        ...value.content.services,
        {
          icon: { src: '', alt: '' },
          title: { type: 'h3', content: 'New Service' },
          description: { type: 'p', content: 'Service description' }
        }
      ]
    });
  }, [value.content.services, updateContent]);

  const handleRemoveService = useCallback((index: number) => {
    const services = value.content.services.filter((_, i) => i !== index);
    updateContent({ services });
  }, [value.content.services, updateContent]);

  const handleUpdateService = useCallback((index: number, updates: Partial<ServiceBlock['content']['services'][0]>) => {
    const services = [...value.content.services];
    services[index] = {
      ...services[index],
      ...updates
    };
    updateContent({ services });
  }, [value.content.services, updateContent]);

  return (
    <div className="space-y-6">
      {/* Main Content Section */}
      <Card>
        <CardContent className="p-4 space-y-4">
          <TextEditor
            label="Heading"
            value={value.content.heading}
            onChange={handleHeadingChange}
          />

          <TextEditor
            label="Subheading"
            value={value.content.subheading}
            onChange={handleSubheadingChange}
          />

          <TextEditor
            label="Description"
            value={value.content.description}
            onChange={handleDescriptionChange}
          />
        </CardContent>
      </Card>

      {/* Services Section */}
      <Card>
        <CardContent className="p-4">
          <div className="flex justify-between items-center mb-3">
            <Label className="block font-medium text-sm text-gray-700">
              Services
            </Label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddService}
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Service
            </Button>
          </div>

          <div className="space-y-6">
            {value.content.services.map((service, index) => (
              <Card key={index}>
                <CardContent className="p-4 relative">
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2 text-red-600 hover:text-red-700"
                    onClick={() => handleRemoveService(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>

                  <div className="space-y-4">
                    <div>
                      <Label className="text-xs mb-2">Icon</Label>
                      <ImageEditor
                        value={service.icon}
                        onChange={(icon) => handleUpdateService(index, { icon })}
                        onUpload={onUpload}
                      />
                    </div>

                    <div>
                      <Label className="text-xs mb-2">Title</Label>
                      <TextEditor
                        value={service.title}
                        onChange={(title) => handleUpdateService(index, { title })}
                      />
                    </div>

                    <div>
                      <Label className="text-xs mb-2">Description</Label>
                      <TextEditor
                        value={service.description}
                        onChange={(description) => handleUpdateService(index, { description })}
                      />
                    </div>

                    {service.link ? (
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <Label className="text-xs">Link</Label>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                            onClick={() => handleUpdateService(index, { link: undefined })}
                          >
                            <Trash2 className="h-3 w-3 mr-1" />
                            Remove Link
                          </Button>
                        </div>
                        <LinkEditor
                          value={service.link}
                          onChange={(link) => handleUpdateService(index, { link })}
                        />
                      </div>
                    ) : (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => handleUpdateService(index, {
                          link: { href: '', text: '' }
                        })}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add Link
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}