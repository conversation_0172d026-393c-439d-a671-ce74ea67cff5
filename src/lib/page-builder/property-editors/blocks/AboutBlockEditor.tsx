import React, { useCallback } from 'react';
import { AboutBlock, BlockText, BlockCounter, BlockIconBox } from '../../../../types/block';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { TextEditor } from '../base/TextEditor';
import { ImageEditor } from '../base/ImageEditor';
import { CounterEditor } from '../base/CounterEditor';
import { IconBoxEditor } from '../base/IconBoxEditor';
import { LinkEditor } from '../base/LinkEditor';
import { Plus, Trash2 } from 'lucide-react';

interface AboutBlockEditorProps {
  value: AboutBlock;
  onChange: (value: AboutBlock) => void;
  onUpload?: (file: File) => Promise<{ url: string; fileId: string }>;
}

export function AboutBlockEditor({
  value,
  onChange,
  onUpload
}: AboutBlockEditorProps) {
  const updateContent = useCallback((updates: Partial<AboutBlock['content']>) => {
    onChange({
      ...value,
      content: {
        ...value.content,
        ...updates
      }
    });
  }, [value, onChange]);

  // Handlers for individual content sections
  const handleHeadingChange = useCallback((heading: BlockText) => {
    updateContent({ heading });
  }, [updateContent]);

  const handleSubheadingChange = useCallback((subheading: BlockText) => {
    updateContent({ subheading });
  }, [updateContent]);

  const handleDescriptionChange = useCallback((description: BlockText) => {
    updateContent({ description });
  }, [updateContent]);

  const handleCounterChange = useCallback((index: number, counter: BlockCounter) => {
    const counters = [...value.content.counters];
    counters[index] = counter;
    updateContent({ counters });
  }, [value.content.counters, updateContent]);

  const handleIconBoxChange = useCallback((index: number, iconBox: BlockIconBox) => {
    const iconBoxes = [...value.content.iconBoxes];
    iconBoxes[index] = iconBox;
    updateContent({ iconBoxes });
  }, [value.content.iconBoxes, updateContent]);

  const handleAddCounter = useCallback(() => {
    updateContent({
      counters: [
        ...value.content.counters,
        { value: 0, label: 'New Counter' }
      ]
    });
  }, [value.content.counters, updateContent]);

  const handleRemoveCounter = useCallback((index: number) => {
    const counters = value.content.counters.filter((_, i) => i !== index);
    updateContent({ counters });
  }, [value.content.counters, updateContent]);

  const handleAddIconBox = useCallback(() => {
    updateContent({
      iconBoxes: [
        ...value.content.iconBoxes,
        {
          icon: { src: '', alt: '' },
          title: { type: 'h3', content: 'New Feature' },
          description: { type: 'p', content: 'Description here' }
        }
      ]
    });
  }, [value.content.iconBoxes, updateContent]);

  const handleRemoveIconBox = useCallback((index: number) => {
    const iconBoxes = value.content.iconBoxes.filter((_, i) => i !== index);
    updateContent({ iconBoxes });
  }, [value.content.iconBoxes, updateContent]);

  return (
    <div className="space-y-6">
      {/* Main Content Section */}
      <Card>
        <CardContent className="p-4 space-y-4">
          <TextEditor
            label="Heading"
            value={value.content.heading}
            onChange={handleHeadingChange}
          />

          <TextEditor
            label="Subheading"
            value={value.content.subheading}
            onChange={handleSubheadingChange}
          />

          <TextEditor
            label="Description"
            value={value.content.description}
            onChange={handleDescriptionChange}
          />
        </CardContent>
      </Card>

      {/* Images Section */}
      <Card>
        <CardContent className="p-4">
          <Label className="block font-medium text-sm text-gray-700 mb-3">
            Images
          </Label>
          <div className="grid grid-cols-2 gap-4">
            {value.content.images.map((image, index) => (
              <ImageEditor
                key={index}
                value={image}
                onChange={(updatedImage) => {
                  const images = [...value.content.images];
                  images[index] = updatedImage;
                  updateContent({ images });
                }}
                onUpload={onUpload}
              />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Counters Section */}
      <Card>
        <CardContent className="p-4">
          <div className="flex justify-between items-center mb-3">
            <Label className="block font-medium text-sm text-gray-700">
              Counters
            </Label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddCounter}
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Counter
            </Button>
          </div>
          <div className="space-y-4">
            {value.content.counters.map((counter, index) => (
              <div key={index} className="relative">
                <CounterEditor
                  value={counter}
                  onChange={(updated) => handleCounterChange(index, updated)}
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute top-0 right-0 text-red-600 hover:text-red-700"
                  onClick={() => handleRemoveCounter(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Icon Boxes Section */}
      <Card>
        <CardContent className="p-4">
          <div className="flex justify-between items-center mb-3">
            <Label className="block font-medium text-sm text-gray-700">
              Feature Boxes
            </Label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddIconBox}
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Feature
            </Button>
          </div>
          <div className="space-y-4">
            {value.content.iconBoxes.map((iconBox, index) => (
              <div key={index} className="relative">
                <IconBoxEditor
                  value={iconBox}
                  onChange={(updated) => handleIconBoxChange(index, updated)}
                  onUpload={onUpload}
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute top-4 right-4 text-red-600 hover:text-red-700"
                  onClick={() => handleRemoveIconBox(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Call to Action Section */}
      {value.content.cta ? (
        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center mb-3">
              <Label className="block font-medium text-sm text-gray-700">
                Call to Action
              </Label>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="text-red-600 hover:text-red-700"
                onClick={() => updateContent({ cta: undefined })}
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Remove CTA
              </Button>
            </div>
            <LinkEditor
              value={value.content.cta}
              onChange={(cta) => updateContent({ cta })}
            />
          </CardContent>
        </Card>
      ) : (
        <Button
          type="button"
          variant="outline"
          onClick={() => updateContent({ cta: { href: '', text: '' } })}
        >
          <Plus className="h-4 w-4 mr-1" />
          Add Call to Action
        </Button>
      )}
    </div>
  );
}