import React, { useCallback } from 'react';
import { BlockIconBox } from '../../../../types/block';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { ImageEditor } from './ImageEditor';
import { TextEditor } from './TextEditor';
import { LinkEditor } from './LinkEditor';

interface IconBoxEditorProps {
  value: BlockIconBox;
  onChange: (value: BlockIconBox) => void;
  label?: string;
  className?: string;
  onUpload?: (file: File) => Promise<{ url: string; fileId: string }>;
}

export function IconBoxEditor({
  value,
  onChange,
  label,
  className,
  onUpload
}: IconBoxEditorProps) {
  const handleIconChange = useCallback((icon: BlockIconBox['icon']) => {
    onChange({
      ...value,
      icon
    });
  }, [value, onChange]);

  const handleTitleChange = useCallback((title: BlockIconBox['title']) => {
    onChange({
      ...value,
      title
    });
  }, [value, onChange]);

  const handleDescriptionChange = useCallback((description: BlockIconBox['description']) => {
    onChange({
      ...value,
      description
    });
  }, [value, onChange]);

  const handleLinkChange = useCallback((link: BlockIconBox['link']) => {
    onChange({
      ...value,
      link
    });
  }, [value, onChange]);

  return (
    <div className={`space-y-4 ${className || ''}`}>
      {label && (
        <Label className="block font-medium text-sm text-gray-700">
          {label}
        </Label>
      )}

      <Card>
        <CardContent className="p-4 space-y-4">
          <div>
            <Label className="text-xs mb-2">Icon</Label>
            <ImageEditor
              value={value.icon}
              onChange={handleIconChange}
              onUpload={onUpload}
            />
          </div>

          <div>
            <Label className="text-xs mb-2">Title</Label>
            <TextEditor
              value={value.title}
              onChange={handleTitleChange}
            />
          </div>

          <div>
            <Label className="text-xs mb-2">Description</Label>
            <TextEditor
              value={value.description}
              onChange={handleDescriptionChange}
            />
          </div>

          {/* Optional link */}
          <div>
            <Label className="text-xs mb-2">Link (Optional)</Label>
            {value.link ? (
              <div className="space-y-2">
                <LinkEditor
                  value={value.link}
                  onChange={handleLinkChange}
                />
                <button
                  type="button"
                  onClick={() => onChange({ ...value, link: undefined })}
                  className="text-xs text-red-600 hover:text-red-700"
                >
                  Remove Link
                </button>
              </div>
            ) : (
              <button
                type="button"
                onClick={() => onChange({
                  ...value,
                  link: { href: '', text: '' }
                })}
                className="text-xs text-blue-600 hover:text-blue-700"
              >
                Add Link
              </button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}