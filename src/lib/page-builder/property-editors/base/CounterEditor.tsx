import React, { useCallback } from 'react';
import { BlockCounter } from '../../../../types/block';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

interface CounterEditorProps {
  value: BlockCounter;
  onChange: (value: BlockCounter) => void;
  label?: string;
  className?: string;
}

export function CounterEditor({
  value,
  onChange,
  label,
  className
}: CounterEditorProps) {
  const handleValueChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const numValue = parseInt(e.target.value, 10);
    onChange({
      ...value,
      value: isNaN(numValue) ? 0 : numValue
    });
  }, [value, onChange]);

  const handleSuffixChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...value,
      suffix: e.target.value
    });
  }, [value, onChange]);

  const handleLabelChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...value,
      label: e.target.value
    });
  }, [value, onChange]);

  return (
    <div className={`space-y-3 ${className || ''}`}>
      {label && (
        <Label className="block font-medium text-sm text-gray-700">
          {label}
        </Label>
      )}

      <div className="grid gap-3">
        <div className="flex gap-2">
          <div className="flex-1">
            <Label htmlFor="counter-value" className="text-xs">Value</Label>
            <Input
              id="counter-value"
              type="number"
              min={0}
              value={value.value}
              onChange={handleValueChange}
              placeholder="Enter number"
            />
          </div>

          <div className="w-24">
            <Label htmlFor="counter-suffix" className="text-xs">Suffix</Label>
            <Input
              id="counter-suffix"
              type="text"
              value={value.suffix || ''}
              onChange={handleSuffixChange}
              placeholder="%"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="counter-label" className="text-xs">Label</Label>
          <Input
            id="counter-label"
            type="text"
            value={value.label}
            onChange={handleLabelChange}
            placeholder="Enter label"
          />
        </div>
      </div>
    </div>
  );
}