import React, { useCallback } from 'react';
import { BlockText } from '../../../../types/block';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface TextEditorProps {
  value: BlockText;
  onChange: (value: BlockText) => void;
  label?: string;
  className?: string;
}

export function TextEditor({ value, onChange, label, className }: TextEditorProps) {
  const handleContentChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...value,
      content: e.target.value
    });
  }, [value, onChange]);

  const handleTypeChange = useCallback((type: BlockText['type']) => {
    onChange({
      ...value,
      type
    });
  }, [value, onChange]);

  const handleClassNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...value,
      className: e.target.value
    });
  }, [value, onChange]);

  return (
    <div className={`space-y-3 ${className}`}>
      {label && (
        <Label className="block font-medium text-sm text-gray-700">
          {label}
        </Label>
      )}

      <div className="flex gap-2">
        <Select
          value={value.type}
          onValueChange={handleTypeChange}
        >
          <SelectTrigger className="w-[100px]">
            <SelectValue placeholder="Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="h1">H1</SelectItem>
            <SelectItem value="h2">H2</SelectItem>
            <SelectItem value="h3">H3</SelectItem>
            <SelectItem value="h4">H4</SelectItem>
            <SelectItem value="h5">H5</SelectItem>
            <SelectItem value="h6">H6</SelectItem>
            <SelectItem value="p">Paragraph</SelectItem>
            <SelectItem value="span">Span</SelectItem>
          </SelectContent>
        </Select>

        <Input
          value={value.content}
          onChange={handleContentChange}
          placeholder="Enter text content"
          className="flex-1"
        />
      </div>

      <Input
        value={value.className || ''}
        onChange={handleClassNameChange}
        placeholder="CSS classes (optional)"
        className="w-full"
      />
    </div>
  );
}
