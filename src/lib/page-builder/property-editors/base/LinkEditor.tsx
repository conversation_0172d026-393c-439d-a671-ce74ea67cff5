import React, { useCallback } from 'react';
import { BlockLink } from '../../../../types/block';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Link as LinkIcon, ExternalLink } from 'lucide-react';

interface LinkEditorProps {
  value: BlockLink;
  onChange: (value: BlockLink) => void;
  label?: string;
  className?: string;
}

export function LinkEditor({
  value,
  onChange,
  label,
  className
}: LinkEditorProps) {
  const handleHrefChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...value,
      href: e.target.value
    });
  }, [value, onChange]);

  const handleTextChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...value,
      text: e.target.value
    });
  }, [value, onChange]);

  const handleClassNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...value,
      className: e.target.value
    });
  }, [value, onChange]);

  const handleIconChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...value,
      icon: e.target.value
    });
  }, [value, onChange]);

  const handleTestLink = useCallback(() => {
    if (value.href) {
      window.open(value.href, '_blank', 'noopener,noreferrer');
    }
  }, [value.href]);

  return (
    <div className={`space-y-3 ${className || ''}`}>
      {label && (
        <Label className="block font-medium text-sm text-gray-700">
          {label}
        </Label>
      )}

      <div className="grid gap-3">
        <div className="flex gap-2">
          <div className="flex-1">
            <Label htmlFor="href" className="text-xs">URL</Label>
            <div className="flex gap-2">
              <div className="relative flex-1">
                <LinkIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  id="href"
                  type="url"
                  value={value.href}
                  onChange={handleHrefChange}
                  placeholder="Enter URL"
                  className="pl-8"
                />
              </div>
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={handleTestLink}
                disabled={!value.href}
                title="Test link"
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        <div>
          <Label htmlFor="text" className="text-xs">Link Text</Label>
          <Input
            id="text"
            type="text"
            value={value.text}
            onChange={handleTextChange}
            placeholder="Enter link text"
          />
        </div>

        <div>
          <Label htmlFor="className" className="text-xs">CSS Classes (optional)</Label>
          <Input
            id="className"
            type="text"
            value={value.className || ''}
            onChange={handleClassNameChange}
            placeholder="Enter CSS classes"
          />
        </div>

        <div>
          <Label htmlFor="icon" className="text-xs">Icon Name (optional)</Label>
          <Input
            id="icon"
            type="text"
            value={value.icon || ''}
            onChange={handleIconChange}
            placeholder="Enter icon name"
          />
        </div>
      </div>
    </div>
  );
}