import React, { useCallback, useState } from 'react';
import { BlockImage } from '../../../../types/block';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ImagePlus, Trash2 } from 'lucide-react';

interface ImageEditorProps {
  value: BlockImage;
  onChange: (value: BlockImage) => void;
  label?: string;
  className?: string;
  onUpload?: (file: File) => Promise<{ url: string; fileId: string }>;
}

export function ImageEditor({
  value,
  onChange,
  label,
  className,
  onUpload
}: ImageEditorProps) {
  const [isUploading, setIsUploading] = useState(false);

  const handleSrcChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...value,
      src: e.target.value
    });
  }, [value, onChange]);

  const handleAltChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...value,
      alt: e.target.value
    });
  }, [value, onChange]);

  const handleFileUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !onUpload) return;

    try {
      setIsUploading(true);
      const { url, fileId } = await onUpload(file);
      onChange({
        ...value,
        src: url,
        fileId: fileId
      });
    } catch (error) {
      console.error('Failed to upload image:', error);
    } finally {
      setIsUploading(false);
      // Reset file input
      e.target.value = '';
    }
  }, [value, onChange, onUpload]);

  const handleRemoveImage = useCallback(() => {
    onChange({
      ...value,
      src: '',
      fileId: undefined
    });
  }, [value, onChange]);

  return (
    <div className={`space-y-3 ${className}`}>
      {label && (
        <Label className="block font-medium text-sm text-gray-700">
          {label}
        </Label>
      )}

      <div className="space-y-3">
        {value.src && (
          <div className="relative w-full aspect-video bg-gray-100 rounded-lg overflow-hidden">
            <img
              src={value.src}
              alt={value.alt}
              className="w-full h-full object-contain"
            />
            <Button
              variant="destructive"
              size="icon"
              className="absolute top-2 right-2"
              onClick={handleRemoveImage}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        )}

        <div className="grid gap-3">
          <div className="flex gap-2">
            <Input
              type="text"
              value={value.src}
              onChange={handleSrcChange}
              placeholder="Image URL"
              className="flex-1"
            />
            {onUpload && (
              <div className="relative">
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  disabled={isUploading}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
                <Button
                  type="button"
                  variant="outline"
                  disabled={isUploading}
                >
                  <ImagePlus className="h-4 w-4 mr-1" />
                  Upload
                </Button>
              </div>
            )}
          </div>

          <Input
            type="text"
            value={value.alt}
            onChange={handleAltChange}
            placeholder="Alt text"
          />
        </div>
      </div>
    </div>
  );
}