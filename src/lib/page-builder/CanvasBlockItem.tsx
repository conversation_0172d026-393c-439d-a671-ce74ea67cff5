import React, { useCallback, useMemo } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { BlockTypes } from '../../types/block';
import BlockRenderer from '../../components/blocks/BlockRenderer';
import { useBlockService } from './hooks/useBlockService';

interface CanvasBlockItemProps {
  blockId: string;
  block: BlockTypes;
  index: number;
  isSelected: boolean;
  isEditing: boolean;
  onSelect: () => void;
  onRemove: () => void;
  onUpdate: (updatedBlock: BlockTypes) => void;
  parentId?: string;
}

export default function CanvasBlockItem({
  blockId,
  block,
  index,
  isSelected,
  isEditing,
  onSelect,
  onRemove,
  onUpdate,
  parentId
}: CanvasBlockItemProps) {
  // Set up block service
  const {
    getComputedStyles,
    getBlockClasses,
    updateBlock
  } = useBlockService(block);

  // Set up sortable item
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
    isOver
  } = useSortable({
    id: blockId,
    data: {
      type: 'CANVAS_BLOCK',
      index,
      parentId
    }
  });

  // Parse computed styles string into object
  const computedStylesObject = useMemo(() => {
    const styleString = getComputedStyles(blockId);
    if (!styleString) return {};

    return styleString.split(';').reduce((acc: Record<string, string>, style) => {
      const [key, value] = style.split(':').map(s => s.trim());
      if (key && value) {
        // Convert kebab-case to camelCase for React
        const camelKey = key.replace(/-([a-z])/g, g => g[1].toUpperCase());
        acc[camelKey] = value;
      }
      return acc;
    }, {});
  }, [getComputedStyles, blockId]);

  // Combine inline styles
  const style = useMemo(() => ({
    transform: CSS.Transform.toString(transform),
    transition,
    ...computedStylesObject
  }), [transform, transition, computedStylesObject]);

  // Handle block updates
  const handleBlockUpdate = useCallback(async (updatedBlock: BlockTypes) => {
    try {
      await updateBlock(blockId, {
        content: updatedBlock.content,
        styles: updatedBlock.styles
      });
      onUpdate(updatedBlock);
    } catch (error) {
      console.error('Failed to update block:', error);
    }
  }, [blockId, updateBlock, onUpdate]);

  // Combine classes
  const blockClasses = useMemo(() => {
    const baseClasses = getBlockClasses(blockId);
    return [
      ...baseClasses,
      'relative border rounded-md transition-all',
      isDragging ? 'opacity-50' : 'opacity-100',
      isSelected ? 'ring-2 ring-blue-500' : '',
      isOver ? 'bg-blue-50' : 'bg-white'
    ].filter(Boolean).join(' ');
  }, [getBlockClasses, blockId, isDragging, isSelected, isOver]);

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={blockClasses}
      onClick={onSelect}
      {...attributes}
    >
      {/* Block controls */}
      <div className="absolute top-2 right-2 z-10 flex space-x-1">
        <button
          type="button"
          className="p-1 bg-gray-200 rounded hover:bg-gray-300 text-gray-700 cursor-move"
          title="Drag to reorder"
          {...listeners}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        <button
          type="button"
          className="p-1 bg-red-100 rounded hover:bg-red-200 text-red-700"
          onClick={(e) => {
            e.stopPropagation();
            onRemove();
          }}
          title="Remove block"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Block content */}
      <BlockRenderer
        blockId={blockId}
        isEditing={isEditing}
        onUpdate={handleBlockUpdate}
        fallback={
          <div className="p-8 text-center text-gray-500">
            Block could not be loaded
          </div>
        }
      />
    </div>
  );
}