"use client"

import React, { useState } from 'react';
import {
  Save,
  Eye,
  <PERSON>cil,
  Undo,
  Redo,
  Co<PERSON>,
  Trash2,
  <PERSON><PERSON>,
  <PERSON><PERSON>s,
  HelpCircle,
  FileText,
  Download,
  Upload,
  Smartphone,
  Tablet,
  Monitor,
  RotateCcw
} from 'lucide-react';
import { <PERSON><PERSON> } from '../../components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { Separator } from '../../components/ui/separator';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '../../components/ui/dropdown-menu';
import { ToggleGroup, ToggleGroupItem } from '../../components/ui/toggle-group';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '../../components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '../../components/ui/alert-dialog';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';

interface EditorToolbarProps {
  onSave: () => void;
  hasUnsavedChanges: boolean;
  editorMode: 'edit' | 'preview';
  onToggleMode: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
  onReset?: () => void;
  onExport?: () => void;
  onImport?: (data: any) => void;
  pageName?: string;
  onPageNameChange?: (name: string) => void;
}

export default function EditorToolbar({
  onSave,
  hasUnsavedChanges,
  editorMode,
  onToggleMode,
  onUndo,
  onRedo,
  canUndo = false,
  canRedo = false,
  onReset,
  onExport,
  onImport,
  pageName = 'Untitled Page',
  onPageNameChange
}: EditorToolbarProps) {
  const [viewportSize, setViewportSize] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [showPageNameDialog, setShowPageNameDialog] = useState(false);
  const [newPageName, setNewPageName] = useState(pageName);
  const [showHelpDialog, setShowHelpDialog] = useState(false);
  const [showResetConfirm, setShowResetConfirm] = useState(false);
  const [fileInputRef, setFileInputRef] = useState<HTMLInputElement | null>(null);

  // Handle page name change
  const handlePageNameSubmit = () => {
    if (onPageNameChange && newPageName.trim()) {
      onPageNameChange(newPageName);
    }
    setShowPageNameDialog(false);
  };

  // Handle file import
  const handleFileImport = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !onImport) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const data = JSON.parse(event.target?.result as string);
        onImport(data);
      } catch (error) {
        console.error('Failed to parse imported file:', error);
        alert('Invalid file format. Please upload a valid JSON file.');
      }
    };
    reader.readAsText(file);

    // Reset the input
    if (fileInputRef) {
      fileInputRef.value = '';
    }
  };

  // Trigger file input click
  const triggerFileInput = () => {
    fileInputRef?.click();
  };

  return (
    <div className="bg-background border-b p-2 flex items-center justify-between">
      {/* Left section - Page info and file operations */}
      <div className="flex items-center space-x-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="gap-1">
              <FileText className="h-4 w-4" />
              <span className="max-w-[150px] truncate">{pageName}</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            <DropdownMenuLabel>Page Options</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {onPageNameChange && (
              <DropdownMenuItem onClick={() => setShowPageNameDialog(true)}>
                Rename Page
              </DropdownMenuItem>
            )}
            {onExport && (
              <DropdownMenuItem onClick={onExport}>
                <Download className="h-4 w-4 mr-2" />
                Export Page
              </DropdownMenuItem>
            )}
            {onImport && (
              <DropdownMenuItem onClick={triggerFileInput}>
                <Upload className="h-4 w-4 mr-2" />
                Import Page
              </DropdownMenuItem>
            )}
            {onReset && (
              <DropdownMenuItem onClick={() => setShowResetConfirm(true)} className="text-destructive">
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset Page
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        <Separator orientation="vertical" className="h-6" />

        {/* History controls */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={onUndo}
                disabled={!canUndo || !onUndo}
              >
                <Undo className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Undo (Ctrl+Z)</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={onRedo}
                disabled={!canRedo || !onRedo}
              >
                <Redo className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Redo (Ctrl+Y)</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Center section - Viewport controls */}
      <div className="flex items-center">
        <ToggleGroup type="single" value={viewportSize} onValueChange={(value) => value && setViewportSize(value as any)}>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <ToggleGroupItem value="mobile" aria-label="Mobile view">
                  <Smartphone className="h-4 w-4" />
                </ToggleGroupItem>
              </TooltipTrigger>
              <TooltipContent>Mobile View</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <ToggleGroupItem value="tablet" aria-label="Tablet view">
                  <Tablet className="h-4 w-4" />
                </ToggleGroupItem>
              </TooltipTrigger>
              <TooltipContent>Tablet View</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <ToggleGroupItem value="desktop" aria-label="Desktop view">
                  <Monitor className="h-4 w-4" />
                </ToggleGroupItem>
              </TooltipTrigger>
              <TooltipContent>Desktop View</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </ToggleGroup>
      </div>

      {/* Right section - Actions */}
      <div className="flex items-center space-x-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                onClick={onToggleMode}
                className="gap-1"
              >
                {editorMode === 'edit' ? (
                  <>
                    <Eye className="h-4 w-4" />
                    <span className="hidden sm:inline">Preview</span>
                  </>
                ) : (
                  <>
                    <Pencil className="h-4 w-4" />
                    <span className="hidden sm:inline">Edit</span>
                  </>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {editorMode === 'edit' ? 'Switch to Preview Mode' : 'Switch to Edit Mode'}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant={hasUnsavedChanges ? "default" : "outline"}
                size="sm"
                onClick={onSave}
                className="gap-1"
                disabled={!hasUnsavedChanges}
              >
                <Save className="h-4 w-4" />
                <span className="hidden sm:inline">Save</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>Save Changes</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <Separator orientation="vertical" className="h-6" />

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowHelpDialog(true)}
              >
                <HelpCircle className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Help</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <DropdownMenu>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Settings className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
              </TooltipTrigger>
              <TooltipContent>Settings</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Editor Settings</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              Show Grid
            </DropdownMenuItem>
            <DropdownMenuItem>
              Snap to Grid
            </DropdownMenuItem>
            <DropdownMenuItem>
              Auto Save
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              Editor Preferences
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Hidden file input for import */}
      {onImport && (
        <input
          type="file"
          ref={ref => setFileInputRef(ref)}
          onChange={handleFileImport}
          accept=".json"
          className="hidden"
        />
      )}

      {/* Page name dialog */}
      <Dialog open={showPageNameDialog} onOpenChange={setShowPageNameDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rename Page</DialogTitle>
            <DialogDescription>
              Enter a new name for your page.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Label htmlFor="pageName">Page Name</Label>
            <Input
              id="pageName"
              value={newPageName}
              onChange={(e) => setNewPageName(e.target.value)}
              className="mt-2"
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPageNameDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handlePageNameSubmit}>
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Help dialog */}
      <Dialog open={showHelpDialog} onOpenChange={setShowHelpDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Page Builder Help</DialogTitle>
            <DialogDescription>
              Learn how to use the page builder effectively.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 max-h-[60vh] overflow-y-auto">
            <h3 className="text-lg font-medium mb-2">Getting Started</h3>
            <p className="mb-4">
              The page builder allows you to create and customize pages by dragging and dropping blocks from the sidebar onto the canvas.
            </p>

            <h3 className="text-lg font-medium mb-2">Toolbar Functions</h3>
            <ul className="list-disc pl-5 space-y-2 mb-4">
              <li><strong>Save</strong> - Save your current page layout</li>
              <li><strong>Preview/Edit</strong> - Toggle between edit and preview modes</li>
              <li><strong>Undo/Redo</strong> - Navigate through your edit history</li>
              <li><strong>Device Preview</strong> - See how your page looks on different devices</li>
              <li><strong>Export/Import</strong> - Save your page design or load a previously saved design</li>
            </ul>

            <h3 className="text-lg font-medium mb-2">Working with Blocks</h3>
            <ul className="list-disc pl-5 space-y-2 mb-4">
              <li><strong>Add Blocks</strong> - Drag blocks from the left sidebar onto the canvas</li>
              <li><strong>Reorder Blocks</strong> - Drag blocks up or down to change their order</li>
              <li><strong>Edit Blocks</strong> - Select a block to edit its properties in the right panel</li>
              <li><strong>Remove Blocks</strong> - Select a block and click the trash icon to remove it</li>
            </ul>

            <h3 className="text-lg font-medium mb-2">Keyboard Shortcuts</h3>
            <div className="grid grid-cols-2 gap-2 mb-4">
              <div>Ctrl+Z</div><div>Undo</div>
              <div>Ctrl+Y</div><div>Redo</div>
              <div>Ctrl+S</div><div>Save</div>
              <div>Ctrl+P</div><div>Toggle Preview</div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowHelpDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reset confirmation dialog */}
      <AlertDialog open={showResetConfirm} onOpenChange={setShowResetConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reset Page</AlertDialogTitle>
            <AlertDialogDescription>
              This will remove all blocks and reset the page to its default state. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={() => {
              if (onReset) onReset();
              setShowResetConfirm(false);
            }} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Reset
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
