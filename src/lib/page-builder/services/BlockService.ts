import { Block, BlockTypes } from '../../../types/block';
import { updateBlock } from '../../../utils/appwrite';

export interface BlockStyleConfig {
  padding?: string;
  margin?: string;
  backgroundColor?: string;
  textColor?: string;
  customClasses?: string[];
  animation?: string;
  [key: string]: string | string[] | undefined;
}

export interface BlockContentConfig {
  isEditing: boolean;
  content: any;
  styles: BlockStyleConfig;
}

export class BlockService {
  private blocks: Map<string, Block>;
  private activeBlocks: Map<string, BlockContentConfig>;

  constructor() {
    this.blocks = new Map();
    this.activeBlocks = new Map();
  }

  // Initialize a block instance
  public initBlock(block: Block): void {
    this.blocks.set(block.id, block);
    this.activeBlocks.set(block.id, {
      isEditing: false,
      content: block.content,
      styles: block.styles || {}
    });
  }

  // Get block configuration
  public getBlockConfig(blockId: string): BlockContentConfig | null {
    return this.activeBlocks.get(blockId) || null;
  }

  // Update block content
  public async updateBlockContent(blockId: string, content: any): Promise<void> {
    const block = this.blocks.get(blockId);
    const config = this.activeBlocks.get(blockId);

    if (!block || !config) {
      throw new Error('Block not found');
    }

    config.content = content;
    block.content = content;

    await this.saveBlock(blockId);
  }

  // Update block styles
  public async updateBlockStyles(blockId: string, styles: Partial<BlockStyleConfig>): Promise<void> {
    const block = this.blocks.get(blockId);
    const config = this.activeBlocks.get(blockId);

    if (!block || !config) {
      throw new Error('Block not found');
    }

    config.styles = {
      ...config.styles,
      ...styles
    };

    // Convert styles to string-only format for block storage
    const storageStyles: { [key: string]: string } = {};
    Object.entries(config.styles).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        storageStyles[key] = value.join(' ');
      } else if (value !== undefined) {
        storageStyles[key] = value;
      }
    });

    block.styles = storageStyles;
    await this.saveBlock(blockId);
  }

  // Toggle edit mode for a block
  public toggleEditMode(blockId: string): void {
    const config = this.activeBlocks.get(blockId);
    if (config) {
      config.isEditing = !config.isEditing;
    }
  }

  // Save block changes to backend
  private async saveBlock(blockId: string): Promise<void> {
    const block = this.blocks.get(blockId);
    if (!block) {
      throw new Error('Block not found');
    }

    try {
      await updateBlock(blockId, block as BlockTypes);
    } catch (error) {
      console.error('Failed to save block:', error);
      throw error;
    }
  }

  // Remove block instance
  public removeBlock(blockId: string): void {
    this.blocks.delete(blockId);
    this.activeBlocks.delete(blockId);
  }

  // Get computed styles for a block
  public getComputedStyles(blockId: string): string {
    const config = this.activeBlocks.get(blockId);
    if (!config?.styles) return '';

    const styleArray: string[] = [];
    const { padding, margin, backgroundColor, textColor } = config.styles;

    if (padding) styleArray.push(`padding: ${padding}`);
    if (margin) styleArray.push(`margin: ${margin}`);
    if (backgroundColor) styleArray.push(`background-color: ${backgroundColor}`);
    if (textColor) styleArray.push(`color: ${textColor}`);

    return styleArray.join('; ');
  }

  // Get CSS classes for a block
  public getBlockClasses(blockId: string): string[] {
    const config = this.activeBlocks.get(blockId);
    const classes: string[] = ['block'];

    if (!config) return classes;

    if (config.isEditing) classes.push('is-editing');
    if (config.styles?.customClasses) {
      classes.push(...config.styles.customClasses);
    }
    if (config.styles?.animation) {
      classes.push(`animate-${config.styles.animation}`);
    }

    return classes;
  }
}

// Export singleton instance
export const blockService = new BlockService();