"use client"

import React, { useState, useRef, useEffect } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { restrictToVerticalAxis, restrictToWindowEdges } from '@dnd-kit/modifiers';

import { Block, BlockTypes, FlexContainerBlock } from '../../types/block';
import { getBlocks, updateBlock } from '../../utils/appwrite';
import EditorSidebar from './EditorSidebar';
import EditorCanvas from './EditorCanvas';
import EditorToolbar from './EditorToolbar';
import EditorProperties from './EditorProperties';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '../../components/ui/resizable';
import { But<PERSON> } from '../../components/ui/button';
import { Pencil } from 'lucide-react';

interface DragDropEditorProps {
  pageId?: string;
  initialBlocks?: string[];
  onSave?: (blockIds: string[]) => void;
}

export default function DragDropEditor({ pageId, initialBlocks = [], onSave }: DragDropEditorProps) {
  // State for blocks in the canvas
  const [canvasBlocks, setCanvasBlocks] = useState<string[]>(initialBlocks);

  // State for available blocks from the library
  const [availableBlocks, setAvailableBlocks] = useState<Block[]>([]);

  // State for the currently selected block
  const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null);

  // State for loading and error states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for tracking if there are unsaved changes
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // State for the editor mode (edit, preview)
  const [editorMode, setEditorMode] = useState<'edit' | 'preview'>('edit');

  // Ref for the canvas element
  const canvasRef = useRef<HTMLDivElement>(null);

  // Fetch available blocks on component mount
  useEffect(() => {
    const fetchAvailableBlocks = async () => {
      try {
        setLoading(true);
        const fetchedBlocks = await getBlocks();

        // Map Appwrite documents to Block objects
        const mappedBlocks: Block[] = fetchedBlocks.map(doc => ({
          id: doc.$id,
          type: doc.type,
          componentName: doc.componentName,
          title: doc.title,
          description: doc.description,
          createdAt: new Date(doc.$createdAt),
          updatedAt: new Date(doc.$updatedAt),
          content: doc.content,
          styles: doc.styles
        }));

        setAvailableBlocks(mappedBlocks);
      } catch (err) {
        setError('Failed to load available blocks');
        console.error('Error fetching blocks:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchAvailableBlocks();
  }, []);

  // Handle adding a block to the canvas
  const handleAddBlock = (blockId: string) => {
    setCanvasBlocks(prev => [...prev, blockId]);
    setHasUnsavedChanges(true);
  };

  // Handle removing a block from the canvas
  const handleRemoveBlock = (blockId: string, parentId?: string) => {
    if (parentId) {
      setAvailableBlocks(prevBlocks =>
        prevBlocks.map(block => {
          if (block.id === parentId && block.type === 'flex-container') {
            const containerBlock = block as FlexContainerBlock;
            return {
              ...containerBlock,
              content: {
                ...containerBlock.content,
                children: containerBlock.content.children.filter(id => id !== blockId),
              },
            };
          }
          return block;
        })
      );
    } else {
      setCanvasBlocks(prev => prev.filter(id => id !== blockId));
    }
    if (selectedBlockId === blockId) {
      setSelectedBlockId(null);
    }
    setHasUnsavedChanges(true);
  };

  // Handle selecting a block
  const handleSelectBlock = (blockId: string) => {
    setSelectedBlockId(blockId);
  };

  // Handle updating a block
  const handleUpdateBlock = async (blockId: string, updatedBlock: BlockTypes) => {
    try {
      await updateBlock(blockId, updatedBlock);

      // Update the available blocks list
      setAvailableBlocks(prevBlocks =>
        prevBlocks.map(block =>
          block.id === blockId ? updatedBlock : block
        )
      );

      setHasUnsavedChanges(true);
    } catch (err) {
      console.error('Error updating block:', err);
    }
  };

  // Configure DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag end for reordering blocks and adding new blocks
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) return;

    const activeId = active.id.toString();
    const overId = over.id.toString();
    
    const activeIsLibraryItem = activeId.startsWith('library-');
    const blockId = activeIsLibraryItem ? activeId.replace('library-', '') : activeId;

    // Find the parents of the active and over items
    const activeParentId = active.data.current?.parentId;
    const overParentId = over.data.current?.isContainer ? overId : over.data.current?.parentId;

    // Scenario 1: Dropping a new block from the library
    if (activeIsLibraryItem) {
      // Add to a container
      if (overParentId) {
        setAvailableBlocks(prev =>
          prev.map(b => {
            if (b.id === overParentId && b.type === 'flex-container') {
              const container = b as FlexContainerBlock;
              const overIndex = over.data.current?.isContainer ? container.content.children.length : container.content.children.indexOf(overId);
              const newChildren = [...container.content.children];
              if (overIndex !== -1) {
                newChildren.splice(overIndex, 0, blockId);
              } else {
                newChildren.push(blockId);
              }
              return { ...container, content: { ...container.content, children: newChildren } };
            }
            return b;
          })
        );
      } else { // Add to the main canvas
        const overIndex = canvasBlocks.indexOf(overId);
        if (overIndex !== -1) {
          setCanvasBlocks(prev => {
            const newBlocks = [...prev];
            newBlocks.splice(overIndex + 1, 0, blockId);
            return newBlocks;
          });
        } else {
          setCanvasBlocks(prev => [...prev, blockId]);
        }
      }
      setSelectedBlockId(blockId);
      setHasUnsavedChanges(true);
      return;
    }

    // Scenario 2: Moving an existing block
    if (activeId === overId) return;

    // Case 1: Reordering within the same container (or root)
    if (activeParentId === overParentId) {
      if (activeParentId) { // Reorder within a FlexContainer
        setAvailableBlocks(prev =>
          prev.map(b => {
            if (b.id === activeParentId && b.type === 'flex-container') {
              const container = b as FlexContainerBlock;
              const oldIndex = container.content.children.indexOf(activeId);
              const newIndex = container.content.children.indexOf(overId);
              if (oldIndex !== -1 && newIndex !== -1) {
                return { ...container, content: { ...container.content, children: arrayMove(container.content.children, oldIndex, newIndex) } };
              }
            }
            return b;
          })
        );
      } else { // Reorder in the main canvas
        const oldIndex = canvasBlocks.indexOf(activeId);
        const newIndex = canvasBlocks.indexOf(overId);
        if (oldIndex !== -1 && newIndex !== -1) {
          setCanvasBlocks(prev => arrayMove(prev, oldIndex, newIndex));
        }
      }
    } else { // Case 2: Moving between containers or to/from the root
      // Remove from the old parent
      if (activeParentId) {
        setAvailableBlocks(prev =>
          prev.map(b => {
            if (b.id === activeParentId && b.type === 'flex-container') {
              const container = b as FlexContainerBlock;
              return { ...container, content: { ...container.content, children: container.content.children.filter(id => id !== activeId) } };
            }
            return b;
          })
        );
      } else {
        setCanvasBlocks(prev => prev.filter(id => id !== activeId));
      }

      // Add to the new parent
      if (overParentId) {
        setAvailableBlocks(prev =>
          prev.map(b => {
            if (b.id === overParentId && b.type === 'flex-container') {
              const container = b as FlexContainerBlock;
              const overIndex = over.data.current?.isContainer ? container.content.children.length : container.content.children.indexOf(overId);
              const newChildren = [...container.content.children];
              if (overIndex !== -1) {
                newChildren.splice(overIndex, 0, activeId);
              } else {
                newChildren.push(activeId);
              }
              return { ...container, content: { ...container.content, children: newChildren } };
            }
            return b;
          })
        );
      } else { // Add to the main canvas
        const overIndex = canvasBlocks.indexOf(overId);
        if (overIndex !== -1) {
          setCanvasBlocks(prev => {
            const newBlocks = [...prev];
            newBlocks.splice(overIndex, 0, activeId);
            return newBlocks;
          });
        } else {
          setCanvasBlocks(prev => [...prev, activeId]);
        }
      }
    }
    setHasUnsavedChanges(true);
  };

  // Handle saving the page
  const handleSave = () => {
    if (onSave) {
      onSave(canvasBlocks);
      setHasUnsavedChanges(false);
    }
  };

  // Handle toggling editor mode
  const handleToggleMode = () => {
    setEditorMode(prev => prev === 'edit' ? 'preview' : 'edit');
  };

  // Get the selected block
  const selectedBlock = selectedBlockId
    ? availableBlocks.find(block => block.id === selectedBlockId)
    : null;

  if (loading) {
    return <div className="p-8 text-center">Loading editor...</div>;
  }

  if (error) {
    return <div className="p-8 text-center text-red-500">{error}</div>;
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
      modifiers={[restrictToVerticalAxis, restrictToWindowEdges]}
    >
      <div className="flex flex-col h-screen">
        {/* Editor Toolbar */}
        <EditorToolbar
          onSave={handleSave}
          hasUnsavedChanges={hasUnsavedChanges}
          editorMode={editorMode}
          onToggleMode={handleToggleMode}
        />

        {/* Resizable 3-column layout */}
        <ResizablePanelGroup
          direction="horizontal"
          className="flex-1 overflow-hidden"
        >
          {/* Left panel - Block library */}
          <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
            <div className="h-full overflow-hidden">
              <EditorSidebar
                blocks={availableBlocks}
                onAddBlock={handleAddBlock}
              />
            </div>
          </ResizablePanel>

          <ResizableHandle withHandle />

          {/* Middle panel - Canvas */}
          <ResizablePanel defaultSize={50}>
            <div className="h-full overflow-hidden">
              <SortableContext
                items={canvasBlocks}
                strategy={verticalListSortingStrategy}
              >
                <EditorCanvas
                  ref={canvasRef}
                  blocks={canvasBlocks}
                  availableBlocks={availableBlocks}
                  selectedBlockId={selectedBlockId}
                  onSelectBlock={handleSelectBlock}
                  onRemoveBlock={handleRemoveBlock}
                  onUpdateBlock={handleUpdateBlock}
                  isEditing={editorMode === 'edit'}
                />
              </SortableContext>
            </div>
          </ResizablePanel>

          <ResizableHandle withHandle />

          {/* Right panel - Properties */}
          <ResizablePanel defaultSize={30} minSize={20} maxSize={40}>
            <div className="h-full overflow-hidden">
              {editorMode === 'edit' ? (
                <EditorProperties
                  selectedBlock={selectedBlock as BlockTypes}
                  onUpdateBlock={handleUpdateBlock}
                />
              ) : (
                <div className="h-full flex items-center justify-center bg-muted/50 text-muted-foreground">
                  <div className="text-center p-4">
                    <p>Properties panel is hidden in preview mode</p>
                    <Button
                      onClick={handleToggleMode}
                      className="mt-2"
                      variant="default"
                      size="sm"
                    >
                      <Pencil className="h-4 w-4 mr-1" />
                      Switch to Edit Mode
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </DndContext>
  );
}