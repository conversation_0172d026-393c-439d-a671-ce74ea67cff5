#!/bin/bash

# Script to replace images from mitc_images to img folder
# This script maps and replaces similar content between the two directories

MITC_DIR="/home/<USER>/Documents/Webdev/mitc2/public/assets/mitc_images"
IMG_DIR="/home/<USER>/Documents/Webdev/mitc2/public/assets/img"

echo "=== Starting systematic image replacement ==="
echo "Source: $MITC_DIR"
echo "Target: $IMG_DIR"
echo ""

# Function to replace images by mapping source to target files
replace_images() {
    local source_dir="$1"
    local target_dir="$2"
    local mapping_type="$3"
    
    echo "Processing: $mapping_type"
    echo "From: $source_dir"
    echo "To: $target_dir"
    
    if [ ! -d "$source_dir" ]; then
        echo "  ❌ Source directory does not exist: $source_dir"
        return
    fi
    
    if [ ! -d "$target_dir" ]; then
        echo "  ❌ Target directory does not exist: $target_dir"
        return
    fi
    
    local source_files=($(find "$source_dir" -maxdepth 1 -type f \( -name "*.jpg" -o -name "*.png" -o -name "*.jpeg" -o -name "*.gif" -o -name "*.svg" \) | sort))
    local target_files=($(find "$target_dir" -maxdepth 1 -type f \( -name "*.jpg" -o -name "*.png" -o -name "*.jpeg" -o -name "*.gif" -o -name "*.svg" \) | sort))
    
    echo "  Source files: ${#source_files[@]}"
    echo "  Target files: ${#target_files[@]}"
    
    # Replace files one by one
    local count=0
    for source_file in "${source_files[@]}"; do
        if [ $count -lt ${#target_files[@]} ]; then
            local target_file="${target_files[$count]}"
            echo "  Replacing: $(basename "$target_file") with $(basename "$source_file")"
            cp "$source_file" "$target_file"
            ((count++))
        else
            echo "  ⚠️  No more target files to replace with $(basename "$source_file")"
        fi
    done
    
    echo "  ✅ Replaced $count files"
    echo ""
}

# 1. Replace blog images
replace_images "$MITC_DIR/blog" "$IMG_DIR/blog" "Blog Images"

# 2. Replace team images
replace_images "$MITC_DIR/team" "$IMG_DIR/team" "Team Images"

# 3. Replace services images (mitc has 'services', img has 'service')
replace_images "$MITC_DIR/services" "$IMG_DIR/service" "Service Images"

# 4. Copy root level images to appropriate locations
echo "Processing: Root Level Images"
if [ -f "$MITC_DIR/Building.jpeg" ]; then
    # Find a suitable background image to replace
    bg_files=($(find "$IMG_DIR/bg" -name "*.jpg" -o -name "*.png" | head -1))
    if [ ${#bg_files[@]} -gt 0 ]; then
        echo "  Replacing background with Building.jpeg"
        cp "$MITC_DIR/Building.jpeg" "${bg_files[0]}"
    fi
fi

if [ -f "$MITC_DIR/it-consult.jpg" ]; then
    # Find a suitable hero image to replace
    hero_files=($(find "$IMG_DIR/hero" -name "*.jpg" -o -name "*.png" | head -1))
    if [ ${#hero_files[@]} -gt 0 ]; then
        echo "  Replacing hero image with it-consult.jpg"
        cp "$MITC_DIR/it-consult.jpg" "${hero_files[0]}"
    fi
fi

if [ -f "$MITC_DIR/videoProd.jpeg" ]; then
    # Find a suitable project image to replace
    project_files=($(find "$IMG_DIR/project" -name "*.jpg" -o -name "*.png" | head -1))
    if [ ${#project_files[@]} -gt 0 ]; then
        echo "  Replacing project image with videoProd.jpeg"
        cp "$MITC_DIR/videoProd.jpeg" "${project_files[0]}"
    fi
fi

# 5. Copy UI elements
if [ -d "$MITC_DIR/ui" ] && [ -d "$IMG_DIR/logo" ]; then
    echo "Processing: UI/Logo Images"
    ui_files=($(find "$MITC_DIR/ui" -name "*.svg" -o -name "*.png"))
    for ui_file in "${ui_files[@]}"; do
        echo "  Copying $(basename "$ui_file") to logo directory"
        cp "$ui_file" "$IMG_DIR/logo/"
    done
fi

# 6. Replace specific service/product related images
if [ -d "$MITC_DIR/Products" ]; then
    echo "Processing: Product Images to Project Directory"
    product_files=($(find "$MITC_DIR/Products" -type f \( -name "*.jpg" -o -name "*.png" \)))
    project_files=($(find "$IMG_DIR/project" -type f \( -name "*.jpg" -o -name "*.png" \) | head -${#product_files[@]}))
    
    for i in "${!product_files[@]}"; do
        if [ $i -lt ${#project_files[@]} ]; then
            echo "  Replacing $(basename "${project_files[$i]}") with $(basename "${product_files[$i]}")"
            cp "${product_files[$i]}" "${project_files[$i]}"
        fi
    done
fi

# 7. Replace testimonial images with faces
if [ -d "$MITC_DIR/faces" ]; then
    echo "Processing: Face Images to Testimonial Directory"
    face_files=($(find "$MITC_DIR/faces" -type f \( -name "*.jpg" -o -name "*.png" \)))
    testimonial_files=($(find "$IMG_DIR/testimonial" -type f \( -name "*.jpg" -o -name "*.png" \) | head -${#face_files[@]}))
    
    for i in "${!face_files[@]}"; do
        if [ $i -lt ${#testimonial_files[@]} ]; then
            echo "  Replacing $(basename "${testimonial_files[$i]}") with $(basename "${face_files[$i]}")"
            cp "${face_files[$i]}" "${testimonial_files[$i]}"
        fi
    done
fi

echo "=== Image replacement completed! ==="
echo ""
echo "Summary:"
echo "- Blog images replaced"
echo "- Team images replaced" 
echo "- Service images replaced"
echo "- Root level images distributed to appropriate directories"
echo "- UI/Logo elements copied"
echo "- Product images mapped to project directory"
echo "- Face images mapped to testimonial directory"
echo ""
echo "A backup of the original img folder was created as img_backup_[timestamp]"
