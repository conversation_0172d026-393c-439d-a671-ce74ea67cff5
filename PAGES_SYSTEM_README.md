# Pages and Dynamic Pages System Documentation

This document provides a comprehensive overview of the pages and dynamic pages system designed for the CMS.

## Overview

The pages system provides a complete solution for rendering both static and dynamic content with:
- **Dynamic page rendering** with multiple templates
- **Block-based content** system for flexible layouts
- **SEO optimization** with structured data
- **Social sharing** integration
- **Comment system** with moderation
- **Related content** suggestions
- **Navigation components** (breadcrumbs, TOC)

## Architecture

```
src/components/pages/
├── page-renderer.tsx           # Main page rendering component
├── dynamic-page.tsx           # Dynamic page loading and routing
├── blocks/                    # Block-based content system
│   └── block-renderer.tsx     # Renders different content blocks
├── seo/                       # SEO and meta components
│   └── seo-head.tsx          # SEO head tags and structured data
├── navigation/                # Navigation components
│   ├── breadcrumb-nav.tsx    # Breadcrumb navigation
│   └── table-of-contents.tsx # Table of contents
├── social/                    # Social features
│   └── share-buttons.tsx     # Social sharing buttons
├── content/                   # Content-related components
│   └── related-content.tsx   # Related content suggestions
├── comments/                  # Comment system
│   └── comment-section.tsx   # Comment display and form
└── index.ts                   # Component exports
```

## Core Components

### PageRenderer
The main component for rendering page content with various templates and features.

**Features:**
- Multiple template support (default, landing, about, contact, blocks)
- SEO optimization
- Social sharing
- Author information
- Related content
- Comment system
- Table of contents
- Breadcrumb navigation

**Usage:**
```tsx
<PageRenderer
  page={pageData}
  template="default"
  showComments={true}
  showRelated={true}
  showTOC={true}
  showBreadcrumbs={true}
  showShare={true}
  showAuthor={true}
  showMeta={true}
  isPreview={false}
  onEdit={handleEdit}
  relatedPages={relatedPages}
/>
```

### DynamicPage
Handles dynamic page loading, routing, and error states.

**Features:**
- Dynamic content loading
- Preview mode support
- Version control
- Localization support
- Error handling
- Loading states
- 404 handling

**Usage:**
```tsx
<DynamicPage
  slug="about-us"
  preview={false}
  version="latest"
  locale="en"
  params={{ category: 'tech' }}
  searchParams={{ utm_source: 'social' }}
/>
```

### BlockRenderer
Renders different types of content blocks for flexible page layouts.

**Supported Block Types:**
- **Text Blocks:** text, heading, paragraph, quote
- **Media Blocks:** image, gallery, video
- **Code Blocks:** syntax-highlighted code
- **UI Blocks:** separator, button, card, grid
- **Content Blocks:** testimonial, team, pricing, FAQ, contact
- **Embed Blocks:** external content, forms

**Usage:**
```tsx
<BlockRenderer
  block={{
    id: 'block-1',
    type: 'image',
    content: {
      src: '/image.jpg',
      alt: 'Description',
      caption: 'Image caption'
    },
    order: 1
  }}
  isEditing={false}
  onEdit={handleEdit}
  onDelete={handleDelete}
/>
```

## Page Templates

### Default Template
Standard page layout with content and sidebar.

### Landing Page Template
Marketing-focused layout with hero section and call-to-action blocks.

### About Page Template
Personal or company information with author details.

### Contact Page Template
Contact information and form layout.

### Blocks Template
Completely flexible layout using content blocks.

## Block System

### Text Blocks

#### Heading Block
```tsx
{
  type: 'heading',
  content: {
    text: 'Page Title',
    level: 2, // 1-6
    id: 'page-title'
  }
}
```

#### Quote Block
```tsx
{
  type: 'quote',
  content: {
    text: 'Inspirational quote text',
    author: 'Author Name',
    source: 'Source Publication'
  }
}
```

### Media Blocks

#### Image Block
```tsx
{
  type: 'image',
  content: {
    src: '/path/to/image.jpg',
    alt: 'Image description',
    caption: 'Image caption',
    width: 800,
    height: 600,
    alignment: 'center' // 'left', 'center', 'right'
  }
}
```

#### Gallery Block
```tsx
{
  type: 'gallery',
  content: {
    images: [
      { src: '/image1.jpg', alt: 'Image 1', caption: 'Caption 1' },
      { src: '/image2.jpg', alt: 'Image 2', caption: 'Caption 2' }
    ],
    columns: 3
  }
}
```

### UI Blocks

#### Button Block
```tsx
{
  type: 'button',
  content: {
    text: 'Click Me',
    href: '/destination',
    variant: 'default', // 'default', 'outline', 'destructive'
    size: 'default', // 'sm', 'default', 'lg'
    icon: '<IconComponent />'
  }
}
```

#### Card Block
```tsx
{
  type: 'card',
  content: {
    title: 'Card Title',
    description: 'Card description',
    image: { src: '/image.jpg', alt: 'Card image' },
    link: { href: '/read-more', text: 'Read More' },
    badge: 'Featured'
  }
}
```

### Content Blocks

#### Testimonial Block
```tsx
{
  type: 'testimonial',
  content: {
    quote: 'Amazing service!',
    author: 'John Doe',
    role: 'CEO',
    company: 'Tech Corp',
    avatar: '/avatar.jpg'
  }
}
```

#### Team Block
```tsx
{
  type: 'team',
  content: {
    members: [
      {
        name: 'Jane Smith',
        role: 'Developer',
        bio: 'Full-stack developer',
        avatar: '/jane.jpg'
      }
    ],
    columns: 3
  }
}
```

#### Pricing Block
```tsx
{
  type: 'pricing',
  content: {
    plans: [
      {
        name: 'Basic',
        price: 29,
        period: 'month',
        description: 'Perfect for starters',
        features: ['Feature 1', 'Feature 2'],
        featured: false,
        buttonText: 'Get Started'
      }
    ],
    columns: 3
  }
}
```

## SEO System

### SEOHead Component
Comprehensive SEO optimization with meta tags and structured data.

**Features:**
- Open Graph tags
- Twitter Card tags
- Structured data (JSON-LD)
- Canonical URLs
- Robot directives
- Favicon management

**Usage:**
```tsx
<SEOHead
  title="Page Title"
  description="Page description"
  image="/og-image.jpg"
  url="/page-url"
  type="article"
  publishedTime={new Date()}
  modifiedTime={new Date()}
  author="Author Name"
  tags={['tag1', 'tag2']}
/>
```

### Structured Data
Automatic generation of structured data for better SEO.

#### Article Schema
```json
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "Article Title",
  "author": {
    "@type": "Person",
    "name": "Author Name"
  },
  "datePublished": "2024-01-01T00:00:00Z",
  "dateModified": "2024-01-01T00:00:00Z"
}
```

#### Breadcrumb Schema
```json
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": "https://example.com/"
    }
  ]
}
```

## Navigation Components

### BreadcrumbNav
Accessible breadcrumb navigation with structured data.

**Features:**
- Automatic path generation
- Custom labels
- Home link option
- Structured data
- Responsive design

**Usage:**
```tsx
<BreadcrumbNav
  items={[
    { label: 'Home', href: '/' },
    { label: 'Blog', href: '/blog' },
    { label: 'Article Title', href: '/blog/article', current: true }
  ]}
  showHome={true}
  maxItems={5}
  showSchema={true}
/>
```

### TableOfContents
Interactive table of contents with scroll tracking.

**Features:**
- Auto-generation from headings
- Active section highlighting
- Smooth scrolling
- Collapsible interface
- Hierarchical structure

**Usage:**
```tsx
<TableOfContents
  items={tocItems}
  title="Table of Contents"
  collapsible={true}
  maxDepth={3}
  showNumbers={false}
  sticky={true}
/>
```

## Social Features

### ShareButtons
Comprehensive social sharing with multiple platforms.

**Supported Platforms:**
- Facebook
- Twitter
- LinkedIn
- WhatsApp
- Telegram
- Email
- Copy link
- Native sharing (mobile)

**Usage:**
```tsx
<ShareButtons
  url="/page-url"
  title="Page Title"
  description="Page description"
  hashtags={['tag1', 'tag2']}
  variant="default" // 'default', 'minimal', 'floating'
  platforms={['facebook', 'twitter', 'linkedin']}
  showLabels={true}
  showCounts={false}
/>
```

## Comment System

### CommentSection
Full-featured comment system with moderation and replies.

**Features:**
- Nested comments (configurable depth)
- Comment moderation
- Voting system (like/dislike)
- Reply functionality
- Edit/delete capabilities
- Guest commenting
- Spam protection

**Usage:**
```tsx
<CommentSection
  pageId="page-123"
  comments={comments}
  allowComments={true}
  requireApproval={true}
  allowReplies={true}
  allowVoting={true}
  allowGuests={true}
  maxDepth={3}
  onCommentSubmit={handleCommentSubmit}
  onCommentUpdate={handleCommentUpdate}
  onCommentDelete={handleCommentDelete}
  onCommentVote={handleCommentVote}
/>
```

## Content Features

### RelatedContent
Smart content recommendations based on categories, tags, and popularity.

**Features:**
- Multiple layout options (grid, list, carousel)
- Smart filtering
- Trending content
- Popular tags
- Author-based suggestions

**Usage:**
```tsx
<RelatedContent
  pages={relatedPages}
  currentPage={currentPage}
  title="Related Articles"
  maxItems={6}
  layout="grid" // 'grid', 'list', 'carousel'
  showAuthor={true}
  showDate={true}
  showStats={true}
  showExcerpt={true}
/>
```

## Hooks and Utilities

### useDynamicPage
Hook for loading and managing dynamic page data.

```tsx
const { page, loading, error, refetch } = useDynamicPage('page-slug', {
  preview: false,
  version: 'latest',
  locale: 'en',
  autoRefresh: false,
  refreshInterval: 30000
})
```

### useTableOfContents
Hook for generating table of contents from content.

```tsx
const tocItems = useTableOfContents(contentElement, {
  selectors: 'h1, h2, h3, h4, h5, h6',
  maxDepth: 6,
  includeLevel1: true
})
```

### useBreadcrumbs
Hook for automatic breadcrumb generation.

```tsx
const breadcrumbs = useBreadcrumbs(pathname, customItems, {
  labels: { '/blog': 'Blog', '/about': 'About Us' },
  excludePaths: ['/admin'],
  includeHome: true
})
```

## Performance Optimizations

### Lazy Loading
- Images with intersection observer
- Comments loaded on demand
- Related content progressive loading

### Caching
- Page data caching
- Image optimization
- Static generation support

### Bundle Optimization
- Code splitting by template
- Dynamic imports for heavy components
- Tree shaking for unused features

## Accessibility

### ARIA Support
- Proper heading hierarchy
- Navigation landmarks
- Screen reader announcements
- Keyboard navigation

### Focus Management
- Skip links
- Focus trapping in modals
- Logical tab order

### Color and Contrast
- High contrast mode support
- Color-blind friendly design
- Sufficient color contrast ratios

## Customization

### Custom Templates
Create new page templates by extending the base renderer:

```tsx
function CustomTemplate({ page }: { page: PageData }) {
  return (
    <div className="custom-layout">
      <header>{page.title}</header>
      <main>{page.content}</main>
      <aside>Custom sidebar</aside>
    </div>
  )
}
```

### Custom Blocks
Add new block types to the block renderer:

```tsx
function CustomBlock({ content }: { content: any }) {
  return (
    <div className="custom-block">
      {/* Custom block implementation */}
    </div>
  )
}

// Register in BlockRenderer switch statement
case 'custom':
  return <CustomBlock content={block.content} />
```

### Styling
All components use Tailwind CSS classes and can be customized:

```tsx
<PageRenderer
  className="custom-page-styles"
  page={page}
/>
```

## API Integration

### Page Data Structure
```typescript
interface PageData {
  id: string
  title: string
  slug: string
  content?: string
  excerpt?: string
  template: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  seoTitle?: string
  seoDescription?: string
  featuredImage?: string
  author: {
    id: string
    name: string
    image?: string
    bio?: string
  }
  category?: {
    id: string
    name: string
    slug: string
    color?: string
  }
  tags?: Array<{
    id: string
    name: string
    slug: string
    color?: string
  }>
  blocks?: Array<{
    id: string
    type: string
    content: any
    order: number
  }>
  _count?: {
    views: number
    comments: number
    likes: number
  }
}
```

### API Endpoints
```
GET /api/pages/[slug]           # Get page by slug
GET /api/pages/[slug]/related   # Get related pages
POST /api/pages/[slug]/comments # Submit comment
PUT /api/pages/[slug]/view      # Track page view
```

## Best Practices

### Performance
1. Use lazy loading for images and heavy components
2. Implement proper caching strategies
3. Optimize images and media
4. Use code splitting for different templates

### SEO
1. Always include proper meta tags
2. Use structured data for rich snippets
3. Implement proper heading hierarchy
4. Add alt text to all images

### Accessibility
1. Test with screen readers
2. Ensure keyboard navigation works
3. Use semantic HTML elements
4. Provide sufficient color contrast

### Security
1. Sanitize user-generated content
2. Implement CSRF protection
3. Validate all inputs
4. Use content security policies

This pages system provides a complete, flexible, and performant solution for rendering dynamic content in your CMS application.