# Blog Management System

A comprehensive blog management system built with Next.js, TypeScript, Prisma, and modern UI components.

## Features

### Admin Interface
- **Rich Text Editor**: Tiptap-based editor with formatting, media insertion, and real-time preview
- **Post Management**: Create, edit, delete, and manage blog posts with advanced filtering and search
- **Category Management**: Organize posts with hierarchical categories and color coding
- **Tag Management**: Flexible tagging system with color customization
- **SEO Optimization**: Built-in SEO fields for meta titles, descriptions, and keywords
- **Publishing Controls**: Draft, published, scheduled, and archived post statuses
- **Media Management**: Image upload and management for featured images and content
- **Bulk Operations**: Bulk edit, publish, and delete operations
- **Analytics Dashboard**: Post performance metrics and insights

### Frontend Features
- **Responsive Design**: Mobile-first design that works on all devices
- **Advanced Filtering**: Filter posts by categories, tags, date ranges, and search terms
- **Pagination**: Efficient pagination with customizable page sizes
- **Social Sharing**: Built-in social media sharing buttons
- **Related Posts**: Automatic related post suggestions
- **SEO Optimized**: Proper meta tags, structured data, and Open Graph support
- **Performance**: Optimized loading with skeleton screens and lazy loading

## Architecture

### Database Schema
The blog system uses Prisma with the following main models:

```prisma
model Post {
  id            String    @id @default(cuid())
  title         String
  slug          String    @unique
  excerpt       String?
  content       String?
  featuredImage String?
  status        PostStatus @default(DRAFT)
  publishedAt   DateTime?
  scheduledAt   DateTime?
  readTime      Int?
  viewCount     Int       @default(0)
  seoTitle      String?
  seoDescription String?
  seoKeywords   String[]
  
  author        User      @relation(fields: [authorId], references: [id])
  authorId      String
  category      Category? @relation(fields: [categoryId], references: [id])
  categoryId    String?
  tags          Tag[]
  comments      Comment[]
  blocks        Block[]
  
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}

model Category {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  color       String?
  image       String?
  parentId    String?
  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  posts       Post[]
  creator     User     @relation(fields: [createdBy], references: [id])
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Tag {
  id        String   @id @default(cuid())
  name      String
  slug      String   @unique
  color     String?
  posts     Post[]
  creator   User     @relation(fields: [createdBy], references: [id])
  createdBy String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
```

### API Endpoints

#### Blog Posts
- `GET /api/blog` - List posts with filtering and pagination
- `POST /api/blog` - Create a new post
- `GET /api/blog/[id]` - Get a specific post
- `PUT /api/blog/[id]` - Update a post
- `DELETE /api/blog/[id]` - Delete a post
- `GET /api/blog/search` - Search posts
- `GET /api/blog/analytics` - Get blog analytics

#### Categories
- `GET /api/blog/categories` - List all categories
- `POST /api/blog/categories` - Create a new category
- `PUT /api/blog/categories/[id]` - Update a category
- `DELETE /api/blog/categories/[id]` - Delete a category

#### Tags
- `GET /api/blog/tags` - List all tags
- `POST /api/blog/tags` - Create a new tag
- `PUT /api/blog/tags/[id]` - Update a tag
- `DELETE /api/blog/tags/[id]` - Delete a tag

### Components Structure

```
src/components/
├── blog/                     # Frontend blog components
│   ├── blog-card.tsx         # Blog post card component
│   ├── blog-listing.tsx      # Main blog listing with filters
│   ├── blog-search.tsx       # Search component
│   ├── blog-filters.tsx      # Category and tag filters
│   ├── blog-pagination.tsx   # Pagination component
│   ├── blog-post-detail.tsx  # Individual post view
│   ├── social-share.tsx      # Social sharing buttons
│   ├── blog-layout.tsx       # Responsive layout utilities
│   └── blog-error-boundary.tsx # Error handling
├── admin-blog/               # Admin interface components
│   ├── enhanced-blog-form.tsx # Post creation/editing form
│   ├── enhanced-blog-list.tsx # Admin post management
│   ├── category-management.tsx # Category CRUD interface
│   ├── blog-editor.tsx       # Rich text editor
│   └── index.ts              # Component exports
└── ui/                       # Shared UI components
```

## Usage

### Admin Interface

#### Creating a Blog Post
1. Navigate to `/admin/blog`
2. Click "New Post"
3. Fill in the post details:
   - Title (auto-generates slug)
   - Excerpt
   - Content using the rich text editor
   - Featured image
   - Category and tags
   - SEO metadata
4. Choose publishing option:
   - Save as Draft
   - Publish Now
   - Schedule for Later

#### Managing Categories
1. Navigate to `/admin/blog/categories`
2. Create new categories with:
   - Name and slug
   - Description
   - Color coding
   - Hierarchical relationships
3. Edit or delete existing categories

### Frontend Usage

#### Blog Listing
The main blog page (`/blog`) displays:
- Responsive grid/list view toggle
- Search functionality
- Category and tag filters
- Pagination
- Featured post highlighting

#### Individual Posts
Post detail pages (`/blog/[slug]`) include:
- Full post content
- Author information
- Social sharing buttons
- Related posts
- Navigation to previous/next posts
- SEO optimization

## Development

### Running Tests
```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Adding New Features

#### Creating a New Blog Component
1. Create the component in the appropriate directory
2. Add TypeScript interfaces in `src/types/blog.ts`
3. Write tests in `src/__tests__/`
4. Export from the appropriate index file

#### Extending the API
1. Add new endpoints in `src/app/api/blog/`
2. Update the BlogService in `src/lib/services/blog.service.ts`
3. Add validation schemas using Zod
4. Write integration tests

### Performance Considerations

- **Database Queries**: Optimized with proper indexing and selective field loading
- **Image Optimization**: Next.js Image component with responsive sizing
- **Caching**: API responses cached where appropriate
- **Lazy Loading**: Components and images loaded on demand
- **Bundle Splitting**: Code splitting for admin and frontend components

## Security

- **Authentication**: All admin operations require authentication
- **Authorization**: Role-based access control
- **Input Validation**: Zod schemas for all API inputs
- **XSS Prevention**: Content sanitization in the rich text editor
- **CSRF Protection**: Built-in Next.js CSRF protection

## SEO Features

- **Meta Tags**: Automatic generation of meta titles and descriptions
- **Open Graph**: Social media preview optimization
- **Structured Data**: JSON-LD structured data for search engines
- **Sitemap**: Automatic sitemap generation for blog posts
- **Canonical URLs**: Proper canonical URL handling

## Deployment

The blog system is designed to work with any Next.js deployment platform:

1. **Database**: Ensure Prisma migrations are run
2. **Environment Variables**: Set up required environment variables
3. **Media Storage**: Configure image upload storage
4. **CDN**: Optional CDN setup for static assets

## Troubleshooting

### Common Issues

1. **Slug Conflicts**: Ensure unique slugs for posts and categories
2. **Image Upload**: Check file size limits and storage configuration
3. **Performance**: Monitor database query performance with large datasets
4. **SEO**: Verify meta tag generation and structured data

### Debug Mode
Enable debug logging by setting `NODE_ENV=development` and checking browser console for detailed error messages.
