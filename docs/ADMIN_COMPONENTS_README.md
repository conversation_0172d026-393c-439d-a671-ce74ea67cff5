# Admin Components Documentation

This document provides a comprehensive overview of the admin forms and UI components designed for the CMS system.

## Overview

The admin components are built with:
- **React Hook Form** for form management
- **Zod** for schema validation and type safety
- **Radix UI** components for accessibility
- **Tailwind CSS** for styling
- **Tanstack Table** for data tables

## Architecture

```
src/components/admin/
├── forms/                    # Form components
│   ├── shared/              # Reusable form components
│   ├── user-form.tsx        # User management form
│   ├── post-form.tsx        # Blog post form
│   ├── category-form.tsx    # Category form
│   ├── tag-form.tsx         # Tag form
│   ├── media-form.tsx       # Media upload/edit form
│   ├── page-form.tsx        # Static page form
│   ├── menu-form.tsx        # Navigation menu form
│   └── settings-form.tsx    # Site settings form
├── data-table/              # Data table components
│   ├── admin-data-table.tsx # Main data table component
│   └── columns.tsx          # Column definitions
├── dashboard/               # Dashboard components
│   └── admin-dashboard.tsx  # Main dashboard
└── index.ts                 # Component exports

src/lib/schemas/             # Zod validation schemas
├── user.schema.ts
├── post.schema.ts
├── category.schema.ts
├── tag.schema.ts
├── media.schema.ts
├── page.schema.ts
├── menu.schema.ts
├── form.schema.ts
├── setting.schema.ts
└── index.ts
```

## Schemas

### User Schema
```typescript
const CreateUserSchema = z.object({
  name: z.string().min(2).max(100),
  email: z.string().email(),
  password: z.string().min(8).optional(),
  role: z.enum(['ADMIN', 'EDITOR', 'AUTHOR', 'USER']),
  image: z.string().url().optional(),
})
```

### Post Schema
```typescript
const CreatePostSchema = z.object({
  title: z.string().min(2).max(200),
  slug: z.string().regex(/^[a-z0-9-]+$/),
  excerpt: z.string().min(10).max(500).optional(),
  content: z.string().min(50).optional(),
  status: z.enum(['DRAFT', 'PUBLISHED', 'ARCHIVED', 'SCHEDULED']),
  // ... more fields
})
```

## Form Components

### UserForm
Handles user creation, editing, and profile management.

**Features:**
- Role-based field visibility
- Password strength validation
- Profile picture upload
- Email verification status

**Usage:**
```tsx
<UserForm
  mode="create" // 'create' | 'edit' | 'profile'
  initialData={userData}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
  isLoading={isLoading}
/>
```

### PostForm
Comprehensive blog post creation and editing.

**Features:**
- Rich text content editing
- SEO optimization fields
- Featured image upload
- Category and tag selection
- Publishing workflow
- Auto-slug generation

**Usage:**
```tsx
<PostForm
  mode="create"
  initialData={postData}
  onSubmit={handleSubmit}
  categories={categories}
  tags={tags}
  authors={authors}
/>
```

### CategoryForm
Category management with hierarchy support.

**Features:**
- Parent-child relationships
- Color coding
- Image upload
- Slug generation
- Circular reference prevention

### MediaForm
File upload and media management.

**Features:**
- Drag & drop upload
- Multiple file support
- Progress tracking
- File type validation
- Metadata editing
- Thumbnail generation

### SettingsForm
Site-wide configuration management.

**Features:**
- Tabbed interface for different setting groups
- General, SEO, Social, Email, Appearance, Security, Performance, Analytics
- Real-time validation
- Preview functionality

## Shared Components

### FormFieldWrapper
Standardized form field container with label, description, and error handling.

### ImageUpload
Versatile image upload component with:
- Drag & drop support
- URL input option
- Media library integration
- Preview functionality

### SlugInput
Auto-generating URL slug input with manual override capability.

### ColorPicker
Color selection with preset colors and custom color input.

### DateTimePicker
Date and time selection with timezone support.

## Data Table

### AdminDataTable
Feature-rich data table component with:

**Features:**
- Sorting and filtering
- Column visibility toggle
- Bulk actions
- Pagination
- Search functionality
- Export/Import capabilities
- Row selection
- Responsive design

**Usage:**
```tsx
<AdminDataTable
  columns={userColumns}
  data={users}
  title="User Management"
  searchKey="name"
  onAdd={() => handleAdd()}
  onEdit={(user) => handleEdit(user)}
  onDelete={(users) => handleDelete(users)}
  filters={[
    {
      key: 'role',
      label: 'Role',
      options: roleOptions
    }
  ]}
/>
```

### Column Definitions
Pre-built column definitions for all data types:
- User columns with avatar and role badges
- Post columns with status indicators
- Category columns with color coding
- Media columns with file type icons
- Comment columns with approval status

## Dashboard

### AdminDashboard
Main dashboard component that orchestrates all admin functionality.

**Features:**
- Overview with statistics
- Tabbed interface for different content types
- Integrated forms and data tables
- Real-time data updates
- Bulk operations
- Export/Import functionality

## Validation

All forms use Zod schemas for:
- Type safety
- Runtime validation
- Error handling
- Data transformation

### Example Schema Usage
```typescript
const form = useForm({
  resolver: zodResolver(CreateUserSchema),
  defaultValues: {
    name: '',
    email: '',
    role: 'USER',
  },
})
```

## Styling

Components use Tailwind CSS with:
- Consistent spacing and typography
- Dark mode support
- Responsive design
- Accessibility features
- Custom component variants

## Accessibility

All components follow accessibility best practices:
- Proper ARIA labels
- Keyboard navigation
- Screen reader support
- Focus management
- Color contrast compliance

## Performance

Optimizations include:
- Lazy loading for large datasets
- Virtualization for long lists
- Debounced search
- Memoized components
- Efficient re-renders

## Usage Examples

### Basic Form Implementation
```tsx
import { UserForm } from '@/components/admin'

function UserManagement() {
  const handleSubmit = async (data) => {
    // API call to create/update user
    await createUser(data)
  }

  return (
    <UserForm
      mode="create"
      onSubmit={handleSubmit}
      onCancel={() => router.back()}
    />
  )
}
```

### Data Table with Custom Actions
```tsx
import { AdminDataTable, userColumns } from '@/components/admin'

function UserList() {
  const handleBulkDelete = async (users) => {
    await deleteUsers(users.map(u => u.id))
  }

  return (
    <AdminDataTable
      columns={userColumns}
      data={users}
      onDelete={handleBulkDelete}
      actions={[
        {
          label: 'Export CSV',
          onClick: (users) => exportToCsv(users),
          icon: <Download className="h-4 w-4" />
        }
      ]}
    />
  )
}
```

### Complete Dashboard Integration
```tsx
import { AdminDashboard } from '@/components/admin'

function AdminPage() {
  const [data, setData] = useState({
    users: [],
    posts: [],
    categories: [],
    // ... other data
  })

  const handleDataChange = (type, change) => {
    // Update data based on the change
    setData(prev => ({
      ...prev,
      [type]: updateData(prev[type], change)
    }))
  }

  return (
    <AdminDashboard
      initialData={data}
      onDataChange={handleDataChange}
    />
  )
}
```

## Customization

### Adding New Form Fields
1. Update the Zod schema
2. Add the field to the form component
3. Update the TypeScript types

### Custom Validation
```typescript
const customSchema = baseSchema.extend({
  customField: z.string().refine(
    (val) => customValidation(val),
    { message: 'Custom validation failed' }
  )
})
```

### Styling Customization
Components accept className props and can be styled with Tailwind utilities or custom CSS.

## Best Practices

1. **Always use Zod schemas** for form validation
2. **Handle loading states** appropriately
3. **Provide meaningful error messages**
4. **Use optimistic updates** where possible
5. **Implement proper error boundaries**
6. **Test accessibility** with screen readers
7. **Validate on both client and server**
8. **Use TypeScript** for type safety

## Contributing

When adding new components:
1. Follow the existing patterns
2. Add proper TypeScript types
3. Include Zod validation
4. Add accessibility features
5. Write comprehensive tests
6. Update documentation

## Dependencies

Key dependencies used:
- `react-hook-form` - Form management
- `zod` - Schema validation
- `@hookform/resolvers` - Zod integration
- `@tanstack/react-table` - Data tables
- `@radix-ui/*` - UI primitives
- `lucide-react` - Icons
- `date-fns` - Date formatting
- `react-dropzone` - File uploads

This admin system provides a complete, type-safe, and accessible interface for managing all aspects of the CMS.