# Typography System - Next.js font/local Setup

This project uses a comprehensive typography system with custom fonts loaded via Next.js `font/local` for optimal performance.

## 🎨 **Available Fonts**

### **1. Montserrat** 
*Geometric Sans-serif • Perfect for Headings & Display Text*
- **Weights**: 100, 200, 300, 400, 500, 600, 700, 800, 900
- **Styles**: Normal, Italic
- **Use Cases**: Headlines, titles, display text, branding

### **2. Manrope**
*Modern Sans-serif • Optimized for Body Text*
- **Weights**: 200, 300, 400, 500, 600, 700, 800
- **Styles**: Normal only
- **Use Cases**: Body text, paragraphs, general content

### **3. Outfit**
*Clean Sans-serif • Great for UI Elements*
- **Weights**: 100, 200, 300, 400, 500, 600, 700, 800, 900
- **Styles**: Normal only
- **Use Cases**: UI components, buttons, forms, navigation

### **4. JetBrains Mono**
*Monospace • Perfect for Code*
- **Weights**: 100, 200, 300, 400, 500, 600, 700, 800
- **Styles**: Normal, Italic
- **Use Cases**: Code blocks, technical content, data tables

---

## 🚀 **Usage Methods**

### **Method 1: Tailwind CSS Classes**

```tsx
// Font Families
<h1 className="font-montserrat">Heading with Montserrat</h1>
<p className="font-manrope">Body text with Manrope</p>
<div className="font-outfit">UI element with Outfit</div>
<code className="font-jetbrains-mono">Code with JetBrains Mono</code>

// Typography Scale
<h1 className="text-display-1 font-montserrat font-black">Large Display</h1>
<h2 className="text-heading-1 font-montserrat font-bold">Main Heading</h2>
<p className="text-body font-manrope">Regular body text</p>
<span className="text-caption font-manrope">Small caption</span>

// Combining with other Tailwind classes
<h1 className="text-display-2 font-montserrat font-bold text-blue-600 mb-6">
  Stylized Heading
</h1>
```

### **Method 2: Typography Components**

```tsx
import { 
  Typography, 
  Heading, 
  Display, 
  Body, 
  Code, 
  Caption, 
  Overline 
} from '@/components/ui/typography'

function MyComponent() {
  return (
    <div>
      {/* Flexible Typography component */}
      <Typography variant="heading-1" font="montserrat" className="text-blue-600">
        Custom Typography
      </Typography>

      {/* Specialized components */}
      <Display level={1}>Hero Display Text</Display>
      <Heading level={2}>Section Heading</Heading>
      <Body size="large">Important body text</Body>
      <Body>Regular paragraph text</Body>
      <Code inline>inline code</Code>
      <Code>
        {`// Code block
const example = "Hello World";`}
      </Code>
      <Caption>Small supporting text</Caption>
      <Overline>CATEGORY LABEL</Overline>
    </div>
  )
}
```

### **Method 3: Custom CSS Classes**

```tsx
// Typography scale classes (defined in globals.css)
<h1 className="text-display-1 font-montserrat">Display 1</h1>
<h1 className="text-display-2 font-montserrat">Display 2</h1>
<h1 className="text-display-3 font-montserrat">Display 3</h1>
<h1 className="text-heading-1 font-montserrat">Heading 1</h1>
<h2 className="text-heading-2 font-montserrat">Heading 2</h2>
<h3 className="text-heading-3 font-montserrat">Heading 3</h3>
<h4 className="text-heading-4 font-montserrat">Heading 4</h4>
<h5 className="text-heading-5 font-montserrat">Heading 5</h5>
<h6 className="text-heading-6 font-montserrat">Heading 6</h6>
<p className="text-body-large font-manrope">Large body</p>
<p className="text-body font-manrope">Regular body</p>
<p className="text-body-small font-manrope">Small body</p>
<span className="text-caption font-manrope">Caption</span>
<span className="text-overline font-manrope">OVERLINE</span>
```

---

## 📏 **Typography Scale**

| Class | Size | Line Height | Letter Spacing | Use Case |
|-------|------|-------------|----------------|----------|
| `text-display-1` | 72px (4.5rem) | 1.1 | -0.02em | Hero sections |
| `text-display-2` | 60px (3.75rem) | 1.1 | -0.02em | Large displays |
| `text-display-3` | 48px (3rem) | 1.15 | -0.01em | Feature headings |
| `text-heading-1` | 40px (2.5rem) | 1.2 | -0.01em | Page titles |
| `text-heading-2` | 32px (2rem) | 1.25 | -0.005em | Section headers |
| `text-heading-3` | 28px (1.75rem) | 1.3 | normal | Subsection headers |
| `text-heading-4` | 24px (1.5rem) | 1.35 | normal | Content headers |
| `text-heading-5` | 20px (1.25rem) | 1.4 | normal | Minor headers |
| `text-heading-6` | 18px (1.125rem) | 1.45 | normal | Small headers |
| `text-body-large` | 18px (1.125rem) | 1.6 | normal | Important content |
| `text-body` | 16px (1rem) | 1.6 | normal | Standard text |
| `text-body-small` | 14px (0.875rem) | 1.5 | normal | Secondary text |
| `text-caption` | 12px (0.75rem) | 1.4 | normal | Fine print |
| `text-overline` | 12px (0.75rem) | 1.4 | 0.1em | Labels (uppercase) |

---

## 🎯 **Best Practices**

### **Font Pairing Guidelines**

1. **Headings**: Use **Montserrat** for maximum impact
   ```tsx
   <h1 className="font-montserrat font-bold">Main Title</h1>
   ```

2. **Body Text**: Use **Manrope** for readability
   ```tsx
   <p className="font-manrope">Long form content...</p>
   ```

3. **UI Elements**: Use **Outfit** for clean interface elements
   ```tsx
   <button className="font-outfit font-medium">Button Text</button>
   ```

4. **Code**: Use **JetBrains Mono** for technical content
   ```tsx
   <pre className="font-jetbrains-mono">const code = true;</pre>
   ```

### **Responsive Typography**

```tsx
// Responsive display text
<h1 className="text-heading-1 md:text-display-3 lg:text-display-1 font-montserrat">
  Responsive Hero
</h1>

// Responsive body text
<p className="text-body-small md:text-body lg:text-body-large font-manrope">
  Responsive paragraph
</p>
```

### **Accessibility Considerations**

```tsx
// Ensure proper heading hierarchy
<h1>Main Page Title</h1>
  <h2>Section Title</h2>
    <h3>Subsection Title</h3>

// Use appropriate contrast
<p className="text-body font-manrope text-foreground">
  High contrast text
</p>

// Provide font size options
<div className="text-body md:text-body-large font-manrope">
  Larger text on bigger screens
</div>
```

---

## 🧪 **Testing the Setup**

To verify the typography setup is working:

1. **Import the test component**:
   ```tsx
   import { TypographyTest } from '@/components/test/typography-test'
   ```

2. **Add to any page**:
   ```tsx
   export default function TestPage() {
     return <TypographyTest />
   }
   ```

3. **Check font loading**:
   - Open browser dev tools
   - Network tab → look for `.ttf` files
   - Verify fonts load successfully

---

## ⚡ **Performance Benefits**

✅ **Faster Loading**: Local fonts eliminate external requests  
✅ **Better Caching**: Fonts cached with your app  
✅ **Reduced Layout Shift**: Proper font display controls  
✅ **Offline Support**: Fonts work without internet  
✅ **TypeScript Support**: Full type safety  

---

## 🔧 **Customization**

### **Adding New Font Weights**

1. Add the font file to `/public/assets/fonts/`
2. Update the font configuration in `/lib/fonts.ts`
3. Add the new weight to the `src` array

### **Custom Typography Classes**

Add new classes to `/app/globals.css`:

```css
.text-micro {
  font-size: 0.625rem;
  line-height: 1.3;
}

.text-mega {
  font-size: 6rem;
  line-height: 1.0;
  letter-spacing: -0.03em;
}
```

### **Font Variable Names**

Current CSS variables available:
- `--font-jetbrains-mono`
- `--font-manrope`
- `--font-montserrat`
- `--font-outfit`

---

## 📁 **File Structure**

```
├── lib/fonts.ts                      # Font configurations
├── components/
│   ├── ui/typography.tsx             # Typography components
│   └── test/typography-test.tsx      # Typography showcase
├── app/
│   ├── globals.css                   # Typography classes
│   └── layout.tsx                    # Font loading
├── tailwind.config.ts                # Tailwind font config
└── public/assets/fonts/
    ├── JetBrains_Mono/              # Monospace fonts
    ├── Manrope/                     # Body text fonts
    ├── Montserrat/                  # Heading fonts
    └── Outfit/                      # UI fonts
```

---

## 🎨 **Design System Integration**

This typography system integrates seamlessly with:

- **Tailwind CSS**: All fonts available as utility classes
- **CSS Variables**: Easy theming and customization
- **React Components**: Type-safe component API
- **Responsive Design**: Built-in responsive utilities
- **Dark Mode**: Compatible with theme switching

The typography system provides a solid foundation for consistent, beautiful, and performant text rendering across your entire application.