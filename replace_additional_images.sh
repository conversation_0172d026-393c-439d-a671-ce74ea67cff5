#!/bin/bash

# Supplementary script to map remaining mitc_images content to appropriate directories

MITC_DIR="/home/<USER>/Documents/Webdev/mitc2/public/assets/mitc_images"
IMG_DIR="/home/<USER>/Documents/Webdev/mitc2/public/assets/img"

echo "=== Additional Image Replacement Script ==="
echo ""

# 1. Map Gallery images to case-study and work directories
if [ -d "$MITC_DIR/gallery" ]; then
    echo "🖼️ Processing Gallery Images..."
    
    # Get gallery images
    gallery_files=($(find "$MITC_DIR/gallery" -maxdepth 1 -type f \( -name "*.jpg" -o -name "*.png" \) | sort))
    
    # Replace case-study images
    case_study_files=($(find "$IMG_DIR/case-study" -type f \( -name "*.jpg" -o -name "*.png" \) | sort))
    echo "  Replacing case-study images with gallery content..."
    for i in "${!case_study_files[@]}"; do
        if [ $i -lt ${#gallery_files[@]} ]; then
            echo "    $(basename "${case_study_files[$i]}") <- $(basename "${gallery_files[$i]}")"
            cp "${gallery_files[$i]}" "${case_study_files[$i]}"
        fi
    done
    
    # Replace work images with remaining gallery content
    work_files=($(find "$IMG_DIR/work" -type f \( -name "*.jpg" -o -name "*.png" \) | sort))
    echo "  Replacing work images with gallery content..."
    start_idx=${#case_study_files[@]}
    for i in "${!work_files[@]}"; do
        gallery_idx=$((start_idx + i))
        if [ $gallery_idx -lt ${#gallery_files[@]} ]; then
            echo "    $(basename "${work_files[$i]}") <- $(basename "${gallery_files[$gallery_idx]}")"
            cp "${gallery_files[$gallery_idx]}" "${work_files[$i]}"
        fi
    done
    
    echo "  ✅ Gallery images mapped to case-study and work directories"
fi

# 2. Map BoardroomSolution images to about directory
if [ -d "$MITC_DIR/BoardroomSolution" ]; then
    echo ""
    echo "🏢 Processing BoardroomSolution Images..."
    
    boardroom_files=($(find "$MITC_DIR/BoardroomSolution" -type f \( -name "*.jpg" -o -name "*.png" \) | sort))
    about_files=($(find "$IMG_DIR/about" -type f \( -name "*.jpg" -o -name "*.png" \) | sort))
    
    for i in "${!about_files[@]}"; do
        if [ $i -lt ${#boardroom_files[@]} ]; then
            echo "  $(basename "${about_files[$i]}") <- $(basename "${boardroom_files[$i]}")"
            cp "${boardroom_files[$i]}" "${about_files[$i]}"
        fi
    done
    
    echo "  ✅ BoardroomSolution images mapped to about directory"
fi

# 3. Copy Partner logos to appropriate locations
if [ -d "$MITC_DIR/Partners" ]; then
    echo ""
    echo "🤝 Processing Partner Logos..."
    
    # Create a partners directory in logo if it doesn't exist
    mkdir -p "$IMG_DIR/logo/partners"
    
    partner_files=($(find "$MITC_DIR/Partners" -type f \( -name "*.jpg" -o -name "*.png" -o -name "*.svg" \)))
    for partner_file in "${partner_files[@]}"; do
        echo "  Copying $(basename "$partner_file") to logo/partners/"
        cp "$partner_file" "$IMG_DIR/logo/partners/"
    done
    
    echo "  ✅ Partner logos copied to logo/partners directory"
fi

# 4. Map Training images to enhance content
if [ -d "$MITC_DIR/Training" ]; then
    echo ""
    echo "🎓 Processing Training Images..."
    
    training_files=($(find "$MITC_DIR/Training" -type f \( -name "*.jpg" -o -name "*.png" \)))
    # Use some misc or others directory images
    others_files=($(find "$IMG_DIR/others" -type f \( -name "*.jpg" -o -name "*.png" \) | head -${#training_files[@]}))
    
    for i in "${!training_files[@]}"; do
        if [ $i -lt ${#others_files[@]} ]; then
            echo "  $(basename "${others_files[$i]}") <- $(basename "${training_files[$i]}")"
            cp "${training_files[$i]}" "${others_files[$i]}"
        fi
    done
    
    echo "  ✅ Training images mapped to others directory"
fi

# 5. Map specialist service images
echo ""
echo "🔧 Processing Specialist Services..."

# SoftwareDevelopment
if [ -d "$MITC_DIR/SoftwareDevelopment" ]; then
    sw_files=($(find "$MITC_DIR/SoftwareDevelopment" -type f \( -name "*.jpg" -o -name "*.png" \)))
    if [ ${#sw_files[@]} -gt 0 ]; then
        # Find a suitable service image to replace
        service_files=($(find "$IMG_DIR/service" -name "*.jpg" -o -name "*.png" | head -1))
        if [ ${#service_files[@]} -gt 0 ]; then
            echo "  Software Development: $(basename "${service_files[0]}") <- $(basename "${sw_files[0]}")"
            cp "${sw_files[0]}" "${service_files[0]}"
        fi
    fi
fi

# WebDevelopment
if [ -d "$MITC_DIR/Webdevelopment" ]; then
    web_files=($(find "$MITC_DIR/Webdevelopment" -type f \( -name "*.jpg" -o -name "*.png" \)))
    if [ ${#web_files[@]} -gt 0 ]; then
        # Find another service image to replace
        service_files=($(find "$IMG_DIR/service" -name "*.jpg" -o -name "*.png" | head -2 | tail -1))
        if [ ${#service_files[@]} -gt 0 ]; then
            echo "  Web Development: $(basename "${service_files[0]}") <- $(basename "${web_files[0]}")"
            cp "${web_files[0]}" "${service_files[0]}"
        fi
    fi
fi

# LiveStreaming and SmartCity to demo directory
if [ -d "$MITC_DIR/LiveStreaming" ]; then
    live_files=($(find "$MITC_DIR/LiveStreaming" -type f \( -name "*.jpg" -o -name "*.png" -o -name "*.jpeg" \)))
    demo_files=($(find "$IMG_DIR/demo" -type f \( -name "*.jpg" -o -name "*.png" \) | head -${#live_files[@]}))
    
    echo "  LiveStreaming to demo directory:"
    for i in "${!live_files[@]}"; do
        if [ $i -lt ${#demo_files[@]} ]; then
            echo "    $(basename "${demo_files[$i]}") <- $(basename "${live_files[$i]}")"
            cp "${live_files[$i]}" "${demo_files[$i]}"
        fi
    done
fi

if [ -d "$MITC_DIR/smartcity" ]; then
    smart_files=($(find "$MITC_DIR/smartcity" -type f \( -name "*.jpg" -o -name "*.png" -o -name "*.jpeg" \)))
    # Use remaining demo files or project files
    remaining_demo_files=($(find "$IMG_DIR/demo" -type f \( -name "*.jpg" -o -name "*.png" \) | tail -${#smart_files[@]}))
    
    echo "  SmartCity to demo directory:"
    for i in "${!smart_files[@]}"; do
        if [ $i -lt ${#remaining_demo_files[@]} ]; then
            echo "    $(basename "${remaining_demo_files[$i]}") <- $(basename "${smart_files[$i]}")"
            cp "${smart_files[$i]}" "${remaining_demo_files[$i]}"
        fi
    done
fi

echo ""
echo "✅ Additional mapping completed!"
echo ""
echo "Final Summary:"
echo "  🖼️ Gallery images -> case-study & work directories"
echo "  🏢 BoardroomSolution -> about directory"  
echo "  🤝 Partner logos -> logo/partners directory"
echo "  🎓 Training images -> others directory"
echo "  💻 Software/Web Development -> service directory"
echo "  📡 LiveStreaming & SmartCity -> demo directory"
echo ""
echo "🎉 All available mitc_images content has been systematically mapped to the img folder!"
