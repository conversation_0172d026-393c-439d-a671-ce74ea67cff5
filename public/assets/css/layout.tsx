
import NextTopLoader from "nextjs-toploader"
import { AuthProvider } from "@/components/providers/session-provider"
import { Toaster } from "@/components/ui/toaster"

import AppData from "@data/app.json";

import type { Metadata } from "next";

export const metadata: Metadata = {
	title: "Motshwanelo IT Consultant | Web Development, Digital Marketing and Cyber Security Services in Botswana",
	description:
		"Motshwanelo IT Consultant is a leading IT company in Botswana offering Web Development, Digital Marketing and Cyber Security Services. We help businesses grow online and protect themselves from cyber threats.",
	keywords: [
		"Web Development",
		"Digital Marketing",
		"Cyber Security",
		"Botswana",
		"IT Company",
		"Motshwanelo IT Consultant",
	],
	creator: "Motshwanelo IT Consultant",
	publisher: "Motshwanelo IT Consultant",
	openGraph: {
		title: "Motshwanelo IT Consultant | Web Development, Digital Marketing and Cyber Security Services in Botswana",
		description:
			"Motshwanelo IT Consultant is a leading IT company in Botswana offering Web Development, Digital Marketing and Cyber Security Services. We help businesses grow online and protect themselves from cyber threats.",
		url: "https://motshwaneloit.com",
		siteName: "Motshwanelo IT Consultant",
		images: [
			{
				url: "https://motshwaneloit.com/images/logo.png",
				width: 1200,
				height: 630,
			},
		],
		type: "website",
	},
	twitter: {
		card: "summary_large_image",
		title: "Motshwanelo IT Consultant | Web Development, Digital Marketing and Cyber Security Services in Botswana",
		description:
			"Motshwanelo IT Consultant is a leading IT company in Botswana offering Web Development, Digital Marketing and Cyber Security Services. We help businesses grow online and protect themselves from cyber threats.",
		creator: "@motshwaneloit",
		images: [
			{
				url: "https://motshwaneloit.com/images/logo.png",
			},
		],
	},
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en">
			<body className="body">
				<AuthProvider>
					<NextTopLoader
					  color={"#e89d1a"}
						height={4}
						showSpinner={true}
					/>
					{children}
					<Toaster />
				</AuthProvider>
			</body>
		</html>
	);
}
