/* 3D Components Styles */

/* Loading placeholder */
.loading-placeholder {
  width: 100%;
  height: 80vh;
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #050505;
  color: white;
  font-size: 18px;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}

.loading-placeholder::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, #03276e, #e89d1a, transparent);
  bottom: 30%;
  left: 0;
  animation: loading-animation 2s infinite;
}

@keyframes loading-animation {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Common styles for all 3D scenes */
.data-center-scene,
.tech-stack-scene,
.hero-scene {
  position: relative;
  width: 100%;
  height: 80vh;
  min-height: 500px;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.scene-instructions {
  position: absolute;
  bottom: 20px;
  left: 0;
  width: 100%;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  pointer-events: none;
  z-index: 10;
}

/* Data Center Scene Styles */
.server-info-panel {
  background-color: rgba(3, 39, 110, 0.9);
  color: white;
  padding: 15px;
  border-radius: 8px;
  width: 200px;
  font-family: 'Inter', sans-serif;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  border-left: 3px solid #e89d1a;
}

.server-info-panel h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 5px;
}

.server-info-panel ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.server-info-panel li {
  font-size: 12px;
  margin-bottom: 5px;
  display: flex;
  justify-content: space-between;
}

.status-active {
  color: #4caf50;
  font-weight: 600;
}

/* Tech Stack Scene Styles */
.category-selector {
  position: absolute;
  top: 20px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 15px;
  z-index: 10;
}

.category-button {
  background-color: transparent;
  border: 2px solid;
  border-radius: 30px;
  padding: 8px 15px;
  color: white;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-button:hover {
  transform: translateY(-3px);
}

.category-button.active {
  color: white;
}

.category-button i {
  font-size: 16px;
}

.tech-info-panel {
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 15px;
  border-radius: 8px;
  width: 200px;
  font-family: 'Inter', sans-serif;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.tech-info-panel h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #03276e;
}

.tech-info-panel p {
  font-size: 12px;
  margin: 0;
  line-height: 1.4;
}

/* Hero Scene Styles */
.hero-scene {
  height: 100vh;
  min-height: 600px;
  border-radius: 0;
  box-shadow: none;
}

.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 0;
  width: 100%;
  text-align: center;
  color: white;
  font-size: 14px;
  animation: bounce 2s infinite;
  pointer-events: none;
  z-index: 10;
}

.scroll-indicator i {
  font-size: 20px;
  margin-top: 5px;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.scroll-html-content {
  pointer-events: none;
  width: 100%;
}

.scroll-section {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.cta-button {
  pointer-events: auto;
  margin-top: 60vh;
}

.cta-button a {
  display: inline-block;
  background-color: #e89d1a;
  color: white;
  padding: 15px 30px;
  border-radius: 30px;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.cta-button a:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  max-width: 1000px;
  width: 90%;
  margin-top: 60vh;
  pointer-events: auto;
}

.service-card {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 25px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.service-card i {
  font-size: 30px;
  color: #03276e;
  margin-bottom: 15px;
}

.service-card h3 {
  font-size: 18px;
  margin: 0 0 10px 0;
  color: #333;
}

.service-card p {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.service-card a {
  display: inline-block;
  color: #03276e;
  font-weight: 600;
  text-decoration: none;
  font-size: 14px;
}

.contact-form {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 30px;
  border-radius: 10px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  margin-top: 50vh;
  pointer-events: auto;
}

.contact-form h3 {
  text-align: center;
  margin: 0 0 20px 0;
  color: #03276e;
  font-size: 24px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
}

.form-group textarea {
  height: 120px;
  resize: none;
}

.contact-form button {
  width: 100%;
  padding: 12px;
  background-color: #03276e;
  color: white;
  border: none;
  border-radius: 5px;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.contact-form button:hover {
  background-color: #021d4e;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .category-selector {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .services-grid {
    grid-template-columns: 1fr;
  }
  
  .category-button {
    font-size: 12px;
    padding: 6px 12px;
  }
  
  .category-button i {
    font-size: 14px;
  }
  
  .contact-form {
    width: 90%;
  }
}