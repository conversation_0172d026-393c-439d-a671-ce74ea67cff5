/* Animated Components Styles */

/* ===== Animated Services Showcase ===== */
.services-categories {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 40px;
}

.category-item {
  flex: 1;
  min-width: 150px;
  text-align: center;
  padding: 20px 15px;
  cursor: pointer;
  border-radius: 10px;
  transition: all 0.3s ease;
  margin: 0 10px 20px;
  background-color: white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.category-item:hover {
  transform: translateY(-5px);
}

.category-item.active {
  background-color: #f9fafc;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.category-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  color: white;
  font-size: 24px;
}

.category-item h4 {
  font-size: 16px;
  margin: 0;
  color: #333;
}

.services-showcase-content {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}

.services-list {
  width: 30%;
  min-width: 250px;
}

.services-list h3 {
  color: #03276e;
  font-weight: 600;
  margin-bottom: 20px;
  border-bottom: 2px solid #e89d1a;
  padding-bottom: 15px;
}

.services-list ul {
  list-style: none;
  padding: 0;
}

.service-list-item {
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  background-color: white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.service-list-item:hover {
  background-color: rgba(3, 39, 110, 0.05);
  transform: translateX(5px);
}

.service-list-item.active {
  background-color: #03276e;
  color: white;
  transform: translateX(10px);
}

.service-list-item h4 {
  font-size: 16px;
  margin: 0;
}

.service-details-wrapper {
  flex: 1;
  min-width: 300px;
  position: relative;
  overflow: hidden;
}

.service-details-wrapper.animating {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-details-wrapper.slide-right {
  animation: slideInRight 0.5s forwards;
}

.service-details-wrapper.slide-left {
  animation: slideInLeft 0.5s forwards;
}

@keyframes slideInRight {
  from {
    transform: translateX(-30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.service-image {
  position: relative;
  height: 250px;
  overflow: hidden;
  border-radius: 10px;
  margin-bottom: 20px;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.service-details-wrapper:hover .service-image img {
  transform: scale(1.05);
}

.service-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(3, 39, 110, 0.9), transparent);
  padding: 30px 20px 20px;
  color: white;
}

.service-overlay h3 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.service-info {
  background-color: white;
  border-radius: 10px;
  padding: 25px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.service-features {
  margin: 25px 0;
}

.service-features h4 {
  color: #6F6F87;
  font-size: 18px;
  margin-bottom: 15px;
  font-weight: 500;
}

.service-features ul {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.service-features li {
  display: flex;
  align-items: center;
}

.service-features li i {
  color: #e89d1a;
  margin-right: 10px;
}

/* ===== Interactive Tech Stack ===== */
.tech-categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
}

.tech-category {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 25px;
  background-color: white;
  border: 2px solid;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.tech-category:hover {
  transform: translateY(-5px);
}

.tech-category.active {
  color: white;
}

.tech-category.active .category-icon {
  background-color: white;
  color: inherit;
}

.tech-category .category-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #03276e;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 20px;
}

.tech-category h4 {
  font-size: 16px;
  margin: 0;
  color: inherit;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

.tech-card {
  background-color: white;
  border: 2px solid transparent;
  border-radius: 10px;
  padding: 25px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.5s ease;
  opacity: 0;
  transform: translateY(20px);
  position: relative;
  overflow: hidden;
}

.tech-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.tech-card.hovered {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.tech-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.tech-logo img {
  max-width: 100%;
  max-height: 100%;
}

.tech-info {
  position: relative;
  z-index: 2;
}

.tech-info h4 {
  margin-bottom: 10px;
  font-weight: 600;
}

.tech-info p {
  color: #6F6F87;
  font-size: 14px;
  margin: 0;
}

.tech-details {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  animation: fadeIn 0.3s forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tech-details h5 {
  margin-bottom: 15px;
  color: #03276e;
  font-weight: 600;
}

.tech-details ul {
  list-style: none;
  padding: 0;
  text-align: center;
}

.tech-details li {
  margin-bottom: 8px;
  color: #6F6F87;
  font-size: 14px;
}

.tech-shine {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  animation: shine 8s infinite linear;
  pointer-events: none;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }
  100% {
    transform: translateX(100%) rotate(30deg);
  }
}

/* ===== Animated Data Center ===== */
.animated-data-center {
  background-color: #f9fafc;
}

.data-center-visualization {
  position: relative;
  height: 500px;
  margin: 0 auto;
  perspective: 1000px;
}

.data-center-floor {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transform: rotateX(60deg) rotateZ(0deg);
  background-color: #111;
  border-radius: 10px;
  overflow: hidden;
}

.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(to right, rgba(50, 50, 50, 0.3) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(50, 50, 50, 0.3) 1px, transparent 1px);
  background-size: 50px 50px;
}

.server-racks-row {
  position: absolute;
  display: flex;
  justify-content: space-around;
  width: 90%;
  left: 5%;
}

.front-row {
  top: 30%;
}

.back-row {
  top: 60%;
}

.server-rack {
  position: relative;
  width: 80px;
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
  transform-style: preserve-3d;
}

.server-rack:hover {
  transform: translateY(-10px);
}

.server-rack.active {
  transform: translateY(-20px);
}

.rack-body {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
}

.rack-front {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #333;
  transform: translateZ(20px);
  display: flex;
  flex-direction: column;
  padding: 5px;
  gap: 2px;
}

.rack-top {
  position: absolute;
  width: 100%;
  height: 40px;
  background-color: #222;
  transform: rotateX(90deg) translateZ(20px);
}

.rack-side {
  position: absolute;
  width: 40px;
  height: 100%;
  background-color: #2a2a2a;
  transform: rotateY(90deg) translateZ(20px);
}

.server-unit {
  flex: 1;
  background-color: #444;
  position: relative;
  transition: all 0.3s ease;
}

.server-unit.active {
  background-color: #e89d1a;
}

.server-led {
  position: absolute;
  top: 50%;
  right: 5px;
  width: 4px;
  height: 4px;
  background-color: #00ff00;
  border-radius: 50%;
  transform: translateY(-50%);
  box-shadow: 0 0 5px #00ff00;
  animation: blink 2s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.rack-label {
  position: absolute;
  bottom: -25px;
  left: 0;
  width: 100%;
  text-align: center;
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.rack-details {
  position: absolute;
  top: -150px;
  left: 50%;
  transform: translateX(-50%);
  width: 250px;
  background-color: rgba(3, 39, 110, 0.9);
  color: white;
  padding: 15px;
  border-radius: 8px;
  z-index: 10;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  border-left: 3px solid #e89d1a;
  animation: fadeInDown 0.3s forwards;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate(-50%, -10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

.rack-details h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 5px;
}

.rack-details p {
  font-size: 12px;
  margin-bottom: 10px;
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.spec-item {
  font-size: 11px;
}

.spec-label {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2px;
}

.spec-value {
  font-weight: 600;
}

.data-packets {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.data-packet {
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: #03a9f4;
  border-radius: 50%;
  box-shadow: 0 0 5px #03a9f4;
  opacity: 0;
}

.packet-1 { animation: packet1 3s infinite; }
.packet-2 { animation: packet2 4s infinite 0.5s; }
.packet-3 { animation: packet3 3.5s infinite 1s; }
.packet-4 { animation: packet4 4.5s infinite 1.5s; }
.packet-5 { animation: packet5 3.2s infinite 2s; }
.packet-6 { animation: packet6 4.2s infinite 2.5s; }
.packet-7 { animation: packet7 3.7s infinite 3s; }
.packet-8 { animation: packet8 4.7s infinite 3.5s; }
.packet-9 { animation: packet9 3.3s infinite 4s; }
.packet-10 { animation: packet10 4.3s infinite 4.5s; }

@keyframes packet1 {
  0% { left: 20%; top: 30%; opacity: 0; }
  50% { opacity: 1; }
  100% { left: 40%; top: 60%; opacity: 0; }
}

@keyframes packet2 {
  0% { left: 40%; top: 60%; opacity: 0; }
  50% { opacity: 1; }
  100% { left: 60%; top: 30%; opacity: 0; }
}

@keyframes packet3 {
  0% { left: 60%; top: 30%; opacity: 0; }
  50% { opacity: 1; }
  100% { left: 80%; top: 60%; opacity: 0; }
}

@keyframes packet4 {
  0% { left: 80%; top: 60%; opacity: 0; }
  50% { opacity: 1; }
  100% { left: 20%; top: 30%; opacity: 0; }
}

@keyframes packet5 {
  0% { left: 30%; top: 40%; opacity: 0; }
  50% { opacity: 1; }
  100% { left: 70%; top: 40%; opacity: 0; }
}

@keyframes packet6 {
  0% { left: 70%; top: 40%; opacity: 0; }
  50% { opacity: 1; }
  100% { left: 30%; top: 40%; opacity: 0; }
}

@keyframes packet7 {
  0% { left: 40%; top: 30%; opacity: 0; }
  50% { opacity: 1; }
  100% { left: 40%; top: 60%; opacity: 0; }
}

@keyframes packet8 {
  0% { left: 60%; top: 60%; opacity: 0; }
  50% { opacity: 1; }
  100% { left: 60%; top: 30%; opacity: 0; }
}

@keyframes packet9 {
  0% { left: 50%; top: 30%; opacity: 0; }
  50% { opacity: 1; }
  100% { left: 50%; top: 60%; opacity: 0; }
}

@keyframes packet10 {
  0% { left: 50%; top: 60%; opacity: 0; }
  50% { opacity: 1; }
  100% { left: 50%; top: 30%; opacity: 0; }
}

.cooling-indicators {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.cooling-indicator {
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

.cooling-wave {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 2px solid rgba(0, 255, 255, 0.5);
  animation: wave 2s infinite;
}

@keyframes wave {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 50px;
    height: 50px;
    opacity: 0;
  }
}

.indicator-1 { left: 20%; top: 30%; }
.indicator-2 { left: 40%; top: 30%; }
.indicator-3 { left: 60%; top: 30%; }
.indicator-4 { left: 20%; top: 60%; }
.indicator-5 { left: 40%; top: 60%; }
.indicator-6 { left: 60%; top: 60%; }

.data-center-instructions {
  position: absolute;
  bottom: -30px;
  left: 0;
  width: 100%;
  text-align: center;
  color: #6F6F87;
  font-size: 14px;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-top: 30px;
}

.benefit-item {
  background-color: white;
  border-radius: 10px;
  padding: 25px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.benefit-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #f0f5ff;
  color: #03276e;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  font-size: 24px;
}

.benefit-item h4 {
  color: #333;
  margin-bottom: 10px;
  font-weight: 600;
}

.benefit-item p {
  color: #6F6F87;
  font-size: 14px;
  margin: 0;
}

/* ===== Animated Hero ===== */
.animated-hero {
  position: relative;
  height: 100vh;
  min-height: 700px;
  overflow: hidden;
  background-color: #050505;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.parallax-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

.layer-1 {
  background-image: url('/img/hero/layer-1.jpg');
  opacity: 0.2;
}

.layer-2 {
  background-image: url('/img/hero/layer-2.png');
  opacity: 0.4;
}

.layer-3 {
  background-image: url('/img/hero/layer-3.png');
  opacity: 0.6;
}

.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background-color: white;
  border-radius: 50%;
  opacity: 0.5;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-10px) translateX(10px);
  }
  50% {
    transform: translateY(0) translateX(20px);
  }
  75% {
    transform: translateY(10px) translateX(10px);
  }
}

.particle-1 { left: 10%; top: 20%; animation: float 8s infinite; }
.particle-2 { left: 20%; top: 40%; animation: float 12s infinite 1s; }
.particle-3 { left: 30%; top: 60%; animation: float 10s infinite 2s; }
.particle-4 { left: 40%; top: 30%; animation: float 9s infinite 3s; }
.particle-5 { left: 50%; top: 70%; animation: float 11s infinite 4s; }
.particle-6 { left: 60%; top: 40%; animation: float 13s infinite 5s; }
.particle-7 { left: 70%; top: 20%; animation: float 7s infinite 6s; }
.particle-8 { left: 80%; top: 50%; animation: float 14s infinite 7s; }
.particle-9 { left: 90%; top: 30%; animation: float 9s infinite 8s; }
.particle-10 { left: 15%; top: 25%; animation: float 10s infinite 9s; }
/* Add more particles as needed */

.hero-content {
  position: relative;
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 70%;
  text-align: center;
  color: white;
  padding: 0 20px;
}

.animated-logo {
  position: relative;
  width: 200px;
  height: 100px;
  margin-bottom: 30px;
}

.logo-m {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
}

.logo-part {
  position: absolute;
  background-color: #03276e;
  transition: all 0.5s ease;
}

.m-left {
  left: 0;
  top: 0;
  width: 10px;
  height: 80px;
  animation: slideInLeft 0.5s forwards;
}

.m-right {
  right: 0;
  top: 0;
  width: 10px;
  height: 80px;
  animation: slideInRight 0.5s forwards;
}

.m-middle-1 {
  left: 50%;
  top: 0;
  width: 10px;
  height: 60px;
  transform: translateX(-50%) rotate(20deg);
  transform-origin: top;
  animation: slideInTop 0.5s 0.3s forwards;
}

.m-middle-2 {
  left: 50%;
  top: 0;
  width: 10px;
  height: 60px;
  transform: translateX(-50%) rotate(-20deg);
  transform-origin: top;
  animation: slideInTop 0.5s 0.3s forwards;
}

.logo-text {
  position: absolute;
  right: 0;
  top: 20px;
  width: 100px;
  height: 60px;
}

.i-letter {
  left: 0;
  top: 0;
  width: 10px;
  height: 60px;
  animation: slideInTop 0.5s 0.6s forwards;
}

.t-letter-1 {
  left: 30px;
  top: 0;
  width: 10px;
  height: 60px;
  animation: slideInTop 0.5s 0.8s forwards;
}

.t-letter-2 {
  left: 15px;
  top: 0;
  width: 40px;
  height: 10px;
  animation: slideInTop 0.5s 1s forwards;
}

@keyframes slideInTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.2;
}

.title-word {
  display: inline-block;
  margin: 0 10px;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.5s forwards;
}

.word-1 {
  animation-delay: 1.2s;
}

.word-2 {
  animation-delay: 1.4s;
}

.word-3 {
  animation-delay: 1.6s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 40px;
  color: #e89d1a;
  opacity: 0;
  animation: fadeIn 0.5s 1.8s forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

.hero-cta {
  display: flex;
  gap: 20px;
  opacity: 0;
  animation: fadeIn 0.5s 2s forwards;
}

.thm-btn.outline {
  background-color: transparent;
  border: 2px solid #e89d1a;
  color: #e89d1a;
}

.thm-btn.outline:hover {
  background-color: #e89d1a;
  color: white;
}

.hero-cards {
  position: absolute;
  bottom: 50px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 30px;
  z-index: 3;
}

.service-card {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 20px;
  width: 200px;
  text-align: center;
  color: white;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.service-card:hover {
  transform: translateY(-10px);
  background-color: rgba(255, 255, 255, 0.2);
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(3, 39, 110, 0.8);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  font-size: 20px;
}

.service-card h3 {
  margin-bottom: 10px;
  font-weight: 600;
}

.service-card p {
  font-size: 14px;
  margin: 0;
  opacity: 0.8;
}

.scroll-indicator {
  position: absolute;
  bottom: 20px;
  left: 0;
  width: 100%;
  text-align: center;
  color: white;
  z-index: 3;
  opacity: 0;
  animation: fadeIn 0.5s 2.2s forwards;
}

.scroll-arrow {
  margin-top: 10px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .services-showcase-content {
    flex-direction: column;
  }
  
  .services-list {
    width: 100%;
    margin-bottom: 30px;
  }
  
  .service-features ul {
    grid-template-columns: 1fr;
  }
  
  .benefits-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .hero-title {
    font-size: 3rem;
  }
  
  .hero-cards {
    flex-wrap: wrap;
    padding: 0 20px;
  }
  
  .service-card {
    width: calc(50% - 15px);
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .category-item {
    min-width: 120px;
  }
  
  .tech-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
  
  .data-center-visualization {
    height: 400px;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
  
  .hero-cta {
    flex-direction: column;
    gap: 10px;
  }
}

@media (max-width: 576px) {
  .tech-categories {
    flex-direction: column;
    align-items: center;
  }
  
  .tech-category {
    width: 80%;
  }
  
  .benefits-grid {
    grid-template-columns: 1fr;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .service-card {
    width: 100%;
  }
}