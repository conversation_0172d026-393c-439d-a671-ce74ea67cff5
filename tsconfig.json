{
  "compilerOptions": {    "target": "es2022",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    
    // Mild strictness settings
    "strict": false,
    "strictNullChecks": true,
    "noImplicitAny": false,
    "noImplicitThis": true,
    "alwaysStrict": true,
    "forceConsistentCasingInFileNames": true,

    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler", // Recommended for Next.js 14
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,

    "plugins": [
      {
        "name": "next"
      }
    ],

    "paths": {
      "@/*": ["./src/*"],
      "@components/*": ["./src/components/*"],
      "@data/*": ["./src/data/*"],
      "@common/*": ["./src/common/*"],
      "@lib/*": ["./src/lib/*"],
      "@library/*": ["./src/lib/*"],
      "@styles/*": ["./src/styles/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
