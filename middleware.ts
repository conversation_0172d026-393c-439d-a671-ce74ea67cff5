import { auth } from "./auth"
import { NextResponse } from "next/server"

// Define public routes that don't require authentication
const publicRoutes = [
  "/",
  "/auth/signin",
  "/auth/signup",
  "/auth/error",
  "/api/auth",
]

// Define admin-only routes
const adminRoutes = [
  "/admin",
]

// Define API routes that need authentication
const protectedApiRoutes = [
  "/api/admin",
  "/api/user",
]

export default auth((req) => {
  const { nextUrl } = req
  const isLoggedIn = !!req.auth
  const userRole = req.auth?.user?.role

  // Check if the route is public
  const isPublicRoute = publicRoutes.some(route => 
    nextUrl.pathname === route || nextUrl.pathname.startsWith(route)
  )

  // Check if the route is admin-only
  const isAdminRoute = adminRoutes.some(route => 
    nextUrl.pathname.startsWith(route)
  )

  // Check if the route is a protected API route
  const isProtectedApiRoute = protectedApiRoutes.some(route => 
    nextUrl.pathname.startsWith(route)
  )

  // Allow access to auth API routes
  if (nextUrl.pathname.startsWith("/api/auth")) {
    return NextResponse.next()
  }

  // Redirect to signin if trying to access protected API routes without authentication
  if (isProtectedApiRoute && !isLoggedIn) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  // Redirect to signin if trying to access admin routes without authentication
  if (isAdminRoute && !isLoggedIn) {
    return NextResponse.redirect(new URL("/auth/signin", nextUrl))
  }

  // Redirect to home if trying to access admin routes without admin role
  if (isAdminRoute && isLoggedIn && userRole !== "ADMIN") {
    return NextResponse.redirect(new URL("/", nextUrl))
  }

  // Redirect authenticated users away from auth pages
  if (isLoggedIn && (nextUrl.pathname.startsWith("/auth/signin") || nextUrl.pathname.startsWith("/auth/signup"))) {
    return NextResponse.redirect(new URL("/", nextUrl))
  }

  return NextResponse.next()
})

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|public/).*)",
  ],
}