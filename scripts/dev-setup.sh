#!/bin/bash

echo "🚀 Setting up MITC Project Management System for Development"
echo "============================================================"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Install dependencies if needed
echo "📦 Checking dependencies..."
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies with pnpm..."
    pnpm install
fi

# Run the seed script to create sample projects
echo "🌱 Seeding project data..."
npx tsx scripts/seed-projects.ts

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p public/assets/img/project
mkdir -p src/data/projects

echo "✅ Setup complete!"
echo ""
echo "🎯 What's been set up:"
echo "   • Project management system with admin interface"
echo "   • TipTap rich text editor for content creation"
echo "   • Predefined content blocks for structured layouts"
echo "   • 3 sample projects with realistic data"
echo "   • Public and admin API endpoints"
echo "   • Dynamic homepage carousel integration"
echo ""
echo "🔗 Available routes:"
echo "   • /projects - Public projects listing"
echo "   • /projects/[slug] - Individual project pages"
echo "   • /admin/projects - Admin project dashboard"
echo "   • /admin/projects/new - Create new project"
echo "   • /admin/projects/[id]/edit - Edit existing project"
echo "   • /admin/projects/categories - Manage categories"
echo "   • /admin/projects/tags - Manage tags"
echo ""
echo "🚀 Ready to start development server:"
echo "   pnpm dev"
echo ""
echo "📝 Sample projects created:"
echo "   1. Enterprise Data Center Cape Town"
echo "   2. Smart City Surveillance Network Johannesburg"
echo "   3. DiGiM Media Broadcasting Platform"
echo ""
echo "Happy coding! 🎉"