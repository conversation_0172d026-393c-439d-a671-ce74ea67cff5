#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 MITC Project Management System - Implementation Verification');
console.log('=============================================================\n');

const checks = [
  {
    name: 'TipTap Dependencies',
    check: () => {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const tiptapDeps = Object.keys(packageJson.dependencies).filter(dep => dep.startsWith('@tiptap/'));
      return {
        success: tiptapDeps.length >= 10,
        details: `Found ${tiptapDeps.length} TipTap packages: ${tiptapDeps.slice(0, 3).join(', ')}...`
      };
    }
  },
  {
    name: 'Project Data Files',
    check: () => {
      const projectsDir = 'src/data/projects';
      if (!fs.existsSync(projectsDir)) return { success: false, details: 'Projects directory not found' };
      
      const files = fs.readdirSync(projectsDir).filter(f => f.endsWith('.json'));
      const sampleProjects = [
        'enterprise-data-center-cape-town.json',
        'smart-city-surveillance-network-johannesburg.json',
        'digim-media-broadcasting-platform.json'
      ];
      
      const foundSamples = sampleProjects.filter(p => files.includes(p));
      return {
        success: foundSamples.length === 3,
        details: `Found ${files.length} project files, ${foundSamples.length}/3 sample projects`
      };
    }
  },
  {
    name: 'Admin Routes',
    check: () => {
      const adminRoutes = [
        'src/app/admin/projects/page.tsx',
        'src/app/admin/projects/new/page.tsx',
        'src/app/admin/projects/[id]/edit/page.tsx',
        'src/app/admin/projects/categories/page.tsx',
        'src/app/admin/projects/tags/page.tsx'
      ];
      
      const existing = adminRoutes.filter(route => fs.existsSync(route));
      return {
        success: existing.length === adminRoutes.length,
        details: `${existing.length}/${adminRoutes.length} admin routes created`
      };
    }
  },
  {
    name: 'Public Routes',
    check: () => {
      const publicRoutes = [
        'src/app/projects/page.tsx',
        'src/app/projects/[slug]/page.tsx'
      ];
      
      const existing = publicRoutes.filter(route => fs.existsSync(route));
      return {
        success: existing.length === publicRoutes.length,
        details: `${existing.length}/${publicRoutes.length} public routes created`
      };
    }
  },
  {
    name: 'API Endpoints',
    check: () => {
      const apiRoutes = [
        'src/app/api/projects/route.ts',
        'src/app/api/projects/[slug]/route.ts'
      ];
      
      const existing = apiRoutes.filter(route => fs.existsSync(route));
      return {
        success: existing.length === apiRoutes.length,
        details: `${existing.length}/${apiRoutes.length} API endpoints created`
      };
    }
  },
  {
    name: 'Content Blocks System',
    check: () => {
      const contentBlockFiles = [
        'src/components/content-blocks/index.tsx',
        'src/components/editor/TipTapEditor.tsx'
      ];
      
      const existing = contentBlockFiles.filter(file => fs.existsSync(file));
      return {
        success: existing.length === contentBlockFiles.length,
        details: `${existing.length}/${contentBlockFiles.length} content system files created`
      };
    }
  },
  {
    name: 'Project Services',
    check: () => {
      const servicesFile = 'src/lib/services/projects.ts';
      if (!fs.existsSync(servicesFile)) return { success: false, details: 'Services file not found' };
      
      const content = fs.readFileSync(servicesFile, 'utf8');
      const hasContentBlocks = content.includes('contentBlocks');
      const hasGetProjects = content.includes('getAllProjects');
      
      return {
        success: hasContentBlocks && hasGetProjects,
        details: `Services file exists with content blocks support: ${hasContentBlocks}`
      };
    }
  },
  {
    name: 'Sample Project Content',
    check: () => {
      const sampleFile = 'src/data/projects/enterprise-data-center-cape-town.json';
      if (!fs.existsSync(sampleFile)) return { success: false, details: 'Sample project not found' };
      
      const project = JSON.parse(fs.readFileSync(sampleFile, 'utf8'));
      const hasContentBlocks = project.contentBlocks && project.contentBlocks.length > 0;
      const hasContent = project.content && project.content.length > 0;
      
      return {
        success: hasContentBlocks && hasContent,
        details: `Sample project has ${project.contentBlocks?.length || 0} content blocks and ${hasContent ? 'rich' : 'no'} content`
      };
    }
  }
];

let allPassed = true;

checks.forEach((check, index) => {
  try {
    const result = check.check();
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${check.name}`);
    console.log(`   ${result.details}\n`);
    
    if (!result.success) allPassed = false;
  } catch (error) {
    console.log(`❌ ${check.name}`);
    console.log(`   Error: ${error.message}\n`);
    allPassed = false;
  }
});

console.log('=============================================================');
if (allPassed) {
  console.log('🎉 All checks passed! The MITC Project Management System is ready.');
  console.log('\n🚀 Next steps:');
  console.log('   1. Run: pnpm dev');
  console.log('   2. Visit: http://localhost:3000/admin/projects');
  console.log('   3. View public projects: http://localhost:3000/projects');
  console.log('\n📝 Sample projects available:');
  console.log('   • Enterprise Data Center Cape Town');
  console.log('   • Smart City Surveillance Network Johannesburg');
  console.log('   • DiGiM Media Broadcasting Platform');
} else {
  console.log('⚠️  Some checks failed. Please review the issues above.');
}

console.log('\n📚 Documentation:');
console.log('   • PROJECT_MANAGEMENT_README.md - Complete system documentation');
console.log('   • IMPLEMENTATION_SUMMARY.md - Implementation overview');
console.log('   • scripts/dev-setup.sh - Development setup script');